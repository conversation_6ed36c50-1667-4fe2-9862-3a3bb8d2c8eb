import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, downloadTemplate, downloadTemplateSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListRequest,
  getListSuccess, importUs, importUsFailure, importUsSuccess, importUsValidate, importUsValidateFailure, importUsValidateSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.USER_STORY_MANAGEMENT}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.USER_STORY_MANAGEMENT + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.USER_STORY_MANAGEMENT + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.user-story-requirements')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.user-story-requirements')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.USER_STORY_MANAGEMENT, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.user-story-requirements')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.user-story-requirements')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.USER_STORY_MANAGEMENT + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.user-story-requirements')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.user-story-requirements')
    }
  }
}

function* handleImportUsValidate(action: Action) {
  if (importUsValidate.match(action)) {
    try {
      const fileList = action.payload
      const url = API_URLS.IMPORT_FILE_VALIDATE_US;
      const res = yield call(apiCall, 'POST', url, fileList)
      yield put(importUsValidateSuccess(res.data));
      ShowAppMessage(null, MESSAGE_TYPES.IMPORT_VALIDATE, 'common.artefact.user-story-requirements')
    } catch (err) {
      ShowAppMessage(err, null, 'common.artefact.user-story-requirements')
      yield put(importUsValidateFailure(null))
    }
  }
}

function* handleDownload(action: Action) {
  if (downloadTemplate.match(action)) {
    try {
      window.open(API_URLS.IMPORT_USECASE, '_blank');
      yield put(downloadTemplateSuccess(null))
    } catch (err) {
      ShowAppMessage(err, null, 'common.artefact.use-case')
    }
  }
}

function* handleImportUs(action: Action) {
  if (importUs.match(action)) {
    try {
      const qualifiedData = action.payload
      const url = API_URLS.IMPORT_FILE_US;
      const res = yield call(apiCall, 'POST', url, qualifiedData)
      yield put(importUsSuccess(null))
      ShowAppMessage(null, MESSAGE_TYPES.IMPORT, 'common.artefact.user-story-requirements')
    } catch (err) {
      importUsFailure(null)
      ShowAppMessage(err, null, 'common.artefact.user-story-requirements')
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
  yield takeLatest(importUsValidate.type, handleImportUsValidate)
  yield takeLatest(importUs.type, handleImportUs)
  yield takeLatest(downloadTemplate.type, handleDownload)
}
export default function* UserStorySaga() {
  yield all([fork(watchFetchRequest)])
}
