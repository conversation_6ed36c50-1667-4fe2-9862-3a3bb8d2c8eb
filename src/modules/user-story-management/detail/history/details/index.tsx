import AppState from '@/store/types'
import {
    <PERSON>read<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import moment from 'moment'
import React, { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, DATE_FORMAT, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, STATUS, USER_STORY_PRIORITY } from '../../../../../constants'
import DeleteButton from '../../../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../../../helper/component/lav-audit-trail'
import LavButtons from '../../../../../helper/component/lav-buttons'
import LavReferences from '../../../../../helper/component/lav-references'
import useModalConfirmationConfig from '../../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../../modules/_shared/comment/type'
import { deleteRequest } from '../../../action'
import HistoryNavigation from '../../../../../modules/history/navigation'
import debounce from 'lodash.debounce'

const { Title, Text } = Typography

interface UserStoryVersionDetailsProps {
    data: any | [],
    userStoryID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any,
    setSelectedRowVersion: (version: string) => void, 
    onDismiss: () => void | null, 
}
const UserStoryVersionDetails = ({ data, userStoryID, onChange, isLoading, isModalShow, setScreenMode, setSelectedRowVersion, onDismiss }: UserStoryVersionDetailsProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const modalConfirmConfig = useModalConfirmationConfig()
    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);


    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;
    useEffect(() => {
        if(data)
            document.title = intl.formatMessage({ id: 'user-story.header.title-short' }) + "-" + data?.code; 
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.USER_STORY);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [

            { field: 'project', title: intl.formatMessage({ id: 'user-story.column.project' }), },
            { field: 'component', title: intl.formatMessage({ id: 'user-story.column.component' }), },
            { field: 'uscode', title: intl.formatMessage({ id: 'user-story.column.uscode' }), },
            { field: 'label', title: intl.formatMessage({ id: 'user-story.column.label' }), },
            { field: 'summary', title: intl.formatMessage({ id: 'user-story.column.summary' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'user-story.column.assignee' }), },
            { field: 'estimation', title: intl.formatMessage({ id: 'user-story.column.estimation' }), },
            { field: 'estimation', title: intl.formatMessage({ id: 'user-story.column.estimation' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'user-story.column.reviewer' }), },
            { field: 'story-point', title: intl.formatMessage({ id: 'user-story.column.story-point' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'user-story.column.due-date' }), },
            { field: 'priority', title: intl.formatMessage({ id: 'user-story.column.priority' }), },
            { field: 'epic', title: intl.formatMessage({ id: 'user-story.column.epic' }), },
            { field: 'sprint', title: intl.formatMessage({ id: 'user-story.column.sprint' }), },
            { field: 'product', title: intl.formatMessage({ id: 'user-story.column.product' }), },
            { field: 'description', title: intl.formatMessage({ id: 'user-story.column.description' }), },
            { field: 'acceptance-criteria', title: intl.formatMessage({ id: 'user-story.column.acceptance-criteria' }), },

        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.USER_STORY,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {intl.formatMessage({ id: 'user-story.header.title-short' })} - {data?.code}
                    </Title>
                </div>


                <Space size="small">
                    <LavButtons
                        url={`${API_URLS.USER_STORY_MANAGEMENT}/${data?.id}`}
                        reviewer={`${data?.reviewer}`}
                        customer={`${data?.customer}`}
                        artefact_type="common.artefact.user-story-requirements"
                        status={data?.status}
                        artefactType={REQ_ARTEFACT_TYPE_ID.USER_STORY}
                        id={userStoryID}
                        changePage={() => onChange()}>                                          
                        <Button onClick={debounce(onDismiss, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>
                    </LavButtons>
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />                      
            { data?.nextPrevious.latestVersion === data?.version ? <></>:
                   <HistoryNavigation data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.user-story-requirements"} artefactType={REQ_ARTEFACT_TYPE_ID.USER_STORY} />
                }
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Row>
                            <Col span={3}>{renderStatusBadge(data?.status)}</Col>
                        </Row>
                        <Card
                            bordered={true}
                        >
                            <Row gutter={[0, 16]} className='rq-card-infor'>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="project">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.project' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>{projectCode}</Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="component">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.component' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.jiraComponent?.name}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="uscode">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.uscode' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.code}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="label">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.label' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.label}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={24}>
                                    <Row>
                                        <Col span={3}>
                                            <TriggerComment field="summary">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.summary' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={21}>
                                            {data?.summary}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="assignee">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.assignee' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.assignee}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="estimation">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.estimation' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.estimation}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="reviewer">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.reviewer' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.reviewer}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="story-point">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.story-point' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.storyPoint}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="due-date">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.due-date' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.dueDate ? moment(data?.dueDate).format(DATE_FORMAT) : ''}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="priority">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.priority' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {USER_STORY_PRIORITY.find(x => x.value == data?.priority)?.label}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="epic">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.epic' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.epic?.name ? <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT_DETAIL}${data.epic.id}`}>{data.epic.name}</Link> : <></>}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={12}>
                                    <Row>
                                        <Col span={6}>
                                            <TriggerComment field="sprint">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.sprint' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={18}>
                                            {data?.sprint?.name ? <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SPRINT_MANAGEMENT_DETAIL}${data.sprint.id}`}>{data.sprint.name}</Link> : <></>}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={24}>
                                    <Row>
                                        <Col span={3}>
                                            <TriggerComment field="product">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.product' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={21}>
                                            {data?.product?.name}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={24}>
                                    <Row>
                                        <Col span={3}>
                                            <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.objects' })}</Text>
                                        </Col>
                                        <Col span={21}>
                                        {data?.objects?.map((x, index) => (
                                            <React.Fragment key={index}>
                                                {x.name}
                                                {index !== data.objects.length - 1 && ', '}
                                            </React.Fragment>
                                        ))}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={24}>
                                    <Row>
                                        <Col span={3}>
                                            <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.useCase' })}</Text>
                                        </Col>
                                        <Col span={21}>
                                        {data?.functions?.map((x, index) => (
                                            <React.Fragment key={index}>
                                                {x.name}
                                                {index !== data.functions.length - 1 && ', '}
                                            </React.Fragment>
                                        ))}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={24}>
                                    <Row>
                                        <Col span={3}>
                                            <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.screens' })}</Text>
                                        </Col>
                                        <Col span={21}>
                                        {data?.screens?.map((x, index) => (
                                            <React.Fragment key={index}>
                                                {x.name}
                                                {index !== data.screens.length - 1 && ', '}
                                            </React.Fragment>
                                        ))}
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={24}>
                                    <Row>
                                        <Col span={3}>
                                            <TriggerComment field="description">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.description' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={21}>
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: data?.description,
                                                }}
                                            ></div>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col span={24}>
                                    <Row>
                                        <Col span={3}>
                                            <TriggerComment field="acceptance-criteria">
                                                <Text type="secondary">{intl.formatMessage({ id: 'user-story.column.acceptance-criteria' })}</Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={21}>
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: data?.acceptanceCriteria,
                                                }}
                                            ></div>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Card>
                        <LavReferences data={data} />

                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default UserStoryVersionDetails
