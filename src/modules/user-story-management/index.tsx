import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import ExportButton from '../../helper/component/lav-table/export'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import AppCommonService from '../../services/app.service'
import UserStoryManagementFormPage from './form/form'
import Import from './import'

const UserStoryManagement = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  const [columns, setColumns] = useState<any>(null)
  const [lstEpics, setLstEpics] = useState<any>([])
  const [lstSprints, setLtSprints] = useState<any>([])
  const [lstMembers, setLstMembers] = useState<any>([])


  useEffect(() => {       
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'user-story.header.title'}); 
  }, [screenMode])

  useEffect(() => {
    AppCommonService.getData3List(
      `${API_URLS.REFERENCES_EPIC_USER_STORY}`,
      `${API_URLS.REFERENCES_SPRINT_USER_STORY}`,
      `${API_URLS.REFERENCES_MEMBERS}?Role=BA`
    ).then((res) => {
      const lstEpics = res[0].map(e => { return { value: e.id, text: e.name } })
      const lstSprints = res[1].map(e => { return { value: e.id, text: e.name } })
      const lstMembers = res[2].map(e => { return { value: e.userName, text: e.fullName } })

      setLstEpics(lstEpics)
      setLtSprints(lstSprints)
      setLstMembers(lstMembers)
      // setColumns(configColumns(lstEpics, lstSprints, lstMembers));
    }).catch(err => {
      setLstEpics([])
      setLtSprints([])
      setLstMembers([])
    })
  }, [])


  useEffect(() => {
    setColumns(configColumns(lstEpics, lstSprints, lstMembers))
  }, [lstEpics, lstSprints, lstMembers])
  const configColumns = (lstEpics, lstSprints, lstMembers) => [
    {
      title: intl.formatMessage({ id: 'user-story.column.user-requirement-code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      }
    },
    {
      title: intl.formatMessage({ id: 'user-story.column.user-summary' }),
      dataIndex: 'summary',
      width: '45%',
      sorter: true,
      ...getColumnSearchProps('summary', SEARCH_TYPE.TEXT),
    },
    {
      title: intl.formatMessage({ id: 'user-story.column.epic' }),
      dataIndex: 'userStoryEpics',
      width: '20%',
      ...getColumnDropdownFilterProps(lstEpics, 'Epics', true, false),
      render: (text: any) => {
        return text?.name ? <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT_DETAIL}${text.id}`}>{text.name}</Link> : <></>
      }
    },
    {
      title: intl.formatMessage({ id: 'user-story.column.sprint' }),
      dataIndex: 'userStorySprints',
      width: '20%',
      ...getColumnDropdownFilterProps(lstSprints, 'Sprints', true, false),
      render: (text: any) => {
        return text?.name ? <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SPRINT_MANAGEMENT_DETAIL}${text.id}`}>{text.name}</Link> : <></>
      }
    },
    {
      title: intl.formatMessage({ id: 'user-story.column.assignee' }),
      dataIndex: 'assignee',
      ...getColumnDropdownFilterProps(lstMembers, 'assignee', true, false),
      sorter: true,
      width: '7%',
    },

    {
      title: intl.formatMessage({ id: 'user-story.column.status' }),
      dataIndex: 'status',
      ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      sorter: true,
      width: '80px',
      render: (record) => renderStatusBadge(record),
    },

  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'sprint-management.create' })}
      </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && record.status !== STATUS.DELETE) ?
      children : <></>
  }

  const ImportComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && <Import onFinish={() => handleDataChange()} />
  }

  const ExportComponent: React.FC<any> = ({ handleExport }) => {
    return <ExportButton fileName='user-story.file-name-export' onExport={handleExport} title='user-story.button.export-user-story'/>
  }


  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW && columns?.length != 0 ? <LavTable
        title="user-story.header.title"
        artefact_type="common.artefact.user-story-requirements"
        apiUrl={API_URLS.USER_STORY_MANAGEMENT}
        columns={columns}
        artefactType={REQ_ARTEFACT_TYPE_ID.USER_STORY}
        updateComponent={UpdateComponent}
        createComponent={CreateComponent}
        deleteComponent={DeleteComponent}
        importComponent={ImportComponent}
        exportComponent={ExportComponent}
      /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <UserStoryManagementFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <UserStoryManagementFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} id={id} /> : <></>
      }
    </Space>
  )
}

export default UserStoryManagement
