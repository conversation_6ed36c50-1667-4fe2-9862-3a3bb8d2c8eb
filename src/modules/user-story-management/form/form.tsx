import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Bread<PERSON><PERSON>b,
  Button,
  Card, Col, DatePicker, Form, Input, InputNumber, Modal, Row, Select, Space, Spin, Tag, Typography
} from 'antd'
import Text from 'antd/lib/typography/Text'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, DATE_FORMAT, MESSAGE_TYPES, PROJECT_PREFIX, SCREEN_MODE, STATUS, USER_STORY_PRIORITY } from '../../../constants'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomModal from '../../../helper/component/custom-modal'
import FormGroup from '../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, currentUserName, extractProjectCode, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import AppCommonService from '../../../services/app.service'
import { initComment, initCommentScreen } from '../../_shared/comment/action'
import TriggerComment from '../../_shared/comment/trigger-comment'
import { CommentState } from '../../_shared/comment/type'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { UserStoryManagementState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { confirm } = Modal
const { Option } = Select
const { Title } = Typography

interface UserStoryFormProps {
  id?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}

interface UserStoryFormModalProps {
  id?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const UserStoryManagementFormPage = ({ id, screenMode, onFinish, onDismiss }: UserStoryFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const getCkeditorData: any = createRef()
  const getCkeditorDataAccept: any = createRef()
  const state = useSelector<AppState | null>((s) => s?.UserStory) as UserStoryManagementState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [members, setMembers] = useState<any[]>([]);
  const [memberReviewer, setMemberReviewer] = useState<any[]>([]);

  const [epics, setEpic] = useState<any[]>([]);
  const [sprint, setSprint] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [objects, setObjects] = useState<any[]>([]);
  const [useCases, setUseCases] = useState<any[]>([]);
  const [screens, setScreens] = useState<any[]>([]);
  const [components, setComponents] = useState<any[]>([]);
  const [description, setDescription] = useState<any>('');
  const [acceptanceCriteria, setAcceptanceCriteria] = useState<any>('');
  const projectCode = extractProjectCode();

  useBeforeUnload()
  // Destroy
  useEffect(() => {
    form.setFieldsValue({
      'priority': 3,
      'dueDate': moment(new Date(), DATE_FORMAT),
      'assignee': currentUserName()
    })
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    form.setFieldsValue({ reviewer: '' })
    AppCommonService.getMembers().then(res => setMembers(res.data)).catch((err) => setMembers([]));
    AppCommonService.getMembersBACustomers().then(res => setMemberReviewer(res.data)).catch((err) => setMemberReviewer([]));

    form.setFieldsValue({ epic: '' })
    AppCommonService.getReferenceEpic().then(res => setEpic(res.data)).catch((err) => setEpic([]));
    form.setFieldsValue({ sprint: '' })
    AppCommonService.getReferenceSprint().then(res => setSprint(res.data)).catch((err) => setSprint([]));
    form.setFieldsValue({ product: '' })
    AppCommonService.getReferenceProduct().then(res => setProducts(res.data)).catch((err) => setProducts([]));

    form.setFieldsValue({ component: '' })
    AppCommonService.getComponents().then(res => setComponents(res.data)).catch((err) => setComponents([]));

    AppCommonService.getData3List(
      `${API_URLS.REFERENCES_OBJECTS}`,
      `${API_URLS.REFERENCES_FUNCTIONS}`,
      `${API_URLS.REFERENCES_SCREENS}`,
    ).then((res) => {
      const lstObject = res[0];
      const lstFuncs = res[1];
      const lstScreens = res[2];

      setObjects(lstObject)
      setUseCases(lstFuncs)
      setScreens(lstScreens)
    }).catch(err => {
      setObjects([])
      setUseCases([])
      setScreens([])
    })
  }, [])



  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
    if (screenMode === SCREEN_MODE.CREATE) {
      form.setFieldsValue({
        'priority': 3,
        'dueDate': moment(new Date(), DATE_FORMAT),
        'assignee': currentUserName()
      })
    }
    document.title = intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'user-story.title-update' : 'user-story.title-create' });
  }, [screenMode, id])

  const getList = (data: any) => {
    if (data?.source === 1) {
      const listMeetings = data?.meetingMinutes?.map((e) => e?.name)
      return listMeetings
    } else if (data?.source === 2) {
      const listReferences = data?.referenceDocumnets?.map((e) => e.name)
      return listReferences
    } else if (data?.source === 3) {
      const sourceOther = data?.sourceValueOther
      return sourceOther
    }
  }

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        project: state.detail.code,
        name: state.detail.name,
        label: state.detail.label,
        component: state?.detail?.jiraComponent?.id,
        priority: state.detail.priority,
        estimation: state.detail.estimation,
        reviewer: state.detail.reviewer,
        storyPoint: state.detail.storyPoint,
        summary: state.detail.summary,
        epicsId: state.detail?.epic?.id,
        sprintsId: state.detail?.sprint?.id,
        productsId: state.detail?.product?.id,
        sourceValue: getList(state?.detail),
        description: state.detail.description,
        acceptanceCriteria: state.detail.acceptanceCriteria,
        dueDate: state.detail.dueDate ? moment(new Date(state.detail.dueDate)) : null,
        assignee: state.detail.assignee,
        objectIds: state.detail?.objects.map(
          (object: any) => object?.name
        ),
        functionIds: state.detail?.functions.map(
          (object: any) => object?.name
        ),
        screenIds: state.detail?.screens.map(
          (object: any) => object?.name
        ),
      })
      setDescription(state.detail.description);
      setAcceptanceCriteria(state.detail.acceptanceCriteria);
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm();
        form.setFieldsValue({
          assignee: currentUserName(),
          dueDate: moment(new Date()),
        })
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss();
      }
      setIsDraft(false);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce(async (values: any, st?: string) => {
    const lstObject: any[] = [];
    const lstUseCase: any[] = [];
    const lstScreen: any[] = [];
    values.objectIds?.forEach((objs) => {
      const object: any = objects.find(
          (item: any) => item.name === objs
      )
      if (object) lstObject.push(object?.id)
    })
    values.functionIds?.forEach((objs) => {
      const object: any = useCases.find(
          (item: any) => item.name === objs
      )
      if (object) lstUseCase.push(object?.id)
    })
    values.screenIds?.forEach((objs) => {
      const object: any = screens.find(
          (item: any) => item.name === objs
      )
      if (object) lstScreen.push(object?.id)
    })
    let mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
    mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(getCkeditorDataAccept.current?.props?.data))
    const requestData: any = {
      id: id || null,
      jiraComponentId: values.component,
      summary: values.summary,
      label: values.label,
      status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
      assignee: values.assignee,
      reviewer: values.reviewer,
      customer: values.customer || '',
      estimation: values.estimation,
      storyPoint: values.storyPoint,
      dueDate: values.dueDate,
      priority: values.priority,
      description: getCkeditorData.current?.props?.data,
      acceptanceCriteria: getCkeditorDataAccept.current?.props?.data,
      epicsId: values.epicsId ? values.epicsId : null,
      sprintsId: values.sprintsId ? values.sprintsId : null,
      productsId: values.productsId,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
      messageAction: !isDraft ? values.reviewer === currentUserName() ? MESSAGE_TYPES.ENDORSE : MESSAGE_TYPES.SUBMIT : null,
      objectIds: lstObject,
      functionIds: lstUseCase,
      screenIds:  lstScreen
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.user-story-requirements' }) }
        ),
        onOk() {
          requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {
        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(false);

    form.resetFields([
      'project',
      'name',
      'label',
      'component',
      'priority',
      'estimation',
      'reviewer',
      'storyPoint',
      'summary',
      'epicsId',
      'sprintsId',
      'productsId',
      'sourceValue',
      'description',
      'acceptanceCriteria',
      'dueDate'
    ])
    if (screenMode === SCREEN_MODE.CREATE) {
      form.setFieldsValue({
        'priority': 3,
        'dueDate': moment(new Date(), DATE_FORMAT),
        'assignee': currentUserName()
      })
    }
  }

  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'project', title: intl.formatMessage({ id: 'user-story.column.project' }), },
      { field: 'component', title: intl.formatMessage({ id: 'user-story.column.component' }), },
      { field: 'uscode', title: intl.formatMessage({ id: 'user-story.column.uscode' }), },
      { field: 'label', title: intl.formatMessage({ id: 'user-story.column.label' }), },
      { field: 'summary', title: intl.formatMessage({ id: 'user-story.column.summary' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'user-story.column.assignee' }), },
      { field: 'estimation', title: intl.formatMessage({ id: 'user-story.column.estimation' }), },
      { field: 'estimation', title: intl.formatMessage({ id: 'user-story.column.estimation' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'user-story.column.reviewer' }), },
      { field: 'story-point', title: intl.formatMessage({ id: 'user-story.column.story-point' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'user-story.column.due-date' }), },
      { field: 'priority', title: intl.formatMessage({ id: 'user-story.column.priority' }), },
      { field: 'epic', title: intl.formatMessage({ id: 'user-story.column.epic' }), },
      { field: 'sprint', title: intl.formatMessage({ id: 'user-story.column.sprint' }), },
      { field: 'product', title: intl.formatMessage({ id: 'user-story.column.product' }), },
      { field: 'description', title: intl.formatMessage({ id: 'user-story.column.description' }), },
      { field: 'acceptance-criteria', title: intl.formatMessage({ id: 'user-story.column.acceptance-criteria' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.USER_STORY,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#region COMMENT INIT

  const tagRender = (props) => {
    const { label, name, value, closable, onClose } = props;


    return (
        <Tag
            // color={value}
            // onMouseDown={onPreventMouseDown}
            closable={closable}
            onClose={onClose}
            style={{
                marginRight: 3,
                border: 'none',
            }}
            title={label}
        >
            {label.length > 20 ? label.substring(0, 20) + '...' : label}
        </Tag>
    );
};

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={10}>
            <Space size="large">
              <Space size="large">
                <Title level={4}>
                  {intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'user-story.title-update' : 'user-story.title-create' })}
                </Title>

              </Space>
            </Space>
          </Col>

          <Col span={14}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                  <Button type="primary" ghost htmlType="submit" onClick={() => setIsDraft(false)}>
                    {intl.formatMessage({ id: 'common.action.submit' })}
                  </Button> : <></>
                }

                <Button onClick={() => {
                  setIsDraft(true);
                  form.validateFields(['reviewer'])
                }} className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                </Button> : <></>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Row align="middle">
          {screenMode === SCREEN_MODE.EDIT ?
            <Col span={5}>
              <div className='status-container'>
                <div>
                  {intl.formatMessage({ id: 'common.field.status' })}
                </div>
                <div>
                  {renderStatusBadge(state.detail?.status)}
                </div>
              </div>
            </Col> : <></>
          }
        </Row>
        <Card className='rq-form-block'>
          <Row gutter={[40, 16]}>
            <Col span={10} style={{ display: 'flex', gap: '8px', paddingTop: "4px" }}>
              <TriggerComment screenMode={screenMode} field="project">
                <Text>
                  {intl.formatMessage({
                    id: 'user-story.column.project',
                  })}
                </Text>
              </TriggerComment>
              <Breadcrumb className='rq-breadcrumb' separator=">">
                <Breadcrumb.Item>
                  {projectCode}
                </Breadcrumb.Item>
              </Breadcrumb>
            </Col>
            <Col span={10}>
              <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="component">
                {intl.formatMessage({ id: 'user-story.column.component' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="component">
                  <Select>
                    {
                      components.map((e, idx) => {
                        return <Option key={idx} value={e.id}>{e.name}</Option>
                      })
                    }
                  </Select>
                </Form.Item>
              </FormGroup>

            </Col>
          </Row>
          <Row gutter={[40, 16]}>
            <Col span={10}>
              {
                screenMode === SCREEN_MODE.EDIT ? <>
                  <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="uscode">
                    {intl.formatMessage({ id: 'user-story.column.uscode' })}
                  </TriggerComment>}>

                    <Form.Item name="project">
                      <Input value={state.detail?.code} disabled maxLength={255} />
                    </Form.Item>
                  </FormGroup>
                </> : <></>
              }
            </Col>
            <Col span={10}>
              <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="label">
                {intl.formatMessage({ id: 'user-story.column.label' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="label">
                  <Input />
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>


          <Row gutter={[40, 16]}>
            <Col span={20}>
              <FormGroup required className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="summary">
                {intl.formatMessage({ id: 'user-story.column.summary' })}
              </TriggerComment>}>
                <Form.Item rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ]} validateTrigger="onBlur" name="summary">
                  <Input />
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>

          <Row gutter={[40, 16]}>
            <Col span={10}>
              <FormGroup required className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="assignee">
                {intl.formatMessage({ id: 'user-story.column.assignee' })}
              </TriggerComment>}>
                <Form.Item rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ]} validateTrigger="onBlur" name="assignee">
                  <Select
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    showSearch
                    allowClear
                    className='full-width'>
                    {
                      members.map((member, idx) => (
                        <Option key={idx} value={member.userName}>{member.fullName}</Option>
                      ))
                    }
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={10}>
              <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="estimation">
                {intl.formatMessage({ id: 'user-story.column.estimation' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="estimation">
                  <InputNumber min={0} maxLength={9} />
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>
          <Row gutter={[40, 16]}>
            <Col span={10}>
              <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="reviewer">
                {intl.formatMessage({ id: 'user-story.column.reviewer' })}
              </TriggerComment>}>
                <Form.Item rules={(isDraft === false || state.detail?.status == STATUS.SUBMITTED) ? [
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ] : []} validateTrigger="onBlur" name="reviewer">
                  <Select
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    showSearch
                    allowClear
                    className='full-width'>
                    {
                      memberReviewer.map((member, idx) => (
                        <Option key={idx} value={member.userName}>{member.fullName}</Option>
                      ))
                    }
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={10}>
              <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="story-point">
                {intl.formatMessage({ id: 'user-story.column.story-point' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="storyPoint">
                  <InputNumber min={0} maxLength={9} />
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>

          <Row gutter={[40, 16]}>
            <Col span={10}>
              <FormGroup required className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="due-date">
                  {intl.formatMessage({ id: 'user-story.column.due-date' })}
                </TriggerComment>
              }>
                <Form.Item validateTrigger="onBlur" name="dueDate" rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  {
                    validator: async (rule, value) => {
                      if (value && value.startOf('day') < moment().startOf('day')) {
                        throw new Error(intl.formatMessage({ id: 'IEM_3_2' }))
                      }
                    },
                  }
                ]}>
                  <DatePicker format={DATE_FORMAT} disabledDate={disabledDate} />
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={10}>
              <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="priority">
                {intl.formatMessage({ id: 'user-story.column.priority' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="priority">
                  <Select>
                    {
                      USER_STORY_PRIORITY.map((e, idx) => {
                        return <Option key={idx} value={e.value}>{e.label}</Option>
                      })
                    }
                  </Select>
                </Form.Item>
              </FormGroup>

            </Col>
          </Row>
          <Row gutter={[40, 16]}>
            <Col span={10}>
              <FormGroup required className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="epic">
                {intl.formatMessage({ id: 'user-story.column.epic' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="epicsId" rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ]}>
                  <Select
                    // defaultValue={state.detail?.epic?.name ? state.detail?.epic?.name : null}
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    showSearch
                    allowClear
                    className='full-width'>
                    {
                      epics.map((epics, idx) => (
                        <Option key={idx} value={epics.id}>{epics.name}</Option>
                      ))
                    }
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={10}>
              <FormGroup className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="sprint">
                {intl.formatMessage({ id: 'user-story.column.sprint' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="sprintsId">
                  <Select
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    showSearch
                    allowClear
                    className='full-width'>
                    {
                      sprint.map((sprint, idx) => (
                        <Option key={idx} value={sprint.id}>{sprint.name}</Option>
                      ))
                    }
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>
          <Row gutter={[40, 16]}>
            <Col span={10}>
              <FormGroup required className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="product">
                {intl.formatMessage({ id: 'user-story.column.product' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="productsId" rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ]}>
                  <Select
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    showSearch
                    allowClear
                    className='full-width'>
                    {
                      products.map((products, idx) => (
                        <Option key={idx} value={products.id}>{products.name}</Option>
                      ))
                    }
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={10}>
              <FormGroup inline label={`${intl.formatMessage({ id: 'user-story.column.objects' })}`} labelSpan={24} controlSpan={24}>
                <Form.Item validateTrigger="onBlur" name="objectIds">
                  <Select
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    mode="multiple"
                    optionLabelProp="label"
                    showSearch
                    allowClear
                    tagRender={tagRender}
                    >
                      {objects?.map(
                        (item: any, index: number) =>
                          item.status !== STATUS.DELETE &&
                          item.status !== STATUS.CANCELLED && (
                          <Option key={index} value={item.name} label={item.name}>{item.name}</Option>
                        )
                      )}
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>
          <Row gutter={[40, 16]}>
            <Col span={10}>
            <FormGroup inline label={`${intl.formatMessage({ id: 'user-story.column.useCase' })}`} labelSpan={24} controlSpan={24}>
                <Form.Item validateTrigger="onBlur" name="functionIds">
                <Select
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    mode="multiple"
                    optionLabelProp="label"
                    showSearch
                    allowClear
                    tagRender={tagRender}
                    >
                      {useCases?.map(
                        (item: any, index: number) =>
                          item.status !== STATUS.DELETE &&
                          item.status !== STATUS.CANCELLED && (
                          <Option key={index} value={item.name} label={item.name}>{item.name}</Option>
                        )
                      )}
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={10}>
              <FormGroup inline label={`${intl.formatMessage({ id: 'user-story.column.screens' })}`} labelSpan={24} controlSpan={24}>
                <Form.Item validateTrigger="onBlur" name="screenIds">
                <Select
                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    mode="multiple"
                    optionLabelProp="label"
                    showSearch
                    allowClear
                    tagRender={tagRender}
                    >
                      {screens?.map(
                        (item: any, index: number) =>
                          item.status !== STATUS.DELETE &&
                          item.status !== STATUS.CANCELLED && (
                          <Option key={index} value={item.name} label={item.name}>{item.name}</Option>
                        )
                      )}
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>

          <Row gutter={[40, 16]}>
            <Col span={24}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field="description">
                  {intl.formatMessage({ id: 'user-story.column.description' })}
                </TriggerComment>
              }>
                <Form.Item
                  name="description"
                  labelAlign="left"
                  wrapperCol={{ span: 24 }}
                >
                  <CkeditorMention
                    ref={getCkeditorData}
                    data={description || ''}
                  />
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>
          <Row gutter={[40, 16]}>
            <Col span={24}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field="acceptance-criteria">
                  {intl.formatMessage({ id: 'user-story.column.acceptance-criteria' })}
                </TriggerComment>
              }>
                <Form.Item
                  name="acceptanceCriteria"
                  labelAlign="left"
                  wrapperCol={{ span: 24 }}
                >
                  <CkeditorMention
                    ref={getCkeditorDataAccept}
                    data={acceptanceCriteria || ''}
                  />
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>
        </Card>
      </Space>

    </Form>
  </Spin>
}

// const UserStoryManagementForm = ({ id, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: UserStoryFormProps) => {
//   const dispatch = useDispatch();
//   const [isModalVisible, setIsModalVisible] = useState<any>(null)

//   useEffect(() => {
//     if (isModalVisible !== null) {
//       dispatch(setModalVisible(isModalVisible))
//     }
//   }, [isModalVisible])


//   return (
//     <>
//       {
//         buttonType === BUTTON_TYPE.TEXT ?
//           <Button
//             ghost={screenMode === SCREEN_MODE.CREATE}
//             type='primary'
//             className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
//             onClick={() => setIsModalVisible(true)}
//             icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
//           >
//             {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'user-story.button.create-us' : 'common.action.update' })}
//           </Button> :
//           buttonType === BUTTON_TYPE.ICON ?
//             <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
//             <></>
//       }
//       {isModalVisible === true ? <UserStoryFormModal id={id} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
//     </>
//   )
// }
export default UserStoryManagementFormPage
