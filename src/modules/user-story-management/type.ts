export interface UserStoryManagementState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  importSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail: UserStoryDetail | null
  selectedData?: UserStoryDetail | null
  importValidateResponse: any
  importResponse: any
  isModalShow?: boolean
}

interface epics {
  activeStatus: number,
  auditTrailDescription: string,
  code: string,
  createdBy: string,
  dateCreated: string
  dateUpdated: string
  deletedBy: string
  description: string
  id: number
  name: string | null
  project: string
  projectId: number
  status: number
  updatedBy: string
}
export interface UserStoryDetail {
  id?: number | null,
  code: string,
  name: string,
  component?: any | null,
  jiraComponent?: any | null,
  summary?: string,
  label?: string,
  status: number | null,
  assignee?: string,
  reviewer?: string,
  customer: string
  estimation?: number | null,
  storyPoint?: number | null,
  dueDate: string,
  priority?: string,
  description: string,
  acceptanceCriteria?: string,
  epic?: any | null,
  sprint?: any | null,
  product?: any | null,
  mentionReferences?: any | null,
  projectId?: number | null
  auditTrail?: any | null,
  objects: any [],
  functions: any [],
  screens: any [],
}
export const defaultState: UserStoryManagementState = {
  detail: {
    id: null,
    code: '',
    name: '',
    component: null,
    summary: '',
    label: '',
    status: 0,
    assignee: '',
    reviewer: '',
    customer: '',
    estimation: null,
    storyPoint: null,
    dueDate: '',
    priority: '',
    description: '',
    acceptanceCriteria: '',
    epic: null,
    sprint: null,
    product: null,
    mentionReferences: null,
    projectId: null,
    auditTrail: null,
    objects: [],
    functions: [],
    screens: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  importSuccess: false,
  isLoadingList: false,
  listData: [],
  importValidateResponse: '',
  importResponse: '',
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/USER_STORY_MANAGEMENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/USER_STORY_MANAGEMENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/USER_STORY_MANAGEMENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/USER_STORY_MANAGEMENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/USER_STORY_MANAGEMENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/USER_STORY_MANAGEMENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/USER_STORY_MANAGEMENT/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/USER_STORY_MANAGEMENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/USER_STORY_MANAGEMENT/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/USER_STORY_MANAGEMENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/USER_STORY_MANAGEMENT/DELETE_FAILED',

  IMPORT_FILE_VALIDATE = '@@MODULES/USER_STORY_MANAGEMENT/IMPORT_FILE_VALIDATE',
  IMPORT_FILE_VALIDATE_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/IMPORT_FILE_VALIDATE_SUCCESS',
  IMPORT_FILE_VALIDATE_FAILURE = '@@MODULES/USER_STORY_MANAGEMENT/IMPORT_FILE_VALIDATE_FAILURE',
  IMPORT_FILE = '@@MODULES/USER_STORY_MANAGEMENT/IMPORT_FILE',
  IMPORT_FILE_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/IMPORT_FILE_SUCCESS',
  IMPORT_FILE_AFTER_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/IMPORT_FILE__AFTER_SUCCESS',
  IMPORT_FILE_FAILURE = '@@MODULES/USER_STORY_MANAGEMENT/IMPORT_FILE_FAILURE',
  DOWNLOAD_FILE = '@@MODULES/USER_STORY_MANAGEMENT/DOWNLOAD_FILE',
  DOWNLOAD_FILE_SUCCESS = '@@MODULES/USER_STORY_MANAGEMENT/DOWNLOAD_FILE_SUCCESS',
  DOWNLOAD_FILE_FAILURE = '@@MODULES/USER_STORY_MANAGEMENT/DOWNLOAD_FILE_FAILURE',

  SET_MODAL_VISIBLE = '@@MODULES/USER_STORY_MANAGEMENT/SET_MODAL_VISIBLE',
}


