import { createReducer } from '@reduxjs/toolkit';
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, downloadTemplate, downloadTemplateFailure, downloadTemplateSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListRequest,
  getListSuccess, importUs, importUsAfterSuccess, importUsFailure, importUsSuccess, importUsValidate, importUsValidateFailure, importUsValidateSuccess, resetState, setModalVisible, updateFailed, updateRequest,
  updateSuccess
} from './action';
import { defaultState, UserStoryManagementState } from './type';

const initState: UserStoryManagementState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })

      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })

      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })

      .addCase(importUsValidate, (state, action) => {
        state.isLoading = true
      })

      .addCase(importUsValidateSuccess, (state, action) => {
        state.isLoading = false
        state.importValidateResponse = action.payload
      })

      .addCase(importUsValidateFailure, (state, action) => {
        state.isLoading = false
        state.importValidateResponse = action.payload
      })

      .addCase(importUs, (state, action) => {
        state.isLoading = true
      })

      .addCase(importUsSuccess, (state, action) => {
        state.isLoading = false
        state.importSuccess = true
      })

      .addCase(importUsAfterSuccess, (state, action) => {
        state.importSuccess = false
      })

      .addCase(importUsFailure, (state, action) => {
        state.isLoading = false
        state.importSuccess = false
      })

      .addCase(downloadTemplate, (state, action) => {
        state.isLoading = true
      })
      .addCase(downloadTemplateSuccess, (state, action) => {
        state.isLoading = false
      })
      .addCase(downloadTemplateFailure, (state, action) => {
        state.isLoading = false
      })
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if (!action.payload) {
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })
  )
})

export default reducer
export { initState as UserStoryManagementState };

