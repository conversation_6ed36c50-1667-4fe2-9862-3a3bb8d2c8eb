import { createReducer } from '@reduxjs/toolkit'
import { createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, getDetailFailed, getDetailRequest, getDetailSuccess, getListFailed, getListFunction, getListFunctionFailure, getListFunctionSuccess, getListRequest, getListSuccess, getSourceObject, getSourceObjectFailure, getSourceObjectSuccess, resetState, setModalVisible, updateFailed, updateRequest, updateSuccess, viewDetailFailed, viewDetailRequest, viewDetailSuccess } from './action'
import { defaultState, WorkFlowState } from './type'

const initState: WorkFlowState = defaultState

const reducer = createReducer(initState, (builder) => {
  return builder
  .addCase(resetState, (state, action?) => {
    Object.assign(state, {
      ...defaultState,
      selectedData: state.selectedData,
      listData: state.listData
    });
  })

  .addCase(getListRequest, (state, action?) => {
    state.isLoadingList = true;
  })
  .addCase(getListSuccess, (state, action) => {
    state.isLoadingList = false
    state.listData = action.payload
  })
  .addCase(getListFailed, (state, action) => {
    state.isLoadingList = false
    state.listData = null
  })

  .addCase(getDetailRequest, (state, action?) => {
    state.isLoading = true;
  })
  .addCase(getDetailSuccess, (state, action) => {
    state.isLoading = false
    state.detail = action.payload
    state.selectedData = action.payload
  })
  .addCase(getDetailFailed, (state, action) => {
    state.isLoading = false
    state.detail = null
    state.selectedData = null
  })

  .addCase(viewDetailRequest, (state, action?) => {
    state.isLoading = true;
  })
  .addCase(viewDetailSuccess, (state, action) => {
    state.isLoading = false
    state.selectedData = action.payload
  })
  .addCase(viewDetailFailed, (state, action) => {
    state.isLoading = false
    state.selectedData = null
  })


  .addCase(createRequest, (state, action?) => {
    state.isLoading = true;
    state.createSuccess = false;
  })
  .addCase(createSuccess, (state, action) => {
    state.isLoading = false;
    state.createSuccess = true;
  })
  .addCase(createFailed, (state, action) => {
    state.isLoading = false;
    state.createSuccess = false;
  })


  .addCase(updateRequest, (state, action?) => {
    state.isLoading = true;
    state.updateSuccess = false;
  })
  .addCase(updateSuccess, (state, action) => {
    state.isLoading = false;
    state.updateSuccess = true;
  })
  .addCase(updateFailed, (state, action) => {
    state.isLoading = false;
    state.updateSuccess = false;
  })


  .addCase(deleteRequest, (state, action?) => {
    state.deleteSuccess = false;
  })
  .addCase(deleteSuccess, (state, action) => {
    state.deleteSuccess = true;
  })
  .addCase(deleteFailed, (state, action) => {
    state.deleteSuccess = false;
  })
  .addCase(getSourceObject, (state, action?) => {
    state.isLoadingObjects = true;
  })
  .addCase(getSourceObjectSuccess, (state, action) => {
    state.isLoadingObjects = false;
    state.listObjects = action.payload
  })
  .addCase(getSourceObjectFailure, (state, action) => {
    state.isLoadingObjects = false;
    state.listObjects = []
  })

  .addCase(getListFunction, (state, action?) => {
    state.isLoadingFunctions = true;
  })
  .addCase(getListFunctionSuccess, (state, action) => {
    state.isLoadingFunctions = false;
    state.listFunctions = action.payload
  })
  .addCase(getListFunctionFailure, (state, action) => {
    state.isLoadingFunctions = false;
    state.listFunctions = []
  })
  
  .addCase(setModalVisible, (state, action) => {
    state.isModalShow = action.payload
    if(!action.payload){
      state.createSuccess = false;
      state.updateSuccess = false;
    }
  })
})

export default reducer
export { initState as WorkFlowState }

