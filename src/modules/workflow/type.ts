import { VersionType } from "../../constants"

export interface WorkFlowInfo {
  id: number | null
  name: string
  status: number
  code: string
  diagram: string
  description: string
  baObject: any | null
  useCases: any[]
  reqElicitation: number | null
  documentation: number | null
  implementation: number | null
  storage: string
  jira: string
  confluence: string
  author: string,
  customer: string
  reviewer: string,
  dueDate: string,
  completeDate: string,
  diagramId: any
  projectId?: number,
  impacts: string,
  versionHistories?: VersionType[]
}


export interface WorkFlowState {
  isLoading: boolean
  detail: WorkFlowInfo | null

  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  listObjects?: any[],
  isLoadingObjects?: boolean,
  selectedData?: WorkFlowInfo | null,
  listFunctions?: any[],
  isLoadingFunctions?: boolean,
  isModalShow?:boolean,
}

export const defaultState: WorkFlowState = {
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  listData: [],
  listFunctions: [],
  listObjects: [],
  isLoadingFunctions: false,
  isLoadingList: false,
  isLoadingObjects: false,
  detail: {
    id: null,
    name: '',
    status: -1,
    code: '',
    diagram: '',
    diagramId: '',
    description: '',
    baObject: null,
    useCases: [],
    reqElicitation: null,
    documentation: null,
    implementation: null,
    storage: '',
    jira: '',
    confluence: '',
    author: '',
    reviewer: '',
    customer: '',
    dueDate: '',
    completeDate: '',
    impacts: '',
    versionHistories: []
  },
}
export enum ActionEnum {
  RESET_STATE = '@@MODULES/WORKFLOW/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/WORKFLOW/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/WORKFLOW/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/WORKFLOW/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/WORKFLOW/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/WORKFLOW/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/WORKFLOW/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/WORKFLOW/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/WORKFLOW/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/WORKFLOW/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/WORKFLOW/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/WORKFLOW/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/WORKFLOW/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/WORKFLOW/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/WORKFLOW/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/WORKFLOW/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/WORKFLOW/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/WORKFLOW/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/WORKFLOW/DELETE_FAILED',

  GET_SOURCE_OBJECT = '@@MODULES/WORKFLOW/GET_SOURCE_OBJECT',
  GET_SOURCE_OBJECT_SUCCESS = '@@MODULES/WORKFLOW/GET_SOURCE_OBJECT_SUCCESS',
  GET_SOURCE_OBJECT_FAILURE = '@@MODULES/WORKFLOW/GET_SOURCE_OBJECT_FAILURE',


  GET_LIST_FUNCTION = '@@MODULES/WORKFLOW/GET_LIST_FUNCTION',
  GET_LIST_FUNCTION_SUCCESS = '@@MODULES/WORKFLOW/GET_LIST_FUNCTION_SUCCESS',
  GET_LIST_FUNCTION_FAILURE = '@@MODULES/WORKFLOW/GET_LIST_FUNCTION_FAILURE',

  SET_MODAL_VISIBLE = '@@MODULES/WORKFLOW/SET_MODAL_VISIBLE',

  RESET = '@@MODULES/WORKFLOW/RESET',
}
