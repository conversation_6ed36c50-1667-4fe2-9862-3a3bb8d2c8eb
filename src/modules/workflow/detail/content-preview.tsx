import { <PERSON>, Col, Row, Space, Typography } from "antd"
import intl from "../../../config/locale.config"
import LavAttachmentPreview from "../../../helper/component/lav-attachment-preview"
import LavReferences from "../../../helper/component/lav-references"
import { renderStatusBadge } from "../../../helper/share"

const { Title, Text } = Typography

const WorkflowDetailInfo = ({ data }) => {
    return <Space direction="vertical" size="middle">
    <Space size="large">
        {renderStatusBadge(data?.status)}
    </Space>

    <Card
        title={
            <Title level={5}>
                {`${intl.formatMessage({
                    id: 'view-workflow.legend.workflow-info',
                })}`}
            </Title>
        }
        bordered={true}
    >
        <Row gutter={[16, 4]}>
            <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-workflow.label.workflow-diagram',
                        })}:
                    </Text>
            </Col>
            <Col span={24}>
                <LavAttachmentPreview attachment={data?.diagram} isCommon={false} />
            </Col>

            <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-workflow.label.workflow-explanation',
                        })}:
                    </Text>
            </Col>
            <Col span={24}>
                <div
                    className="tableDangerous"
                    dangerouslySetInnerHTML={{
                        __html: data?.description,
                    }}
                ></div>
            </Col>
        </Row>
    </Card>

    <LavReferences data={data} />
    {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.WORKFLOW} onChange={() => { }} isViewMode={true} /> : <></>} */}

</Space>
    
}
export default WorkflowDetailInfo