import AppCommonService from '../../../services/app.service'
import AppState from '@/store/types'
import {
    Button,
    Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb/index'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import LavVersion from '../../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, getListUserRequirementsRequest, resetState, updateRequest } from '../action'
import { OtherRequirementState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Option } = Select
const { Text } = Typography
const { confirm } = Modal

interface OtherRequirementFormProps {
    id?: number;
    onFinish?: () => void | null,
    buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE,
    onDismiss: () => void | null

}
interface OtherRequirementFormModalProps {
    id?: number;
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}

const OtherRequirementFormPage = ({ id, screenMode, onFinish, onDismiss }: OtherRequirementFormModalProps) => {
    const dispatch = useDispatch();
    const getCkeditorData: any = createRef()
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.OtherRequirement) as OtherRequirementState
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const { height: windowHeight } = useWindowDimensions()
    const modalConfirmConfig = useModalConfirmationConfig()
    const [description, setDescription] = useState('')

    useBeforeUnload()
    // Destroy
    useEffect(() => {
        dispatch(getListUserRequirementsRequest(null))
        form.setFieldsValue({
            assignee: currentUserName()
        })
        return () => {
            dispatch(resetState(null));
            resetForm();
            form.resetFields(['createMore']);
        }
    }, [])

    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(id))
        }
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'orm.title-create' : 'orm.title-update' });
    }, [screenMode, id])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }

    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);
            form.setFieldsValue({
                code: state.detail.code,
                otherRequirementName: state.detail.name,
                req: state.detail.reqElicitation,
                documentation: state.detail.documentation,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
                userRequirements: state.detail?.userRequirements?.map(
                    (userRequirement: any) => userRequirement?.name
                ),
            })
            setDescription(state.detail.description)
        }
    }, [state.detail])


    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            const version = form.getFieldValue('version')
            const changeDescription = form.getFieldValue('changeDescription')

            if (version && version !== '' ) {
                const payload = {
                    version: version.substring(version.length - 1) === "." ? `${version}0` : version,
                    description: changeDescription,
                    artefactCode: state.detail?.code,
                }
                AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT, state.detail?.id).then((e) => {
                    if (isCreateMore) {
                        resetForm();
                        form.setFieldsValue({
                            assignee: currentUserName(),
                            dueDate: moment(new Date()),
                        })
                    } else {
                        if (onFinish) {
                            onFinish();
                        }
                        onDismiss();
                    }
                    setIsDraft(null);
                    setIsCreateMore(false);
                })
            } else {
                if (isCreateMore) {
                    resetForm();
                    form.setFieldsValue({
                        assignee: currentUserName(),
                        dueDate: moment(new Date()),
                    })
                } else {
                    if (onFinish) {
                        onFinish();
                    }
                    onDismiss();
                }
                setIsDraft(null);
                setIsCreateMore(false);
            }
        }
    }, [state.createSuccess, state.updateSuccess])
    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }
    const onSubmit = debounce(async (values: any, st?: string) => {
        const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
        let lstUserRequirement: any = []
        values.userRequirements?.forEach((ur) => {
            const urObj: any = state.listUserRequirements?.find(
                (item: any) => item.name === ur
            )
            if (urObj) lstUserRequirement.push(urObj?.id)
        })
        const requestData: any = {
            id: id || null,
            name: values.otherRequirementName,
            version: values.version,
            description: getCkeditorData.current?.props?.data,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
            userRequirements: lstUserRequirement ? lstUserRequirement : [],
            reqElicitation: values.req,
            documentation: values.documentation,
            status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
            reviewer: values.reviewer || '',
            customer: values.customer || '',
            dueDate: values.dueDate ? values.dueDate?.toDate() : null,
            completeDate: values.completeDate ? values.completeDate?.toDate() : null,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
            impacts: impacts
        }
        setIsCreateMore(values.createMore);
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.other-requirement' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
    }, 500)

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        setIsDraft(null);
        setDescription('');
        form.resetFields([
            'version',
            'code',
            'otherRequirementName',
            'workflowExplanation',
            'req',
            'documentation',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
            'reviewer',
            'customer',
            'dueDate',
            'completeDate',
            'userRequirements'
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'orm-detail', title: intl.formatMessage({ id: 'orm.orm-detail' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'createscreen.label.req' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'createscreen.label.documentation' }), },
            { field: 'userRequirements', title: intl.formatMessage({ id: 'data.user-requirement' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.OTHER_REQUIREMENT,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    const tagRender = (props) => {
        const { label, name, value, closable, onClose } = props;


        return (
            <Tag
                // color={value}
                // onMouseDown={onPreventMouseDown}
                closable={closable}
                onClose={onClose}
                style={{
                    marginRight: 3,
                    border: 'none',
                }}
                title={label}
            >
                {label.length > 20 ? label.substring(0, 20) + '...' : label}
            </Tag>
        );
    };
    //#endregion COMMENT INIT

    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >

            <LavPageHeader
                showBreadcumb
                title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'orm.title-create' : 'orm.title-update' })}
            >
                <Space size="small">
                    {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                        style={{ marginBottom: '0px' }}
                        valuePropName="checked"
                        name="createMore"
                        wrapperCol={{ span: 24 }}
                    >
                        <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                    </Form.Item> : <></>}
                    <Button onClick={debounce(confirmCancel, 500)}>
                        {intl.formatMessage({ id: 'common.action.close' })}
                    </Button>
                    {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                        <Form.Item style={{ marginBottom: '0px' }}>
                            <Button type="primary" ghost htmlType="submit" onClick={() => {
                                setIsDraft(false)
                                setIsSubmitForm(true)
                            }}>
                                {intl.formatMessage({ id: 'common.action.submit' })}
                            </Button>
                        </Form.Item> : <></>
                    }
                    <Form.Item style={{ marginBottom: '0px' }}>
                        <Button
                            onClick={() => {
                                setIsDraft(true)
                                setIsSubmitForm(true)
                            }}
                            className="success-btn"
                            htmlType="submit"
                        >
                            {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                        </Button>
                    </Form.Item>
                </Space>

            </LavPageHeader>
            <Row align="middle" style={{ marginTop: 10 }}>
                {/* <Col span={2}>
                    <TriggerComment screenMode={screenMode} field="version">
                        <Text>
                            {intl.formatMessage({
                                id: 'createobject.place-holder.version',
                            })}
                        </Text>
                    </TriggerComment>
                </Col>
                <Col span={2}>
                    <Form.Item
                        className="mb-0"
                        name="version"
                        rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                    >
                        <Input maxLength={255} />
                    </Form.Item>
                </Col> */}
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderStatusBadge(state.detail?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>

            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={`${intl.formatMessage({ id: 'orm.infor' })}`}>
                    {
                        screenMode === SCREEN_MODE.EDIT ? <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                            <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                <Input disabled maxLength={255} />
                            </Form.Item>
                        </FormGroup> : <></>
                    }
                    <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                        <Form.Item
                            name="otherRequirementName"
                            rules={[
                                {
                                    required: true,
                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                },
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        >
                            <Input
                                placeholder={`${intl.formatMessage({
                                    id: `orm.placeholder.orm`,
                                })}${intl.formatMessage({
                                    id: `common.mandatory.*`,
                                })}`}
                                maxLength={255}
                            />
                        </Form.Item>
                    </FormGroup>


                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="orm-detail">
                            {intl.formatMessage({ id: 'orm.orm-detail' })}
                        </TriggerComment>}>
                        <Form.Item name="workflowExplanation">
                            <CkeditorMention
                                ref={getCkeditorData}
                                data={description}
                            />
                        </Form.Item>
                    </FormGroup>
                </Card>

                <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'data.reference' })}>
                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="userRequirements">
                            {intl.formatMessage({ id: 'state.user-requirement' })}
                        </TriggerComment>}>
                        <Form.Item name="userRequirements">
                            <Select
                                mode="multiple"
                                optionLabelProp="label"
                                tagRender={tagRender}
                            >
                                {state.listUserRequirements?.map(
                                    (item: any, index: number) =>
                                        item.status !== STATUS.DELETE &&
                                        item.status !== STATUS.CANCELLED && (
                                            <Option key={index} value={item.name} label={item.name}>
                                                {item.name}
                                            </Option>
                                        )
                                )}
                            </Select>
                        </Form.Item>
                    </FormGroup>
                </Card>
                <AssignTaskComponent
                    form={form}
                    data={screenMode === SCREEN_MODE.CREATE ? null : state.detail}
                    isSubmit={isDraft == false}
                    screenMode={screenMode}
                />
                {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT} onChange={onChange} isSubmitForm={isSubmitForm} />}

                <LavEffortEstimationForm
                    screenMode={screenMode}
                    hasDevelopment={state?.detail?.hasOwnProperty('development')}
                    hasImplementation={state?.detail?.hasOwnProperty('implementation')}
                />
                <LavRelatedLinksForm form={form} screenMode={screenMode} />
                {
                    screenMode === SCREEN_MODE.EDIT ?
                        <LavVersion screenMode={screenMode} data={state?.detail?.versionHistories} form={form}/> : <></>
                }
            </Space>

        </Form>
    </Spin>
}

export default OtherRequirementFormPage
