import { VersionType } from "../../constants"

export interface OtherRequirementState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: OtherRequirementDetail | null,
  selectedData?: OtherRequirementDetail | null,
  isModalShow?:boolean
  listUserRequirements: any[]
}
export interface OtherRequirementDetail {
  id?: number | null,
  name: string,
  version: string,
  description: string,
  storage: string
  jira: string
  confluence: string
  reqElicitation: string,
  documentation: number | null,
  status: number,
  code: string,
  assignee: string,
  reviewer: string,
  dueDate: any,
  customer: string
  completeDate: any
  projectId?: number,
  impacts: string,
  userRequirements: any[]
  versionHistories?: VersionType[]
}

export const defaultState: OtherRequirementState = {
  detail: {
    id: null,
    name: '',
    version: '',
    description: '',
    storage: '',
    jira: '',
    confluence: '',
    reqElicitation: '',
    documentation: 0,
    status: 0,
    code: '',
    assignee: '',
    reviewer: '',
    dueDate: '',
    completeDate: '',
    impacts: '',
    customer: '',
    userRequirements: [],
    versionHistories: []
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  listUserRequirements: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/OTHER_REQUIREMENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/OTHER_REQUIREMENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/OTHER_REQUIREMENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/OTHER_REQUIREMENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/OTHER_REQUIREMENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/OTHER_REQUIREMENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/OTHER_REQUIREMENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/OTHER_REQUIREMENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/OTHER_REQUIREMENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/OTHER_REQUIREMENT/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/OTHER_REQUIREMENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/OTHER_REQUIREMENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/OTHER_REQUIREMENT/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/OTHER_REQUIREMENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/OTHER_REQUIREMENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/OTHER_REQUIREMENT/DELETE_FAILED',


  GET_LIST_USER_REQUIREMENTS_REQUEST = '@@MODULES/OTHER_REQUIREMENT/GET_LIST_USER_REQUIREMENTS_REQUEST',
  GET_LIST_USER_REQUIREMENTS_SUCCESS = '@@MODULES/OTHER_REQUIREMENT/GET_LIST_USER_REQUIREMENTS_SUCCESS',
  GET_LIST_USER_REQUIREMENTS_FAILED = '@@MODULES/OTHER_REQUIREMENT/GET_LIST_USER_REQUIREMENTS_FAILED',
  SET_MODAL_VISIBLE = '@@MODULES/OTHER_REQUIREMENT/SET_MODAL_VISIBLE',
}
