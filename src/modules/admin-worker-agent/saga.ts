import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest, takeEvery } from 'redux-saga/effects'
import { ShowAppMessage } from '../../helper/share'
import {
  getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListRequest,
  getListSuccess,
  updateInstructionsRequest, updateInstructionsSuccess, updateInstructionsFailed,
  toggleStatusRequest, toggleStatusSuccess, toggleStatusFailed
} from './action'
import { Agent, AgentCode } from '../../modules/_shared/ai'
import AIService from '../../services/ai.service'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const data: Agent[] = yield call(AIService.getListAgents);

      yield put(getListSuccess(data.filter((agent: Agent) => agent.code !== AgentCode.Master)));
    } catch (err) {
      console.error('API Error Details:', err); // Debug log
      yield put(getListFailed(err));
      
      ShowAppMessage(err, null, 'Failed to fetch worker agents');
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      // Extract agent ID and project ID from payload
      const data = yield call(AIService.getAgentDetails, action.payload);

      yield put(getDetailSuccess(data));
    } catch (err: any) {
      console.error('Failed to fetch agent details:', err); // Debug log
      yield put(getDetailFailed(err));
      ShowAppMessage(err, null, 'Failed to fetch agent details');
    }
  }
}

function* handleUpdateInstructions(action: Action) {
  if (updateInstructionsRequest.match(action)) {
    try {
      const data = yield call(AIService.updateAgentInstruction, action.payload.agentCode, action.payload);
      yield put(updateInstructionsSuccess(data));
      ShowAppMessage(null, 'Success', 'Agent instructions updated successfully');
    } catch (err: any) {
      yield put(updateInstructionsFailed(err));
      ShowAppMessage(err, null, 'Failed to update agent instructions');
    }
  }
}

function* handleToggleStatus(action: Action) {
  if (toggleStatusRequest.match(action)) {
    try {
      console.log('Saga: handleToggleStatus called with agent code:', action.payload)
      const data = yield call(AIService.toggleAgentStatus, action.payload);
      console.log('Saga: Toggle API response received:', data)
      yield put(toggleStatusSuccess(data));
      console.log('Saga: toggleStatusSuccess dispatched')
      ShowAppMessage(null, 'Success', 'Agent status updated successfully');
    } catch (err: any) {
      console.error('Saga: Toggle failed:', err)
      yield put(toggleStatusFailed(err));
      ShowAppMessage(err, null, 'Failed to update agent status');
    }
  } else {
    console.log('Saga: handleToggleStatus called but action does not match')
  }
}

function* watchGetList() {
  yield takeLatest(getListRequest.type, handleGetList)
}

function* watchGetDetail() {
  yield takeLatest(getDetailRequest.type, handleGetDetail)
}


function* watchUpdateInstructions() {
  yield takeLatest(updateInstructionsRequest.type, handleUpdateInstructions)
}

function* watchToggleStatus() {
  console.log('Saga: watchToggleStatus watcher started')
  yield takeEvery(toggleStatusRequest.type, function* (action) {
    console.log('Saga: watchToggleStatus received action:', action)
    yield call(handleToggleStatus, action)
  })
}

export default function* adminWorkerAgentSaga() {
  yield all([
    fork(watchGetList),
    fork(watchGetDetail),
    fork(watchUpdateInstructions),
    fork(watchToggleStatus),
  ])
}
