import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Row,
  Space,
  Spin,
  Typography,
  Tag,
} from 'antd'
import { useEffect } from 'react'
import { Link } from 'react-router-dom'
import intl from '@/config/locale.config'
import { APP_ROUTES, SCREEN_MODE, AGENT_STATUS } from '@/constants'
import { renderAgentStatusBadge } from '@/helper/share'

import { Agent } from '../../_shared/ai'

const { Title, Text } = Typography

interface RightControlProps {
  data: Agent | null
  agentId: string
  onChange: () => void
  isLoading: boolean
  setScreenMode: (mode: any) => void
  onDismiss: () => void
}

const RightControl = ({
  data,
  isLoading,
  setScreenMode,
  onDismiss,
}: RightControlProps) => {
  useEffect(() => {
    if (data) {
      document.title = `${data?.code} - ${data?.name}`
    }
  }, [data])

  const formatDate = (dateString?: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString()
  }

  return data ? (
    <Space
      direction="vertical"
      size="middle"
      className="record-detail-right-control-container p-1rem"
    >
      <Row align="middle" justify="space-between">
        <div>
          <Breadcrumb className="rq-breadcrumb" separator=">">
            <Breadcrumb.Item>
              <Link
                className="breadcrumb-link-btn"
                to={APP_ROUTES.ADMIN_WORKER_AGENT}
              >
                Worker Agents Management
              </Link>
            </Breadcrumb.Item>
          </Breadcrumb>
          <Title level={3} className="rq-page-title">
            {data?.name}
          </Title>
        </div>

        <Space size="small">
          {/* Update Button */}
          <Button
            type="primary"
            className="lav-btn-create"
            onClick={() => {
              setScreenMode(SCREEN_MODE.EDIT)
            }}
          >
            {intl.formatMessage({ id: 'common.action.update' })}
          </Button>

          {/* Cancel/Back Button */}
          <Button onClick={onDismiss}>
            {intl.formatMessage({ id: 'common.action.cancel' })}
          </Button>
        </Space>
      </Row>
      <Divider className="mt-0 mb-0" />
      <Spin spinning={isLoading}>
        <Space direction="vertical" size="middle">
          <Space size="large">
            <span>
              <Button
                type="link"
                style={{ padding: 0 }}
                onClick={() => {
                  // TODO: Implement version history view
                  // setScreenMode(SCREEN_MODE.HISTORY)
                  console.log('Version history clicked for agent:', data?.code)
                }}
              >
                {intl.formatMessage({ id: `common.label.version` })}{' '}
                {data?.version || '1.0'}
              </Button>
            </span>
            {renderAgentStatusBadge(data?.status || AGENT_STATUS.DRAFT)}
            <Tag color={data?.isActive ? 'green' : 'red'}>
              {data?.isActive ? 'Active' : 'Inactive'}
            </Tag>
          </Space>

          {/* Basic Information Card */}
          <Card
            title={<Title level={5}>Agent Information</Title>}
            bordered={true}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text type="secondary">Agent Code:</Text>
                <div>{data?.code}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Agent Name:</Text>
                <div>{data?.name}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Role:</Text>
                <div>{data?.role}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Model ID:</Text>
                <div>{data?.modelId}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Project:</Text>
                <div>{data?.project}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Parent ID:</Text>
                <div>{data?.parentId || 'None'}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Created At:</Text>
                <div>{formatDate(data?.createdAt)}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Updated At:</Text>
                <div>{formatDate(data?.updatedAt)}</div>
              </Col>
              <Col span={24}>
                <Text type="secondary">Description:</Text>
                <div style={{ marginTop: '8px' }}>
                  <div
                    className="tableDangerous"
                    dangerouslySetInnerHTML={{
                      __html: data?.description || 'No description available',
                    }}
                  ></div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* System Prompt Card */}
          <Card title={<Title level={5}>System Prompt</Title>} bordered={true}>
            <Row>
              <Col span={24}>
                <div
                  style={{
                    background: '#f5f5f5',
                    padding: '12px',
                    borderRadius: '4px',
                    fontFamily: 'monospace',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                  }}
                >
                  {data?.systemPrompt || 'No system prompt configured'}
                </div>
              </Col>
            </Row>
          </Card>

          {/* Additional Prompt Card */}
          {data?.additionalPrompt && (
            <Card
              title={<Title level={5}>Additional Prompt</Title>}
              bordered={true}
            >
              <Row>
                <Col span={24}>
                  <div
                    style={{
                      background: '#f5f5f5',
                      padding: '12px',
                      borderRadius: '4px',
                      fontFamily: 'monospace',
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                    }}
                  >
                    {data?.additionalPrompt}
                  </div>
                </Col>
              </Row>
            </Card>
          )}

          {/* Tools Card */}
          {data?.tools && data.tools.length > 0 && (
            <Card title={<Title level={5}>Tools</Title>} bordered={true}>
              <Row>
                <Col span={24}>
                  <Space size={[8, 8]} wrap>
                    {data.tools.map((tool, index) => (
                      <Tag key={index} color="blue">
                        {tool}
                      </Tag>
                    ))}
                  </Space>
                </Col>
              </Row>
            </Card>
          )}
        </Space>
      </Spin>
    </Space>
  ) : (
    <></>
  )
}

export default RightControl
