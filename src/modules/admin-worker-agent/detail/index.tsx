import { Col, Row } from 'antd'
import { useCallback, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { getDetailRequest } from '../action'
import { AdminWorkerAgentState } from '../type'
import RightControl from './content'

interface AdminWorkerAgentDetailProps {
  agentCode: string
  setScreenMode: (mode: any) => void
  onDismiss: () => void
  onFinish: () => void
}

const AdminWorkerAgentDetail = ({ agentCode, onDismiss, setScreenMode }: AdminWorkerAgentDetailProps) => {
  const dispatch = useDispatch()
  const adminWorkerAgentState = useSelector(
    (state: any) => state.AdminWorkerAgent
  ) as AdminWorkerAgentState

  useEffect(() => {
    document.title = 'Worker Agent Details'
  }, [])

  const loadData = useCallback(() => {
    if (agentCode) {
      dispatch(getDetailRequest(agentCode))
    }
  }, [agentCode, dispatch])

  useEffect(() => {
    loadData()
  }, [loadData])

  return (
    <Row className='antRowHeight'>
      <Col span={24}>
        <RightControl
          setScreenMode={setScreenMode}
          onChange={loadData}
          isLoading={adminWorkerAgentState?.isLoading || false}
          data={adminWorkerAgentState?.detail || null}
          agentId={agentCode}
          onDismiss={onDismiss}
        />
      </Col>
    </Row>
  )
}

export default AdminWorkerAgentDetail
