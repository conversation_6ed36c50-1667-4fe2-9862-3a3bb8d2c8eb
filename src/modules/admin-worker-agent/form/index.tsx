import { Button, Col, Form, Input, Row, Select, Space, Spin } from 'antd'
import { useEffect, useState, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_COMMON_ROLES, AGENT_STATUS, SCREEN_MODE } from '@/constants'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import { ShowAppMessage, hasCommonRole } from '@/helper/share'
import {
  updateInstructionsRequest,
  resetState,
  getDetailRequest,
} from '../action'
import { ModelId } from '@/modules/_shared/ai'

const { Option } = Select

interface AdminWorkerAgentFormProps {
  code: string
  screenMode: number
  onFinish: () => void
  onDismiss: () => void
}

const AdminWorkerAgentForm = ({
  code,
  screenMode,
  onFinish,
  onDismiss,
}: AdminWorkerAgentFormProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState<boolean>(false)
  const [agentStatus, setAgentStatus] = useState<number>(AGENT_STATUS.DRAFT)
  const dispatch = useDispatch()
  const adminWorkerAgentState = useSelector(
    (state: any) => state.AdminWorkerAgent
  )

  // Available models for selection
  const models = Object.values(ModelId)

  // Get current user role
  const isReviewer = hasCommonRole(APP_COMMON_ROLES.REVIEWER)
  const isBA = hasCommonRole(APP_COMMON_ROLES.BA_MEMBER)
  const isSystemAdmin = hasCommonRole(APP_COMMON_ROLES.SYSTEM_ADMIN)

  const loadAgentData = useCallback(async () => {
    setLoading(true)
    try {
      // Dispatch action to get agent details by ID with project_id
      dispatch(getDetailRequest(code))
    } catch (error) {
      console.error('Error loading agent data:', error)
      ShowAppMessage(error, null, 'Failed to load agent data')
    } finally {
      setLoading(false)
    }
  }, [code, dispatch])

  // Load agent data when in edit mode
  useEffect(() => {
    if (screenMode === SCREEN_MODE.EDIT) {
      loadAgentData().catch(console.error)
    }
  }, [screenMode, loadAgentData])

  // Listen for agent detail data and populate form
  useEffect(() => {
    if (adminWorkerAgentState.detail) {
      const agentDetail = adminWorkerAgentState.detail
      form.setFieldsValue(agentDetail)
      // Set the agent status, default to DRAFT if not provided
      setAgentStatus(agentDetail.status ?? AGENT_STATUS.DRAFT)
    }
  }, [adminWorkerAgentState.detail, form, code])

  // Listen for update instructions success and errors
  useEffect(() => {
    if (adminWorkerAgentState.updateInstructionsSuccess) {
      ShowAppMessage(null, 'Success', 'Agent instructions updated successfully')
      onFinish() // Close the form after successful update
    }
  }, [adminWorkerAgentState.updateInstructionsSuccess, onFinish])

  // Handle loading state from Redux
  useEffect(() => {
    if (adminWorkerAgentState.isLoading !== undefined) {
      setLoading(adminWorkerAgentState.isLoading)
    }
  }, [adminWorkerAgentState.isLoading])

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      dispatch(resetState(null))
    }
  }, [dispatch])

  const onSubmit = async (values: any) => {
    try {
      if (screenMode === SCREEN_MODE.EDIT) {
        dispatch(
          updateInstructionsRequest({
            ...values,
            agentCode: code,
            // Keep the current status for Save action
            status: agentStatus,
          })
        )
      } else {
        ShowAppMessage(null, 'Info', 'Create functionality not implemented yet')
      }
    } catch (error) {
      ShowAppMessage(error, null, 'Failed to save agent')
    }
  }

  const onSubmitWithStatusChange = async (newStatus: number) => {
    try {
      const values = await form.validateFields()
      dispatch(
        updateInstructionsRequest({
          ...values,
          agentCode: code,
          status: newStatus,
        })
      )
      // Update local state to reflect the new status
      setAgentStatus(newStatus)
    } catch (error) {
      ShowAppMessage(error, null, 'Failed to update agent status')
    }
  }

  const handleSubmit = () => {
    onSubmitWithStatusChange(AGENT_STATUS.SUBMITTED)
  }

  const handleApprove = () => {
    onSubmitWithStatusChange(AGENT_STATUS.APPROVE)
  }

  const renderLabelRequired = (fieldName: string) => {
    return <div className="field-required">{fieldName}</div>
  }

  const renderActionButtons = () => {
    // Cancel button is always available for everyone
    const cancelButton = (
      <Button onClick={() => onDismiss()}>
        {intl.formatMessage({ id: 'common.action.close' })}
      </Button>
    )

    // For edit mode - role-based button rendering
    if (isReviewer) {
      // Reviewer can only view and cancel
      return <Space size="small">{cancelButton}</Space>
    }

    if (isBA) {
      // BA can Save and Submit (Draft -> Submitted)
      return (
        <Space size="small">
          {cancelButton}
          <Form.Item style={{ marginBottom: '0px' }}>
            <Button className="success-btn" htmlType="submit" loading={loading}>
              {intl.formatMessage({ id: 'common.action.save' })}
            </Button>
          </Form.Item>
          {agentStatus === AGENT_STATUS.DRAFT && (
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              {intl.formatMessage({ id: 'common.action.submit' })}
            </Button>
          )}
        </Space>
      )
    }

    if (isSystemAdmin) {
      // SystemAdmin can Save like BA, and Approve if status is Submitted
      return (
        <Space size="small">
          {cancelButton}
          <Form.Item style={{ marginBottom: '0px' }}>
            <Button className="success-btn" htmlType="submit" loading={loading}>
              {intl.formatMessage({ id: 'common.action.save' })}
            </Button>
          </Form.Item>
          {agentStatus === AGENT_STATUS.DRAFT && (
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              {intl.formatMessage({ id: 'common.action.submit' })}
            </Button>
          )}
          {agentStatus === AGENT_STATUS.SUBMITTED && (
            <Button
              type="primary"
              onClick={handleApprove}
              loading={loading}
              style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
            >
              {intl.formatMessage({ id: 'common.action.approve' })}
            </Button>
          )}
        </Space>
      )
    }

    // Default fallback - should not reach here but just in case
    return <Space size="small">{cancelButton}</Space>
  }

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        onFinish={onSubmit}
        labelCol={{
          span: 2,
          offset: 0,
        }}
        scrollToFirstError={{ block: 'center' }}
      >
        <div className="rq-modal-header">
          <LavPageHeader
            showBreadcumb={false}
            title={intl.formatMessage({
              id: 'common_update_project.page_title'
            })}
          >
            {renderActionButtons()}
          </LavPageHeader>
        </div>

        <Space
          direction="vertical"
          size="middle"
          style={{ padding: '0 10px 2px 0' }}
        >
          <Row gutter={24}>
            <Col span={24}>
              {/* Agent Name Field - Disabled */}
              <FormGroup
                inline
                label="Agent Code"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="code">
                  <Input
                    maxLength={255}
                    disabled={true}
                    placeholder="Agent code (read-only)"
                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                  />
                </Form.Item>
              </FormGroup>
              <FormGroup
                inline
                label="Agent Name"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="name">
                  <Input
                    maxLength={255}
                    disabled={true}
                    placeholder="Agent name (read-only)"
                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                  />
                </Form.Item>
              </FormGroup>

              {/* Description Field - Disabled */}
              <FormGroup
                inline
                label="Description"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="description">
                  <Input
                    maxLength={500}
                    disabled={true}
                    placeholder="Agent description"
                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                  />
                </Form.Item>
              </FormGroup>

              {/* Instructions Field - Display Only (Disabled) */}
              <FormGroup
                inline
                label="Instructions"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="systemPrompt">
                  <Input.TextArea
                    placeholder="System instructions"
                    disabled={isReviewer}
                    style={{
                      backgroundColor: isReviewer ? '#f5f5f5' : 'white',
                      color: isReviewer ? '#666' : 'black',
                      height: '50vh',
                      resize: 'none',
                    }}
                  />
                </Form.Item>
              </FormGroup>

              {/* Additional Context Field - Editable and Optional */}
              <FormGroup
                inline
                label="Additional Context"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="additionalPrompt">
                  <Input.TextArea
                    rows={4}
                    placeholder="Enter additional context for the agent (optional)"
                    maxLength={2000}
                    showCount
                    disabled={isReviewer}
                    style={{
                      backgroundColor: isReviewer ? '#f5f5f5' : 'white',
                      color: isReviewer ? '#666' : 'black',
                    }}
                  />
                </Form.Item>
              </FormGroup>

              {/* Model Selection Field */}
              <FormGroup
                inline
                label={renderLabelRequired('Model')}
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item
                  name="modelId"
                  rules={[
                    {
                      required: !isReviewer,
                      message: 'Please select a model.',
                    },
                  ]}
                >
                  <Select
                    placeholder="Select a model"
                    style={{ width: '100%' }}
                    showSearch
                    optionFilterProp="children"
                    disabled={isReviewer}
                  >
                    {models.map((model) => (
                      <Option key={model} value={model}>
                        {model}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>
        </Space>
      </Form>
    </Spin>
  )
}

export default AdminWorkerAgentForm
