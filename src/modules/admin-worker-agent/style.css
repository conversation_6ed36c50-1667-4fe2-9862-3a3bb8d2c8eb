/* Admin Worker Agent Page Styles */
.admin-worker-agent__container {
  width: 100%;
  padding: 20px;
}

.admin-worker-agent__header {
  border-bottom: 2px solid #ebecf0;
  margin-bottom: 10px;
  position: sticky;
  top: 0;
  z-index: 99;
  background-color: white;
}

.admin-worker-agent__agent-code {
  color: #2979FF;
  cursor: pointer;
}

.admin-worker-agent__filter-info {
  margin-bottom: 16px;
  font-size: 12px;
  color: #6b778c;
}

/* Admin Worker Agent Form Styles */
.admin-worker-agent-form__container {
  padding: 0 10px 2px 0;
}

.admin-worker-agent-form__guidance {
  padding: 12px 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  margin-bottom: 16px;
}

.admin-worker-agent-form__guidance-text {
  font-size: 13px;
  color: #6b778c;
}

.admin-worker-agent-form__field--disabled {
  background-color: #f5f5f5 !important;
  color: #666 !important;
}

.admin-worker-agent-form__field--instructions {
  height: 50vh !important;
  resize: none !important;
  background-color: #f5f5f5 !important;
  color: #666 !important;
}

.admin-worker-agent-form__model-select {
  width: 100%;
}

.admin-worker-agent-form__submit-container {
  margin-bottom: 0px;
}

/* Common field required styling */
.field-required {
  color: #ff4d4f;
}

.field-required::after {
  content: " *";
}
