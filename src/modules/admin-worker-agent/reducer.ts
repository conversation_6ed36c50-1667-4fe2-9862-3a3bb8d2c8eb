import { createReducer } from '@reduxjs/toolkit'
import {
  getDetailFailed,
  getDetailRequest,
  getDetailSuccess,
  getListFailed,
  getListRequest,
  getListSuccess,
  resetState,
  updateInstructionsRequest,
  updateInstructionsSuccess,
  updateInstructionsFailed,
  toggleStatusRequest,
  toggleStatusSuccess,
  toggleStatusFailed,
} from './action'
import { defaultState, AdminWorkerAgentState } from './type'

const initState: AdminWorkerAgentState = defaultState

const reducer = createReducer(initState, (builder) => {
  return builder
    .addCase(resetState, (state, action?) => {
      Object.assign(state, {
        ...defaultState,
        selectedData: state.selectedData,
        listData: state.listData,
      })
    })

    .addCase(getListRequest, (state, action?) => {
      state.isLoadingList = true
    })
    .addCase(getListSuccess, (state, action) => {
      state.isLoadingList = false
      state.listData = action.payload
    })
    .addCase(getListFailed, (state, action) => {
      state.isLoadingList = false
      state.listData = null
    })

    .addCase(getDetailRequest, (state, action?) => {
      state.isLoading = true
    })
    .addCase(getDetailSuccess, (state, action) => {
      state.isLoading = false
      state.detail = action.payload
    })
    .addCase(getDetailFailed, (state, action) => {
      state.isLoading = false
      state.detail = null
    })
    .addCase(updateInstructionsRequest, (state, action?) => {
      state.isLoading = true
      state.updateInstructionsSuccess = false
    })
    .addCase(updateInstructionsSuccess, (state, action) => {
      state.isLoading = false
      state.updateInstructionsSuccess = true
    })
    .addCase(updateInstructionsFailed, (state, action) => {
      state.isLoading = false
      state.updateInstructionsSuccess = false
    })

    .addCase(toggleStatusRequest, (state, action?) => {
      console.log('Reducer: toggleStatusRequest received for agent:', action?.payload)
      state.toggleStatusSuccess = false
    })
    .addCase(toggleStatusSuccess, (state, action) => {
      state.toggleStatusSuccess = true
      console.log('Toggle success payload:', action.payload)
      // Update the agent in the list with the new data from response
      if (state.listData && Array.isArray(state.listData)) {
        const updatedAgent = action.payload as any
        const index = state.listData.findIndex((agent: any) => agent.code === updatedAgent.code)
        console.log(`Found agent at index ${index} for code ${updatedAgent.code}`)
        if (index !== -1) {
          const oldAgent = state.listData[index] as any
          console.log(`Updating agent ${updatedAgent.code}: old isActive=${oldAgent.isActive}, new isActive=${updatedAgent.isActive}`)
          // Force a complete array replacement to trigger React re-render
          const newListData = [...state.listData]
          newListData[index] = { ...oldAgent, ...updatedAgent }
          state.listData = newListData
          console.log('State updated. New listData:', state.listData.map((a: any) => ({ code: a.code, isActive: a.isActive })))
        } else {
          console.error(`Agent with code ${updatedAgent.code} not found in list`)
        }
      } else {
        console.error('listData is not an array or is null')
      }
    })
    .addCase(toggleStatusFailed, (state, action) => {
      console.log('Reducer: toggleStatusFailed received:', action.payload)
      state.toggleStatusSuccess = false
    })
})

export type { AdminWorkerAgentState } from './type'
export default reducer
