import { createAction } from '@reduxjs/toolkit';
import { ActionEnum } from './type';
import { Agent, AgentPromptUpdate } from '../_shared/ai'

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<unknown>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<Agent[]>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<string>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<Agent>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const updateInstructionsRequest = createAction<AgentPromptUpdate>(ActionEnum.UPDATE_INSTRUCTIONS_REQUEST);
export const updateInstructionsSuccess = createAction<any>(ActionEnum.UPDATE_INSTRUCTIONS_SUCCESS);
export const updateInstructionsFailed = createAction<any>(ActionEnum.UPDATE_INSTRUCTIONS_FAILED);

export const toggleStatusRequest = createAction<string>(ActionEnum.TOGGLE_STATUS_REQUEST);
export const toggleStatusSuccess = createAction<Agent>(ActionEnum.TOGGLE_STATUS_SUCCESS);
export const toggleStatusFailed = createAction<any>(ActionEnum.TOGGLE_STATUS_FAILED);
