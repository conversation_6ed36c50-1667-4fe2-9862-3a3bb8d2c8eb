import { Card, Space, Table, Typography } from 'antd'
import moment from 'moment'
import { useEffect, useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  AGENT_STATUS_FILTER,
  DATE_FORMAT,
  SCREEN_MODE,
  SEARCH_TYPE,
} from '@/constants'
import {
  getColumnDropdownFilterProps,
  getColumnSearchProps,
  renderAgentStatusBadge,
} from '@/helper/share'
import LavPageHeader from '../../helper/component/lav-breadcumb'
import { getListRequest, resetState } from './action'
import AdminWorkerAgentForm from './form/index'
import AdminWorkerAgentDetail from './detail/index'
import './style.css'
import { Agent, AgentCode } from '@/modules/_shared/ai'

const { Text } = Typography

const AdminWorkerAgent = () => {
  const dispatch = useDispatch()
  const adminWorkerAgentState = useSelector(
    (state: any) => state.AdminWorkerAgent
  )
  const { listData = [] } = adminWorkerAgentState || {}

  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.LIST)
  const [agentCode, setAgentCode] = useState('')

  useEffect(() => {
    console.log(screenMode)
    if (screenMode === SCREEN_MODE.LIST) {
      dispatch(getListRequest({}))
    }
  }, [screenMode, dispatch])

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      dispatch(resetState({}))
    }
  }, [dispatch])

  const columns = [
    {
      title: 'Agent Code',
      dataIndex: 'code',
      key: 'code',
      width: '10%',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        return (
          <Text
            className="admin-worker-agent__agent-code"
            onClick={() => {
              setAgentCode(record.code)
              setScreenMode(SCREEN_MODE.VIEW) // Changed to VIEW mode for detail view
            }}
          >
            {text}
          </Text>
        )
      },
    },
    {
      title: 'Agent Name',
      dataIndex: 'name',
      key: 'name',
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '100px',
      ...getColumnDropdownFilterProps(AGENT_STATUS_FILTER, 'Status'),
      sorter: true,
      render: (status: number) => {
        // Default to Draft (0) if status is null or undefined
        const agentStatus = status === null || status === undefined ? 0 : status
        return renderAgentStatusBadge(agentStatus)
      },
    },
    {
      title: 'Last Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text: any) => {
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
    },
  ]

  const filteredListData = useMemo(
    () =>
      listData?.filter((agent: Agent) => agent.code !== AgentCode.Master) ?? [],
    [listData]
  )

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.LIST && (
        <>
          <LavPageHeader
            showBreadcumb={false}
            title="Worker Agents Management"
          />
          <Card>
            <Table
              key={`agents-table-${filteredListData.length}`}
              dataSource={filteredListData}
              columns={columns}
              rowKey={(record) => {
                const key = record.code
                console.log(`Row key for ${record.code}:`, key)
                return key
              }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
              }}
              scroll={{ x: true }}
            />
          </Card>
        </>
      )}

      {Boolean(screenMode === SCREEN_MODE.VIEW) && (
        <AdminWorkerAgentDetail
          setScreenMode={setScreenMode}
          agentCode={agentCode}
          onDismiss={() => {
            setScreenMode(SCREEN_MODE.VIEW)
            setAgentCode('')
          }}
          onFinish={() => {
            setScreenMode(SCREEN_MODE.VIEW)
            setAgentCode('')
            dispatch(getListRequest({})) // Refresh data
          }}
        />
      )}

      {screenMode === SCREEN_MODE.EDIT && (
        <AdminWorkerAgentForm
          screenMode={SCREEN_MODE.EDIT}
          onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)}
          onFinish={() => {
            setScreenMode(SCREEN_MODE.VIEW)
            dispatch(getListRequest({})) // Refresh data after edit
          }}
          code={agentCode} // Keep the code for display purposes
        />
      )}
    </Space>
  )
}

export default AdminWorkerAgent
