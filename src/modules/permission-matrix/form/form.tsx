import AppState from '@/store/types'
import { <PERSON><PERSON>, Card, Col, Form, Modal, Row, Space, Spin, Typography } from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTask from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import LavImpact from '../../../helper/component/lav-impact'
import LavVersion from '../../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import AppCommonService from '../../../services/app.service'
import { getDataRequest, submitFailed, submitRequest, updateFailed, updateRequest } from '../action'
import { PermissionMatrixState } from '../type'

const { Title } = Typography
const { confirm } = Modal

interface PermissionMatrixFormProps {
    data?: any
    onFinish?: () => void | null
}

const PermissionMatrixFormPage = ({
    onFinish,
}: PermissionMatrixFormProps) => {
    const [form] = Form.useForm()
    const dispatch = useDispatch()
    const getCkeditorData: any = createRef()
    const state = useSelector<AppState | null>(
        (s) => s?.PermissionMatrix
    ) as PermissionMatrixState
    const modalConfirmConfig = useModalConfirmationConfig()
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const [isDraft, setIsDraft] = useState<any>(null);

    // Destroy
    useEffect(() => {
        return () => {
            setIsDraft(null);
        }
    }, [])

    useEffect(() => {
        form.resetFields()
        dispatch(getDataRequest(null))        
        document.title = intl.formatMessage({ id: 'common.action.update' }) + " " + intl.formatMessage({ id: 'common.header.permission-matrix' });
    }, [])

    useEffect(() => {
        if (state.updateSuccess || state.submitSuccess) {

            const version = form.getFieldValue('version')
            const changeDescription = form.getFieldValue('changeDescription')

            if (version && version !== '') {
                const payload = {
                    version: version.substring(version.length - 1) === "." ? `${version}0` : version,
                    description: changeDescription,
                    artefactCode: 'Permission Matrix',
                }
                AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX, state.data?.id).then((e) => {
                })
            }
            if (onFinish) {
                onFinish()
            }
            setIsDraft(null);
            dispatch(submitFailed(null))
            dispatch(updateFailed(null))
        }
    }, [state.updateSuccess, state.submitSuccess])
    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }
    const onSubmit = debounce(async (values: any) => {
        /* const regex = /(?:<table>)(.*?)(?:<\/table>)/
        const table = getCkeditorData.current?.props?.data?.match(regex)
        let editContent = ''
        if (table && table.length > 0) {
            editContent = table[0]
        } */
        const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
        const requestData: any = {
            editContent: getCkeditorData.current?.props?.data,
            isCustom: true,
            status: isDraft ? ((state.data?.status === STATUS.APPROVE || state.data?.status === STATUS.REJECT_CUSTOMER || state.data?.status === STATUS.REJECT || state.data?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.data?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            author: values.assignee,
            reviewer: values.reviewer,
            customer: values.customer,
            dueDate: values.dueDate ? values.dueDate.toDate() : null,
            completeDate: values.completeDate ? values.completeDate.toDate() : null,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
            impacts: impacts
        }
        if (isDraft) {
            requestData.messageAction = MESSAGE_TYPES.UPDATE
            dispatch(updateRequest(requestData))
            setIsDraft(null)
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.permission' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(submitRequest(requestData))
                    setIsDraft(null)
                },
                onCancel() { },
            })
        }
    }, 500)

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                if (onFinish) {
                    onFinish()
                }
            },
            onCancel() { },
        })
    }


    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === state.data?.id && co?.artefactType == REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, state.data])

    useEffect(() => {
        if (!state.data?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'permission-matrix', title: intl.formatMessage({ id: 'common.header.permission-matrix' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
        ];
        dispatch(initComment({ projectId: state.data.projectId, itemId: state.data.id, fields }));

        const payload = {
            projectId: state.data.projectId,
            itemId: state.data.id,
            artefact: ARTEFACT_COMMENT.PERMISSION_MATRIX,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.data])

    //#endregion COMMENT INIT

    return (
        <div style={{ padding: 15 }}>
            <Spin spinning={state?.isLoading}>
                <Form
                    form={form}
                    onFinish={onSubmit}
                    labelCol={{
                        span: 2,
                        offset: 0,
                    }}
                    scrollToFirstError={{ block: 'center' }}
                >
                    <div className="rq-modal-header">
                        <Row>
                            <Col span={10}>
                                <Space size="large">
                                    <Title level={4}>
                                        {intl.formatMessage({
                                            id: 'common.header.permission-matrix',
                                        })}
                                    </Title>
                                    {renderStatusBadge(state.data?.status)}
                                </Space>
                            </Col>

                            <Col span={14}>
                                <Row justify="end">
                                    <Space size="small">
                                        <Button onClick={debounce(confirmCancel, 500)}>
                                            {intl.formatMessage({ id: 'common.action.close' })}
                                        </Button>

                                        {state.data?.status === STATUS.DRAFT || state.data?.status === STATUS.REJECT || state.data?.status === STATUS.REJECT_CUSTOMER || (hasRole(APP_ROLES.BA) || currentUserName() === state?.data?.customer) && state.data?.status === STATUS.APPROVE ? (
                                            <Button
                                                type="primary"
                                                ghost
                                                htmlType='submit'
                                                onClick={() => {
                                                    setIsDraft(false)
                                                    setIsSubmitForm(true)
                                                }}
                                            >
                                                {intl.formatMessage({ id: 'common.action.submit' })}
                                            </Button>
                                        ) : (
                                            <></>
                                        )}
                                        <Button
                                            className="success-btn"
                                            htmlType='submit'
                                            onClick={() => {
                                                setIsDraft(true)
                                                setIsSubmitForm(true)
                                            }}
                                        >
                                            {intl.formatMessage({ id: 'common.action.update' })}
                                        </Button>
                                    </Space>
                                </Row>
                            </Col>
                        </Row>
                    </div>

                    <Space direction='vertical' size="large">
                        <TriggerComment field="permission-matrix">
                            {intl.formatMessage({ id: 'common.header.permission-matrix' })}
                        </TriggerComment>
                        <Card>

                            {state.data?.editContent ? <CkeditorMention
                                ref={getCkeditorData}
                                data={state.data.editContent}
                            /> : <></>}
                        </Card>

                        <AssignTask form={form} data={state.data ? state.data : null} isSubmit={isDraft == false || state.data?.status === STATUS.SUBMITTED} />
                        {<LavImpact dataDetail={state?.data} artefactType={REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX} onChange={onChange} isSubmitForm={isSubmitForm} />}

                        <LavVersion screenMode={SCREEN_MODE.EDIT} data={state?.data?.versionHistories} form={form}/> : <></>
                    </Space>
                </Form>
            </Spin>
        </div>
    )
}

export default PermissionMatrixFormPage
