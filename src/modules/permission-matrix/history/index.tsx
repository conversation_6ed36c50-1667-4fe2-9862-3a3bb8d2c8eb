import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Col, Modal, Row, Space, Spin, Typography } from 'antd'
import { FC, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTaskDetail from '../../../helper/component/assign-task-detail'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavButtons from '../../../helper/component/lav-buttons'
import LavReferences from '../../../helper/component/lav-references'
import LavVersion from '../../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, extractProjectCode, getProjectName, hasRole } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import AppState from '../../../store/types'
import { renderStatusBadge } from './../../../helper/share/index'
import { generateRequest, getDataRequest } from '../action'
import { PermissionMatrixState } from '../type'
import debounce from 'lodash.debounce'
import AppCommonService from '../../../services/app.service'
import HistoryNavigation from '../../../modules/history/navigation'
import { Scrollbars } from 'react-custom-scrollbars'

const { confirm } = Modal
const { Title, Text } = Typography

interface PermissionMatrixVersionDetailsProps {
    permissionMatrixID: string,
    onChange: () => void,
    isLoading: boolean,
    selectedRowVersion: string,
    setSelectedRowVersion: (version: string) => void,  
    setScreenMode: (mode: SCREEN_MODE) => void,
    onDismiss: () => void | null,
}

const PermissionMatrixVersionDetails = ({permissionMatrixID, onChange, onDismiss, selectedRowVersion, setSelectedRowVersion, setScreenMode} : PermissionMatrixVersionDetailsProps) => {
  const state = useSelector<AppState | null>(
    (s) => s?.PermissionMatrix
  ) as PermissionMatrixState
  const dispatch = useDispatch()
  const modalConfirmConfig = useModalConfirmationConfig()

  const projectCode = extractProjectCode()
  const projectName = getProjectName(projectCode)  
  const [historyLoading, setHistoryLoading] = useState(false)
  const [data, setSelectedVersionData] = useState<any>(null)

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.PERMISSION_MATRIX + '/version/' + selectedRowVersion).then((e) => {        
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);  
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (data?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'permission-matrix', title: intl.formatMessage({ id: 'common.header.permission-matrix' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
      { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
    ];
    dispatch(initComment({ projectId: data?.projectId, itemId: data?.id, fields }));

    const payload = {
      projectId: data?.projectId,
      itemId: data?.id,
      artefact: ARTEFACT_COMMENT.PERMISSION_MATRIX,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [data])

  //#endregion COMMENT INIT

  return (
    <>
    <Space
        direction="vertical"
        size="middle"
        className="record-detail-right-control-container p-1rem"
    >
        <div className="rq-page-heading">
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className="rq-breadcrumb" separator=">">
                    <Breadcrumb.Item>
                        <Link
                        className="breadcrumb-link-btn"
                        to={`${PROJECT_PREFIX}${projectCode}/dashboard`}
                        >
                        {projectCode} - {projectName}
                        </Link>
                    </Breadcrumb.Item>
                    </Breadcrumb>
                    <Col span={24}>
                    <Space size="large">
                        <Title level={3}>
                        {intl.formatMessage({
                            id: 'common.header.permission-matrix',
                        })}
                        </Title>
                        {renderStatusBadge(data?.status)}
                    </Space>
                    </Col>
                </div>
                <Space size="small">
                    <LavButtons
                    url={`${API_URLS.PERMISSION_MATRIX}/${data?.id}`}
                    reviewer={`${data?.reviewer}`}
                    customer={`${data?.customer}`}
                    artefact_type="common.artefact.permission"
                    status={data?.status}
                    artefactType={REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX}
                    id={data?.id}
                    changePage={() => dispatch(getDataRequest(null))}
                    >                    
                        <Button onClick={debounce(onDismiss, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>
                    </LavButtons>
                </Space>
            </Row>
        </div>                 
        { data?.nextPrevious.latestVersion === data?.version ? <></>:
          <HistoryNavigation data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.permission"} artefactType={REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX}/>                
        }
        <Spin spinning={historyLoading}>
            <Scrollbars
                autoHide
            >
                <Space direction="vertical">
                    <TriggerComment field="permission-matrix">
                    <Text type="secondary">
                        {intl.formatMessage({ id: 'common.header.permission-matrix', })}
                    </Text>:
                    </TriggerComment>
                    <div
                    style={{ width: '100%', overflowY: 'auto', marginTop: '10px' }}
                    id="tablePerMission"
                    className="table-permission-matrix"
                    dangerouslySetInnerHTML={{ __html: data?.editContent }}
                    ></div>
                    <AssignTaskDetail data={data} />
                    <LavReferences data={data} />
                    <Col span={24}>
                        <LavVersion screenMode={SCREEN_MODE.VERSION} data={state?.data?.versionHistories} />
                    </Col>
                    <Row>
                    <Col span={24}>
                        <LavAuditTrail data={data?.auditTrail} />
                    </Col>
                    </Row>
                </Space>
            </Scrollbars>
        </Spin> 
    </Space>   
    </>
  )
}

export default PermissionMatrixVersionDetails
