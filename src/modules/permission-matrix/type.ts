export interface PermissionMatrixState {
  data: any,
  isLoading: boolean,
  submitSuccess?: boolean,
  updateSuccess?: boolean,
  generateSuccess?: boolean,
  isModalShow?:boolean
}

export const defaultState = {
  data: null,
  isLoading: false,
  submitSuccess: false,
  updateSuccess: false,
  generateSuccess: false,
}
export enum PERMISSION_SYMBOL {
  isPerMission = 'O',
  isNotPermission = 'X',
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/PERMISSION_MATRIX/RESET_STATE',

  GENERATE_REQUEST = '@@MODULES/PERMISSION_MATRIX/GENERATE_REQUEST',
  GENERATE_SUCCESS = '@@MODULES/PERMISSION_MATRIX/GENERATE_SUCCESS',
  GENERATE_FAILED = '@@MODULES/PERMISSION_MATRIX/GENERATE_FAILED',

  SUBMIT_REQUEST = '@@MODULES/PERMISSION_MATRIX/SUBMIT_REQUEST',
  SUBMIT_SUCCESS = '@@MODULES/PERMISSION_MATRIX/SUBMIT_SUCCESS',
  SUBMIT_FAILED = '@@MODULES/PERMISSION_MATRIX/SUBMIT_FAILED',

  UPDATE_REQUEST = '@@MODULES/PERMISSION_MATRIX/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/PERMISSION_MATRIX/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/PERMISSION_MATRIX/UPDATE_FAILED',

  GET_DATA_REQUEST = '@@MODULES/PERMISSION_MATRIX/GET_DATA_REQUEST',
  GET_DATA_SUCCESS = '@@MODULES/PERMISSION_MATRIX/GET_DATA_SUCCESS',
  GET_DATA_FAILED = '@@MODULES/PERMISSION_MATRIX/GET_DATA_FAILED',

  CHANGE_DATA = '@@MODULES/PERMISSION_MATRIX/CHANGE_DATA',
  SET_MODAL_VISIBLE = '@@MODULES/PERMISSION_MATRIX/SET_MODAL_VISIBLE',
}
