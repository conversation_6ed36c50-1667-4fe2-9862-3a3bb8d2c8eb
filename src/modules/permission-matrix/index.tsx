import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Col, Modal, Row, Space, Typography } from 'antd'
import { FC, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../constants'
import AssignTaskDetail from '../../helper/component/assign-task-detail'
import LavAuditTrail from '../../helper/component/lav-audit-trail'
import LavButtons from '../../helper/component/lav-buttons'
import LavReferences from '../../helper/component/lav-references'
import LavVersion from '../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, extractProjectCode, getProjectName, hasRole } from '../../helper/share'
import { initComment, initCommentScreen } from '../../modules/_shared/comment/action'
import TriggerComment from '../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../modules/_shared/comment/type'
import AppState from '../../store/types'
import { renderStatusBadge } from './../../helper/share/index'
import { generateRequest, getDataRequest } from './action'
import PermissionMatrixFormPage from './form/form'
import { PermissionMatrixState } from './type'
import HistoryScreen from '../history'
import PermissionMatrixVersionDetails from './history'

const { confirm } = Modal
const { Title, Text } = Typography

const PermissionMatrix: FC = () => {
  const state = useSelector<AppState | null>(
    (s) => s?.PermissionMatrix
  ) as PermissionMatrixState
  const dispatch = useDispatch()
  const [screenMode, setScreenMode] = useState(SCREEN_MODE.VIEW)
  const modalConfirmConfig = useModalConfirmationConfig()
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    dispatch(getDataRequest(null))
  }, [])

  useEffect(() => {
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'common.header.permission-matrix' });
  }, [screenMode]);

  const generateMatrix = () => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'MT_C' })}`,
      onOk() {
        dispatch(generateRequest(null))
      },
      onCancel() { },
    })
  }

  const handleReloadData = () => {
    dispatch(getDataRequest(null))
  }

  const projectCode = extractProjectCode()
  const projectName = getProjectName(projectCode)

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state?.data?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'permission-matrix', title: intl.formatMessage({ id: 'common.header.permission-matrix' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
      { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
    ];
    dispatch(initComment({ projectId: state.data.projectId, itemId: state.data.id, fields }));

    const payload = {
      projectId: state.data.projectId,
      itemId: state.data.id,
      artefact: ARTEFACT_COMMENT.PERMISSION_MATRIX,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.data])

  //#endregion COMMENT INIT

  return (
    <>
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>
            <div className="rq-page-heading" style={{ padding: '10px 20px' }}>
              <Row align="middle" justify="space-between">
                <div>
                  <Breadcrumb className="rq-breadcrumb" separator=">">
                    <Breadcrumb.Item>
                      <Link
                        className="breadcrumb-link-btn"
                        to={`${PROJECT_PREFIX}${projectCode}/dashboard`}
                      >
                        {projectCode} - {projectName}
                      </Link>
                    </Breadcrumb.Item>
                  </Breadcrumb>
                  <Col span={24}>
                    <Space size="large">
                      <Title level={3}>
                        {intl.formatMessage({
                          id: 'common.header.permission-matrix',
                        })}
                      </Title>                                                           
                      <span>          
                          <TriggerComment field='version'>                               
                              <a onClick={() => {
                                  setScreenMode(SCREEN_MODE.HISTORY)
                              }}>
                                  {intl.formatMessage({ id: `common.label.version` })}  {state.data?.version || ''}
                              </a>
                          </TriggerComment>
                      </span>
                      {renderStatusBadge(state.data?.status)}
                    </Space>
                  </Col>
                </div>
                <Space size="small">
                  <LavButtons
                    url={`${API_URLS.PERMISSION_MATRIX}/${state.data?.id}`}
                    reviewer={`${state.data?.reviewer}`}
                    customer={`${state.data?.customer}`}
                    artefact_type="common.artefact.permission"
                    status={state.data?.status}
                    isHasReject={true}
                    isHasCancel={true}
                    isHasApprove={true}
                    isHasEndorse={true}
                    artefactType={REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX}
                    id={state.data?.id}
                    changePage={() => dispatch(getDataRequest(null))}
                  >
                    {hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA) ? (
                      <>
                        {
                          ((((hasRole(APP_ROLES.BA) && state.data?.status !== STATUS.SUBMITTED) || ((currentUserName() === state.data?.reviewer)
                            && (state.data?.status === STATUS.SUBMITTED || state.data?.status === STATUS.REJECT || state.data?.status === STATUS.REJECT_CUSTOMER || state.data?.status === STATUS.APPROVE))) &&
                            state.data?.status !== STATUS.CANCELLED &&
                            state.data?.status !== STATUS.DELETE &&
                            state.data?.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && state.data?.status !== STATUS.CANCELLED &&
                            state.data?.status !== STATUS.DELETE
                          ) ? <Button type='primary' className='lav-btn-update' onClick={() => setScreenMode(SCREEN_MODE.EDIT)}>
                            {intl.formatMessage({ id: 'common.action.update' })}
                          </Button> : <></>
                        }
                        <Button onClick={generateMatrix} type="primary" ghost>
                          {intl.formatMessage({ id: 'common.action.generate' })}
                        </Button>
                      </>
                    ) : (
                      <></>
                    )}
                  </LavButtons>
                </Space>
              </Row>
            </div>
            <Space direction="vertical" size="middle" className="full-width p-20px">
              <TriggerComment field="permission-matrix">
                <Text type="secondary">
                  {intl.formatMessage({ id: 'common.header.permission-matrix', })}
                </Text>:
              </TriggerComment>
              <div
                style={{ width: '100%', overflowY: 'auto', marginTop: '10px' }}
                id="tablePerMission"
                className="table-permission-matrix"
                dangerouslySetInnerHTML={{ __html: state.data?.editContent }}
              ></div>
              <AssignTaskDetail data={state.data} />
              <LavReferences data={state.data} />
              <Col span={24}>
                <LavVersion screenMode={screenMode} data={state?.data?.versionHistories} />
              </Col>
              <Row>
                <Col span={24}>
                  <LavAuditTrail data={state.data?.auditTrail} />
                </Col>
              </Row>
            </Space>
          </> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <PermissionMatrixFormPage onFinish={() => {
          handleReloadData()
          setScreenMode(SCREEN_MODE.VIEW)
        }} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={24} style={{height: '100%'}}>
              <HistoryScreen artefact_type = "common.artefact.permission"
                            apiURL = {API_URLS.HISTORY}
                            artefactType = {REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX}
                            onFinish={handleReloadData} pageTitle={intl.formatMessage({
                              id: 'common.header.permission-matrix',
                            })}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.data} />
            </Col> 
          </>: <></>
      }          
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={24} style={{height: '100%'}}>
              <PermissionMatrixVersionDetails permissionMatrixID = {state.data?.id} selectedRowVersion ={selectedRowVersion} setSelectedRowVersion = {setSelectedRowVersion} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} />
            </Col> 
          </>: <></>
      }
    </>
  )
}

export default PermissionMatrix
