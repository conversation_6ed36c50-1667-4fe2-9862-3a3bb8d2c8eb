import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  generateFailed, generateRequest, generateSuccess, getDataFailed, getDataRequest,
  getDataSuccess, submitFailed, submitRequest, submitSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGenerateSRS(action: Action) {
  if (generateRequest.match(action)) {
    try {
      const url = API_URLS.PERMISSION_MATRIX + '/generate'
      const res = yield call(apiCall, 'GET', url)
      yield put(generateSuccess(null))
      yield put(getDataRequest(null))
    } catch (err) {
      yield put(generateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.permission')
    }
  }
}

function* handleGetData(action: Action) {
  if (getDataRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.PERMISSION_MATRIX);
      yield put(getDataSuccess(res.data));
    } catch (err) {
      yield put(getDataFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleSubmit(action: Action) {
  if (submitRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'PUT', API_URLS.PERMISSION_MATRIX, request as any)
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.SUBMIT, 'common.artefact.permission')
      yield put(submitSuccess(null));
      yield put(getDataRequest(null))
    } catch (err) {
      yield put(submitFailed(null));
      ShowAppMessage(err, null, 'common.artefact.permission')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'PUT', API_URLS.PERMISSION_MATRIX, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.permission')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.permission', err)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getDataRequest.type, handleGetData)
  yield takeLatest(generateRequest.type, handleGenerateSRS)
  yield takeLatest(submitRequest.type, handleSubmit)
  yield takeLatest(updateRequest.type, handleUpdate)
}
export default function* PermissionMatrixSaga() {
  yield all([fork(watchFetchRequest)])
}
