import { createAction } from '@reduxjs/toolkit'
import {ActionEnum} from './type';

export const resetState = createAction<any>(ActionEnum.RESET_STATE)

export const generateRequest = createAction<any>(ActionEnum.GENERATE_REQUEST)
export const generateSuccess = createAction<any>(ActionEnum.GENERATE_SUCCESS)
export const generateFailed = createAction<any>(ActionEnum.GENERATE_FAILED)

export const submitRequest = createAction<any>(ActionEnum.SUBMIT_REQUEST)
export const submitSuccess = createAction<any>(ActionEnum.SUBMIT_SUCCESS)
export const submitFailed = createAction<any>(ActionEnum.SUBMIT_FAILED)

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST)
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS)
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED)

export const getDataRequest = createAction<any>(ActionEnum.GET_DATA_REQUEST)
export const getDataSuccess = createAction<any>(ActionEnum.GET_DATA_SUCCESS)
export const getDataFailed = createAction<any>(ActionEnum.GET_DATA_FAILED)

export const changeData = createAction<any>(ActionEnum.CHANGE_DATA)
export const setModalVisible = createAction<any>(ActionEnum.SET_MODAL_VISIBLE)