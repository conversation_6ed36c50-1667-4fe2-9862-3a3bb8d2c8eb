import { createReducer } from '@reduxjs/toolkit'
import {
  updateRequest,
  updateSuccess,
  updateFailed,
  resetState,
  getDataRequest,
  getDataSuccess,
  getDataFailed,
  submitRequest,
  submitSuccess,
  submitFailed,
  generateRequest,
  generateSuccess,
  generateFailed,
  changeData,
  setModalVisible,
} from './action';
import { PermissionMatrixState, defaultState } from './type';

const initState: PermissionMatrixState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, defaultState);
      })

      .addCase(getDataRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDataSuccess, (state, action) => {
        state.isLoading = false
        state.data = action.payload
      })
      .addCase(getDataFailed, (state, action) => {
        state.isLoading = false
        state.data = null
      })

      .addCase(submitRequest, (state, action?) => {
        state.isLoading = true;
        state.submitSuccess = false;
      })
      .addCase(submitSuccess, (state, action) => {
        state.isLoading = false;
        state.submitSuccess = true;
      })
      .addCase(submitFailed, (state, action) => {
        state.isLoading = false;
        state.submitSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })


      .addCase(generateRequest, (state, action?) => {
        state.generateSuccess = false;
      })
      .addCase(generateSuccess, (state, action) => {
        state.generateSuccess = true;
      })
      .addCase(generateFailed, (state, action) => {
        state.generateSuccess = false;
      })

      .addCase(changeData, (state, action) => {
        state.data.editContent = action.payload
      })
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        // if (!action.payload) {
        //   state.createSuccess = false;
        //   state.updateSuccess = false;
        // }
      })
  )
})

export default reducer
export { initState as PermissionMatrixState }
