import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import ExportButton from '../../helper/component/lav-table/export'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import ObjectsFormPage from './form/form'

const Objects = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  useEffect(() => {
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'object.header.title' });
  }, [screenMode]);

  const columns = [
    {
      title: intl.formatMessage({ id: 'common.table.header.code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'object.column.object-name' }),
      dataIndex: 'name',
      width: '15%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: intl.formatMessage({ id: 'object.column.description' }),
      dataIndex: 'description',
      width: '65%',
      sorter: true,
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      render: (text) => {
        return <div
          className="tableDangerous"
          dangerouslySetInnerHTML={{ __html: text }}
        ></div>
      }
    },
    {
      title: intl.formatMessage({ id: 'object.column.active-properties' }),
      dataIndex: 'activeProperties',
      width: '5%',
      // ...getColumnSearchProps('activeProperties', SEARCH_TYPE.TEXT),
      sorter: true
    },
    {
      title: intl.formatMessage({ id: 'object.column.status' }),
      dataIndex: 'status',
      width: '80px',
      ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      // defaultFilteredValue: [0, 1, 2, 3],
      sorter: true,
      render: (record) => renderStatusBadge(record),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <Button ghost={true}
      type='primary'
      className='lav-btn-create'
      icon={<PlusOutlined />}
      onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'object.button.create-object' })}
    </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }
  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (record.status !== STATUS.DELETE && (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? children : <></>
  }

  const ExportComponent: React.FC<any> = ({ handleExport }) => {
    return <ExportButton fileName='object.header.title' onExport={handleExport} title='object.button.export-object'/>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        screenMode === SCREEN_MODE.VIEW ?
          <LavTable
            title="object.header.title"
            artefact_type="common.artefact.object"
            apiUrl={API_URLS.OBJECT}
            columns={columns}
            artefactType={REQ_ARTEFACT_TYPE_ID.OBJECT}
            updateComponent={UpdateComponent}
            createComponent={CreateComponent}
            deleteComponent={DeleteComponent}
            exportComponent={ExportComponent}
          />
          :
          <></>
      }


      {
        screenMode === SCREEN_MODE.CREATE ? <ObjectsFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <ObjectsFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} objectID={id} /> : <></>
      }
    </Space>
  )
}

export default Objects
