import { createReducer } from '@reduxjs/toolkit'
import {
  getDetailRequest,
  getDetailSuccess,
  getDetailFailed,
  createRequest,
  createSuccess,
  createFailed,
  updateRequest,
  updateSuccess,
  updateFailed,
  getListRequest,
  getListSuccess,
  getListFailed,
  deleteRequest,
  deleteSuccess,
  deleteFailed,
  resetState,
  getListPropertiesRequest,
  getListPropertiesSuccess,
  getListPropertiesFailed,
  getListObjectsRequest,
  getListObjectsSuccess,
  getListObjectsFailed,
  setModalVisible,
  getListUrRequest,
  getListUrSuccess,
  getListUrFailed,
} from './action';
import { ObjectsState, defaultState } from './type';

const initState: ObjectsState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getListObjectsRequest, (state, action?) => {
        state.isLoadingObjects = true;
      })
      .addCase(getListObjectsSuccess, (state, action) => {
        state.isLoadingObjects = false
        state.objects = action.payload
      })
      .addCase(getListObjectsFailed, (state, action) => {
        state.isLoadingObjects = false
        state.objects = []
      })

      .addCase(getListPropertiesRequest, (state, action?) => {
        state.isLoadingProperties = true;
      })
      .addCase(getListPropertiesSuccess, (state, action) => {
        state.isLoadingProperties = false
        state.objectProperties = action.payload
      })
      .addCase(getListPropertiesFailed, (state, action) => {
        state.isLoadingProperties = false
        state.objectProperties = null
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })

      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })

      .addCase(getListUrRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getListUrSuccess, (state, action) => {
        state.isLoading = false
        state.listUserRequirements = action.payload
      })
      .addCase(getListUrFailed, (state, action) => {
        state.isLoading = false
        state.listUserRequirements = []
      })


      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if(!action.payload){
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })
  )
})

export default reducer
export { initState as ObjectsState }
