import { CheckOutlined } from "@ant-design/icons"
import { Card, Col, Row, Space, Table, Typography } from "antd"
import { useEffect, useRef, useState } from "react"
import intl from "../../../config/locale.config"
import LavReferences from "../../../helper/component/lav-references"
import { renderStatusBadge, ShowWarningMessge } from "../../../helper/share"

const { Title, Text } = Typography

interface ObjDetailInfoProps {
    data: any,
    onChange?: any,
    selectable?: boolean
}
const ObjectDetailInfo = ({ data, onChange, selectable = true }: ObjDetailInfoProps) => {
    const tableUpdateRef = useRef<any>()
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>(null)
    const columns = [
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.object-property' }),
            dataIndex: 'name',
            width: '13%',
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.unique' }),
            dataIndex: 'unique',
            width: '10%',
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.mandatory' }),
            dataIndex: 'mandatory',
            width: '5%',
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.max-length' }),
            dataIndex: 'maxLength',
            width: '10%',
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.meaning' }),
            dataIndex: 'meaning',
            width: '27s%',
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object' }),
            dataIndex: 'sourceObject',
            width: '15%',
            render: (text, item) => {
                return item.sourceObject?.name
            }
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object-property' }),
            dataIndex: 'refObjectProperty',
            width: '20%',
            render: (text, item) => {
                return item.refObjectProperty?.name
            }
        },
    ]

    useEffect(() => {
        setSelectedRowKeys(null)
        return () => {
            setSelectedRowKeys(null)
        }
    }, [])

    useEffect(() => {
        if (data) {
            setSelectedRowKeys(data?.objectPropertyIds || [])
        }
    }, [data])
    return <Space direction="vertical" size="middle">
        <Space size="large">
            {/* <span>
                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
            </span> */}
            {renderStatusBadge(data?.status)}
        </Space>

        <Card
            title={
                <Title level={5}>
                    {`${intl.formatMessage({
                        id: 'createobject.card-title.object-infomation',
                    })}`}
                </Title>
            }
            bordered={true}
        >
            <Row gutter={[16, 4]}>
                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({ id: 'createobject.label.description' })}:
                    </Text>
                </Col>
                <Col className="description" span={24}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: data?.description }}
                    ></div>
                </Col>
                <Col span={24} style={{ width: '100%', overflowX: 'scroll' }}>
                    <Text type="secondary">
                        {intl.formatMessage({ id: 'createobject.label.property' })}:
                    </Text>
                    {
                        selectable ? <Table
                            bordered
                            dataSource={data?.objectProperties}
                            columns={columns}
                            rowKey="id"
                            pagination={false}
                            rowSelection={{
                                selectedRowKeys: selectedRowKeys,
                                onChange: (selectedRowKeys: any, selectedRows: any) => {
                                    if (selectedRowKeys?.length <= 0) {
                                        tableUpdateRef.current.scrollIntoView()
                                        ShowWarningMessge('common.message.require-select')
                                    } else {
                                        setSelectedRowKeys(selectedRowKeys);
                                        onChange(selectedRowKeys);
                                    }
                                }
                            }}
                        /> : <Table
                            bordered
                            dataSource={data?.objectProperties}
                            columns={columns}
                            rowKey="id"
                            pagination={false}
                        />
                    }
                    <div ref={tableUpdateRef}></div>
                </Col>
            </Row>
        </Card>

        <LavReferences data={data} />
    </Space>
}
export default ObjectDetailInfo