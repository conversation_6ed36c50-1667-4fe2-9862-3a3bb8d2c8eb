import React from 'react'
import intl from '../../../config/locale.config'
import { Table, Typography } from 'antd'
import { CheckOutlined } from '@ant-design/icons'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { SCREEN_MODE } from '../../../constants'
const { Text } = Typography
const ObjectPropertiesTable = (props) => {
    const columns = [
        {
            title: intl.formatMessage({ id: 'createobject.column.object-property' }),
            dataIndex: 'name',
            width: '12%',
            render: (text, record) => {
                return <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(record?.id)}>
                    <Text>{text}</Text>
                </TriggerComment>
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.unique' }),
            dataIndex: 'unique',
            width: '10%',
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.mandatory' }),
            dataIndex: 'mandatory',
            width: '5%',
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.max-length' }),
            dataIndex: 'maxLength',
            width: '10%',
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.meaning' }),
            dataIndex: 'description',
            width: '27%',
            render: (text) => {
              return <div style={{wordWrap : 'break-word', whiteSpace: 'normal'}}> {text} </div>
            }
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object' }),
            dataIndex: 'sourceObject',
            width: '15%',
            render: (text, item) => {
                return item.sourceObject?.name
            }
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object-property' }),
            dataIndex: 'refObjectProperty',
            width: '20%',
            render: (text, item) => {
                return item.refObjectProperty?.name
            }
        },
    ]

    return <Table
        style={{ whiteSpace: 'pre' }}
        bordered
        dataSource={props.dataSource}
        columns={columns}
        rowKey="id"
        pagination={false}
    />
}

export default React.memo(ObjectPropertiesTable)
