import AppState from '@/store/types'
import {
  <PERSON>read<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTaskDetail from '../../../helper/component/assign-task-detail'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavButtons from '../../../helper/component/lav-buttons'
import LavImpact from '../../../helper/component/lav-impact'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../_shared/comment/action'
import TriggerComment from '../../_shared/comment/trigger-comment'
import { CommentState } from '../../_shared/comment/type'
import { deleteRequest } from '../action'
import { ObjectsState } from '../type'
import ObjectPropertiesTable from './object-properties'

const { Title, Text } = Typography
interface RightControlProps {
  data: any | [],
  objectID: string,
  onChange: () => void,
  isLoading: boolean,
  isModalShow?: boolean
  setScreenMode: any
}
const RightControl = ({ data, objectID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
  const { height: windowHeight } = useWindowDimensions()
  const history = useHistory()
  const dispatch = useDispatch();
  const state = useSelector<AppState | null>((s) => s?.Objects) as ObjectsState;

  const projectCode = extractProjectCode();
  const projectName = getProjectName(projectCode);

  const [dataSourceUpdate, setDataSourceUpdate] = useState([])
  const [fieldsComment, setFieldsComment] = useState<any[]>([
    { field: 'version', title: intl.formatMessage({ id: 'common.label.version' }), },
    { field: 'description', title: intl.formatMessage({ id: 'createobject.label.description' }), },
    { field: 'property', title: intl.formatMessage({ id: 'createobject.label.property' }), },
    { field: 'actor', title: intl.formatMessage({ id: 'createobject.label.actor' }), },
    { field: 'source-object', title: intl.formatMessage({ id: 'createobject.label.source-object' }), },
    { field: 'target-object', title: intl.formatMessage({ id: 'createobject.label.target-object' }), },
    { field: 'use-case', title: intl.formatMessage({ id: 'createobject.label.use-case' }), },
    { field: 'screen', title: intl.formatMessage({ id: 'createobject.label.screen' }), },
    { field: 'workflow', title: intl.formatMessage({ id: 'createobject.label.workflow' }), },
    { field: 'state-transition', title: intl.formatMessage({ id: 'createobject.label.state-transition' }), },
    { field: 'data-migration', title: intl.formatMessage({ id: 'createobject.label.data-migration' }), },
    { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
    { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
    { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
    { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
    { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
    { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
    { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
  ])

  useEffect(() => {
    if(data)
        document.title = data?.code + "-" + data?.name; 
    setDataSourceUpdate(() => {
      const newData: any = []
      data?.objectProperties.map(item => {
        return newData[item.order] = item
      })
      return newData
    })
  }, [data])


  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    const getCoString = localStorage.getItem('comment')
    if (getCoString != null) {
      const co = JSON.parse(getCoString || '')
      if (commentState.fields && co?.itemId === data?.id) {
        const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.OBJECT);
        if (fieldObj) {
          const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
          dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
        }
      }
    }
  }, [commentState.fields, data])
  useEffect(() => {
    // if (!data?.id || commentState.isLoading || isModalShow) {
    //   return;
    // }

    let fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.label.version' }), },
      { field: 'description', title: intl.formatMessage({ id: 'createobject.label.description' }), },
      { field: 'property', title: intl.formatMessage({ id: 'createobject.label.property' }), },
      { field: 'actor', title: intl.formatMessage({ id: 'createobject.label.actor' }), },
      { field: 'source-object', title: intl.formatMessage({ id: 'createobject.label.source-object' }), },
      { field: 'target-object', title: intl.formatMessage({ id: 'createobject.label.target-object' }), },
      { field: 'use-case', title: intl.formatMessage({ id: 'createobject.label.use-case' }), },
      { field: 'screen', title: intl.formatMessage({ id: 'createobject.label.screen' }), },
      { field: 'workflow', title: intl.formatMessage({ id: 'createobject.label.workflow' }), },
      { field: 'state-transition', title: intl.formatMessage({ id: 'createobject.label.state-transition' }), },
      { field: 'data-migration', title: intl.formatMessage({ id: 'createobject.label.data-migration' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
      { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
      { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    if (state?.detail?.id !== null) {
      if (state?.detail?.objectProperties) {
        state?.detail?.objectProperties?.forEach((e) => {
          fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
        })
      }

      setFieldsComment(fields)
      dispatch(initComment({ projectId: state?.detail?.projectId, itemId: state?.detail?.id, fields: fields }));

      const payload = {
        projectId: state?.detail?.projectId,
        itemId: state?.detail?.id,
        artefact: ARTEFACT_COMMENT.OBJECT,
        fields: fields.map(o => o.field)
      };
      dispatch(initCommentScreen(payload));
    }
  }, [state?.detail])

  //#endregion COMMENT INIT

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE) ? <DeleteButton
      type={BUTTON_TYPE.TEXT}
      content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.object' }) })}
      okCB={() => dispatch(deleteRequest(objectID))}
      confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
  }
  return data ? (
    <Space
      direction="vertical"
      size="middle"
      className="record-detail-right-control-container p-1rem"
    >
      <Row align="middle" justify="space-between">
        <div>
          <Breadcrumb className='rq-breadcrumb' separator=">">
            <Breadcrumb.Item>
              <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
            </Breadcrumb.Item>
          </Breadcrumb>
          <Title level={3} className='rq-page-title'>
            {data?.code} - {data?.name}
          </Title>
        </div>

        <Space size="small">
          <LavButtons
            url={`${API_URLS.OBJECT}/${data?.id}`}
            reviewer={`${data?.reviewer}`}
            customer={`${data?.customer}`}
            artefact_type="common.artefact.object"
            artefactType={REQ_ARTEFACT_TYPE_ID.OBJECT}
            id={objectID}
            status={data?.status}
            isHasReject={true}
            isHasCancel={true}
            isHasApprove={true}
            isHasEndorse={true}
            deleteButton={DeleteComponent}
            changePage={() => onChange()}>

            {/* Update record */}
            {((((hasRole(APP_ROLES.BA) && data.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer)
              && (data?.status === STATUS.SUBMITTED || data?.status === STATUS.REJECT || data?.status === STATUS.REJECT_CUSTOMER || data?.status === STATUS.APPROVE))) &&
              data.status !== STATUS.CANCELLED &&
              data.status !== STATUS.DELETE &&
              data.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && data.status !== STATUS.CANCELLED &&
              data.status !== STATUS.DELETE
            ) ?
              <Button
                type='primary'
                className='lav-btn-create'
                onClick={() => {                  
                  setScreenMode(SCREEN_MODE.EDIT)
                }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
            }
          </LavButtons>
        </Space>
      </Row>
      <Divider className="mt-0 mb-0" />
      <Spin spinning={isLoading}>
        <Scrollbars
          autoHide
        >
          <Space direction="vertical" size="middle">
            <Space size="large">
              <span>          
                  <TriggerComment field='version'>                               
                      <a onClick={() => {
                          setScreenMode(SCREEN_MODE.HISTORY)
                      }}>
                          {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                      </a>
                  </TriggerComment>
              </span>
              {renderStatusBadge(data?.status)}
            </Space>

            <Card
              title={
                <Title level={5}>
                  {`${intl.formatMessage({
                    id: 'createobject.card-title.object-infomation',
                  })}`}
                </Title>
              }
              bordered={true}
            >
              <Row gutter={[16, 4]}>
                <Col span={24}>
                  <TriggerComment field="description">
                    <Text type="secondary">
                      {intl.formatMessage({ id: 'createobject.label.description' })}:
                    </Text>
                  </TriggerComment>
                </Col>
                <Col className="description" span={24}>
                  <div
                    className="tableDangerous"
                    dangerouslySetInnerHTML={{ __html: data?.description }}
                  ></div>
                </Col>
                <Col span={24} style={{ width: '100%', overflowX: 'scroll' }}>
                  <TriggerComment field="property">
                    <Text type="secondary">
                      {intl.formatMessage({ id: 'createobject.label.property' })}:
                    </Text>
                  </TriggerComment>
                  <ObjectPropertiesTable dataSource={dataSourceUpdate} />
                </Col>
              </Row>
            </Card>
            <LavReferences data={data} />
            <AssignTaskDetail data={data} />
            {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.OBJECT} onChange={() => { }} isViewMode={true} />}
            {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.OBJECT} onChange={() => { }} isViewMode={true} /> : <></>} */}
            <Row>
              <Col span={24}>
                <LavRelatedLinks data={data} />
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <LavAuditTrail data={data?.auditTrail} />
              </Col>
            </Row>
          </Space>
        </Scrollbars>
      </Spin>
    </Space >
  ) : <></>
}

export default RightControl
