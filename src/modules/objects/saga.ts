import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListObjectsFailed, getListObjectsRequest,
  getListObjectsSuccess, getListPropertiesFailed, getListPropertiesRequest, getListPropertiesSuccess, getListRequest,
  getListSuccess, getListUrFailed, getListUrRequest, getListUrSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.OBJECT}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetObjects(action: Action) {
  if (getListObjectsRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_OBJECTS
      const res = yield call(apiCall, 'GET', url)
      yield put(getListObjectsSuccess(res.data));
    } catch (err) {
      yield put(getListObjectsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetObjectProperties(action: Action) {
  if (getListPropertiesRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_OBJECTS + '/' + action.payload + '/objectproperties'
      const res = yield call(apiCall, 'GET', url)
      yield put(getListPropertiesSuccess(res.data));
    } catch (err) {
      yield put(getListPropertiesFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.OBJECT + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.OBJECT + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.object')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.object')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.OBJECT, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.object')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.object')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.OBJECT + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.object')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.object', err)
    }
  }
}

function* getListUrFlow(action: Action) {
  if (getListUrRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_USER_REQUIREMENTS);
      yield put(getListUrSuccess(res.data))
    } catch (err) {
      yield put(getListUrFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getListObjectsRequest.type, handleGetObjects)
  yield takeLatest(getListPropertiesRequest.type, handleGetObjectProperties)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
  yield takeLatest(getListUrRequest.type, getListUrFlow)
}
export default function* ObjectsSaga() {
  yield all([fork(watchFetchRequest)])
}
