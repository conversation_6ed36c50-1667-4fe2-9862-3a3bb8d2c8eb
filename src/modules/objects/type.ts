
export interface ObjectsState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: ObjectDetail | null,
  selectedData?: ObjectDetail | null,
  isLoadingObjects?: boolean,
  objects: any[] | []
  isLoadingProperties?: boolean,
  objectProperties: any | [],
  listUserRequirements: any[]
  isModalShow?:boolean
}
export interface ObjectDetail {
  id?: number | null,
  code: string,
  name: string,
  description: string,
  product: any,
  objectProperties?: ObjectProperties[] | null,
  storage: string
  jira: string
  confluence: string
  status: number | 0,
  author: string,
  reviewer: string,
  dueDate: string,
  customer: string
  completeDate: string,
  projectId?: number,
  impacts: string,
  userRequirements: any[]
}

export interface ObjectProperties {
  name: string,
  meaning?: string,
  mandatory?: boolean,
  maxLength?: number,
  unique?: boolean,
  sourceObject?: number,
  refProperty?: number,
  status: number,
  id: number | null
}

export const defaultState : ObjectsState = {
  detail: {
    id: null,
    code: '',
    name: '',
    description: '',
    product: {},
    objectProperties: null,
    storage: '',
    jira: '',
    confluence: '',
    status: 0,
    author: '',
    reviewer: '',
    dueDate: '',
    customer: '',
    completeDate: '',
    impacts: '',
    userRequirements: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  isLoadingObjects: false,
  objects: [],
  isLoadingProperties: false,
  objectProperties: [],
  listUserRequirements: [],
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/OBJECTS/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/OBJECTS/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/OBJECTS/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/OBJECTS/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/OBJECTS/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/OBJECTS/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/OBJECTS/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/OBJECTS/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/OBJECTS/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/OBJECTS/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/OBJECTS/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/OBJECTS/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/OBJECTS/GET_LIST_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/OBJECTS/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/OBJECTS/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/OBJECTS/GET_LIST_OBJECTS_FAILED',

  GET_LIST_OBJECTS_PROPERTIES_REQUEST = '@@MODULES/OBJECTS/GET_LIST_OBJECTS_PROPERTIES_REQUEST',
  GET_LIST_OBJECTS_PROPERTIES_SUCCESS = '@@MODULES/OBJECTS/GET_LIST_OBJECTS_PROPERTIES_SUCCESS',
  GET_LIST_OBJECTS_PROPERTIES_FAILED = '@@MODULES/OBJECTS/GET_LIST_OBJECTS_PROPERTIES_FAILED',

  GET_LIST_UR_REQUEST = '@@MODULES/OBJECTS/GET_LIST_UR_REQUEST',
  GET_LIST_UR_SUCCESS = '@@MODULES/OBJECTS/GET_LIST_UR_SUCCESS',
  GET_LIST_UR_FAILED = '@@MODULES/OBJECTS/GET_LIST_UR_FAILED',

  DELETE_REQUEST = '@@MODULES/OBJECTS/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/OBJECTS/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/OBJECTS/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/OBJECTS/SET_MODAL_VISIBLE',
}
