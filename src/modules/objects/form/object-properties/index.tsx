import AppState from '@/store/types'
import { CheckOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Button, Checkbox, Form, Input, Modal, Row, Select, Space, Table, Typography
} from 'antd'
import { ColumnsType } from 'antd/es/table'
import {
  useEffect,
  useState
} from 'react'
import { useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { DEFAULT_DATA_OBJ_PROPERTY, SCREEN_MODE, STATUS } from '../../../../constants'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import { ShowMessgeAdditionalSubmit } from '../../../../helper/share'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import AppCommonService from '../../../../services/app.service'
import { ObjectsState } from '../../type'
import './style.css'
const { Text } = Typography
const { confirm } = Modal
const { Option } = Select
const TableEdit = (props: any) => {
  const [dataSource, setDataSource] = useState<any>([]);
  const modalConfirmConfig = useModalConfirmationConfig()
  const state = useSelector<AppState | null>(
    (s) => s?.Objects
  ) as ObjectsState


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      setDataSource([])
    }
  }, [state.createSuccess, state.updateSuccess])

  useEffect(() => {
    if (props.onUpdate) {
      props.onUpdate(dataSource)
    }
  }, [props.isSubmitForm])

  useEffect(() => {
    if (props.data?.length !== 0) {
      let currentData = props.data?.map((e, index) => {
        return {
          ...e,
          editting: e?.editting ? e?.editting : false,
          order: index + 1,
          sourceObject: e?.sourceObject?.id,
          refProperty: e?.refObjectProperty?.id,
          listPropertiesSelect: [],
        }
      })
      setDataSource(currentData)
    }
  }, [props.data])

  useEffect(() => {
    return () => {
      setDataSource([]);
    }
  }, [])

  const setEditting = (order, item) => {
    if (item?.editting && (item?.name === "" || item?.description === "")) {
      ShowMessgeAdditionalSubmit('EMSG_36')
      return;
    }
    if (item?.editting && item.sourceObject && !item.refProperty) {
      ShowMessgeAdditionalSubmit('EMSG_36A')
      return;
    }

    if (item?.editting) {
      let valueArr = dataSource?.map((item) => { return item?.name });
      let isDuplicate = valueArr.some((item, idx) => {
        return valueArr.indexOf(item) != idx
      });
      if (isDuplicate) {
        ShowMessgeAdditionalSubmit('EMSG_7', 'common.artefact.property');
        return;
      }
    }

    if (!item?.editting) {
      if (item?.sourceObject) {
        let list = []
        AppCommonService.getObjectProperties(item?.sourceObject).then((result) => {
          if (result?.data) {
            list = result?.data
            const currentSource = dataSource.map((ele) => {
              let proName = ""
              ele?.listPropertiesSelect.map((element) => {
                if (element?.id === ele?.id) proName += element?.name
              })
              return {
                ...ele,
                editting: ele?.order === order ? !ele.editting : ele.editting,
                sourceObject: ele?.order === order ? item?.sourceObject : ele?.sourceObject,
                listPropertiesSelect: ele?.order === order ? list : ele?.listPropertiesSelect,
                refObjectPropertyName: proName
              }
            })
            setDataSource(currentSource)
          }
        })
      } else {
        const list = dataSource.map(record => {
          let nxtRecord = record;
          let proName = ""
          record?.listPropertiesSelect.map((element) => {
            if (element?.id === record?.id) proName += element?.name
          })
          if (record.order === order) {
            nxtRecord = {
              ...nxtRecord,
              editting: !nxtRecord.editting,
              refObjectPropertyName: proName
            };
          }
          return nxtRecord;
        })
        setDataSource(list)
      }
    }
    if (item?.editting) {
      const list = dataSource.map(record => {
        let nxtRecord = record;
        let proName = ""
        record?.listPropertiesSelect.map((element) => {
          if (element?.id === record?.refProperty) proName += element?.name
        })
        if (record.order === order) {
          nxtRecord = {
            ...nxtRecord,
            editting: !nxtRecord.editting,
            refObjectPropertyName: proName
          };
        }
        return nxtRecord;
      })
      setDataSource(list)
    }
  }

  const columns: ColumnsType<any> = [
    {
      title: intl.formatMessage({ id: 'createobject.column.object-property' }),
      dataIndex: 'name',
      width: '12.5%',
      render: (text, record) => {
        return record?.editting ? 
        <TriggerComment screenMode={props.screenMode} field={JSON.stringify(record?.id)}>
            <Input maxLength={255} defaultValue={text} onChange={handleChangeObjectPropertyText(record?.order, 'name')} /> 
        </TriggerComment>
        
        : <TriggerComment screenMode={props.screenMode} field={JSON.stringify(record?.id)}>
          <Text>{text}</Text>
          </TriggerComment>
      },
    },
    {
      title: intl.formatMessage({ id: 'createobject.column.unique' }),
      dataIndex: 'unique',
      width: '7%',
      align: 'center',
      render: (unique: boolean, record) => {
        return record?.editting ? <Checkbox checked={unique} onChange={(e) => handleChangeObjectProperty(record?.order, 'unique', e?.target.checked)} /> : <Checkbox checked={unique} disabled />
      },
    },
    {
      title: intl.formatMessage({ id: 'createobject.column.mandatory' }),
      dataIndex: 'mandatory',
      align: 'center',
      width: '7%',
      render: (mandatory: boolean, record) => {
        return record?.editting ? <Checkbox checked={mandatory} onChange={(e) => handleChangeObjectProperty(record?.order, 'mandatory', e?.target.checked)} /> :
          <Checkbox checked={mandatory} disabled />
      },
    },
    {
      title: intl.formatMessage({ id: 'createobject.column.max-length' }),
      dataIndex: 'maxLength',
      width: '10%',
      render: (text, record) => {
        return record?.editting ? <Input maxLength={255} defaultValue={text} onChange={handleChangeObjectPropertyText(record?.order, 'maxLength')} /> : text
      }
    },
    {
      title: intl.formatMessage({ id: 'createobject.column.meaning' }),
      dataIndex: 'description',
      width: '30.5%',
      className: 'white-pre',
      render: (text, record) => {
        return record?.editting ? <Input.TextArea defaultValue={text} onChange={handleChangeObjectPropertyText(record?.order, 'description')} maxLength={1024} /> : <div style={{wordWrap : 'break-word', whiteSpace: 'normal'}}> {text} </div>
      }
    },
    {
      title: intl.formatMessage({ id: 'createobject.column.source-object' }),
      dataIndex: 'sourceObject',
      width: '12%',
      render: (text, record) => {
        return record?.editting ? <Select
          allowClear
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          defaultValue={text ? parseInt(text) : text}
          style={{ width: '100%' }}
          onChange={(e) => handleChangeObject(record?.order, e)}
        >
          {state.objects && state.objects.map(
            (item: any) =>
              item.status !== STATUS.DELETE &&
              item.status !== STATUS.CANCELLED &&
              item?.sourceObject !== parseInt(text) &&
              <Option key={item.id} value={item.id}>{item.name}</Option>
          )}
        </Select> : state?.objects.map((e) => {
          return e?.id === text ? e?.name : ""
        })
      }
    },
    {
      title: intl.formatMessage({ id: 'createobject.column.source-object-property' }),
      dataIndex: 'refProperty',
      width: '16%',
      render: (text, record) => {
        return record?.editting ? <Select
          allowClear
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          value={text ? parseInt(text) : text}
          style={{ width: '100%' }}
          onChange={(e) => handleChangeObjectProperty(record?.order, 'refProperty', e)}
        >
          {record?.listPropertiesSelect && record?.listPropertiesSelect?.map(
            (item: any) =>
              item.status !== STATUS.DELETE &&
              item.status !== STATUS.CANCELLED &&
              item?.refProperty !== parseInt(text) &&
              <Option key={item.id} value={item.id}>{item.name}</Option>
          )}
        </Select> : record.refObjectProperty ? record.refObjectProperty?.name : record?.refObjectPropertyName
      }
    },
    {
      title: intl.formatMessage({ id: 'createobject.column.action' }),
      align: 'center',
      dataIndex: 'order',
      className: 'rq-action',
      width: '5%',
      render: (text, record: any, index: number) => {
        return (
          <Space size='small'>
            {
              record?.editting ? <Button icon={<CheckOutlined name="EditCustomIcon" />} type="link" onClick={() => setEditting(text, record)}></Button>
                : <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={() => setEditting(text, record)}></Button>
            }
            <Button
              type="text"
              icon={<CustomSvgIcons name="DeleteCustomIcon" />}
              onClick={() => deleteRow(index)}
            />
          </Space>
        )
      },
    },
  ]

  const deleteRow = (rowIndex) => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_1' })}`,
      onOk() {
        let currentSource: any = Object.assign([], dataSource);
        if (currentSource.length == 1) {
          currentSource = [];
        } else {
          currentSource.splice(rowIndex, 1);
        }

        let list = currentSource.map((e, index) => {
          return {
            ...e,
            order: index + 1,
          }
        })
        setDataSource(list);
      },
      onCancel() { },
    })


  }

  const handleAddObjectProperties = () => {
    setDataSource([...dataSource, { order: dataSource.length + 1, ...DEFAULT_DATA_OBJ_PROPERTY }]);
  }

  const handleChangeObject = async (order, e) => {
    let list = []
    if (e) {
      AppCommonService.getObjectProperties(e).then((result) => {
        if (result?.data) {
          list = result?.data
          const currentSource = dataSource.map((ele) => {
            return {
              ...ele,
              sourceObject: ele?.order === order ? e : ele?.sourceObject,
              refProperty: ele?.order === order ? null : ele?.refProperty,
              listPropertiesSelect: ele?.order === order ? list : ele?.listPropertiesSelect
            }
          })
          setDataSource(currentSource)
        }
      })
    } else {
      const currentSource = dataSource.map((ele) => {
        return {
          ...ele,
          sourceObject: ele?.order === order ? null : ele?.sourceObject,
          refProperty: ele?.order === order ? null : ele?.refProperty,
        }
      })
      setDataSource(currentSource)
    }
  }

  const updateRecordObjectProperty = (order, partialRecord) => {
    const list = dataSource.map(record => {
      let nxtRecord = record;
      if (record.order === order) {
        nxtRecord = { ...nxtRecord, ...partialRecord };
      }
      return nxtRecord;
    })
    setDataSource(list)
  };

  const handleChangeObjectProperty = (order, prop, value: any) => {
    if (prop === 'sourceObject') {
      AppCommonService.getObjectProperties(value).then((e) => {
        updateRecordObjectProperty(order, { ['listPropertiesSelect']: e?.data });
        updateRecordObjectProperty(order, { [prop]: value });
      })
    } else {
      updateRecordObjectProperty(order, { [prop]: value });
    }
  };
  const handleChangeObjectPropertyText = (order, prop) => ({ target }) => {
    updateRecordObjectProperty(order, { [prop]: target?.value });
  };

  return (
    <>
      <FormGroup inline className="rq-fg-comment scrollButtonSC" label={
        <TriggerComment screenMode={props?.screenMode} field="property">
          {intl.formatMessage({ id: 'createobject.label.property' })}
        </TriggerComment>}>
        <Form.Item name="updatetablescreen">
          <Space direction="vertical" size="small">
            <Row justify='end'>
              <Button type="primary" onClick={() => handleAddObjectProperties()} icon={<PlusOutlined />} >
                {
                  intl.formatMessage({
                    id: 'createobject.create-modal-title.add-property',
                  })
                }
              </Button>
            </Row>
          </Space>
        </Form.Item>
      </FormGroup>
      <Table
        // style={{ whiteSpace: 'pre' }}
          bordered
          dataSource={dataSource}
          columns={columns}
          rowKey="id"
          // scroll={{ y: 1000 }}
          pagination={false}
        />
    </>
  )
}

export default TableEdit
