import AppState from '@/store/types'
import {
    Button,
    Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createFailed, createRequest, getDetailRequest, getListObjectsRequest, getListUrRequest, resetState, updateFailed, updateRequest } from '../action'
import { ObjectsState } from '../type'
import TableEdit from './object-properties'
import AppCommonService from '../../../services/app.service'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'
const { Text } = Typography
const { confirm } = Modal
const { Option } = Select
interface ObjectFormModalProps {
    objectID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}


const ObjectsFormPage = ({ objectID, screenMode, onFinish, onDismiss }: ObjectFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.Objects) as ObjectsState
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const [isSubmit, setIsSubmit] = useState<boolean>(false)
    const [objectProperties, setObjectProperties] = useState<any>([]);
    const getCkeditorDataDes: any = createRef()
    const modalConfirmConfig = useModalConfirmationConfig()
    const tableRef = createRef();
    const tableEditRef = createRef<any>();
    const defaultDescription = '\u2022 This data object contains information of all the {Object} in the system \n\u2022 Each {Object} is Submitted By...\n\u2022 This data object contains some fields such as...';
    // Destroy
    useEffect(() => {
        dispatch(getListObjectsRequest(null))
        dispatch(getListUrRequest(null))
        form.setFieldsValue({
            assignee: currentUserName(),
            description: defaultDescription
        });
        return () => {
            dispatch(resetState(null));
            resetForm();
        }
    }, [])

    useEffect(() => {
        if (objectID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(objectID))
        }
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createobject.page_create_title' : 'createobject.page_update_title' });
    }, [screenMode, objectID])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }

    useEffect(() => {
        if (objectID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);
            form.setFieldsValue({
                id: state.detail.id,
                name: state.detail.name,
                description: state.detail.description,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
                userRequirementIds: state.detail?.userRequirements.map(
                    (userRequirement: any) => userRequirement?.name
                ),
            })
            setObjectProperties(state.detail.objectProperties);
        }
    }, [state.detail])


    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            if (isCreateMore) {
                resetForm();
                form.setFieldsValue({
                    assignee: currentUserName(),
                    dueDate: moment(new Date()),
                })
            } else {
                if (onFinish) {
                    onFinish();
                }
                onDismiss();
            }
            setIsDraft(null);
            setIsCreateMore(false);
            dispatch(createFailed(null))
            dispatch(updateFailed(null))
        }
    }, [state.createSuccess, state.updateSuccess])

    useBeforeUnload()

    const onSubmit = debounce(async (values: any, st?: string) => {
        // const lstUserRequirement: any[] = [];
        // values.userRequirement?.forEach((ur) => {
        //     const urObj: any = state.userRequirements.find(
        //         (item: any) => item.name === ur
        //     )
        //     if (urObj) lstUserRequirement.push(urObj?.id)
        // })
        const lstUserRequirement: any[] = [];
        values.userRequirementIds?.forEach((ur) => {
            const urObj: any = state.listUserRequirements.find(
                (item: any) => item.name === ur
            )
            if (urObj) lstUserRequirement.push(urObj?.id)
        })
        let mentionReferences = getReferencesFromEditor(getCkeditorDataDes.current?.props?.data);
        const requestData: any = {
            ...values,
            id: objectID || null,
            status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            objectProperties: objectProperties.map((e, index) => {
                return {
                    "name": e.name,
                    "description": e.description,
                    "mandatory": e.mandatory,
                    "maxLength": e.maxLength,
                    "unique": e.unique,
                    "sourceObject": e.sourceObject?.id ? e.sourceObject?.id : (e.sourceObject ? e.sourceObject : undefined),
                    "refProperty": e.refObjectProperty?.id ? e.refObjectProperty?.id : (e.refProperty ? e.refProperty : undefined),
                    "status": e.status,
                    "id": e.id,
                    "order": index + 1
                }
            }),
            userRequirementIds: lstUserRequirement ? lstUserRequirement : [],
            author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
            reviewer: values.reviewer || '',
            customer: values.customer || '',
            dueDate: values.dueDate ? values.dueDate?.toDate() : null,
            completeDate: values.completeDate ? values.completeDate?.toDate() : null,
            impacts: impacts,
            description: getCkeditorDataDes?.current?.props?.data,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
        }
        if (requestData.status === STATUS.SUBMITTED || requestData.status === STATUS.ENDORSE) {
            if (objectProperties.length === 0) {
                tableEditRef.current.scrollIntoView('tableEdit')
                ShowMessgeAdditionalSubmit('EMSG_12', 'common.artefact.object')
                return
            }
        }

        const checkObj = objectProperties?.filter(e => e.name.replace(/\s+/g, ' ')
            .trim() === "" || e?.description.replace(/\s+/g, ' ')
                .trim() === "")
        if (checkObj && checkObj.length > 0) {
            tableEditRef.current.scrollIntoView('tableEdit')
            ShowMessgeAdditionalSubmit('EMSG_36');
            return;
        }

        const checkProperty = objectProperties?.filter(e => e?.sourceObject && !e?.refProperty)
        if (checkProperty && checkProperty.length > 0) {
            tableEditRef.current.scrollIntoView('tableEdit')
            ShowMessgeAdditionalSubmit('EMSG_36A');
            return;
        }

        let valueArr = objectProperties?.map((item) => { return item?.name });
        let isDuplicate = valueArr.some((item, idx) => {
            return valueArr.indexOf(item) != idx
        });
        if (isDuplicate) {
            tableEditRef.current.scrollIntoView('tableEdit')
            ShowMessgeAdditionalSubmit('EMSG_7', 'common.artefact.property');
            return;
        }
        setIsCreateMore(values.createMore);
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.object' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
    }, 500)

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        setIsDraft(null);
        form.resetFields([
            'name',
            'version',
            'code',
            'description',
            'category',
            'message',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
            'req',
            'documentation',
            'createMore',
            'reviewer',
            'dueDate',
            'completeDate'
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })
        setObjectProperties([]);
    }

    const handleTableUpdated = (e) => {
        setObjectProperties(e);
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.label.version' }), },
            { field: 'description', title: intl.formatMessage({ id: 'createobject.label.description' }), },
            { field: 'user-requirement', title: intl.formatMessage({ id: 'createobject.label.user-requirement' }), },
            { field: 'property', title: intl.formatMessage({ id: 'createobject.label.property' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];

        state.detail?.objectProperties?.forEach((e) => {
            fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
        })
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.OBJECT,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    const tagRender = (props) => {
        const { label, name, value, closable, onClose } = props;
        return (
            <Tag
                // color={value}
                // onMouseDown={onPreventMouseDown}
                closable={closable}
                onClose={onClose}
                style={{
                    marginRight: 3,
                    border: 'none',
                }}
                title={label}
            >
                {label.length > 20 ? label.substring(0, 20) + '...' : label}
            </Tag>
        );
    };
    //#endregion COMMENT INIT

    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb={false}
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createobject.page_create_title' : 'createobject.page_update_title' })}
                >
                    <Space size="small">
                        {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                            style={{ marginBottom: '0px' }}
                            valuePropName="checked"
                            name="createMore"
                            wrapperCol={{ span: 24 }}
                        >
                            <Checkbox disabled={state.isLoading}>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                        </Form.Item> : <></>}
                        <Button onClick={debounce(confirmCancel, 500)} >
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>

                        {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                            <Button type="primary" ghost htmlType="submit" onClick={() => {
                                setIsDraft(false)
                                setIsSubmitForm(true)
                                setIsSubmit(!isSubmit)
                            }}>
                                {intl.formatMessage({ id: 'common.action.submit' })}
                            </Button> : <></>
                        }
                        <Button onClick={() => {
                            setIsDraft(true)
                            setIsSubmitForm(true)
                            setIsSubmit(!isSubmit)
                        }}
                            className="success-btn"
                            htmlType="submit"
                        >
                            {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                        </Button>
                    </Space>
                </LavPageHeader>
            </div>

            <Row align="middle">
                {/* <Col span={2}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={14} controlSpan={10} label={
                                <TriggerComment screenMode={screenMode} field='actor'>
                                    {intl.formatMessage({ id: 'createobject.place-holder.version' })}
                                </TriggerComment>}>
                                <Form.Item
                                    name="version"
                                    rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col> */}
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderStatusBadge(state.detail?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>

            {/* <Space direction="vertical" size="large">
                <Row align="middle">
                    <Col span={2}>
                        <TriggerComment screenMode={screenMode} field="version">
                            <Text>
                                {intl.formatMessage({
                                    id: 'createobject.place-holder.version',
                                })}
                            </Text>
                        </TriggerComment>
                    </Col>
                    <Col span={2}>
                        <Form.Item style={{ display: 'none' }} name="id"><Input readOnly /></Form.Item>
                        <Form.Item
                            className="mb-0"
                            name="version"
                            rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                        >
                            <Input maxLength={255} />
                        </Form.Item>
                    </Col>
                </Row>
            </Space> */}

            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'common.label.code' })} >
                    {
                        screenMode === SCREEN_MODE.EDIT ? <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'updateobject.label.object-code' })}>
                            <Form.Item>
                                <Input
                                    value={state.detail?.code}
                                    disabled
                                    maxLength={500}
                                />
                            </Form.Item>
                        </FormGroup>
                            : <></>
                    }

                    <FormGroup
                        inline
                        required
                        label={intl.formatMessage({ id: 'common.label.name' })}
                        labelSpan={3}
                        controlSpan={21}
                    >
                        <Form.Item
                            name="name"
                            rules={[
                                {
                                    required: true,
                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                },
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        >
                            <Input
                                maxLength={255}
                                placeholder={`${intl.formatMessage({
                                    id: `createobject.place-holder.object-name`,
                                })}${intl.formatMessage({
                                    id: `common.mandatory.*`,
                                })}`}
                            />
                        </Form.Item>
                    </FormGroup>
                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="description">
                            {intl.formatMessage({ id: 'createobject.label.description' })}
                        </TriggerComment>}>
                        <Form.Item
                            name="description"
                            labelAlign="left"
                            rules={[{
                                validator: async (rule, value) => {
                                    const description = getCkeditorDataDes?.current?.props?.data
                                    if ((description == '' || description == undefined) && (!isDraft || state.detail?.status === STATUS.ENDORSE || state.detail?.status === STATUS.SUBMITTED)) {
                                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                    }
                                }
                            }]}
                            wrapperCol={{ span: 24 }}
                        >
                            <CkeditorMention
                                ref={getCkeditorDataDes}
                                data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
                            />
                        </Form.Item>
                    </FormGroup>
                    <div ref={tableEditRef} style={{ marginBottom: '10px' }}>
                        <TableEdit
                            name="tableEdit"
                            ref={tableRef}
                            data={objectProperties}
                            sourceObject={state.objects}
                            form={form}
                            isSubmitForm={isSubmit}
                            screenMode={screenMode}
                            onUpdate={handleTableUpdated} />
                    </div>
                </Card>
                    <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'common.label.reference' })}>
                        <FormGroup inline labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field='user-requirement'>
                                {intl.formatMessage({ id: 'createobject.label.user-requirement' })}
                            </TriggerComment>}>
                            <Form.Item name="userRequirementIds" wrapperCol={{ span: 24 }}>
                                <Select mode="multiple" optionLabelProp="label" tagRender={tagRender}>
                                    {state.listUserRequirements?.map(
                                        (item: any, index: number) =>
                                            item.status !== STATUS.DELETE &&
                                            item.status !== STATUS.CANCELLED && (
                                                <Option key={index} value={item.name} label={item.name}>
                                                    {item.name}
                                                </Option>
                                            )
                                    )}
                                </Select>
                            </Form.Item>
                        </FormGroup>
                    </Card>
                <AssignTaskComponent form={form} data={screenMode === SCREEN_MODE.CREATE ? null : state.detail} isSubmit={isDraft == false} screenMode={screenMode} />
                {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.OBJECT} onChange={onChange} isSubmitForm={isSubmitForm} />}
                <LavRelatedLinksForm form={form} screenMode={screenMode} />
            </Space>
        </Form>
    </Spin>
}
export default ObjectsFormPage
