import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import ActorFormPage from '../form/form'
import { ActorState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'

const ActorDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.Actor) as ActorState;
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.actorID) {
      dispatch(getDetailRequest(props.match.params.actorID))
    }
  }, [props])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.ACTOR}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.actorID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.ACTOR_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>
            <Col span={5}>
              <LavLeftControl
                activeId={props.match.params.actorID}
                apiUrl={API_URLS.REFERENCES_ACTORS}
                route={APP_ROUTES.ACTOR_DETAIL}
                title='actor.table.title'
                reload={reload}
                artefactType={REQ_ARTEFACT_TYPE_ID.ACTOR}
                reloadSuccess={() => setReload(false)}
                handleCreate={handleCreate}
                hideStatus={true}
              >
                {
                  ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <Button ghost={true}
                    type='primary'
                    className='lav-btn-create'
                    icon={<PlusOutlined />}
                    onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'actor.action.create' })}
                  </Button> : <></>
                }
              </LavLeftControl>
            </Col>
            <Col span={19}>
              <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} actorID={props.match.params.actorID} isModalShow={state?.isModalShow} setScreenMode={() => setScreenMode(SCREEN_MODE.EDIT)} />
            </Col>
          </> : <></>
      }

      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <ActorFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <ActorFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
            }} actorID={props.match.params.actorID} />
          </Col> : <></>
      }
    </Row>
  )
}

export default ActorDetail