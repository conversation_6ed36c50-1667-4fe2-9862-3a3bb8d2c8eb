import AppState from '@/store/types'
import {
    Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, STATUS } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavImpact from '../../../helper/component/lav-impact'
import LavReferences from '../../../helper/component/lav-references'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../_shared/comment/action'
import TriggerComment from '../../_shared/comment/trigger-comment'
import { CommentState } from '../../_shared/comment/type'
import { deleteRequest } from '../action'

const { Title, Text } = Typography
interface RightControlProps {
    data: any | [],
    actorID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}
const RightControl = ({ data, actorID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;


    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + data?.name; 
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.ACTOR);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'actor-name', title: intl.formatMessage({ id: 'actor.column.name' }), },
            { field: 'description', title: intl.formatMessage({ id: 'actor.column.description' }), },
            { field: 'object', title: intl.formatMessage({ id: 'view-screen-list.label.object' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
            { field: 'screen', title: intl.formatMessage({ id: 'view-screen-list.label.screen' }), },
            { field: 'workflow', title: intl.formatMessage({ id: 'view-use-case-details.label.workflow' }), },
            { field: 'state-transition', title: intl.formatMessage({ id: 'view-screen-list.label.state-transition' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'common.assign-task.customer' }), },
        ];

        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.ACTOR,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    return data ? (
        <Space direction="vertical" size="middle" className="record-detail-right-control-container p-1rem">
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>{data?.code}-{data?.name}</Title>
                </div>
                <Space size="small">
                    {(hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && (data?.status !== STATUS.DELETE) ?
                        <DeleteButton
                            type={BUTTON_TYPE.TEXT}
                            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.actor-short' }) })}
                            okCB={() => dispatch(deleteRequest(actorID))}
                            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>}
                    {((((hasRole(APP_ROLES.BA) && data.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer)
                        && (data?.status === STATUS.SUBMITTED || data?.status === STATUS.REJECT || data?.status === STATUS.REJECT_CUSTOMER || data?.status === STATUS.APPROVE))) &&
                        data.status !== STATUS.CANCELLED &&
                        data.status !== STATUS.DELETE &&
                        data.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && data.status !== STATUS.CANCELLED &&
                        data.status !== STATUS.DELETE
                    ) ?
                        <Button
                            type='primary'
                            className='lav-btn-create'
                            onClick={() => {
                                setScreenMode()
                            }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                    }
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars autoHide>
                    <Space direction="vertical" size="middle">
                        <Space size="large">
                            {/* <span>
                                <TriggerComment field="version">
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'common.label.version',
                                        })}:
                                    </Text> {data?.version || ''}
                                </TriggerComment>
                            </span> */}
                        </Space>

                        <Card
                            title={<Title level={5}>{intl.formatMessage({ id: 'actor.information' })}</Title>}
                            bordered={true}>
                            <Row gutter={[16, 4]}>
                                <Col span={3}>
                                    <TriggerComment field="actor-name">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'actor.column.name',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col className="actor" span={20}>
                                    {data?.name}
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field="description">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'actor.column.description',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24} className="description">
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{
                                            __html: data?.description,
                                        }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>
                        <LavReferences data={data} />
                        {/* <AssignTaskDetail data={data} /> */}
                        {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.ACTOR} onChange={() => { }} isViewMode={true} />}
                        {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.ACTOR} onChange={() => { }} isViewMode={true} /> : <></> } */}

                        <Row>
                            <Col span={24}>
                                <LavAuditTrail data={data?.auditTrail} />
                            </Col>
                        </Row>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
