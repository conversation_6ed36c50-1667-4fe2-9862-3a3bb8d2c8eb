import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import React, { FC, useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import {
  currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge
} from '../../helper/share'
import ActorFormPage from './form/form'

const ActorPage: FC = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)

  useEffect(() => {
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'actor.table.title' });
  }, [screenMode]);

  const [id, setId] = useState<number>(0)
  const columns = [
    {
      title: intl.formatMessage({ id: 'actor.column.code' }),
      dataIndex: 'code',
      width: '',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      sorter: true,
      sortOrder: 'descend',
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.ACTOR_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'actor.column.name' }),
      dataIndex: 'name',
      ellipsis: true,
      width: '15%',
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
      sorter: true
    },
    {
      title: intl.formatMessage({ id: 'actor.column.description' }),
      dataIndex: 'description',
      ellipsis: true,
      sorter: true,
      width: '85%',
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      render: (text) => (
        <div
            className="tableDangerous"
            dangerouslySetInnerHTML={{ __html: text }}
        ></div>
    ),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)
    ) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'actor.action.create' })}
      </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (
      record.status !== STATUS.DELETE &&
      (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM))
    ) ?
      children : <></>
  }

  //#endregion

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        screenMode === SCREEN_MODE.VIEW ?
          <LavTable
            title="actor.table.title"
            artefact_type="common.artefact.actor"
            apiUrl={API_URLS.ACTORS}
            columns={columns}
            artefactType={REQ_ARTEFACT_TYPE_ID.ACTOR}
            updateComponent={UpdateComponent}
            createComponent={CreateComponent}
            deleteComponent={DeleteComponent}
          /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <ActorFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <ActorFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} actorID={id} /> : <></>
      }
    </Space>
  )
}

export default ActorPage
