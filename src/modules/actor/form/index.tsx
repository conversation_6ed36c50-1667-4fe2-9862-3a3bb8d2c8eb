import LavImpact from '../../../helper/component/lav-impact'
import AppState from '@/store/types'
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons'
import {
  Button, Checkbox, Col, Form, Input, Modal, Row, Space, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomModal from '../../../helper/component/custom-modal'
import TextAreaBullet from '../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { ActorState } from '../type'
import FormGroup from '../../../helper/component/form-group'

const { Text } = Typography
const { confirm } = Modal

interface ActorFormProps {
  actorID?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface ActorFormModalProps {
  actorID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const ActorFormModal = ({ actorID, screenMode, onFinish, onDismiss }: ActorFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.Actor) as ActorState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [impacts, setImpacts] = useState<any>(false)
  const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
  const [isCreateMore, setIsCreateMore] = useState(false);
  const modalConfirmConfig = useModalConfirmationConfig()

  // Destroy
  useEffect(() => {
    form.setFieldsValue({
      assignee: currentUserName(),
    })
    return () => {
      form.resetFields(['createMore']);
      dispatch(resetState(null));
      resetForm();
    }
  }, [])

  useEffect(() => {
    if (actorID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(actorID))
    }
  }, [screenMode, actorID])

  useEffect(() => {
    if (actorID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        code: state.detail.code,
        name: state.detail.name,
        description: state.detail.description
      })
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm();
        form.setFieldsValue({
          assignee: currentUserName(),
        })
      } else {
        onDismiss();
      }
      setIsDraft(null);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])
  const onChange = (e) => {
    setImpacts(JSON.stringify(e))
  }
  const onSubmit = debounce((values: any, st?: string) => {
    const requestData: any = {
      id: actorID || null,
      name: values.name,
      description: values.description,
      status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS.DRAFT : state.detail?.status) : STATUS.SUBMITTED,
      version: values.version,
      // Apply assign task - step 1
      author: values.assignee,
      reviewer: values.reviewer || '',
      customer: values.customer || '',
      dueDate: values.dueDate ? values.dueDate?.toDate() : null,
      completeDate: values.completeDate ? values.completeDate?.toDate() : null,
      impacts: impacts
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.actor-short' }) }
        ),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(null);
    form.resetFields([
      'version',
      'code',
      'name',
      'description',
      'version',
      // Apply assign task - step 2
      'reviewer',
      'dueDate',
      'completeDate'
    ])
    form.setFieldsValue({
      assignee: currentUserName()
    })
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'actor-name', title: intl.formatMessage({ id: 'actor.column.name' }), },
      { field: 'description', title: intl.formatMessage({ id: 'actor.form.description' }), },
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
      { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
      { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));
    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.ACTOR,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT

  return <CustomModal
    isLoading={state.isLoading}
    className='rq-modal-fill'
    closable={false}
    visible={true}
    footer={null}
  >
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header-filled'>
        <Space size="middle">
          <h4>{intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'actor.action.create' : 'actor.action.update' })}</h4>
          <b>{screenMode === SCREEN_MODE.EDIT ? renderStatusBadge(state.detail?.status) : <></>}</b>
        </Space>
        <CloseCircleFilled size={24} style={{ cursor: 'pointer' }} onClick={() => onDismiss()} />
      </div>

      <div className='rq-modal-inner'>
        <Row gutter={30}>
        {
          screenMode === SCREEN_MODE.EDIT ?
            <Col span={24}>
              <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={intl.formatMessage({ id: 'actor.form.code' })}>
                <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                  <Input disabled maxLength={255} />
                </Form.Item>
              </FormGroup>
            </Col> : <></>
        }

        <Col span={24}>
          <FormGroup inline labelSpan={3} required controlSpan={21} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="actor-name">
            {intl.formatMessage({ id: 'actor.form.name' })}
          </TriggerComment>}>
            <Form.Item
              name="name"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                {
                  max: 255,
                  message: `${intl.formatMessage({ id: 'warning_max_length_255' })}`,
                },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
            >
              <Input placeholder={intl.formatMessage({ id: `actor.form.name` })} maxLength={255} />
            </Form.Item>
          </FormGroup>
        </Col>

        <Col span={24}>
          <FormGroup inline labelSpan={3} required controlSpan={21} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="description">
            {intl.formatMessage({ id: 'actor.form.description' })}
          </TriggerComment>}>
            <TextAreaBullet
              reload={false}
              reloadAfterBack={false}
              label=""
              name="description"
              labelAlign="left"
              rules={[
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
            ></TextAreaBullet>
          </FormGroup>
        </Col>

        <Col span={24}>
          <FormGroup inline labelSpan={3} required controlSpan={21} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="version">
            {intl.formatMessage({ id: 'common.form.version' })}
          </TriggerComment>}>
            <Form.Item name="version" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
              <Input placeholder={intl.formatMessage({ id: `createobject.place-holder.version` })} maxLength={255} />
            </Form.Item>
          </FormGroup>
        </Col>
      </Row>

        {/* Apply assign task - step 3 */}
        <AssignTaskComponent form={form} data={screenMode === SCREEN_MODE.CREATE ? null : state.detail} isDifferent={true} isSubmit={isDraft == false} screenMode={screenMode} />
        {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.ACTOR} onChange={onChange} isSubmitForm={isSubmitForm} />}
      </div>


      <div className='rq-modal-footer'>
        <Row align="middle" justify="space-between">
          <Button onClick={debounce(confirmCancel, 500)}>
            {intl.formatMessage({ id: 'common.action.close' })}
          </Button>
          <Space size="small" align='end'>
            {screenMode === SCREEN_MODE.CREATE ? <Form.Item
              style={{ marginBottom: '0px' }}
              valuePropName="checked"
              name="createMore"
              wrapperCol={{ span: 24 }}
            >
              <Checkbox><b>{intl.formatMessage({ id: 'common.action.create-another' })}</b></Checkbox>
            </Form.Item> : <></>}

            {screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.DRAFT || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT ?
              <Button type="primary" ghost htmlType="submit" onClick={() => {
                setIsDraft(false)
                setIsSubmitForm(true)
              }}>
                {intl.formatMessage({ id: 'common.action.submit' })}
              </Button> : <></>
            }
            {
              screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.DRAFT || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT ?
                <Button onClick={() => {
                  setIsDraft(true)
                  setIsSubmitForm(true)
                }} className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'actor.action.update' })}
                </Button> : <></>
            }
          </Space>
        </Row>
      </div>
    </Form>
  </CustomModal>
}
const ActorForm = ({ actorID, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: ActorFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return <>
    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button
          ghost={screenMode === SCREEN_MODE.CREATE}
          type='primary'
          className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
          onClick={() => setIsModalVisible(true)}
          icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
        >
          {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'actor.action.create' : 'common.action.update' })}
        </Button> :
        buttonType === BUTTON_TYPE.ICON ?
          <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
          <></>
    }
    {isModalVisible === true ? <ActorFormModal actorID={actorID} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}

export default ActorForm
