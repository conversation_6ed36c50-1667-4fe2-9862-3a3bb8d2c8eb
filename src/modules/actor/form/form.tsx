import AppState from '@/store/types'
import {
    Button, Card, Checkbox, Col, Form, Input, Modal, Row, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavImpact from '../../../helper/component/lav-impact'
import TextAreaBullet from '../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { ActorState } from '../type'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text } = Typography
const { confirm } = Modal

interface ActorFormModalProps {
    actorID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}

const ActorFormPage = ({ actorID, screenMode, onFinish, onDismiss }: ActorFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.Actor) as ActorState
    const [impacts, setImpacts] = useState<any>(false)
    const { height: windowHeight } = useWindowDimensions()
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const [isCreateMore, setIsCreateMore] = useState(false);
    const modalConfirmConfig = useModalConfirmationConfig()
    const getCkeditorDataDes: any = createRef()



    // Destroy
    useEffect(() => {
        form.setFieldsValue({
            assignee: currentUserName(),
        })
        return () => {
            form.resetFields(['createMore']);
            dispatch(resetState(null));
            resetForm();
        }
    }, [])

    useEffect(() => {
        if (actorID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(actorID))
        }
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'actor.title-create' : 'actor.title-update' });
    }, [screenMode, actorID])

    useEffect(() => {
        if (actorID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            form.setFieldsValue({
                code: state.detail.code,
                name: state.detail.name,
                description: state.detail.description
            })
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            if (isCreateMore) {
                resetForm();
                form.setFieldsValue({
                    assignee: currentUserName(),
                    dueDate: moment(new Date()),
                })
            } else {
                if (onFinish) {
                    onFinish();
                }
                onDismiss();
            }
            setIsCreateMore(false);
        }
    }, [state.createSuccess, state.updateSuccess])

    useBeforeUnload()

    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }
    const onSubmit = debounce(async (values: any, st?: string) => {
        let mentionReferences = getReferencesFromEditor(getCkeditorDataDes.current?.props?.data);
        const requestData: any = {
            id: actorID || null,
            name: values.name,
            description: getCkeditorDataDes.current?.props?.data,
            version: values.version,
            // Apply assign task - step 1
            impacts: impacts,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
        }
        setIsCreateMore(values.createMore);
        requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
        dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    }, 500)

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        form.resetFields([
            'version',
            'code',
            'name',
            'description',
            'version',
            // Apply assign task - step 2
            'reviewer',
            'customer',
            'dueDate',
            'completeDate'
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'actor-name', title: intl.formatMessage({ id: 'actor.column.name' }), },
            { field: 'description', title: intl.formatMessage({ id: 'actor.form.description' }), },
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'common.assign-task.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'common.assign-task.customer' }), },
        ];
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));
        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.ACTOR,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    //#endregion COMMENT INIT

    return <Spin spinning={state.isLoading}>
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <LavPageHeader
                showBreadcumb
                title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'actor.title-create' : 'actor.title-update' })}
            >
                <Space size="small">
                    {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                        style={{ marginBottom: '0px' }}
                        valuePropName="checked"
                        name="createMore"
                        wrapperCol={{ span: 24 }}
                    >
                        <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                    </Form.Item> : <></>}
                    <Button onClick={confirmCancel}>
                        {intl.formatMessage({ id: 'common.action.close' })}
                    </Button>

                    <Form.Item style={{ marginBottom: '0px' }}>
                        <Button
                            className="success-btn"
                            htmlType="submit"
                            onClick={() => {
                                setIsSubmitForm(true)
                            }}
                        >
                            {intl.formatMessage({ id: 'common.action.save' })}
                        </Button>
                    </Form.Item>
                </Space>
            </LavPageHeader>
            <br />
            <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 150}>
                <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                    <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'actor.actor-information' })}>
                        <Row gutter={30}>
                            {
                                screenMode === SCREEN_MODE.EDIT ?
                                    <Col span={24}>
                                        <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={intl.formatMessage({ id: 'common.label.code' })}>
                                            <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                                <Input disabled maxLength={255} />
                                            </Form.Item>
                                        </FormGroup>
                                    </Col> : <></>
                            }

                            <Col span={24}>
                                <FormGroup inline labelSpan={3} required controlSpan={21} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="actor-name">
                                    {intl.formatMessage({ id: 'common.label.name' })}
                                </TriggerComment>}>
                                    <Form.Item
                                        name="name"
                                        rules={[
                                            {
                                                required: true,
                                                message: intl.formatMessage({ id: 'IEM_1' }),
                                            },
                                            {
                                                max: 255,
                                                message: `${intl.formatMessage({ id: 'warning_max_length_255' })}`,
                                            },
                                            {
                                                validator: async (rule, value) => {
                                                    if (value && value.trim().length === 0) {
                                                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                                    }
                                                },
                                            },
                                        ]}
                                    >
                                        <Input placeholder={intl.formatMessage({ id: `actor.form.name` })} maxLength={255} />
                                    </Form.Item>
                                </FormGroup>
                            </Col>

                            <Col span={24}>
                                <FormGroup inline labelSpan={3} required controlSpan={21} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="description">
                                    {intl.formatMessage({ id: 'actor.form.description' })}
                                </TriggerComment>}>
                                    <Form.Item
                                        name="description"
                                        labelAlign="left"
                                        rules={[{
                                            validator: async (rule, value) => {
                                                const description = getCkeditorDataDes?.current?.props?.data
                                                if (description == '' || description == undefined) {
                                                    throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                                }
                                            }
                                        }]}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <CkeditorMention
                                            ref={getCkeditorDataDes}
                                            data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
                                        />
                                    </Form.Item>
                                </FormGroup>
                            </Col>
                        </Row>
                    </Card>
                    {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.ACTOR} onChange={onChange} isSubmitForm={isSubmitForm} />}
                </Space>
            </Scrollbars>
        </Form>
    </Spin>
}

export default ActorFormPage
