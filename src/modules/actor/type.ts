export interface ActorState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: ActorStateDetail | null,
  selectedData?: ActorStateDetail | null,
  isModalShow?:boolean,
}
export interface ActorStateDetail {
  id: number | null,
  code: string,
  name: string,
  description: string,
  status: number,
  author: string,
  reviewer: string,
  customer: string
  dueDate: string,
  completeDate: string,
  projectId?: number | null
  impacts: string
}

export const defaultState = {
  detail: {
    id: null,
    code: '',
    name: '',
    description: '',
    status: 0,
    version: '',
    author: '',
    reviewer: '',
    dueDate: '',
    completeDate: '',
    customer: '',
    projectId:null,
    impacts: '',
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/ACTOR/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/ACTOR/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/ACTOR/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/ACTOR/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/ACTOR/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/ACTOR/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/ACTOR/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/ACTOR/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/ACTOR/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/ACTOR/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/ACTOR/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/ACTOR/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/ACTOR/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/ACTOR/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/ACTOR/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/ACTOR/DELETE_FAILED',
  SET_MODAL_VISIBLE = '@@MODULES/ACTOR/SET_MODAL_VISIBLE',
}
