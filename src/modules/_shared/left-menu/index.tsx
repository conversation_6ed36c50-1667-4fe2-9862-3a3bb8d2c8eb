import { PaginationState } from '../../../helper/component/lav-table/type'
import AppState from '@/store/types'
import { Col, Menu, Pagination, Row, Space, Spin, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useSelector } from 'react-redux'
import { useHistory } from 'react-router'
import intl from '../../../config/locale.config'
import { DEFAULT_PAGE_SIZE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID } from '../../../constants'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, renderCommonStatusBadge, renderStatusBadge } from '../../../helper/share'
import AppCommonService from '../../../services/app.service'
import TableService from '../../../services/lav-table-service'

const { Title } = Typography

interface LavLeftControlProps {
    title: string,
    activeId: number,
    route: string,
    apiUrl: string,
    reload?: boolean,
    reloadSuccess?: any,
    children: any,
    isCommon?: boolean,
    hideStatus?: boolean,
    handleCreate?: any,
    artefactType?: number
}
const LavLeftControl = ({ title, activeId, apiUrl, reload = false, reloadSuccess, route, children, isCommon, artefactType, hideStatus = false, handleCreate }: LavLeftControlProps) => {
    const { height: windowHeight } = useWindowDimensions();
    const [allData, setAllData] = useState<any>([]);
    const [dataSource, setDataSource] = useState<any>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [pageIndex, setPageIndex] = useState(1);
    const currentUserProjects = localStorage.getItem('currentUserProjects') || ''
    const currentUserProjectsParse: any = JSON.parse(currentUserProjects)
    const currentProj = currentUserProjectsParse?.projects?.filter((e) => e.projectCode === extractProjectCode())
    let defaultPaging: any
    if (currentProj) {
        defaultPaging = currentProj[0]?.defaultPaging
    }
    const [pageSize, setPageSize] = useState(defaultPaging ? defaultPaging : DEFAULT_PAGE_SIZE);
    const [totalRecord, setTotalRecord] = useState(0);

    const history = useHistory()
    const paginationState = useSelector<AppState | null>((s) => s?.Pagination) as PaginationState;

    const loadData = () => {
        setIsLoading(true);
        if (artefactType == paginationState.artefactType && paginationState.paginationProps?.pagination !== '') {            
            TableService
                .getData(paginationState.paginationUrl.apiUrl, paginationState.paginationUrl?.pageIndex, paginationState.paginationUrl?.pageSize, paginationState.paginationUrl?.paramFilter, paginationState.paginationUrl?.paramSorter)
                .then((res) => {
                    let response = res.data.data ? res.data.data : res.data
                    setDataSource(response)
                    setTotalRecord(res.data.total)
                    setPageIndex(paginationState.paginationUrl?.pageIndex)
                    setPageSize(paginationState.paginationUrl?.pageSize)
                    setIsLoading(false)
                })
        } else {
            AppCommonService.getData(apiUrl + '?ignoreStatusFilter=true').then(res => {
                res.sort((a, b) => {
                    return b.code.match(/\d+/)[0] - a.code.match(/\d+/)[0]
                });
                handleCreate(res)
                setAllData(res);
                setTotalRecord(res.length);
                setIsLoading(false);
            }).catch(err => {
                setAllData([]);
                setTotalRecord(0);
                setIsLoading(false);
            })
        }
    }

    useEffect(() => {
        loadData();
    }, [])

    useEffect(() => {
        if (reload) {
            loadData();
            reloadSuccess();
        }
    }, [reload])


    useEffect(() => {
        if (activeId && allData.length > 0) {
            // Set active pagesize
            let existsIndex = allData.findIndex(e => e.id == activeId);
            if (existsIndex != -1) {
                existsIndex += 1;
                const pageActive = existsIndex % pageSize != 0 ? (parseInt((existsIndex / pageSize).toString()) + 1) : existsIndex / pageSize
                setPageIndex(pageActive);
            }

            // Set active view
            window.setTimeout(() => {
                document.getElementById(activeId.toString())?.focus()
            }, 500)
        }
    }, [activeId, allData])

    useEffect(() => {
        if (artefactType == paginationState.artefactType && paginationState.paginationProps?.pagination !== '') {

        } else {
            if (allData.length > 0 && pageIndex && pageSize) {
                // Set datasource
                let newDataSource = Object.assign([], allData);
                newDataSource = newDataSource.slice((pageIndex - 1) * pageSize, pageIndex * pageSize);
                setDataSource(newDataSource);
            }
        }
    }, [allData, pageIndex, pageSize])

    const handlePageChange = (pageI, pageS) => {
        if (artefactType == paginationState.artefactType && paginationState.paginationProps?.pagination !== '') {
            TableService
                .getData(paginationState.paginationUrl.apiUrl, pageI, pageS, paginationState.paginationUrl?.paramFilter, paginationState.paginationUrl?.paramSorter)
                .then((res) => {
                    let response = res.data.data ? res.data.data : res.data
                    setDataSource(response)
                    setTotalRecord(res.data.total)
                    setPageIndex(pageI)
                    setPageSize(pageS)
                    setIsLoading(false)
                })
        } else {
            setPageIndex(pageI);
            setPageSize(pageS);
            // dispatch(getListRequest({ skip: pageI, take: pageS }))
        }
    }

    const changeMenu = (id) => {
        if (activeId !== id) {
            const href = isCommon ? `${route}${id}` : `${PROJECT_PREFIX}${extractProjectCode()}${route}${id}`
            history.push(href)
        }
    }

    return <>
        <div className="record-detail-left-control-container p-1rem">
            <Spin spinning={isLoading}>
                <Space direction="vertical" className="full-width">
                    <div className='rq-panel-heading'>
                        <Title className='rq-panel-title' level={3}>{intl.formatMessage({ id: title })}</Title>
                        <div>{children}</div>
                    </div>
                    <Scrollbars autoHide >
                        <Menu selectedKeys={[activeId?.toString()]} mode="inline" theme="light" className="detail-menu">
                            {
                                dataSource?.map((item: any) => (
                                    <Menu.Item id={item?.id?.toString()} className="detail-menu-item" key={item.id} onClick={() => changeMenu(item.id)}>
                                        <Row>
                                            <Col span={24}>{`${item.code}`}</Col>
                                            {hideStatus ? <></> : <Col span={24}>{isCommon ? renderCommonStatusBadge(item.status) : renderStatusBadge(item.status, artefactType === REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT ? true : false)}</Col>}
                                        </Row>
                                        <div className='text-ellipsis'>{item.name}</div> 
                                    </Menu.Item>
                                ))}
                        </Menu>
                    </Scrollbars>
                </Space>
            </Spin>
        </div>
        <div style={{ padding: '0 1rem', textAlign: 'center' }}>
            <Pagination simple
                onChange={handlePageChange}
                size='small'
                current={pageIndex}
                total={totalRecord}
            />
        </div>
    </>
}

export default React.memo(LavLeftControl)
