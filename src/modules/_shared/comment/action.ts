import { createAction } from '@reduxjs/toolkit'
import { ActionEnum, CommentModel } from './type'

export const initComment = createAction<{ projectId, itemId, fields: any[] }>(ActionEnum.INIT)

export const initCommentScreen = createAction<{ projectId, artefact, field?, code?, itemId }>(ActionEnum.INIT_SCREEN)
export const initCommentScreenSuccess = createAction<any[]>(
  ActionEnum.INIT_SCREEN_SUCCESS
)
export const initCommentScreenFailure = createAction<string>(
  ActionEnum.INIT_SCREEN_FAILURE
)

export const openComment = createAction<{ index, title, field }>(ActionEnum.OPEN_COMMENT)
export const closeComment = createAction<void>(ActionEnum.CLOSE_COMMENT)

export const addCommentRequest = createAction<CommentModel>(ActionEnum.ADD_REQUEST)
export const addCommentRequestSuccess = createAction<CommentModel>(
  ActionEnum.ADD_REQUEST_SUCCESS
)
export const addCommentRequestFailure = createAction<string>(
  ActionEnum.ADD_REQUEST_FAILURE
)

export const updateCommentRequest = createAction<{ id, category, commentBody }>(ActionEnum.UPDATE_REQUEST)
export const updateCommentRequestSuccess = createAction<CommentModel>(
  ActionEnum.UPDATE_REQUEST_SUCCESS
)
export const updateCommentRequestFailure = createAction<string>(
  ActionEnum.UPDATE_REQUEST_FAILURE
)

export const deleteCommentRequest = createAction<number>(ActionEnum.DELETE_REQUEST)
export const deleteCommentRequestSuccess = createAction<number>(
  ActionEnum.DELETE_REQUEST_SUCCESS
)
export const deleteCommentRequestFailure = createAction<string>(
  ActionEnum.DELETE_REQUEST_FAILURE
)

export const replyCommentRequest = createAction<{ commentId, replyBody }>(ActionEnum.REPLY_REQUEST)
export const replyCommentRequestSuccess = createAction<{ id, commentId, createdUserFullName, replyBody, replyDate }>(
  ActionEnum.REPLY_REQUEST_SUCCESS
)
export const replyCommentRequestFailure = createAction<string>(
  ActionEnum.REPLY_REQUEST_FAILURE
)

export const actionCommentRequest = createAction<{ actionType, commentId, rowVersion }>(ActionEnum.ACTION_REQUEST)
export const actionCommentRequestSuccess = createAction<{ id, status }>(
  ActionEnum.ACTION_REQUEST_SUCCESS
)
export const actionCommentRequestFailure = createAction<string>(
  ActionEnum.ACTION_REQUEST_FAILURE
)

export const updateReplyRequest = createAction<{ id, replyBody }>(ActionEnum.UPDATE_REPLY_REQUEST)
export const updateReplyRequestSuccess = createAction<{ commentId, id, replyBody, replyDate }>(
  ActionEnum.UPDATE_REPLY_REQUEST_SUCCESS
)
export const updateReplyRequestFailure = createAction<string>(
  ActionEnum.UPDATE_REPLY_REQUEST_FAILURE
)

export const deleteReplyRequest = createAction<number>(ActionEnum.DELETE_REPLY_REQUEST)
export const deleteReplyRequestSuccess = createAction<{ commentId, id }>(
  ActionEnum.DELETE_REPLY_REQUEST_SUCCESS
)
export const deleteReplyRequestFailure = createAction<string>(
  ActionEnum.DELETE_REPLY_REQUEST_FAILURE
)
