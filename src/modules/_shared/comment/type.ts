export interface CommentState {
  isLoading: boolean
  initSuccess?: boolean

  projectId?: number,
  itemId?: number,
  index: number
  title?: string
  artefact: string
  field: string
  isVisible?: boolean

  fields: { field, title }[]
  comments: CommentModel[]
}

export interface CommentModel {
  id?: number
  projectId?: number
  artefact: string
  field: string
  code?: string
  itemId?: number | undefined | null
  category?: string
  createdUserFullName?: string
  commentBody?: string
  commentDate?: string
  status?: string
  rowVersion?: string
  color?: string
  orderField?: number

  canEdit?: boolean
  canDelete?: boolean
  canReply?: boolean
  canResolve?: boolean
  canClose?: boolean
  canReopen?: boolean
  canCancel?: boolean

  replies?: ReplyModel[]
}

export interface ReplyModel {
  id?: number
  commentId?: string
  createdUserFullName?: string
  replyBody?: string
  replyDate?: string
  rowVersion?: string

  canEdit?: boolean
  canDelete?: boolean
}

export enum ActionEnum {
  INIT = '@@MODULES/VIEWCOMMENT/INIT',

  INIT_SCREEN = '@@MODULES/VIEWCOMMENT/INIT_SCREEN',
  INIT_SCREEN_SUCCESS = '@@MODULES/VIEWCOMMENT/INIT_SCREEN_SUCCESS',
  INIT_SCREEN_FAILURE = '@@MODULES/VIEWCOMMENT/INIT_SCREEN_FAILURE',

  OPEN_COMMENT = '@@MODULES/VIEWCOMMENT/OPEN_COMMENT',
  CLOSE_COMMENT = '@@MODULES/VIEWCOMMENT/CLOSE_COMMENT',

  ADD_REQUEST = '@@MODULES/VIEWCOMMENT/ADD_REQUEST',
  ADD_REQUEST_SUCCESS = '@@MODULES/VIEWCOMMENT/ADD_REQUEST_SUCCESS',
  ADD_REQUEST_FAILURE = '@@MODULES/VIEWCOMMENT/ADD_REQUEST_FAILURE',

  UPDATE_REQUEST = '@@MODULES/VIEWCOMMENT/UPDATE_REQUEST',
  UPDATE_REQUEST_SUCCESS = '@@MODULES/VIEWCOMMENT/UPDATE_REQUEST_SUCCESS',
  UPDATE_REQUEST_FAILURE = '@@MODULES/VIEWCOMMENT/UPDATE_REQUEST_FAILURE',

  DELETE_REQUEST = '@@MODULES/VIEWCOMMENT/DELETE_REQUEST',
  DELETE_REQUEST_SUCCESS = '@@MODULES/VIEWCOMMENT/DELETE_REQUEST_SUCCESS',
  DELETE_REQUEST_FAILURE = '@@MODULES/VIEWCOMMENT/DELETE_REQUEST_FAILURE',

  REPLY_REQUEST = '@@MODULES/VIEWCOMMENT/REPLY_REQUEST',
  REPLY_REQUEST_SUCCESS = '@@MODULES/VIEWCOMMENT/REPLY_REQUEST_SUCCESS',
  REPLY_REQUEST_FAILURE = '@@MODULES/VIEWCOMMENT/REPLY_REQUEST_FAILURE',

  ACTION_REQUEST = '@@MODULES/VIEWCOMMENT/ACTION_REQUEST',
  ACTION_REQUEST_SUCCESS = '@@MODULES/VIEWCOMMENT/ACTION_REQUEST_SUCCESS',
  ACTION_REQUEST_FAILURE = '@@MODULES/VIEWCOMMENT/ACTION_REQUEST_FAILURE',

  UPDATE_REPLY_REQUEST = '@@MODULES/VIEWCOMMENT/UPDATE_REPLY_REQUEST',
  UPDATE_REPLY_REQUEST_SUCCESS = '@@MODULES/VIEWCOMMENT/UPDATE_REPLY_REQUEST_SUCCESS',
  UPDATE_REPLY_REQUEST_FAILURE = '@@MODULES/VIEWCOMMENT/UPDATE_REPLY_REQUEST_FAILURE',

  DELETE_REPLY_REQUEST = '@@MODULES/VIEWCOMMENT/DELETE_REPLY_REQUEST',
  DELETE_REPLY_REQUEST_SUCCESS = '@@MODULES/VIEWCOMMENT/DELETE_REPLY_REQUEST_SUCCESS',
  DELETE_REPLY_REQUEST_FAILURE = '@@MODULES/VIEWCOMMENT/DELETE_REPLY_REQUEST_FAILURE',
}
