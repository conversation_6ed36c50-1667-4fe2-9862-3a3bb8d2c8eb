import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { apiCall } from '../../../helper/api/aloApi'
import { ShowAppMessage } from '../../../helper/share'
import {
  actionCommentRequest,
  actionCommentRequestFailure,
  actionCommentRequestSuccess,
  addCommentRequest,
  addCommentRequestFailure,
  addCommentRequestSuccess,
  deleteCommentRequest, deleteCommentRequestFailure, deleteCommentRequestSuccess, deleteReplyRequest, deleteReplyRequestFailure, deleteReplyRequestSuccess, initCommentScreen, initCommentScreenFailure, initCommentScreenSuccess, replyCommentRequest, replyCommentRequestFailure, replyCommentRequestSuccess, updateCommentRequest, updateCommentRequestFailure, updateCommentRequestSuccess, updateReplyRequest, updateReplyRequestFailure, updateReplyRequestSuccess
} from './action'
import CommentApi from './url'

function* initScreenFlow(action: Action) {
  if (initCommentScreen.match(action)) {
    try {
      const res = yield call(apiCall, 'POST', CommentApi.GET_ALL_COMMENTS, action.payload)
      yield put(initCommentScreenSuccess(res.data))
    } catch (err) {
      yield put(initCommentScreenFailure(''))
      ShowAppMessage(err)
    }
  }
}

function* addRequestFlow(action) {
  try {
    const res = yield call(apiCall, 'POST', CommentApi.ADD_UPDATE_DELETE, action.payload)
    yield put(addCommentRequestSuccess(res.data))
  } catch (err) {
    yield put(addCommentRequestFailure(''))
    ShowAppMessage(err)
  }
}

function* updateRequestFlow(action) {
  try {
    const url = `${CommentApi.ADD_UPDATE_DELETE}/${action.payload.id}`
    const res = yield call(apiCall, 'PUT', url, action.payload)
    yield put(updateCommentRequestSuccess(res.data))
  } catch (err) {
    yield put(updateCommentRequestFailure(''))
    ShowAppMessage(err, null, 'comment')
  }
}

function* deleteRequestFlow(action) {
  try {
    const url = `${CommentApi.ADD_UPDATE_DELETE}/${action.payload}`
    const res = yield call(apiCall, 'DELETE', url)
    yield put(deleteCommentRequestSuccess(action.payload))
  } catch (err) {
    yield put(deleteCommentRequestFailure(''))
    ShowAppMessage(err)
  }
}

function* replyRequestFlow(action) {
  try {
    const url = `${CommentApi.ACTION}/${action.payload.commentId}/reply`
    const res = yield call(apiCall, 'PUT', url, action.payload)
    yield put(replyCommentRequestSuccess(res.data))
  } catch (err) {
    yield put(replyCommentRequestFailure(''))
    ShowAppMessage(err)
  }
}

function* actionRequestFlow(action) {
  try {
    const url = `${CommentApi.ACTION}/${action.payload.commentId}/${action.payload.actionType}`
    const res = yield call(apiCall, 'PUT', url, action.payload)
    yield put(actionCommentRequestSuccess(res.data))
  } catch (err) {
    yield put(actionCommentRequestFailure(''))
    ShowAppMessage(err)
  }
}

function* updateReplyRequestFlow(action) {
  try {
    const url = `${CommentApi.ADD_UPDATE_DELETE_REPLY}/${action.payload.id}`
    const res = yield call(apiCall, 'PUT', url, action.payload)
    yield put(updateReplyRequestSuccess(res.data))
  } catch (err) {
    yield put(updateReplyRequestFailure(''))
    ShowAppMessage(err, null, 'reply')
  }
}

function* deleteReplyRequestFlow(action) {
  try {
    const url = `${CommentApi.ADD_UPDATE_DELETE_REPLY}/${action.payload}`
    const res = yield call(apiCall, 'DELETE', url)
    yield put(deleteReplyRequestSuccess(res.data))
  } catch (err) {
    yield put(deleteReplyRequestFailure(''))
    ShowAppMessage(err)
  }
}

function* watchFetchRequest() {
  yield takeLatest(initCommentScreen.type, initScreenFlow)
  yield takeLatest(addCommentRequest.type, addRequestFlow)
  yield takeLatest(updateCommentRequest.type, updateRequestFlow)
  yield takeLatest(deleteCommentRequest.type, deleteRequestFlow)
  yield takeLatest(replyCommentRequest.type, replyRequestFlow)
  yield takeLatest(actionCommentRequest.type, actionRequestFlow)
  yield takeLatest(updateReplyRequest.type, updateReplyRequestFlow)
  yield takeLatest(deleteReplyRequest.type, deleteReplyRequestFlow)
}

export default function* commentSaga() {
  yield all([fork(watchFetchRequest)])
}
