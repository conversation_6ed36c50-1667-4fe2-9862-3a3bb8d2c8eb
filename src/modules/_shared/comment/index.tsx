import AppState from '@/store/types'
import { DownOutlined, ReloadOutlined, UpOutlined, UserOutlined } from '@ant-design/icons'
import { useAccount, useMsal } from '@azure/msal-react'
import { Badge, Button, Dropdown, Form, Input, Menu, Modal, Row, Select, Space, Typography } from 'antd'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import Draggable from 'react-draggable'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { COMMENT_CATEGORY, COMMENT_STATUS, DATETIME_FORMAT } from '../../../constants'
import Ckeditor from '../../../helper/component/ckeditor'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { CommentModel, CommentState } from '../../../modules/_shared/comment/type'
import { actionCommentRequest, addCommentRequest, closeComment, deleteCommentRequest, deleteReplyRequest, initCommentScreen, openComment, replyCommentRequest, updateCommentRequest, updateReplyRequest } from './action'
const { Text } = Typography
const { Option } = Select
const { TextArea } = Input

interface SharedCommentProps {
    artefact: string,
    field: string,
    title?: string,
    index: number,
    isVisible?: boolean,
    comments: CommentModel[],
    projectId?: number,
    itemId?: number,
}
const SharedComment = ({ artefact, field, title, index, isVisible, comments, projectId, itemId }: SharedCommentProps) => {
    const { accounts } = useMsal();
    const currentUser = useAccount(accounts[0] || {});
    const colors = {
        record_opened_color: '#515560',
        record_resolved_color: '#00C853',
        record_closed_color: '#515560',
        record_cancelled_color: '#faad14',
    }
    const [form] = Form.useForm();
    const draggleRef = useRef<any>(null);
    const commentListRef = useRef<any>(null);
    const dispatch = useDispatch();
    const modalConfirmConfig = useModalConfirmationConfig()
    const widthModal = 450;

    // STATE
    const [commentItems, setCommentItems] = useState<any[]>([]);
    const [editId, setEditId] = useState<number>(-1);
    const [replyId, setReplyId] = useState<number | null>(null);
    const [visible, setVisible] = useState(false);
    const [disableDraggable, setDisableDraggable] = useState(false);
    const [bounds, setBounds] = useState({
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
    });
    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    //#region EDITOR

    const commentRef: any = createRef()
    const [commentMessage, setCommentMessage] = useState('');

    const commentChange = (e) => {
        form.setFieldsValue({ message: e })
    }

    //#endregion EDITOR

    //#region LIFE CYCLE

    useEffect(() => {
        setVisible(isVisible === true ? true : false);
        if (isVisible === true) {
            // RESET Category to BUG
            form.setFieldsValue({
                category: COMMENT_CATEGORY.BUG,
            })
        }
    }, [isVisible])

    useEffect(() => {
        const commentsByField = comments?.filter((e) => e.field === field)?.map(e => ({ ...e })) || [];
        commentsByField.forEach(o => {
            switch (o.status) {
                case COMMENT_STATUS.OPEN:
                    o.color = colors.record_opened_color;
                    break;
                case COMMENT_STATUS.RESOLVED:
                    o.color = colors.record_resolved_color;
                    break;
                case COMMENT_STATUS.CLOSED:
                    o.color = colors.record_closed_color;
                    break;
                case COMMENT_STATUS.CANCELLED:
                    o.color = colors.record_cancelled_color;
                    break;
            }
        });
        setCommentItems(commentsByField);
    }, [field, comments])

    //#endregion LIFE CYCLE

    //#region METHODS

    const onStart = (_event, uiData) => {
        const { clientWidth, clientHeight } = window.document.documentElement;
        const targetRect = draggleRef.current?.getBoundingClientRect();
        if (!targetRect) {
            return;
        }

        setBounds({
            left: -targetRect.left + uiData.x,
            right: clientWidth - (targetRect.right - uiData.x),
            top: -targetRect.top + uiData.y,
            bottom: clientHeight - (targetRect.bottom - uiData.y),
        });
    };

    const handleAddComment = (value: any) => {
        const newComment: CommentModel = {
            artefact: artefact as string,
            field: field as string,
            category: value.category,
            commentBody: commentRef?.current?.props?.data //value.message,
        };
        onCommentHandle.onAdd(newComment);
        form.resetFields(['id', 'category', 'message', 'rowVersion']);
        setCommentMessage('');
    }

    const handleEditComment = (comment) => {
        setEditId(comment.id);
        setReplyId(null);
        form.setFieldsValue({
            category: comment.category,
            message: comment.commentBody,
            rowVersion: comment.rowVersion
        })
        setCommentMessage(comment.commentBody);
    }

    const handleReplyComment = (id) => {
        setReplyId(-1);
        setEditId(-1);
        form.resetFields(['id', 'category', 'message', 'rowVersion']);
        setCommentMessage('');
    }

    const handleAddUpdateReply = (replyValue) => {
        if (replyValue.id) { // Update
            const reply = {
                id: replyValue.id,
                replyBody: commentRef?.current?.props?.data, //replyValue.message
                rowVersion: replyValue.rowVersion
            };
            onCommentHandle.onUpdateReply(reply);
            handleCancelReply();
        } else { // Add
            const reply = {
                commentId: commentItems[0].id,
                replyBody: commentRef?.current?.props?.data //replyValue.message
            };
            onCommentHandle.onReply(reply);
            form.resetFields(['id', 'category', 'message', 'rowVersion']);
            setCommentMessage('');
        }
        setTimeout(() => {
            commentListRef?.current?.scrollIntoView({ behavior: "smooth" })
        }, 1000)
    }

    const handleCancelReply = () => {
        setReplyId(null);
        form.resetFields(['id', 'category', 'message', 'rowVersion']);
        setCommentMessage('');
    }

    const handleEditReply = (reply) => {
        setEditId(-1);
        setReplyId(reply.id);
        form.setFieldsValue({
            id: reply.id,
            message: reply.replyBody,
            rowVersion: reply.rowVersion
        })
        setCommentMessage(reply.replyBody);
    }

    const handleRemoveReply = (id) => {
        onCommentHandle.onDeleteReply(id);
        if (id === replyId) {
            handleCancelReply();
        }
    }

    const handleSaveComment = (id, value: any) => {
        const hasComment = comments.find(o => o.id === id);
        if (hasComment) {
            const updateItem = {
                id: id,
                category: value.category,
                commentBody: commentRef?.current?.props?.data, //value.message,
                rowVersion: value.rowVersion,
            }
            onCommentHandle.onUpdate(updateItem);
        }
        form.resetFields(['id', 'category', 'message', 'rowVersion']);
        setCommentMessage('');
        setEditId(-1);
    }

    const handleCancel = () => {
        form.resetFields(['id', 'category', 'message', 'rowVersion']);
        setCommentMessage('');
        setEditId(-1);
    }

    const handleVisible = (visible) => {
        setVisible(visible);
        onCommentHandle.onReset();

        form.resetFields(['id', 'category', 'message', 'rowVersion']);
        setCommentMessage('');
        setTimeout(() => {
            setEditId(-1);
            setReplyId(null);
        }, 300)
    }

    const handleReloadComment = () => {
        const payload = {
            projectId: commentState.projectId,
            itemId: commentState.itemId,
            artefact: commentState.artefact,
            fields: commentState.fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }

    //#endregion METHOD

    //#region COMMENT HANDLER
    const _onAddComment = (newComment: CommentModel) => {
        newComment.projectId = projectId;
        newComment.itemId = itemId;
        dispatch(addCommentRequest(newComment));
    }

    const _onUpdateComment = (updateComment: { id, category, commentBody }) => {
        dispatch(updateCommentRequest(updateComment));
    }

    const _onRemoveComment = (commentId: number) => {
        Modal.confirm({
            ...modalConfirmConfig,
            title: 'Confirm',
            content: intl.formatMessage({ id: 'CDF_0' }),
            zIndex: 1020,
            okText: modalConfirmConfig.okText,
            okButtonProps: { danger: true },
            onOk() {
                dispatch(deleteCommentRequest(commentId));
            },
        })
    }

    const _onReplyComment = (reply: { commentId, replyBody }) => {
        dispatch(replyCommentRequest(reply));
    }

    const _onResolveComment = (comment: any) => {
        dispatch(actionCommentRequest({ actionType: 'resolve', commentId: comment.id, rowVersion: comment.rowVersion }));
    }

    const _onReopenComment = (comment: any) => {
        dispatch(actionCommentRequest({ actionType: 'reopen', commentId: comment.id, rowVersion: comment.rowVersion }));
    }

    const _onCloseComment = (comment: any) => {
        dispatch(actionCommentRequest({ actionType: 'close', commentId: comment.id, rowVersion: comment.rowVersion }));
    }

    const _onCancelComment = (comment: any) => {
        dispatch(actionCommentRequest({ actionType: 'cancel', commentId: comment.id, rowVersion: comment.rowVersion }));
    }

    const _onUpdateReply = (reply: { id, replyBody }) => {
        dispatch(updateReplyRequest(reply));
    }

    const _onDeleteReply = (replyId: number) => {
        Modal.confirm({
            ...modalConfirmConfig,
            title: 'Confirm',
            content: intl.formatMessage({ id: 'CDF_1' }),
            okText: modalConfirmConfig.okText,
            zIndex: 1020,
            okButtonProps: { danger: true },
            onOk() {
                dispatch(deleteReplyRequest(replyId));
            },
        })
    }

    const _onResetComment = () => {
        dispatch(closeComment());
    }

    const _onPreviousComment = () => {
        if (commentState.index <= 0) return;

        const indexComment = commentState.index - 1;
        const comment = commentState.comments[indexComment];
        if (comment) {
            const field = commentState.fields.find(o => o.field === comment.field);
            dispatch(openComment({ index: indexComment, title: field?.title, field: comment.field }));
        }
    }

    const _onNextComment = () => {
        if (commentState.index >= commentState.comments.length - 1) return;

        const indexComment = commentState.index + 1;
        const comment = commentState.comments[indexComment];
        if (comment) {
            const field = commentState.fields.find(o => o.field === comment.field);
            dispatch(openComment({ index: indexComment, title: field?.title, field: comment.field }));
        }
    }

    const onCommentHandle = {
        onReset: _onResetComment,
        onPrevious: _onPreviousComment,
        onNext: _onNextComment,

        onAdd: _onAddComment,
        onUpdate: _onUpdateComment,
        onDelete: _onRemoveComment,
        onReply: _onReplyComment,
        onResolve: _onResolveComment,
        onReopen: _onReopenComment,
        onClose: _onCloseComment,
        onCancel: _onCancelComment,
        onUpdateReply: _onUpdateReply,
        onDeleteReply: _onDeleteReply
    }
    //#endregion COMMENT HANDLER

    //#region VIEW
    const CommentTitle = () => {
        return <Row className='rq-comment-title' align='middle' justify='space-between'>
            <div className='left-title'>
                <ReloadOutlined className={'reload'} onClick={() => { handleReloadComment() }} style={{ fontSize: '14px' }} />
                {
                    commentItems.length > 0
                        ? <>
                            <UpOutlined className={'up-icon ' + (index <= 0 ? 'disabled' : '')} onClick={() => { onCommentHandle.onPrevious() }} />
                            <DownOutlined className={'down-icon ' + (index >= comments.length - 1 ? 'disabled' : '')} onClick={() => { onCommentHandle.onNext() }} />
                        </>
                        : <></>
                }
            </div>
            <div className='center-title handle-draggable' style={{ cursor: 'move', flex: 1, textAlign: 'center' }}>
                {commentItems.length > 0 ? <span className='page-info'>{index + 1} of {comments.length} - </span> : <></>}
                <span onMouseOver={() => { if (disableDraggable) { setDisableDraggable(false); } }} onMouseOut={() => { setDisableDraggable(true); }}>{title}</span>
            </div>
            <div className='right-title'>
                {commentItems.length > 0 ? <Dropdown.Button className='edit-menu' trigger={['click']} overlay={<EditMenu comment={commentItems[0]} />} /> : <></>}
            </div>
        </Row>
    }

    const CommentContent = () => {
        return <Space className='rq-comment-body' direction='vertical' size='small'>
            {
                commentItems.length > 0
                    ? <Scrollbars
                        autoHide
                        autoHeight
                        autoHeightMax={400}
                    >
                        <div className='rq-comment-items'>
                            {
                                commentItems.map((co) => {
                                    return <div key={co.id} className={`rq-comment-item ${editId === co.id ? 'active' : ''}`}>
                                        <Row style={{ marginBottom: 5 }} align='middle' justify='space-between'>
                                            <div className='comment-author'><UserOutlined /><span className='author'>{co.createdUserFullName}</span></div>
                                            <div className='comment-right'><span className='created'>{moment(co.commentDate).format(DATETIME_FORMAT)}</span></div>
                                        </Row>
                                        {editId === co.id ? <EditCommentForm co={co} /> : <ViewCommentForm co={co} />}
                                        {
                                            co.replies.map(re => {
                                                return <div key={re.id} className={`rq-reply-item ${re.canEdit ? 'can-edit' : ''} ${re.canDelete ? 'can-delete' : ''} ${replyId === re.id ? 'active' : ''}`}>
                                                    <Row style={{ marginBottom: 3 }} align='middle' justify='space-between'>
                                                        <div className='reply-author'><UserOutlined /><span className='author'>{re.createdUserFullName}</span></div>
                                                        <span className='reply-right'>
                                                            <span className='created'>{moment(re.replyDate).format(DATETIME_FORMAT)}</span>
                                                            {
                                                                re.canEdit
                                                                    ? <Button htmlType='button' className='rq-edit-reply' type='link' size='small' onClick={() => handleEditReply(re)}>
                                                                        <CustomSvgIcons name="EditCustomIcon" />
                                                                    </Button>
                                                                    : <></>
                                                            }
                                                            {
                                                                re.canDelete
                                                                    ? <Button htmlType='button' className='rq-remove-reply' type='link' size='small' onClick={() => handleRemoveReply(re.id)}>
                                                                        <CustomSvgIcons name="DeleteCustomIcon" />
                                                                    </Button>
                                                                    : <></>
                                                            }
                                                        </span>
                                                    </Row>
                                                    <div className='reply-body'>
                                                        <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: re?.replyBody }}></div>
                                                    </div>
                                                </div>
                                            })
                                        }
                                    </div>
                                })
                            }
                        </div>
                    </Scrollbars>
                    : <></>
            }

            {commentItems.length === 0 ? <div className='rq-add-comment-section'><NewCommentForm /></div> : <></>}
            {commentItems.length > 0 && replyId ? <div className='rq-add-reply-section'><NewReplyForm re={null} /></div> : <></>}
        </Space>
    }

    const NewCommentForm = () => {
        return <div style={{ paddingTop: 8, borderTop: '1px dashed #eee', padding: '5px 10px 8px', backgroundColor: '#f5f5f5', borderRadius: 6 }}>
            <div style={{ marginBottom: 5 }}>
                <UserOutlined /><span className='author'>{currentUser?.name}</span>
            </div>

            <Form name="basic" onFinish={handleAddComment} autoComplete="off" form={form}>
                <Form.Item name="category" rules={[
                    { required: true, message: 'Please input your category' },
                    { validator: async (rule, value) => { if (value && value.trim().length === 0) { throw new Error(intl.formatMessage({ id: 'IEM_1' })) } } }
                ]}>
                    <Select style={{ width: '200px' }} placeholder='Category' size='small'>
                        <Option key={COMMENT_CATEGORY.BUG} value={COMMENT_CATEGORY.BUG}>{COMMENT_CATEGORY.BUG}</Option>
                        <Option key={COMMENT_CATEGORY.QA} value={COMMENT_CATEGORY.QA}>{COMMENT_CATEGORY.QA}</Option>
                        <Option key={COMMENT_CATEGORY.SUGGESTION} value={COMMENT_CATEGORY.SUGGESTION}>{COMMENT_CATEGORY.SUGGESTION}</Option>
                    </Select>
                </Form.Item>
                <Form.Item name="message" rules={[{ required: true, message: 'Please input your comment' }]}>
                    <Ckeditor ref={commentRef} data={commentMessage} onChange={commentChange} comment placeholder="Add comment..." />
                    <TextArea style={{ display: 'none' }} rows={3} placeholder='Add comment...' size='small' />
                </Form.Item>
                <Space direction='horizontal' size='small'>
                    <Button type='primary' htmlType='submit' size='small'>Add</Button>
                    <Button type='ghost' onClick={() => { handleVisible(false); }} size='small'>Cancel</Button>
                </Space>
            </Form>
        </div>
    }

    const EditCommentForm = ({ co }) => {
        return <Form name="basic" onFinish={(event) => handleSaveComment(co.id, event)} autoComplete="off" form={form} className='rq-add-comment'>
            <Form.Item name="rowVersion" hidden={true} />
            <Form.Item name="category" rules={[
                { required: true, message: 'Please input your category' },
                { validator: async (rule, value) => { if (value && value.trim().length === 0) { throw new Error(intl.formatMessage({ id: 'IEM_1' })) } } }
            ]}>
                <Select style={{ width: '200px', }} placeholder='Category' defaultValue={co.category} size='small'>
                    <Option key={COMMENT_CATEGORY.BUG} value={COMMENT_CATEGORY.BUG}>{COMMENT_CATEGORY.BUG}</Option>
                    <Option key={COMMENT_CATEGORY.QA} value={COMMENT_CATEGORY.QA}>{COMMENT_CATEGORY.QA}</Option>
                    <Option key={COMMENT_CATEGORY.SUGGESTION} value={COMMENT_CATEGORY.SUGGESTION}>{COMMENT_CATEGORY.SUGGESTION}</Option>
                </Select>
            </Form.Item>
            <Form.Item name="message" rules={[{ required: true, message: 'Please input your comment' }]}>
                <Ckeditor ref={commentRef} data={commentMessage} onChange={commentChange} comment placeholder="Add comment..." />
                <TextArea style={{ display: 'none' }} rows={3} placeholder='Add comment...' size='small' />
            </Form.Item>
            <Space direction='horizontal' size='small' align='end'>
                <Button type='primary' htmlType='submit' size='small'>Save</Button>
                <Button type='ghost' onClick={handleCancel} size='small'>Cancel</Button>
            </Space>
        </Form>
    }

    const ViewCommentForm = ({ co }) => {
        return <div className='comment-detail'>
            <div className='comment-info'>
                <span className='category'>{co.category}</span>
                <Badge
                    className="comment-status"
                    color={co.color}
                    text={
                        <Text style={{ color: co.color }}>
                            {co.status}
                        </Text>
                    }
                />
            </div>
            <div className='comment-body'>
                <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: co?.commentBody }}></div>
                <div ref={commentListRef} />
            </div>
        </div>
    }

    const NewReplyForm = ({ re }) => {
        return <div style={{ paddingTop: 8, borderTop: '1px dashed #eee', padding: '5px 10px 8px', backgroundColor: '#f5f5f5', borderRadius: 6 }}>
            <div style={{ marginBottom: 5 }}>
                <UserOutlined /><span className='author'>{currentUser?.name}</span>
            </div>

            <Form name="basic" onFinish={handleAddUpdateReply} autoComplete="off" form={form}>
                <Form.Item name="id" hidden={true} />
                <Form.Item name="rowVersion" hidden={true} />
                <Form.Item name="message" rules={[{ required: true, message: 'Please input your comment' }]}>
                    <Ckeditor ref={commentRef} data={commentMessage} onChange={commentChange} comment placeholder="Add reply..." />
                    <TextArea style={{ display: 'none' }} rows={3} placeholder='Add reply...' size='small' />
                </Form.Item>
                <Space direction='horizontal' size='small'>
                    <Button type='primary' htmlType='submit' size='small'>{replyId === -1 ? 'Add' : 'Save'}</Button>
                    <Button type='ghost' onClick={handleCancelReply} size='small'>Cancel</Button>
                </Space>
            </Form>
        </div>
    }

    const EditMenu = ({ comment }) => {
        if (!comment) {
            return <></>;
        }

        return <Menu className='rq-comment-menu'>
            {comment.canEdit ? <Menu.Item key={'Edit' + comment.id} onClick={() => handleEditComment(comment)}>Edit</Menu.Item> : <></>}
            {comment.canReply ? <Menu.Item key={'Reply' + comment.id} onClick={() => handleReplyComment(comment.id)}>Reply</Menu.Item> : <></>}
            {comment.canResolve ? <Menu.Item key={'Resolve' + comment.id} onClick={() => onCommentHandle.onResolve(comment)}>Resolve</Menu.Item> : <></>}
            {comment.canReopen ? <Menu.Item key={'Re-open' + comment.id} onClick={() => onCommentHandle.onReopen(comment)}>Re-open</Menu.Item> : <></>}
            {comment.canClose ? <Menu.Item key={'Close' + comment.id} onClick={() => onCommentHandle.onClose(comment)}>Close</Menu.Item> : <></>}
            {comment.canCancel ? <Menu.Item key={'Cancel' + comment.id} onClick={() => onCommentHandle.onCancel(comment)}>Cancel</Menu.Item> : <></>}
            {comment.canDelete ? <Menu.Item key={'Delete' + comment.id} onClick={() => onCommentHandle.onDelete(comment.id)}>Delete</Menu.Item> : <></>}
        </Menu>
    }

    //#endregion VIEW

    return <Modal title={<CommentTitle />}
        visible={visible}
        footer={null}
        className='rq-comment-wrapper'
        onCancel={() => {
            handleVisible(false)
            localStorage.removeItem('comment')
        }}
        zIndex={1010}
        modalRender={(modal) => (
            <Draggable
                handle='.handle-draggable'
                disabled={disableDraggable}
                bounds={bounds}
                onStart={(event, uiData) => onStart(event, uiData)}
            >
                <div ref={draggleRef}>{modal}</div>
            </Draggable>
        )}
        width={widthModal}
    >
        <CommentContent />
    </Modal>
}
export default SharedComment
