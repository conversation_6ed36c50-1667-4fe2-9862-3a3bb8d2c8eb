import { createReducer } from '@reduxjs/toolkit'
import {
  actionCommentRequestSuccess, addCommentRequestSuccess, closeComment, deleteCommentRequestSuccess, deleteReplyRequestSuccess, initComment, initCommentScreen, initCommentScreenFailure, initCommentScreenSuccess, openComment, replyCommentRequestSuccess, updateCommentRequestSuccess, updateReplyRequestSuccess
} from './action'
import { CommentState } from './type'

const initState: CommentState = {
  isLoading: false,
  comments: [],
  index: -1,
  artefact: '',
  field: '',
  fields: []
}

const reducer = createReducer(initState, (builder) => {
  return builder
    .addCase(initComment, (state, action?) => {
      state.projectId = action.payload.projectId;
      state.itemId = action.payload.itemId;
      state.fields = action.payload.fields;
    })

    .addCase(initCommentScreen, (state, action?) => {
      state.isLoading = true;
      state.artefact = action.payload.artefact;
    })
    .addCase(initCommentScreenSuccess, (state, action) => {
      state.isLoading = false;
      state.comments = action.payload
    })
    .addCase(initCommentScreenFailure, (state, action) => {
      state.isLoading = false;
    })

    .addCase(openComment, (state, action) => {
      if (!action.payload) return;
      state.index = action.payload.index;
      state.title = action.payload.title;
      state.field = action.payload.field;
      state.isVisible = true;
    })
    .addCase(closeComment, (state, action) => {
      state.index = -1;
      state.title = '';
      state.field = '';
      state.isVisible = false;
    })

    .addCase(addCommentRequestSuccess, (state, action) => {
      if (!action.payload) return;
      action.payload.orderField = state.fields.findIndex(o => o.field === action.payload.field);
      state.comments.push(action.payload);
      state.comments.sort((a, b) => (a.orderField as number) - (b.orderField as number));
      state.index = state.comments.findIndex(o => o.field === action.payload.field);
    })

    .addCase(updateCommentRequestSuccess, (state, action) => {
      if (!action.payload) return;
      const comment = state.comments.find(o => o.id === action.payload.id);
      if (comment) {
        delete action.payload['replies'];
        Object.assign(comment, action.payload);
      }
    })

    .addCase(deleteCommentRequestSuccess, (state, action) => {
      const commentIndex = state.comments.findIndex(o => o.id === action.payload);
      if (commentIndex !== -1) {
        state.comments.splice(commentIndex, 1);
      }
    })

    .addCase(replyCommentRequestSuccess, (state, action) => {
      if (!action.payload) return;
      const comment = state.comments.find(o => o.id === action.payload.commentId);
      if (comment) {
        comment.replies?.push(action.payload);
      }
    })

    .addCase(actionCommentRequestSuccess, (state, action) => {
      if (!action.payload) return;
      const comment = state.comments.find(o => o.id === action.payload.id);
      if (comment) {
        delete action.payload['replies'];
        Object.assign(comment, action.payload)
      }
    })

    .addCase(updateReplyRequestSuccess, (state, action) => {
      if (!action.payload) return;
      const comment = state.comments.find(o => o.id === action.payload.commentId);
      if (comment) {
        const reply = comment.replies?.find(o => o.id === action.payload.id);
        if (reply) {
          Object.assign(reply, action.payload);
        }
      }
    })

    .addCase(deleteReplyRequestSuccess, (state, action) => {
      if (!action.payload) return;
      const comment = state.comments.find(o => o.id === action.payload.commentId);
      if (comment) {
        const replyIndex = comment.replies?.findIndex(o => o.id === action.payload.id) as number;
        if (replyIndex !== -1) {
          comment.replies?.splice(replyIndex, 1);
        }
      }
    })
})

export default reducer
export { initState as CommentState }

