import AppState from '@/store/types'
import { CommentOutlined } from '@ant-design/icons'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { COMMENT_STATUS, SCREEN_MODE } from '../../../constants'
import { CommentState } from '../../../modules/_shared/comment/type'
import { openComment } from './action'

interface TriggerCommentProps {
    field: string,
    screenMode?: SCREEN_MODE,
    children: any,
}
const TriggerComment = ({ field, screenMode = SCREEN_MODE.VIEW, children }: TriggerCommentProps) => {
    const dispatch = useDispatch()

    // STATE
    const [commentItem, setCommentItem] = useState<any>([]);
    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    //#region LIFE CYCLE

    useEffect(() => {
        const commentByCode = commentState.comments?.find((e) => e.field === field);
        setCommentItem(commentByCode);
    }, [commentState.comments])

    //#endregion LIFE CYCLE

    //#region METHODS

    const handleClick = () => {
        const fieldObj = commentState.fields.find(o => o.field === field);
        if (fieldObj) {
            const indexComment = commentState.comments.findIndex(o => o.field === field);
            dispatch(openComment({ index: indexComment, title: fieldObj.title, field: field }));
        }
    }

    //#endregion METHOD

    return <div className={`rq-comment-field ${field === commentState.field ? 'active' : ''}`}>
        <span className='rq-comment-field-label'>{children}</span>
        {
            (screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.EDIT)
                ? <span className={`rq-comment-trigger ${commentItem ? 'd-inline' : ''}`} onClick={handleClick}>
                    <CommentOutlined style={{ color: commentItem?.status === COMMENT_STATUS.OPEN ? '#FF9900' : (commentItem?.status === COMMENT_STATUS.RESOLVED ? '#9FC5F8' : (commentItem?.status ? '#6AA84F' : '')) }} />
                </span>
                : <></>
        }

    </div>
}
export default TriggerComment
