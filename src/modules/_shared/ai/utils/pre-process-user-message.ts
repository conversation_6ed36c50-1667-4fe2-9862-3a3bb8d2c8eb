import { resolveIdToContent } from '../components/tiptap/suggestion'
import { convertToPlainText } from '@/helper/component/tiptap'

export async function preProcessUserMessage(message: string): Promise<string> {
  try {
    const parser = new DOMParser()
    // Check if message can be parsed as HTML
    const _document = parser.parseFromString(message, 'text/html')
    const referenceResolved: Record<string, string> = {}

    // Find all <a> tags with reference="true"
    const anchors = _document.querySelectorAll('a[reference="true"]')

    // Add extra content to each anchor
    await Promise.allSettled(
      Array.from(anchors).map(async (anchor) => {
        // Check if reference content is already added
        if (anchor.getAttribute('reference-content')) return
        const id = anchor.getAttribute('data-id')
        const code = anchor.getAttribute('data-label')
        if (!id || !code) return
        const content = await resolveIdToContent(id)
        if (content) {
          referenceResolved[code] = content
        }
      })
    )
    let content = convertToPlainText(_document.body.innerHTML)

    const referenceContent = Object.entries(referenceResolved)
      .map(([code, content]) => `[${code}]: ${content}`)
      .join('\n')
    if (referenceContent) {
      content +=
        '\n<references>' + referenceContent + '</references>'
    }
    return content
  } catch (e) {
    return message
  }
}
