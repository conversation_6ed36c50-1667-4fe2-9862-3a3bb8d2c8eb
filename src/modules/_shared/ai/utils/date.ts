import moment from 'moment'

export const formatDate = (date: string | Date) => {
  const momentDate = moment(date)
  const now = moment()

  if (momentDate.isSame(now, 'day')) {
    return momentDate.format('HH:mm')
  } else if (momentDate.isSame(now, 'week')) {
    return momentDate.format('ddd HH:mm')
  } else if (momentDate.isSame(now, 'year')) {
    return momentDate.format('MMM DD')
  } else {
    return momentDate.format('MMM DD, YYYY')
  }
}