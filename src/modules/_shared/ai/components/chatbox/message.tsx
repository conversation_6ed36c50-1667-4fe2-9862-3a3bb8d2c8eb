import React from 'react'
import { Message, ReferenceType } from '../../types'
import {
  Avatar,
  Button,
  Dropdown,
  MenuProps,
  Space,
  Tooltip,
  Typography,
} from 'antd'
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  CopyOutlined,
  DownloadOutlined,
  LoadingOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON><PERSON>Outlined,
} from '@ant-design/icons'
import intl from '../../../../../config/locale.config'
import copy from 'copy-to-clipboard'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import Markdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { MessageStep } from './message-step'
import './message.less'
import aiService from '@/services/ai.service'
import { useDispatch } from 'react-redux'
import { addReference } from '@/modules/_shared/ai'

interface MessageContentProps {
  message: Message
  openCanvas: () => void
}

const userMessageFormat = (message: string) => {
  // Remove <references> tag and its content
  message = message.replace(/<references>[\s\S]*<\/references>/, '')
  return message
}

const UserMessage: React.FC<Message> = (message) => {
  const dispatch = useDispatch()

  return (
    <div className="user-message-wrapper">
      <div
        className="d-flex mt-2 flex-wrap justify-content-end p-2"
        style={{ maxWidth: '70%' }}
      >
        {message.references
          ?.filter((r) => r.type === ReferenceType.File)
          .map((reference, index) => {
            const items: MenuProps['items'] = [
              {
                key: 'download',
                label: (
                  <div
                    onClick={async () =>
                      aiService
                        .getFileViewUrl(reference.id)
                        .then((url) => window.open(url, '_blank'))
                    }
                  >
                    Download
                  </div>
                ),
                icon: <DownloadOutlined />,
              },
              {
                key: 'suggestion',
                label: (
                  <div
                    onClick={() => {
                      dispatch(
                        addReference({
                          type: ReferenceType.File,
                          id: reference.id,
                          name: reference.name,
                        })
                      )
                    }}
                  >
                    Mention
                  </div>
                ),
                icon: <CustomSvgIcons name="MentionIcon" />,
              },
            ]
            return (
              <div
                key={`${message.id}-${index}-${reference.id}`}
                className="p-2 mr-2 mb-2 d-flex align-items-center mw-100 position-relative file-card"
                style={{ backgroundColor: 'rgba(196, 196, 196, 0.4)' }}
              >
                <CustomSvgIcons name="MentionIcon" />
                <div className="text-truncate mw-100 mx-3">
                  {reference.name}
                </div>
                <Dropdown menu={{ items }} placement="bottom">
                  <Button
                    type="text"
                    icon={<MoreOutlined />}
                    onClick={() => {}}
                  />
                </Dropdown>
              </div>
            )
          })}
      </div>
      <div className="user-message">
        <div className="markdown-content">
          <Markdown remarkPlugins={[remarkGfm]} children={userMessageFormat(message.content)} />
        </div>
      </div>
    </div>
  )
}

const BotMessage: React.FC<
  Message & Pick<MessageContentProps, 'openCanvas'>
> = (message) => {
  return (
    <div className="bot-message" key={message.id}>
      {message.steps?.map(MessageStep)}
      <div>
        <div className="markdown-content">
          <Markdown
            remarkPlugins={[remarkGfm]}
            components={{
              table: ({ children, ...props }) => (
                <div className="table-wrapper">
                  <table {...props}>{children}</table>
                </div>
              ),
            }}
            children={message.content}
          />
        </div>
        {message.isLoading && <LoadingOutlined />}
      </div>
      {Boolean(message.content) && (
        <div className="d-flex justify-content-between align-items-center w-100">
          <div className="message-tools">
            <Tooltip title="Copy" placement="bottom">
              <Button
                type="text"
                icon={<CopyOutlined style={{ color: 'black' }} />}
                onClick={() => copy(message.content)}
              />
            </Tooltip>
            <Tooltip title="Edit in Canvas" placement="bottom">
              <Button
                type="text"
                icon={<CustomSvgIcons name="CanvasButtonIcon" fontSize={12} />}
                onClick={message.openCanvas}
                disabled={message.isLoading}
              />
            </Tooltip>
            <Tooltip title="Copy message trace" placement="bottom">
              <Button
                type="text"
                icon={<ThunderboltOutlined style={{ color: 'black' }} />}
                onClick={() => copy(`'${message.userId}' in input.value`)}
              />
            </Tooltip>
          </div>
          {Boolean(message.promptTokens && message.completionTokens) && (
            <Space>
              <Tooltip title="Prompt Tokens">
                <ArrowUpOutlined /> {message.promptTokens}
              </Tooltip>
              <Tooltip title="Completion Tokens">
                <ArrowDownOutlined /> {message.completionTokens}
              </Tooltip>
            </Space>
          )}
        </div>
      )}
    </div>
  )
}

export const MessageContent: React.FC<MessageContentProps> = ({
  message,
  openCanvas,
}) => {
  return (
    <div key={message.id} className="message-container">
      {message.isBot && (
        <Space style={{ marginBottom: '.5rem' }}>
          <Avatar
            size="small"
            icon={<RobotOutlined />}
            style={{ backgroundColor: '#2979FF' }}
          />
          <Typography.Text strong className="agent-name">
            {intl.formatMessage({ id: 'ai.agent.name' })}
          </Typography.Text>
        </Space>
      )}
      <div>
        {!message.isBot && <UserMessage {...message} />}
        {message.isBot && <BotMessage {...message} openCanvas={openCanvas} />}
      </div>
    </div>
  )
}
