import React from 'react'
import { Select, Tooltip } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { setInputState } from '../../actions'
import { getModeById, switchMode } from '../../config/modes'
import { AIAssistantState } from '../../types'
import AppState from '@/store/types'

const { Option } = Select

interface ModeSelectorProps {
  disabled?: boolean
}

/**
 * ModeSelector component provides a dropdown to choose between coordinate and route modes.
 *
 * Features:
 * - Shows only coordinate and route as main options
 * - Hover tooltips show descriptions for each mode
 * - Maintains selected state after form submission
 * - Controls slash menu activation/deactivation
 */
export const ModeSelector: React.FC<ModeSelectorProps> = ({ disabled = false }) => {
  const dispatch = useDispatch()
  const aiState = useSelector<AppState>(
    (state) => state?.aiAssistant
  ) as AIAssistantState

  const currentMode = aiState.input.mode || 'coordinate'

  // Determine the base mode (coordinate or route) for display
  const baseMode = currentMode.startsWith('route') ? 'route' : 'coordinate'

  /**
   * Handles mode change from dropdown selection
   * When switching to route, keeps current route sub-mode if applicable
   * When switching to coordinate, resets to coordinate mode
   */
  const handleModeChange = (selectedValue: string) => {
    let targetModeId = selectedValue

    // If switching to route and user was already in a specific route mode, keep it
    if (selectedValue === 'route' && currentMode.startsWith('route-') && currentMode !== 'route') {
      targetModeId = currentMode // Keep the specific route agent mode
    }

    const selectedMode = getModeById(targetModeId)
    if (!selectedMode) return

    const currentContent = aiState.input.content || ''

    // Use shared utility to handle mode switching and prefix management
    const { mode: newModeId, content: newContent } = switchMode(selectedMode, currentContent)

    dispatch(setInputState({
      mode: newModeId,
      content: newContent
    }))
  }

  // Define the main mode options
  const mainModes = [
    {
      value: 'coordinate',
      label: 'Coordinate',
      description: 'Default AI assistant mode - Master agent will delegate task to other worker agents and then collect the output'
    },
    {
      value: 'route',
      label: 'Route',
      description: 'Route to one of worker agents and receive its result directly'
    }
  ]

  return (
    <Select
      value={baseMode}
      onChange={handleModeChange}
      disabled={disabled}
      size="small"
      style={{ minWidth: 120 }}
      placeholder="Select mode"
    >
      {mainModes.map((mode) => (
        <Option key={mode.value} value={mode.value}>
          <Tooltip title={mode.description} placement="left" mouseEnterDelay={0.3}>
            <div style={{ fontWeight: 500 }}>
              {mode.label}
            </div>
          </Tooltip>
        </Option>
      ))}
    </Select>
  )
}
