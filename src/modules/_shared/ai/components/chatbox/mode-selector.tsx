import React from 'react'
import { Select, Tooltip } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { setInputState } from '../../actions'
import { CHAT_MODES, getModeById, switchMode } from '../../config/modes'
import { AIAssistantState } from '../../types'
import AppState from '@/store/types'

const { Option } = Select

interface ModeSelectorProps {
  disabled?: boolean
}

export const ModeSelector: React.FC<ModeSelectorProps> = ({ disabled = false }) => {
  const dispatch = useDispatch()
  const aiState = useSelector<AppState>(
    (state) => state?.aiAssistant
  ) as AIAssistantState

  const currentMode = aiState.input.mode || 'coordinate'

  /**
   * Handles mode change from dropdown selection
   * Uses shared utility to manage prefix switching
   */
  const handleModeChange = (selectedId: string) => {
    const selectedMode = getModeById(selectedId)
    if (!selectedMode) return

    const currentContent = aiState.input.content || ''

    // Use shared utility to handle mode switching and prefix management
    const { mode: newModeId, content: newContent } = switchMode(selectedMode, currentContent)

    dispatch(setInputState({
      mode: newModeId,
      content: newContent
    }))
  }

  return (
    <Tooltip title="Select AI response mode" placement="top">
      <Select
        value={currentMode}
        onChange={handleModeChange}
        disabled={disabled}
        size="small"
        style={{ minWidth: 120 }}
        placeholder="Select mode"
      >
        {CHAT_MODES.map((mode) => (
          <Option key={mode.id} value={mode.id}>
            <div>
              <div style={{ fontWeight: 500 }}>{mode.label}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {mode.description}
              </div>
              {mode.textPrefix && (
                <div style={{ fontSize: '11px', color: '#1890ff', fontStyle: 'italic' }}>
                  Prefix: "{mode.textPrefix.trim()}"
                </div>
              )}
            </div>
          </Option>
        ))}
      </Select>
    </Tooltip>
  )
}
