import { Button, Space } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { allowedFileTypes } from '@/modules/_shared/ai/config'
import React, { useCallback, useEffect } from 'react'
import aiService from '@/services/ai.service'
import { useDispatch, useSelector } from 'react-redux'
import {
  AIAssistantState,
  FileStatus,
  ReferenceType,
  updateFileState,
} from '@/modules/_shared/ai'
import AppState from '@/store/types'

export const FileUploader: React.FC = () => {
  const inputState = useSelector<AppState>(
    (state) => state?.aiAssistant.input
  ) as AIAssistantState['input']
  const inputRef = React.useRef<HTMLInputElement>(null)
  const dispatch = useDispatch()

  const onFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      Promise.allSettled(
        new Array(files.length).fill(0).map(async (_, index) => {
          let fileId = ''
          try {
            const _file = await aiService.uploadFile(files[index], {
              onFileCreated(_file) {
                fileId = _file.id
                dispatch(updateFileState(_file))
              },
              onFileUploadProgress: (_file, progress) => {
                dispatch(
                  updateFileState({
                    ..._file,
                    uploadProgress: progress,
                    type: ReferenceType.File,
                  })
                )
              },
            })
            dispatch(updateFileState({ ..._file, status: FileStatus.uploaded }))
          } catch (e: any) {
            if (fileId) {
              dispatch(
                updateFileState({
                  id: fileId,
                  status: FileStatus.error,
                  error: e.message,
                })
              )
            }
          }
        })
      ).catch()
    }
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  const monitorFileStatus = useCallback(() => {
    Promise.allSettled(
      inputState.references
        .filter(
          ({ status, type }: any) =>
            type === ReferenceType.File &&
            status &&
            status !== FileStatus.processed
        )
        .map(async ({ id }) => {
          const file = await aiService.getFileMetadata(id)
          dispatch(updateFileState(file))
        })
    ).catch()
  }, [dispatch, inputState.references])

  useEffect(() => {
    const intervalId = setInterval(() => {
      monitorFileStatus()
    }, 5000)
    return () => clearInterval(intervalId)
  }, [monitorFileStatus])

  return (
    <Space>
      <Button
        type="text"
        icon={<PlusOutlined />}
        onClick={() => inputRef.current?.click()}
      />
      <input
        type="file"
        hidden
        ref={inputRef}
        accept={allowedFileTypes.join(',')}
        multiple
        onChange={onFileChange}
      />
    </Space>
  )
}
