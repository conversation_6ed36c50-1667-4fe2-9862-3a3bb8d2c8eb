import { Extension } from '@tiptap/core'
import Suggestion from '@tiptap/suggestion'
import { slashCommandsSuggestion } from './slash-commands'

/**
 * Options interface for the SlashCommand extension
 */
export interface SlashCommandOptions {
  /** Callback function called when a mode is selected from the slash menu */
  onModeSelect: (mode: any) => void
}

/**
 * TipTap extension that provides slash command functionality for AI chat modes.
 *
 * When users type "/" in the editor, this extension:
 * 1. Shows a dropdown menu with available AI modes
 * 2. Allows navigation with arrow keys
 * 3. Handles mode selection and applies the chosen mode
 *
 * @example
 * ```typescript
 * SlashCommand.configure({
 *   onModeSelect: (mode) => {
 *     // Handle mode selection
 *     setMode(mode.id)
 *     insertPrefix(mode.textPrefix)
 *   }
 * })
 * ```
 */
export const SlashCommand = Extension.create<SlashCommandOptions>({
  name: 'slashCommand',

  /**
   * Default options for the extension
   */
  addOptions() {
    return {
      onModeSelect: () => {}, // Default no-op function
    }
  },

  /**
   * Adds the ProseMirror plugin that handles slash command suggestions
   */
  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        char: '/',
        startOfLine: false, 
        allowSpaces: false, 
        ...slashCommandsSuggestion, 
        command: ({ editor, range, props }) => {
          // Get the current content before making changes
          const currentContent = editor.getText()

          // The range includes the "/" character and any query text after it
          // We need to find the actual "/" position to remove it completely
          const slashStart = range.from
          const slashEnd = range.to

          // Find the "/" character position within the range
          const rangeText = currentContent.substring(slashStart, slashEnd)
          const slashIndex = rangeText.indexOf('/')
          const actualSlashStart = slashIndex >= 0 ? slashStart + slashIndex : slashStart

          // Create new content by removing the "/" and query, then adding the agent prefix
          const beforeSlash = currentContent.substring(0, actualSlashStart)
          const afterSlash = currentContent.substring(slashEnd)
          const newContent = beforeSlash + (props.textPrefix ? props.textPrefix + ' ' : '') + afterSlash

          // Update the editor content
          editor.commands.setContent(newContent)

          // Call the mode select handler with the new content
          this.options.onModeSelect({ ...props, newContent })
        },
      }),
    ]
  },
})
