import { Extension } from '@tiptap/core'
import Suggestion from '@tiptap/suggestion'
import { slashCommandsSuggestion } from './slash-commands'

/**
 * Options interface for the SlashCommand extension
 */
export interface SlashCommandOptions {
  /** Callback function called when a mode is selected from the slash menu */
  onModeSelect: (mode: any) => void
}

/**
 * TipTap extension that provides slash command functionality for AI chat modes.
 *
 * When users type "/" in the editor, this extension:
 * 1. Shows a dropdown menu with available AI modes
 * 2. Allows navigation with arrow keys
 * 3. Handles mode selection and applies the chosen mode
 *
 * @example
 * ```typescript
 * SlashCommand.configure({
 *   onModeSelect: (mode) => {
 *     // Handle mode selection
 *     setMode(mode.id)
 *     insertPrefix(mode.textPrefix)
 *   }
 * })
 * ```
 */
export const SlashCommand = Extension.create<SlashCommandOptions>({
  name: 'slashCommand',

  /**
   * Default options for the extension
   */
  addOptions() {
    return {
      onModeSelect: () => {}, // Default no-op function
    }
  },

  /**
   * Adds the ProseMirror plugin that handles slash command suggestions
   */
  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        char: '/',
        startOfLine: false, 
        allowSpaces: false, 
        ...slashCommandsSuggestion, 
        command: ({ editor, range, props }) => {
          // Delete the entire slash command range (includes "/" and any query text)
          editor.chain().focus().deleteRange(range).run()

          // Insert the clean agent prefix (without any "/" character)
          if (props.textPrefix) {
            editor.chain().focus().insertContent(props.textPrefix).run()
          }

          // Call the mode select handler to update Redux state
          this.options.onModeSelect(props)
        },
      }),
    ]
  },
})
