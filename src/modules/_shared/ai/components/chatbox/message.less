// Message Component Styles
.message-container {
  margin-bottom: 1rem;
  
  .agent-name {
    color: #2979FF;
  }
  
  // Override any global message-content styles to prevent horizontal scrolling
  .message-content {
    overflow-x: hidden !important;
    word-wrap: break-word;
    word-break: break-word;
    
    // Only tables should have horizontal scrolling
    .table-wrapper {
      overflow-x: auto !important;
    }
  }
}

// User Message Styles
.user-message-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  align-items: flex-end;
  
  .user-message {
    background-color: #f0f2f5;
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 70%;
    word-wrap: break-word;
    overflow-x: hidden; // Prevent horizontal scroll on user messages
    
    .markdown-content {
      overflow-x: hidden; // Ensure no horizontal scroll on markdown content
      p {
        margin-bottom: 0;
      }
      
      // Only tables should have horizontal scrolling
      .table-wrapper {
        overflow-x: auto;
        margin: 8px 0;
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        
        table {
          min-width: 600px;
          width: 100%;
          border-collapse: collapse;
          background-color: #fff;
          
          th, td {
            border: 1px solid #e8e8e8;
            padding: 8px 12px;
            text-align: left;
            min-width: 120px;
            word-wrap: break-word;
          }
          
          th {
            background-color: #fafafa;
            font-weight: 600;
            color: #262626;
          }
          
          tr:hover {
            background-color: #f5f5f5;
          }
          
          // Responsive design for tables
          @media (max-width: 768px) {
            min-width: 500px;
            
            th, td {
              min-width: 100px;
              padding: 6px 8px;
              font-size: 14px;
            }
          }
          
          @media (max-width: 480px) {
            min-width: 400px;
            
            th, td {
              min-width: 80px;
              padding: 4px 6px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// Bot Message Styles  
.bot-message {
  background-color: #fff;
  border-radius: 12px;
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
  margin-bottom: 1rem;
  overflow-x: hidden; // Prevent horizontal scroll on bot messages
  
  .markdown-content {
    overflow-x: hidden; // Ensure no horizontal scroll on markdown content
    word-wrap: break-word;
    line-height: 1.6;
    
    // Only tables should have horizontal scrolling
    .table-wrapper {
      overflow-x: auto;
      margin: 12px 0;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      
      table {
        min-width: 600px;
        width: 100%;
        border-collapse: collapse;
        background-color: #fff;
        
        th, td {
          border: 1px solid #e8e8e8;
          padding: 10px 14px;
          text-align: left;
          min-width: 120px;
          word-wrap: break-word;
          vertical-align: top;
        }
        
        th {
          background-color: #fafafa;
          font-weight: 600;
          color: #262626;
          position: sticky;
          top: 0;
          z-index: 1;
        }
        
        tr:nth-child(even) {
          background-color: #fafafa;
        }
        
        tr:hover {
          background-color: #e6f7ff;
        }
        
        // Responsive design for tables
        @media (max-width: 768px) {
          min-width: 500px;
          
          th, td {
            min-width: 100px;
            padding: 8px 10px;
            font-size: 14px;
          }
        }
        
        @media (max-width: 480px) {
          min-width: 400px;
          
          th, td {
            min-width: 80px;
            padding: 6px 8px;
            font-size: 12px;
          }
        }
      }
    }
    
    // Other markdown elements should not cause horizontal scrolling
    p, ul, ol, blockquote, pre, code {
      overflow-wrap: break-word;
      word-wrap: break-word;
      word-break: break-word;
    }
    
    pre {
      overflow-x: auto;
      background-color: #f6f8fa;
      padding: 12px;
      border-radius: 6px;
      border: 1px solid #e1e4e8;
    }
    
    code {
      background-color: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 85%;
    }
  }
  
  .message-tools {
    margin-top: 8px;
    display: flex;
    gap: 4px;
    
    .ant-btn {
      border: none;
      box-shadow: none;
      color: #8c8c8c;
      
      &:hover {
        color: #1890ff;
        background-color: #f0f2f5;
      }
    }
  }
  
  .token-display {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 8px;
    
    .anticon {
      margin-right: 4px;
    }
  }
}
