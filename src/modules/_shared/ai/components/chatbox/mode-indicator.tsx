import React from 'react'
import { useSelector } from 'react-redux'
import { Tag } from 'antd'
import { getModeById } from '../../config/modes'
import { AIAssistantState } from '../../types'
import AppState from '@/store/types'

/**
 * ModeIndicator component displays the currently selected AI chat mode
 * as a small blue tag in the top-left corner of the chat input area.
 *
 * Features:
 * - Shows current mode label (e.g., "Mode: Coordinate")
 * - Positioned absolutely in top-left corner
 * - Only renders if a valid mode is selected
 * - Updates automatically when mode changes
 *
 * @returns JSX element with mode indicator or null if no valid mode
 */
export const ModeIndicator: React.FC = () => {
  // Get current AI assistant state from Redux store
  const aiState = useSelector<AppState>(
    (state) => state?.aiAssistant
  ) as AIAssistantState

  // Get current mode ID, defaulting to 'coordinate'
  const currentMode = aiState.input.mode || 'coordinate'

  // Look up mode configuration by ID
  const modeConfig = getModeById(currentMode)

  // Don't render anything if mode configuration is not found
  if (!modeConfig) {
    return null
  }

  return (
    <div style={{
      position: 'absolute',
      top: '8px',
      left: '8px',
      zIndex: 100 // Ensure it appears above other elements
    }}>
      <Tag
        color="blue"
        style={{
          fontSize: '11px',
          padding: '2px 6px',
          margin: 0,
          borderRadius: '4px'
        }}
      >
        Mode: {modeConfig.label}
      </Tag>
    </div>
  )
}
