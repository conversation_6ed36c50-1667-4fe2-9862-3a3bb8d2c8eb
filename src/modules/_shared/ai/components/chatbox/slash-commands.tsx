import { useState, useEffect, forwardRef, useImperative<PERSON><PERSON><PERSON>, useRef } from 'react'
import { ReactRenderer } from '@tiptap/react'
import { SuggestionOptions } from '@tiptap/suggestion'
import { CHAT_MODES, ChatMode } from '../../config/modes'

/**
 * Props for the SlashCommands component
 */
interface SlashCommandsProps {
  /** Array of available chat modes to display */
  items: ChatMode[]
  /** Callback function when a mode is selected */
  command: (item: ChatMode) => void
}

/**
 * Ref interface for keyboard event handling
 */
export interface SlashCommandsRef {
  /** Handle keyboard events (arrow keys, enter, escape) */
  onKeyDown: (props: { event: KeyboardEvent }) => boolean
}

/**
 * SlashCommands component renders a dropdown menu for AI chat mode selection.
 *
 * Features:
 * - Keyboard navigation with arrow keys
 * - Auto-scrolling to keep selected item visible
 * - Mouse hover support
 * - Visual feedback for selected items
 *
 * @param items - Array of available chat modes
 * @param command - Callback when a mode is selected
 * @param ref - Forwarded ref for keyboard event handling
 */
const SlashCommands = forwardRef<SlashCommandsRef, SlashCommandsProps>(
  ({ items, command }, ref) => {
    // State for tracking which item is currently selected
    const [selectedIndex, setSelectedIndex] = useState(0)

    // Refs for DOM manipulation and scrolling
    const menuRef = useRef<HTMLDivElement>(null)
    const selectedItemRef = useRef<HTMLDivElement>(null)

    /**
     * Selects an item by index and triggers the command callback
     */
    const selectItem = (index: number) => {
      const item = items[index]
      if (item) {
        command(item)
      }
    }

    /**
     * Handles up arrow key - moves selection up with circular navigation
     */
    const upHandler = () => {
      setSelectedIndex((prevIndex) => {
        return (prevIndex + items.length - 1) % items.length
      })
    }

    /**
     * Handles down arrow key - moves selection down with circular navigation
     */
    const downHandler = () => {
      setSelectedIndex((prevIndex) => {
        return (prevIndex + 1) % items.length
      })
    }

    /**
     * Handles enter key - selects the currently highlighted item
     */
    const enterHandler = () => {
      selectItem(selectedIndex)
    }

    // Reset selection to first item when items change
    useEffect(() => setSelectedIndex(0), [items])

    /**
     * Auto-scroll effect to keep the selected item visible in the menu
     * Triggers whenever the selectedIndex changes
     */
    useEffect(() => {
      if (selectedItemRef.current && menuRef.current) {
        const menuElement = menuRef.current
        const selectedElement = selectedItemRef.current

        const menuRect = menuElement.getBoundingClientRect()
        const selectedRect = selectedElement.getBoundingClientRect()

        // Scroll up if selected item is above visible area
        if (selectedRect.top < menuRect.top) {
          selectedElement.scrollIntoView({ block: 'start', behavior: 'smooth' })
        }
        // Scroll down if selected item is below visible area
        else if (selectedRect.bottom > menuRect.bottom) {
          selectedElement.scrollIntoView({ block: 'end', behavior: 'smooth' })
        }
      }
    }, [selectedIndex])

    /**
     * Expose keyboard event handler to parent component via ref
     * Returns true if the event was handled, false otherwise
     */
    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        switch (event.key) {
          case 'ArrowUp':
            upHandler()
            return true // Event handled
          case 'ArrowDown':
            downHandler()
            return true // Event handled
          case 'Enter':
            enterHandler()
            return true // Event handled
          default:
            return false // Event not handled
        }
      },
    }))

    return (
      <div
        ref={menuRef}
        className="slash-commands-menu"
        style={{
          background: 'white',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          padding: '4px 0',
          maxHeight: '200px',
          overflowY: 'auto',
          minWidth: '280px',
          zIndex: 1000,
          scrollBehavior: 'smooth'
        }}
      >
        {items.length ? (
          items.map((item, index) => (
            <div
              key={item.id}
              ref={index === selectedIndex ? selectedItemRef : null}
              onClick={() => selectItem(index)}
              onMouseEnter={() => setSelectedIndex(index)}
              style={{
                padding: '8px 12px',
                cursor: 'pointer',
                backgroundColor: index === selectedIndex ? '#e6f7ff' : 'transparent',
                borderLeft: index === selectedIndex ? '3px solid #1890ff' : '3px solid transparent',
                transition: 'all 0.2s ease'
              }}
            >
              <div style={{
                fontWeight: index === selectedIndex ? 600 : 500,
                fontSize: '14px',
                color: index === selectedIndex ? '#1890ff' : '#000'
              }}>
                /{item.label}
              </div>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                {item.description}
              </div>
              {item.textPrefix && (
                <div style={{ fontSize: '11px', color: '#1890ff', fontStyle: 'italic', marginTop: '2px' }}>
                  Prefix: "{item.textPrefix.trim()}"
                </div>
              )}
            </div>
          ))
        ) : (
          <div style={{ padding: '8px 12px', color: '#999' }}>
            No commands found
          </div>
        )}
      </div>
    )
  }
)

SlashCommands.displayName = 'SlashCommands'

/**
 * Configuration object for TipTap's Suggestion plugin
 * Handles the slash command menu behavior and rendering
 */
export const slashCommandsSuggestion: Omit<SuggestionOptions, 'editor'> = {
  /**
   * Filter and return items based on user's query
   * Only shows route-related modes (excluding the base 'route' mode)
   * @param query - The text typed after the slash character
   * @returns Filtered array of route agent modes matching the query
   */
  items: ({ query }) => {
    return CHAT_MODES
      .filter(mode =>
        // Only show route agent modes (not the base 'route' mode)
        mode.id.startsWith('route-') &&
        (
          // Search in both label and description for better discoverability
          mode.label.toLowerCase().includes(query.toLowerCase()) ||
          mode.description.toLowerCase().includes(query.toLowerCase())
        )
      )
      .slice(0, 10) // Limit to 10 items for performance
  },

  /**
   * Render function that manages the lifecycle of the slash commands menu
   * Creates, positions, and destroys the popup menu
   */
  render: () => {
    let component: ReactRenderer<SlashCommandsRef>
    let popup: HTMLElement

    return {
      /**
       * Called when the slash command is triggered
       * Creates and positions the menu popup
       */
      onStart: (props) => {
        // Create React component renderer
        component = new ReactRenderer(SlashCommands, {
          props,
          editor: props.editor,
        })

        // Exit early if we can't get cursor position
        if (!props.clientRect) {
          return
        }

        // Create popup container and add to DOM
        popup = document.createElement('div')
        popup.style.position = 'absolute'
        popup.style.zIndex = '1000'
        document.body.appendChild(popup)
        popup.appendChild(component.element)

        // Position popup above the cursor to avoid covering content
        const rect = props.clientRect()
        if (rect) {
          const menuHeight = 200 // Approximate max height of menu
          const offsetY = 10 // Small gap between cursor and menu
          popup.style.top = `${rect.top + window.scrollY - menuHeight - offsetY}px`
          popup.style.left = `${rect.left + window.scrollX}px`
        }
      },

      /**
       * Called when the query changes (user types more characters)
       * Updates component props and repositions if needed
       */
      onUpdate(props) {
        component.updateProps(props)

        if (!props.clientRect || !popup) {
          return
        }

        // Update popup position to follow cursor
        const rect = props.clientRect()
        if (rect) {
          const menuHeight = 200
          const offsetY = 10
          popup.style.top = `${rect.top + window.scrollY - menuHeight - offsetY}px`
          popup.style.left = `${rect.left + window.scrollX}px`
        }
      },

      /**
       * Handles keyboard events for the menu
       * @param props - Event properties including the keyboard event
       * @returns true if event was handled, false otherwise
       */
      onKeyDown(props) {
        // Handle escape key to close menu
        if (props.event.key === 'Escape') {
          if (popup && popup.parentNode) {
            popup.parentNode.removeChild(popup)
          }
          component.destroy()
          return true
        }

        // Delegate other key events to the component
        return (component.ref as SlashCommandsRef)?.onKeyDown(props) || false
      },

      /**
       * Called when the suggestion session ends
       * Cleans up the popup and component
       */
      onExit() {
        if (popup && popup.parentNode) {
          popup.parentNode.removeChild(popup)
        }
        component.destroy()
      },
    }
  },
}
