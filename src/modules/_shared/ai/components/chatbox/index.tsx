import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Button, Card, Space, Spin, Tooltip, Typography } from 'antd'
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  HistoryOutlined,
  PlusOutlined,
  RobotOutlined,
} from '@ant-design/icons'
import { AIAssistantState } from '../../types'
import {
  createConversationRequest,
  fetchLastConversationOrCreate,
  setCurrentEditingMessage,
  enterCanvasOnlyMode,
} from '../../actions'
import AppState from '@/store/types'
import { extractProjectCode } from '@/helper/share'
import intl from '@/config/locale.config'
import { MessageContent } from './message'
import { ConversationHistoryDialog } from '../dialogs/conversation-history-dialog'
import { motion } from 'framer-motion'
import { InputArea } from './input-area'

const { Text, Title } = Typography

interface AIAssistantProps {
  onClose: () => void
}

export const AIChatBox: React.FC<AIAssistantProps> = () => {
  const dispatch = useDispatch()
  const aiState = useSelector<AppState>(
    (state) => state?.aiAssistant,
  ) as AIAssistantState
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const conversationHistoryDialogRef = useRef<any>(null)
  const lastUserMessageRef = useRef<HTMLDivElement>(null)
  const typingIndicatorRef = useRef<HTMLDivElement>(null)
  const [typingIndicationMinHeight, setTypingIndicationMinHeight] = useState(0)
  const lastScrollTopRef = useRef(0)
  const isAutoScrollingRef = useRef(false)

  useEffect(() => {
    // Create default conversation if none exists and we're visible
    if (!aiState.currentConversation && !aiState.isLoading) {
      dispatch(fetchLastConversationOrCreate())
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [aiState.currentConversation])


  const handleNewConversation = () => {
    dispatch(
      createConversationRequest({
        projectId: extractProjectCode() ?? '',
      }),
    )
  }

  const scrollUserMessageToTop = () => {
    if (!aiState.currentConversation?.messages || aiState.currentConversation.messages.length === 0) {
      return
    }

    // Find the last user message
    const messages = aiState.currentConversation.messages
    let lastUserMessageIndex = -1

    for (let i = messages.length - 1; i >= 0; i--) {
      if (!messages[i].isBot) {
        lastUserMessageIndex = i
        break
      }
    }

    if (lastUserMessageIndex !== -1 && lastUserMessageRef.current) {
      // Scroll the user message to the top of the container
      const container = messagesContainerRef.current
      const messageElement = lastUserMessageRef.current

      if (container && messageElement) {
        const containerRect = container.getBoundingClientRect()
        const messageRect = messageElement.getBoundingClientRect()
        const scrollOffset = messageRect.top - containerRect.top + container.scrollTop

        // Mark that we're auto-scrolling
        isAutoScrollingRef.current = true

        container.scrollTo({
          top: scrollOffset,
          behavior: 'smooth',
        })

        // Reset auto-scrolling flag after scroll completes
        setTimeout(() => {
          isAutoScrollingRef.current = false
          if (container) {
            lastScrollTopRef.current = container.scrollTop
          }
        }, 500) // Smooth scroll typically takes ~300-500ms
      }
    }
  }

  // Track previous message count to detect when user sends a new message
  const prevMessageCountRef = useRef(0)

  useEffect(() => {
    const currentMessages = aiState.currentConversation?.messages || []
    const currentMessageCount = currentMessages.length

    // Only scroll if a new message was added and the last message is from user
    if (currentMessageCount > prevMessageCountRef.current) {
      const lastMessage = currentMessages[currentMessages.length - 1]
      if (lastMessage && !lastMessage.isBot) {
        // User just sent a message, scroll it to top
        setTimeout(() => scrollUserMessageToTop(), 100) // Small delay to ensure DOM is updated
      }
    }

    prevMessageCountRef.current = currentMessageCount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [aiState.currentConversation?.messages])

  // Handle manual scroll detection to reset min height
  useEffect(() => {
    const container = messagesContainerRef.current
    if (!container) return

    const handleScroll = () => {
      const currentScrollTop = container.scrollTop

      // Only process if we're not auto-scrolling
      if (!isAutoScrollingRef.current) {
        // Check if user scrolled up (current scroll position is less than last recorded)
        if (currentScrollTop < lastScrollTopRef.current) {
          // User scrolled up, reset min height to 0
          setTypingIndicationMinHeight(0)
        }

        // Update last scroll position
        lastScrollTopRef.current = currentScrollTop
      }
    }

    container.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const isEmptyConversation = useMemo(() => Boolean(aiState.currentConversation?.messages.length), [aiState.currentConversation?.messages])

  useEffect(() => {
    messagesContainerRef.current?.scrollTo({
      top: messagesContainerRef.current.scrollHeight,
      behavior: 'smooth',
    })
  }, [aiState.currentConversation?.id, isEmptyConversation])

  // Initialize scroll position when messages container is available
  useEffect(() => {
    const container = messagesContainerRef.current
    if (container) {
      lastScrollTopRef.current = container.scrollTop
    }
  }, [aiState.currentConversation?.messages])

  const messageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.2 } },
  }


  return (
    <div className="ai-assistant-container">
      <Card className="ai-assistant-card" bodyStyle={{ padding: 0 }}>
        {/* Current Task */}
        <div className="current-task">
          <Title level={5}>
            {aiState.currentConversation?.title ?? 'New Chat'}
          </Title>
          <div className="task-actions">
            <Tooltip title="New Conversation" placement="topRight">
              <Button
                type="text"
                icon={<PlusOutlined />}
                size="small"
                onClick={handleNewConversation}
              />
            </Tooltip>
            <Tooltip title="Conversation History" placement="topRight">
              <Button
                type="text"
                icon={<HistoryOutlined />}
                size="small"
                onClick={conversationHistoryDialogRef.current?.toggle}
              />
            </Tooltip>
          </div>
        </div>

        {/* Token Usage */}
        <div className="token-usage">
          <div className="d-flex justify-content-between">
            <Text strong>Token:</Text>
            <Space>
              <Tooltip
                title={intl.formatMessage({ id: 'ai.send-token.description' })}
              >
                <ArrowUpOutlined /> {aiState.currentConversation?.promptTokens}
              </Tooltip>
              <Tooltip
                title={intl.formatMessage({
                  id: 'ai.receive-token.description',
                })}
              >
                <ArrowDownOutlined />{' '}
                {aiState.currentConversation?.completionTokens}
              </Tooltip>
            </Space>
            <Text strong>{aiState.currentConversation?.totalCost} USD</Text>
          </div>
        </div>

        {/* Messages */}
        <div className="ai-messages" ref={messagesContainerRef}>
          {aiState.currentConversation?.messages.length === 0 &&
            !aiState.isTyping ? (
            <div className="empty-conversation">
              <div className="empty-conversation-content">
                <RobotOutlined className="empty-icon" />
                <Title level={4}>
                  {intl.formatMessage({ id: 'ai.welcome-message.title' })}
                </Title>
                <Text type="secondary">
                  {intl.formatMessage({ id: 'ai.welcome-message.description' })}
                </Text>
              </div>
            </div>
          ) : (
            <>
              {aiState.currentConversation?.messages.map((message, index) => {
                // Find if this is the last user message
                const messages = aiState.currentConversation?.messages || []
                let isLastUserMessage = false

                if (!message.isBot) {
                  // Check if this is the last user message
                  let lastUserMessageIndex = -1
                  for (let i = messages.length - 1; i >= 0; i--) {
                    if (!messages[i].isBot) {
                      lastUserMessageIndex = i
                      break
                    }
                  }
                  isLastUserMessage = index === lastUserMessageIndex
                }

                return (
                  <div
                    key={message.id}
                    ref={isLastUserMessage ? lastUserMessageRef : null}
                  >
                    <MessageContent
                      message={message}
                      openCanvas={() => {
                        // Set the current editing message
                        dispatch(setCurrentEditingMessage(message.id))
                        // Enter canvas-only mode with the message content
                        dispatch(enterCanvasOnlyMode(message.content || ''))
                      }}
                    />
                  </div>
                )
              })}
              <div style={{ minHeight: typingIndicationMinHeight }} ref={typingIndicatorRef}>
                {aiState.isTyping && (
                  <motion.div
                    key="typing-indicator"
                    variants={messageVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <Text type="secondary">AI is thinking...</Text>{' '}
                    <Spin size="small" />
                  </motion.div>
                )}
              </div>
            </>
          )}
        </div>
        <InputArea onSendMessage={() => {
          const container = messagesContainerRef.current
          const lastUserMessage = lastUserMessageRef.current

          if (container && lastUserMessage) {
            const containerHeight = container.clientHeight
            const lastUserMessageHeight = lastUserMessage.offsetHeight
            const calculatedMinHeight = containerHeight - lastUserMessageHeight - 30
            setTypingIndicationMinHeight(Math.max(0, calculatedMinHeight))
          } else {
            // Fallback to original value if refs are not available
            setTypingIndicationMinHeight(500)
          }
        }} />
        <ConversationHistoryDialog ref={conversationHistoryDialogRef} />
      </Card>
    </div>
  )
}
