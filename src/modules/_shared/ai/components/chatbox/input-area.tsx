import React, { use<PERSON><PERSON>back, useEffect } from 'react'
import { Button, Progress, Space, Tooltip } from 'antd'
import {
  CheckCircleFilled,
  CloseCircleFilled,
  FileOutlined,
  LoadingOutlined,
  SendOutlined,
} from '@ant-design/icons'
import { useDispatch, useSelector } from 'react-redux'
import AppState from '@/store/types'
import {
  AIAssistantState,
  clearSuggestions,
  FileStatus,
  IFileWithUploadState,
  Reference,
  ReferenceType,
  removeFile,
  removeReference,
  sendMessageRequest,
  setInputState,
} from '@/modules/_shared/ai'
import { EditorContent, mergeAttributes, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { Mention } from '@tiptap/extension-mention'
import { Placeholder } from '@tiptap/extension-placeholder'

import { FileUploader } from './file-uploader'
import { ModeSelector } from './mode-selector'
import { toast } from '@/helper/share'
import { getModeById } from '../../config/modes'
import { SlashCommand } from './slash-command-extension'
import aiService from '@/services/ai.service'
import CustomSvgIcons from '@/helper/component/custom-icons'
import { clearMentionsCache, resolveIdToLink, suggestion } from '../tiptap/suggestion'
import { Extension } from '@tiptap/core'

/**
 * Props for the InputArea component
 */
type InputAreaProps = {
  /** Callback function called when a message is sent */
  onSendMessage: () => void
}

/**
 * InputArea component provides the main chat input interface with rich text editing,
 * file uploads, mode selection, and slash commands.
 *
 * Features:
 * - Rich text editor with TipTap
 * - Slash commands for quick mode selection (type "/")
 * - File upload support
 * - Reference management (@mentions)
 * - Mode indicator showing current AI mode
 * - Automatic prefix insertion based on selected mode
 *
 * @param onSendMessage - Callback triggered when user sends a message
 */
export const InputArea: React.FC<InputAreaProps> = ({ onSendMessage }) => {
  const aiState = useSelector<AppState>(
    (state) => state?.aiAssistant
  ) as AIAssistantState
  const inputState = aiState.input
  const dispatch = useDispatch()

  /**
   * Handles mode selection from slash commands
   * The content insertion is handled directly in the slash command extension
   * This function just updates the mode state
   *
   * @param mode - The selected chat mode
   */
  const handleSlashModeSelect = (mode: any) => {
    // Update the mode in Redux state
    dispatch(setInputState({
      mode: mode.id
    }))
  }

  // Determine if slash commands should be enabled based on current mode
  const currentMode = inputState?.mode || 'coordinate'
  const isRouteMode = currentMode.startsWith('route') || currentMode === 'route'

  // Build extensions array conditionally
  const extensions = [
    StarterKit,
    Placeholder.configure({
      placeholder:
        'Input a prompt, or type @ to call reference doc/artefacts',
    }),
    Mention.configure({
      deleteTriggerWithBackspace: true,
      suggestion,
      renderHTML: ({ node, options }) => {
        return [
          'a',
          mergeAttributes(
            {
              href: resolveIdToLink(node.attrs.id),
              target: '_blank',
              reference: true,
            },
            options.HTMLAttributes
          ),
          `${options.suggestion.char}${node.attrs.label}`,
        ]
      },
    }),
    Extension.create({
      addKeyboardShortcuts() {
        return {
          Enter: () => true,
        }
      },
    }),
  ]

  // Only add SlashCommand extension when in route mode
  if (isRouteMode) {
    extensions.push(
      SlashCommand.configure({
        onModeSelect: handleSlashModeSelect,
      })
    )
  }

  const editor = useEditor({
    extensions,
    content: inputState?.content,
    onUpdate: ({ editor }) => {
      const text = editor.getText()
      onChange(text)
    },
  }, [isRouteMode]) // Re-create editor when route mode changes

  const onChange = (value: string) => {
    dispatch(setInputState({ content: value }))
  }

  useEffect(() => {
    clearMentionsCache()
  }, [])

  // Sync editor content with Redux state when input content changes
  useEffect(() => {
    if (editor) {
      const currentEditorText = editor.getText()
      const stateContent = inputState.content || ''

      // Only update if the content is actually different
      if (stateContent !== currentEditorText) {
        editor.commands.setContent(stateContent)
      }
    }
  }, [editor, inputState.content])

  const handleSendMessage = useCallback(
    function () {
      if (inputState.content.trim() && aiState.currentConversation?.id) {
        onSendMessage()

        // Get the API value for the selected mode
        const selectedModeConfig = getModeById(inputState.mode || 'coordinate')
        const apiModeValue = selectedModeConfig?.value || 'coordinate'

        dispatch(
          sendMessageRequest({
            sse: true,
            content: inputState.content.trim(), // Send the complete content (prefix + user text)
            mode: apiModeValue,
            references: inputState.references.map(
              (r): Reference => ({
                id: r.id,
                type: r.type,
                name: r.name,
                chunkId: r.chunkId,
                content: r.content,
              })
            ),
            conversationId: aiState.currentConversation.id,
          })
        )
        dispatch(clearSuggestions())
        editor?.commands.setContent('')
      }
    },
    [inputState, aiState.currentConversation?.id, dispatch, editor?.commands]
  )

  const inputOnKeyDown = useCallback(
    (e) => {
      if (e.metaKey && e.keyCode === 13) {
        handleSendMessage()
      }
    },
    [handleSendMessage]
  )

  const isLoading =
    aiState.isTyping ||
    aiState.currentConversation?.messages?.some((m) => m.isLoading)

  const renderFileUpload = (file: IFileWithUploadState) => {
    const deleteFile = async (fileId: string) => {
      try {
        dispatch(removeFile(fileId))
        await aiService.deleteFile(fileId)
      } catch (e) {
        toast.error(e)
      }
    }
    return (
      <div
        className="p-2 mr-2 mb-2 d-flex align-items-center mw-100 position-relative file-card"
        style={{ backgroundColor: 'rgba(196, 196, 196, 0.4)' }}
      >
        <FileOutlined />
        <div className="text-truncate mw-100 mx-4">{file.name}</div>
        {[FileStatus.processing, FileStatus.uploaded].includes(
          file.status as any
        ) && <LoadingOutlined />}
        {file.status === FileStatus.created && (
          <Progress
            type="circle"
            percent={file.uploadProgress}
            width={16}
            showInfo={false}
          />
        )}
        {file.status === FileStatus.processed && (
          <CheckCircleFilled style={{ color: 'green' }} />
        )}
        {file.status === FileStatus.error && (
          <Tooltip title={file.error}>
            <CloseCircleFilled style={{ color: 'red' }} />
          </Tooltip>
        )}
        <Button
          type="text"
          size="small"
          icon={<CloseCircleFilled />}
          className="position-absolute file-remove-btn"
          disabled={file.status === FileStatus.created}
          onClick={() => deleteFile(file.id)}
        />
      </div>
    )
  }

  const renderReference = (reference: any, index: number) => {
    return (
      <div key={`reference-${index}-${reference.id}`}>
        {reference.type === ReferenceType.File &&
          reference.status &&
          renderFileUpload(reference)}
        {!reference.status && (
          <div
            className="p-2 mr-2 mb-2 d-flex align-items-center mw-100 position-relative file-card"
            style={{ backgroundColor: 'rgba(196, 196, 196, 0.4)' }}
          >
            <CustomSvgIcons name="MentionIcon" />
            <div className="text-truncate mw-100 mx-4">
              {reference.type}: {reference.name}
            </div>
            <Button
              type="text"
              size="small"
              icon={<CloseCircleFilled />}
              className="position-absolute file-remove-btn"
              onClick={() => dispatch(removeReference(reference.id))}
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="ai-input-area" style={{ position: 'relative' }}>
      <div className="input-container">
        <div
          className="d-flex mt-2 flex-wrap p-2"
          style={{ maxHeight: '120px', overflowX: 'hidden', overflowY: 'auto' }}
        >
          {inputState.references.map(renderReference)}
        </div>
        {editor && (
          <EditorContent
            editor={editor}
            className="message-input"
            onKeyDown={inputOnKeyDown}
          />
        )}
        <div className="d-flex justify-content-between align-items-center mt-2">
          <div className="d-flex align-items-center">
            <FileUploader />
            <div style={{ marginLeft: '8px' }}>
              <ModeSelector disabled={isLoading} />
            </div>
          </div>
          <Space>
            <div className="text-hide">???/???</div>
            <Button
              type="text"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              disabled={!aiState.input.content.trim() || isLoading}
            />
          </Space>
        </div>
      </div>
    </div>
  )
}
