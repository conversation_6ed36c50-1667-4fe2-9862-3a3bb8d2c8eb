import { <PERSON>act<PERSON>ender<PERSON> } from '@tiptap/react'
import tippy from 'tippy.js'
import { MentionList } from './mention-list'
import type { SuggestionOptions } from '@tiptap/suggestion'
import AppCommonService from '@/services/app.service'
import { API_URLS, REQ_ARTEFACT_TYPE_ID } from '@/constants'
import { extractProjectCode } from '@/helper/share'

let isMentionsLoaded = false
let cachedMentions: any[] = []

export const clearMentionsCache = () => {
  isMentionsLoaded = false
  cachedMentions = []
}

export const suggestion: Omit<SuggestionOptions, 'editor'> = {
  char: '@',
  items: async ({ query }) => {
    if (!isMentionsLoaded) {
      cachedMentions = await AppCommonService.getMentionsForAIModule()
      isMentionsLoaded = true
    }
    return cachedMentions
      .filter((item) => {
        return item.id.toLowerCase().includes(query.toLowerCase())
      })
      .slice(0, 20)
  },
  render: () => {
    let component
    let popup

    return {
      onBeforeStart: (props) => {
        if (isMentionsLoaded) return
        AppCommonService.getMentionsForAIModule()
          .then((data) => {
            cachedMentions = data
            isMentionsLoaded = true
          })
          .catch((err) => {
            cachedMentions = []
          })
      },
      onStart: (props) => {
        component = new ReactRenderer(MentionList, {
          props,
          editor: props.editor,
        })
        // @ts-ignore
        popup = tippy('body', {
          getReferenceClientRect: props.clientRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'top-end',
        })
      },

      onUpdate(props) {
        component?.updateProps?.(props)

        popup?.[0]?.setProps?.({
          getReferenceClientRect: props.clientRect,
        })
      },

      onKeyDown(props) {
        if (props.event.key === 'Escape') {
          popup?.[0]?.hide?.()

          return true
        }

        return component?.ref?.onKeyDown?.(props)
      },

      onExit() {
        popup?.[0]?.destroy?.()
        component?.destroy?.()
      },
    }
  },
}

const MAP_LINK_TO_ARTEFACT_TYPE = {
  [REQ_ARTEFACT_TYPE_ID.ACTOR]: '/#/PRJ/{projectCode}/actor/{systemId}',
  [REQ_ARTEFACT_TYPE_ID.OBJECT]: '/#/PRJ/{projectCode}/object/{systemId}',
  [REQ_ARTEFACT_TYPE_ID.USECASE]: '/#/PRJ/{projectCode}/use-case/{systemId}',
  [REQ_ARTEFACT_TYPE_ID.WORKFLOW]: '/#/PRJ/{projectCode}/workflow/{systemId}',
  [REQ_ARTEFACT_TYPE_ID.SCREEN]: '/#/PRJ/{projectCode}/screen/{systemId}',
}
const MAP_API_DETAIL_TO_ARTEFACT_TYPE = {
  [REQ_ARTEFACT_TYPE_ID.ACTOR]: `${API_URLS.ACTORS}/{systemId}`,
  [REQ_ARTEFACT_TYPE_ID.OBJECT]: `${API_URLS.OBJECT}/{systemId}`,
  [REQ_ARTEFACT_TYPE_ID.USECASE]: `${API_URLS.USE_CASE}/{systemId}`,
  [REQ_ARTEFACT_TYPE_ID.WORKFLOW]: `${API_URLS.WORKFLOWS}/{systemId}`,
  [REQ_ARTEFACT_TYPE_ID.SCREEN]: `${API_URLS.SCREENS}/{systemId}`,
}

export const resolveIdToLink = (id: string) => {
  if (!id) return '#'
  const [artefactType, systemId] = id.split('/')
  const link = MAP_LINK_TO_ARTEFACT_TYPE[artefactType]
  const projectCode = extractProjectCode()

  if (!systemId || !link || !projectCode) return '#'
  return link
    .replace('{projectCode}', projectCode)
    .replace('{systemId}', systemId)
}

const contentMapper = (artefactType: string) => {

  switch (artefactType) {
    case REQ_ARTEFACT_TYPE_ID.OBJECT.toString():
      return ({ name, description, objectProperties }) =>
        `${name} - ${description} - (Properties: ${objectProperties
          ?.map((e: any) => {
            console.debug(e.name)
            const validations: string[] = [];
            if (e.mandatory) validations.push('Mandatory');
            if (e.unique) validations.push('Unique');
            if (e.maxLength) validations.push(`Max Length: ${e.maxLength}`);
            return  e.name + (validations.length ? `[${validations.join(', ')}]` : '')
          })
          .join(', ')})`
    default:
      return ({ name, description }) =>
        `Name: ${name}; Description: ${description}`
  }
}

export const resolveIdToContent = async (
  id: string
): Promise<string | null> => {
  if (!id) return null
  const [artefactType, systemId] = id.split('/')
  const apiUrl = MAP_API_DETAIL_TO_ARTEFACT_TYPE[artefactType]
  const projectCode = extractProjectCode()

  if (!systemId || !apiUrl || !projectCode) return null
  return await AppCommonService.getData(
    apiUrl.replace('{systemId}', systemId)
  ).then(contentMapper(artefactType))
}
