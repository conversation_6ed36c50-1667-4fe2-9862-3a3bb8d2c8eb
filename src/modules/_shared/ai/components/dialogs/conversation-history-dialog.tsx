import React, {
  forwardRef,
  PropsWithRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react'
import { Modal, Typo<PERSON>, Button, Tooltip, Space, Avatar, Empty } from 'antd'
import {
  DeleteOutlined,
  MessageOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons'
import { AIAssistantState, Conversation } from '../../types'
import { formatDate } from '@/modules/_shared/ai/utils/date'
import aiService from '@/services/ai.service'
import { useDispatch, useSelector } from 'react-redux'
import AppState from '@/store/types'
import { toast } from '@/helper/share'
import {
  fetchLastConversationOrCreate,
  setCurrentConversation,
} from '@/modules/_shared/ai'

const { Text } = Typography

interface ConversationHistoryModalProps extends PropsWithRef<any> {}

export const ConversationHistoryDialog: React.FC<ConversationHistoryModalProps> =
  forwardRef((_props, ref) => {
    const [isOpen, setIsOpen] = React.useState(false)
    const [conversations, setConversations] = useState<Conversation[]>([])
    const [loading, setLoading] = React.useState(false)
    const aiState = useSelector<AppState>(
      (state) => state?.aiAssistant
    ) as AIAssistantState
    const dispatch = useDispatch()

    useImperativeHandle(ref, () => ({
      toggle: () => setIsOpen((_prev) => !_prev),
    }))

    const onOpenDialog = async () => {
      setLoading(true)
      try {
        const { data: conversations } = await aiService.listConversations()
        setConversations(conversations)
      } catch (e) {
        toast.error(e)
      } finally {
        setLoading(false)
      }
    }

    const onSelectConversation = (conversation: Conversation) => {
      dispatch(setCurrentConversation(conversation))
      setIsOpen(false)
    }

    const onDeleteConversation = async (conversationId: string) => {
      try {
        await aiService.deleteConversation(conversationId)
        setConversations(conversations.filter((c) => c.id !== conversationId))
        if (aiState.currentConversation?.id === conversationId) {
          dispatch(fetchLastConversationOrCreate())
        }
      } catch (e) {
        toast.error(e)
      }
    }

    useEffect(() => {
      if (isOpen) {
        onOpenDialog().catch(console.error)
      }
    }, [isOpen])

    return (
      <Modal
        title={
          <div className="conversation-history-header">
            <MessageOutlined className="header-icon" />
            <span>Conversation History</span>
          </div>
        }
        open={isOpen}
        onCancel={() => setIsOpen(false)}
        footer={null}
        width={700}
        className="conversation-history-modal"
        destroyOnClose
      >
        <div className="conversation-modal-content">
          {loading && (
            <div className="text-center justify-content-center m-2">
              <Space>
                <LoadingOutlined />
                <Text type="secondary">Loading conversations...</Text>
              </Space>
            </div>
          )}
          {!loading && (
            <>
              {conversations.length === 0 ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <span className="empty-description">
                      No conversations found
                      <br />
                      <Text type="secondary">
                        Start a new conversation to see it here
                      </Text>
                    </span>
                  }
                />
              ) : (
                <div className="conversation-list">
                  {conversations.map((conversation) => {
                    const isActive =
                      conversation.id === aiState.currentConversation?.id
                    const formattedDate = formatDate(conversation.updatedAt)

                    return (
                      <div
                        key={conversation.id}
                        className={`conversation-item ${
                          isActive ? 'active' : ''
                        }`}
                        onClick={() => onSelectConversation(conversation)}
                      >
                        <div className="conversation-avatar">
                          <Avatar
                            size={40}
                            style={{
                              backgroundColor: isActive ? '#1890ff' : '#f0f0f0',
                              color: isActive ? '#fff' : '#666',
                            }}
                          >
                            <MessageOutlined />
                          </Avatar>
                        </div>

                        <div className="conversation-content">
                          <div className="conversation-header">
                            <Text
                              className="conversation-title"
                              strong={isActive}
                            >
                              {conversation.title || 'Untitled Conversation'}
                            </Text>
                            <div className="conversation-meta">
                              <Space size={4}>
                                <ClockCircleOutlined className="meta-icon" />
                                <Text
                                  type="secondary"
                                  className="conversation-time"
                                >
                                  {formattedDate}
                                </Text>
                              </Space>
                            </div>
                          </div>

                          <div className="conversation-stats">
                            <Space size={12}>
                              {isActive && (
                                <div className="active-indicator">
                                  <CheckCircleOutlined className="active-icon" />
                                  <Text
                                    type="secondary"
                                    className="active-text"
                                  >
                                    Active
                                  </Text>
                                </div>
                              )}
                            </Space>
                          </div>
                        </div>

                        <div className="conversation-actions">
                          <Tooltip title="Delete conversation" placement="left">
                            <Button
                              type="text"
                              icon={<DeleteOutlined />}
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation()
                                return onDeleteConversation(conversation.id)
                              }}
                              className="delete-conversation-btn"
                              danger
                            />
                          </Tooltip>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </>
          )}
        </div>
      </Modal>
    )
  })
