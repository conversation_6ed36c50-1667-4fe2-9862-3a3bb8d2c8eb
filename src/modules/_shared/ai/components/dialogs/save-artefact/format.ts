import { getPriority, SCOPE_TYPE } from '@/constants'

export const formatScopeType = (typeValue: number | string): string => {
  const numValue =
    typeof typeValue === 'string' ? parseInt(typeValue) : typeValue
  switch (numValue) {
    case SCOPE_TYPE.ORIGINAL.value:
      return SCOPE_TYPE.ORIGINAL.label
    case SCOPE_TYPE.CHANGE_REQUEST.value:
      return SCOPE_TYPE.CHANGE_REQUEST.label
    default:
      return typeValue?.toString() || ''
  }
}

export const formatPriority = (priorityValue: number | string): string => {
  const numValue =
    typeof priorityValue === 'string' ? parseInt(priorityValue) : priorityValue
  if (numValue && numValue >= 1 && numValue <= 4) {
    return getPriority(numValue)
  }
  return priorityValue?.toString() || ''
}
