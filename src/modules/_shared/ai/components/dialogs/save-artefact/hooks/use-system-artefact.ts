import { createContext, useContext, useState } from 'react'
import { apiCall } from '@/helper/api/aloApi'
import { API_URLS } from '@/constants'

interface Artefact {
  id: number
  code: string
  name: string
  description: string
}

export interface SystemArtefactContextType {
  objects: Artefact[]
  useCases: Artefact[]
  screens: Artefact[]
  workflows: Artefact[]
  actors: Artefact[],
  userRequirements: Artefact[]
}

const defaultState: SystemArtefactContextType = {
  objects: [],
  useCases: [],
  screens: [],
  workflows: [],
  actors: [],
  userRequirements: []
}

export const SystemArtefactContext =
  createContext<SystemArtefactContextType>(defaultState)

export const useExistingArtefact = () => {
  const context = useContext(SystemArtefactContext)
  return (
    artefactType: keyof SystemArtefactContextType,
    artefactName: string
  ) => {
    return context[artefactType].find((item) => item.name === artefactName)
  }
}

export const useSystemArtefact = () => {
  const [systemArtefact, setSystemArtefact] =
    useState<SystemArtefactContextType>(defaultState)
  const loadSystemArtefact = async () => {
    try {
      const mapper = ({ data }) =>
        data?.data?.map((item: any) => ({
          id: item.id,
          code: item.code,
          name: item.name,
        }))
      const [objects, useCases, screens, workflows, actors, userRequirements] = await Promise.all(
        [
          apiCall('GET', API_URLS.OBJECT, { Take: 1000, Skip: 0 }).then(mapper),
          apiCall('GET', API_URLS.USE_CASE, { Take: 1000, Skip: 0 }).then(mapper),
          apiCall('GET', API_URLS.SCREENS, { Take: 1000, Skip: 0 }).then(mapper),
          apiCall('GET', API_URLS.WORKFLOWS, { Take: 1000, Skip: 0 }).then(mapper),
          apiCall('GET', API_URLS.ACTORS, { Take: 1000, Skip: 0 }).then(mapper),
          apiCall('GET', API_URLS.USER_REQUIREMENTS, { Take: 1000, Skip: 0 }).then(mapper),
        ]
      )
      setSystemArtefact({ objects, useCases, screens, workflows, actors, userRequirements })
      return { objects, useCases, screens, workflows, actors, userRequirements }
    } catch (error) {
      console.error(error)
    }
    return defaultState
  }
  return { systemArtefact, loadSystemArtefact }
}
