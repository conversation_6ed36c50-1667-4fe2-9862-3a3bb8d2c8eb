import { useCallback, useMemo, useState } from 'react'
import { get as _get, set as _set } from 'lodash'
import {
  ArtefactParseResult,
  ArtefactType,
  ParsedArtefact,
  SavingStatus,
} from '@/modules/_shared/ai/types'
import { ArtifactPersistentService } from '../artifact-persistent.service'
import intl from '@/config/locale.config'
import { SystemArtefactContextType } from './use-system-artefact'
import { FIELD, FIELD_IDS, TYPED_FIELD } from '../constant'

const DEFAULT_STATE = {
  isSaveInProgress: false,
  artifactParsed: {
    [FIELD[ArtefactType.Actor]]: [],
    [FIELD[ArtefactType.Object]]: [],
    [FIELD[ArtefactType.UseCase]]: [],
    [FIELD[ArtefactType.Workflow]]: [],
    [FIELD[ArtefactType.Screen]]: [],
    [FIELD[ArtefactType.UserRequirement]]: [],
  } as unknown as ArtefactParseResult,
}

export const nameMatcher = (name: string, existingName: string) => {
  if (!name || !existingName) {
    return false
  }
  return name.toLowerCase() === existingName.toLowerCase()
}

const resolveArtefact = (
  item: ParsedArtefact,
  systemArtefact: SystemArtefactContextType,
  artefactType: ArtefactType
): boolean => {
  const field = FIELD[artefactType]
  const idsField = FIELD_IDS[artefactType]
  if (item[field]) {
    item[idsField] = []
    for (const name of item[field]) {
      const existingItem = systemArtefact[field].find(
        ({ name: existingName }) => nameMatcher(name, existingName)
      )
      if (existingItem?.id) {
        item[idsField].push(existingItem.id)
      } else {
        item.error = `${artefactType} ${name} did not match any existing items`
        return false
      }
    }
  }
  // If not exist field, return true
  return true
}

const resolveDependencies = (
  item: ParsedArtefact,
  systemArtefact: SystemArtefactContextType
): boolean => {
  return (
    resolveArtefact(item, systemArtefact, ArtefactType.Actor) &&
    resolveArtefact(item, systemArtefact, ArtefactType.Object) &&
    resolveArtefact(item, systemArtefact, ArtefactType.UserRequirement) &&
    resolveArtefact(item, systemArtefact, ArtefactType.Screen) &&
    resolveArtefact(item, systemArtefact, ArtefactType.UseCase)
  )
}

export const useSave = () => {
  const [isSaveInProgress, setIsSaveInProgress] = useState(
    DEFAULT_STATE.isSaveInProgress
  )
  const [artifactParsed, _setArtifactParsed] = useState<ArtefactParseResult>(
    DEFAULT_STATE.artifactParsed
  )

  const setArtifactParsed = useCallback(
    (state: Partial<ArtefactParseResult>) => {
      _setArtifactParsed((prev) => ({ ...prev, ...state }))
    },
    [_setArtifactParsed]
  )

  const setItemState = useCallback(
    (path: string, item: Partial<ParsedArtefact>) => {
      if (path) {
        _setArtifactParsed((prev) => {
          const currentItem = _get(prev, path, {})
          _set(prev, path, {
            ...currentItem,
            ...item,
          })
          return { ...prev }
        })
      }
    },
    [_setArtifactParsed]
  )

  const reset = useCallback(() => {
    setIsSaveInProgress(DEFAULT_STATE.isSaveInProgress)
    _setArtifactParsed(DEFAULT_STATE.artifactParsed)
  }, [_setArtifactParsed])

  const isSaveButtonDisabled = useMemo(() => {
    let isAnyItemSelected = false
    for (const items of Object.values(artifactParsed)) {
      for (const item of items) {
        if (item.isSelected) {
          isAnyItemSelected = true
          return (
            Boolean(item.error) ||
            Object.values(item.validationErrors || {}).length > 0
          )
        }
      }
    }
    return !isAnyItemSelected
  }, [artifactParsed])

  const _delay = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms))

  const saveSection = useCallback(
    async (items: ParsedArtefact[]) => {
      const savePromises: Promise<Partial<ParsedArtefact>>[] = []
      const artifactPersistentService = ArtifactPersistentService.getInstance()
      const saveFirstItems = [
        ...items.filter((item) => Boolean(item.id)),
        ...items.filter((item) => !item.id),
      ]
      for (let item of saveFirstItems) {
        if (item.path) {
          if (item.error) {
            setItemState(item.path!, {
              status: SavingStatus.Error,
              error: item.error,
            })
          } else {
            setItemState(item.path!, { status: SavingStatus.Saving })
            // TO AVOID CODE DUPLICATED
            if (!item.id) {
              await _delay(200)
            }
            savePromises.push(
              artifactPersistentService.saveArtefact(item).then(
                (updatedItem) => {
                  const updatedState: Partial<ParsedArtefact> = {
                    status: SavingStatus.Saved,
                  }
                  if (updatedItem.id) {
                    updatedState.id = updatedItem.id
                  }
                  setItemState(item.path!, updatedState)
                  return updatedItem
                },
                (error) => {
                  if (error?.response?.data) {
                    error = intl.formatMessage(
                      { id: error.response.data },
                      { Artefact: item.artefactType }
                    )
                  }
                  setItemState(item.path!, {
                    status: SavingStatus.Error,
                    error,
                  })
                }
              )
            )
          }
        }
      }
      return Promise.allSettled(savePromises)
    },
    [setItemState]
  )

  const onSave = useCallback(
    async (systemArtefact: SystemArtefactContextType) => {
      const SEQUENCE_TO_SAVE = [
        [ArtefactType.Actor, ArtefactType.Object, ArtefactType.UserRequirement],
        [ArtefactType.UseCase],
        [ArtefactType.Workflow, ArtefactType.Screen],
      ]

      for (const [section, items] of Object.entries(artifactParsed)) {
        items.forEach((item: ParsedArtefact, index: number) => {
          if (item.isSelected) {
            item.artefactType = TYPED_FIELD[section]
            item.path = `${section}.${index}`
          }
        })
      }

      setIsSaveInProgress(true)
      try {
        for (const artefactTypes of SEQUENCE_TO_SAVE) {
          for (const artefactType of artefactTypes) {
            const itemsToSave = artifactParsed[FIELD[artefactType]]
              .filter((item: ParsedArtefact) => item.isSelected)
              // .map((item: ParsedArtefact) => ({ ...item, error: undefined }))
              .filter(
                (item: ParsedArtefact) =>
                  resolveDependencies(item, systemArtefact) || true
              )
            const results = await saveSection(itemsToSave)
            for (const result of results) {
              if (result.status === 'fulfilled' && result.value) {
                const existingItem = systemArtefact[FIELD[artefactType]].find(
                  (item: any) => item.id === result.value.id
                )
                if (!existingItem) {
                  systemArtefact[FIELD[artefactType]].push(result.value)
                }
              }
            }
          }
        }
      } finally {
        setIsSaveInProgress(false)
      }
    },
    [artifactParsed, saveSection]
  )

  return {
    isSaveInProgress,
    artifactParsed,
    setArtifactParsed,
    setItemState,
    reset,
    onSave,
    isSaveButtonDisabled,
  }
}
