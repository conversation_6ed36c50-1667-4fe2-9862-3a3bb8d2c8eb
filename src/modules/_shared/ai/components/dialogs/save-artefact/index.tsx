import { Collapse, Modal, Space, Typography } from 'antd'
import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react'
import { useIntl } from 'react-intl'
import {
  AIAssistantState,
  ArtefactType,
  ParsedArtefact,
} from '@/modules/_shared/ai/types'
import { LoadingOutlined } from '@ant-design/icons'
import aiService from '@/services/ai.service'
import { useSelector } from 'react-redux'
import AppState from '@/store/types'
import { extractProjectCode, toast } from '@/helper/share'
import { nameMatcher, useSave } from './hooks/use-save'
import {
  HLRSection,
  ScreenSpecificationSection,
  UserRequirementSection,
  UseCaseSpecificationSection,
} from './sections'
import { ArtifactValidationService } from './artifact-validation.service'
import {
  SystemArtefactContext,
  SystemArtefactContextType,
  useSystemArtefact,
} from './hooks/use-system-artefact'
import { FIELD } from './constant'

type SaveArtefactDialogProps = {
  ref: any // Ref for imperative handle
}

type Section = {
  section: string
  items: ParsedArtefact[]
  onChange: (items: ParsedArtefact[]) => void
  onChangeItem: (index: number, item: ParsedArtefact) => void
  Component: any
}

export const SaveArtefactDialog: React.FC<SaveArtefactDialogProps> = forwardRef(
  (_props, ref) => {
    const intl = useIntl()

    const [isOpen, setIsOpen] = useState(false)
    const [isParsing, setIsParsing] = useState(false)
    const {
      artifactParsed,
      setArtifactParsed,
      isSaveInProgress,
      setItemState,
      reset,
      onSave,
      isSaveButtonDisabled,
    } = useSave()
    const { systemArtefact, loadSystemArtefact } = useSystemArtefact()

    const aiState = useSelector<AppState>(
      (state) => state?.aiAssistant
    ) as AIAssistantState
    const onClose = useCallback(() => {
      reset()
      setIsOpen(false)
    }, [reset])

    const handleOpen = async (initialContent: string) => {
      setIsOpen(true)
      const _systemArtefact = await loadSystemArtefact()
      return handleParse(initialContent, _systemArtefact)
    }

    const getUserRequest = useCallback(() => {
      console.debug(aiState.currentConversation?.messages)

      const botMessage = aiState.currentConversation?.messages.find(
        (msg) => msg.id === aiState.currentEditingMessage
      )
      const userMessage = aiState.currentConversation?.messages.find(
        (msg) => msg.id === botMessage?.replyTo
      )
      return userMessage?.content ?? ''
    }, [aiState.currentConversation?.messages, aiState.currentEditingMessage])

    const handleParse = useCallback(
      async (content: string, _systemArtefact: SystemArtefactContextType) => {
        setIsParsing(true)
        const validationService = ArtifactValidationService.getInstance()
        try {
          const userRequest = getUserRequest()
          const parsedData = await aiService.parseContentToArtefacts({
            content,
            userRequest,
            projectId: extractProjectCode(),
            context: _systemArtefact,
          })
          Object.entries(parsedData).forEach(
            ([section, items]: [string, ParsedArtefact[]]) => {
              items.forEach((item: ParsedArtefact) => {
                item.validationErrors = validationService.validate(
                  item.artefactType,
                  item
                )
                item.isSelected =
                  Object.values(item.validationErrors).length === 0

                item.id = _systemArtefact[section].find((i) =>
                  nameMatcher(item.name, i.name)
                )?.id
              })
            }
          )

          setArtifactParsed(parsedData)
        } catch (error) {
          toast.error(error)
        } finally {
          setIsParsing(false)
        }
      },
      [getUserRequest, setArtifactParsed]
    )

    useImperativeHandle(ref, () => ({
      open: handleOpen,
    }))

    const sections = useMemo<Section[]>(
      () =>
        Object.entries({
          [ArtefactType.Actor]: HLRSection,
          [ArtefactType.Object]: HLRSection,
          [ArtefactType.UseCase]: UseCaseSpecificationSection,
          [ArtefactType.Workflow]: HLRSection,
          [ArtefactType.Screen]: ScreenSpecificationSection,
          [ArtefactType.UserRequirement]: UserRequirementSection,
        }).map(([artefactType, Component]) => ({
          section: FIELD[artefactType],
          items: artifactParsed[FIELD[artefactType]] ?? [],
          Component,
          onChange: (data) => {
            setArtifactParsed({ [FIELD[artefactType]]: data })
          },
          onChangeItem: (index, item) => {
            setItemState(`${FIELD[artefactType]}.${index}`, item)
          },
        })),
      [artifactParsed, setArtifactParsed, setItemState]
    )

    return (
      <Modal
        title={intl.formatMessage({ id: 'ai.save-artefact.title' })}
        zIndex={1003}
        open={isOpen}
        width="70%"
        closable={false}
        cancelText={intl.formatMessage({ id: 'common.action.close' })}
        okText={intl.formatMessage({ id: 'common.action.save' })}
        cancelButtonProps={{
          disabled: isSaveInProgress,
        }}
        okButtonProps={{
          loading: isSaveInProgress,
          disabled: isParsing || isSaveButtonDisabled,
        }}
        onOk={() => onSave(systemArtefact)}
        onCancel={onClose}
      >
        <SystemArtefactContext.Provider value={systemArtefact}>
          {isParsing && (
            <Space>
              <LoadingOutlined />{' '}
              {intl.formatMessage({ id: 'ai.save-artefact.parsing' })}
            </Space>
          )}
          {!isParsing && (
            <div
              className="h-75"
              style={{ overflowY: 'auto', maxHeight: '75vh' }}
            >
              {sections.length ? (
                <>
                  <Typography.Text strong>
                    {intl.formatMessage({ id: 'ai.save-artefact.description' })}
                  </Typography.Text>
                  <br />
                  <Typography.Text italic>
                    Notes:
                    <ul>
                      <li>
                        {intl.formatMessage({ id: 'ai.save-artefact.note.1' })}
                      </li>
                      <li>
                        {intl.formatMessage({ id: 'ai.save-artefact.note.2' })}
                      </li>
                    </ul>
                  </Typography.Text>
                </>
              ) : (
                <Typography.Text type="danger">
                  {intl.formatMessage({ id: 'ai.save-artefact.no-items' })}
                </Typography.Text>
              )}

              <Collapse
                bordered={false}
                ghost
                defaultActiveKey={sections.map(
                  (_, index) => `section-save-artifact-${index}`
                )}
              >
                {sections
                  .filter((section) => section.items.length)
                  .map(
                    (
                      { Component, items, onChangeItem, onChange, section },
                      index
                    ) => (
                      <Collapse.Panel
                        header={
                          intl.formatMessage({
                            id: `ai.save-artefact.${section}.title`,
                          }) + ` (${items.length})`
                        }
                        key={`section-save-artifact-${index}`}
                      >
                        <Component
                          section={section}
                          items={items}
                          onChange={onChange}
                          onChangeItem={onChangeItem}
                        />
                      </Collapse.Panel>
                    )
                  )}
              </Collapse>
            </div>
          )}
        </SystemArtefactContext.Provider>
      </Modal>
    )
  }
)
