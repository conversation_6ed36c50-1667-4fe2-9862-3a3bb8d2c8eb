import intl from '@/config/locale.config'
import { Space, Tag, Typography } from 'antd'
import { ParsedArtefact, SavingStatus } from '@/modules/_shared/ai'
import {
  CheckOutlined,
  CloseOutlined,
  LoadingOutlined,
} from '@ant-design/icons'
import React from 'react'

export const getCheckboxProps = (record: ParsedArtefact) => {
  return {
    disabled: Boolean(Object.keys(record.validationErrors || {}).length > 0),
  }
}

export const nameColumn = {
  title: 'Name',
  dataIndex: 'name',
  key: 'name',
  width: '15%',
  render: (value: string, record: ParsedArtefact) => {
    return (
      <>
        <Typography.Text>{value}</Typography.Text>
        {!record.id && <><br /><Tag color="blue">NEW</Tag></>}
        <div className="text-danger font-italic small">
          {record?.validationErrors?.name}
        </div>
      </>
    )
  },
}

export const statusColumn = {
  title: intl.formatMessage({
    id: `ai.save-artefact.status-column`,
  }),
  dataIndex: 'status',
  key: 'status',
  width: '20%',
  render: (value: string, record: ParsedArtefact) => {
    switch (value) {
      case SavingStatus.Saving:
        return <LoadingOutlined style={{ color: 'blue' }} title="Saving..." />
      case SavingStatus.Saved:
        return (
          <Space>
            <CheckOutlined style={{ color: 'green' }} /> Success
          </Space>
        )
      case SavingStatus.Error:
        return (
          <Space>
            <CloseOutlined style={{ color: 'red' }} />{' '}
            {`Failed: ${record.error}`}
          </Space>
        )
      default:
        return ''
    }
  },
}
