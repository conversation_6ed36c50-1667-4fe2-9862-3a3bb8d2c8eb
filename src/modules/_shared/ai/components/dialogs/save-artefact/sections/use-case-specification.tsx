import { HLRSection, HLRSectionProps } from './hlr'
import { ParsedArtefact, UseCaseSpecification } from '@/modules/_shared/ai'
import { Table } from 'antd'
import React from 'react'
import { get as _get } from 'lodash'

interface UserSpecificationSectionProps extends HLRSectionProps {}

export function UseCaseSpecificationSection(
  props: UserSpecificationSectionProps
) {
  return (
    <HLRSection
      {...props}
      rowExpandable={({ trigger, preCondition, postCondition, businessRules }: any) => (
        trigger ||
        preCondition ||
        postCondition ||
        businessRules.length
      )}
      expandedRowRender={
        ((record: UseCaseSpecification) => (
          <div>
            <div className="row">
              <div className="col-2">Actor</div>
              <div className="col-10">{record.actors.join(', ')}</div>
            </div>
            <div className="row">
              <div className="col-2">Description</div>
              <div className="col-10">{record.description}</div>
            </div>
            <div className="row">
              <div className="col-2">Trigger</div>
              <div className="col-10">{record.trigger}</div>
            </div>
            <div className="row">
              <div className="col-2">Pre Condition</div>
              <div className="col-10">{record.preCondition}</div>
            </div>
            <div className="row">
              <div className="col-2">Post Condition</div>
              <div className="col-10">{record.postCondition}</div>
            </div>
            <div className="font-weight-bold my-2">Business Rule</div>
            <Table
              dataSource={record.businessRules}
              columns={[
                {
                  title: 'Step',
                  dataIndex: 'step',
                  width: '20%',
                  render: (step, record, index) => (
                    <>
                      <div>{step}</div>
                      <div className="text-danger">
                        {_get(
                          record,
                          `validationErrors.businessRules[${index}].step`
                        )}
                      </div>
                    </>
                  ),
                },
                {
                  title: 'Description',
                  dataIndex: 'description',
                  render: (_, record, index) => (
                    <>
                      <div className="font-weight-bold">{record.name}</div>
                      <div className="text-danger">
                        {_get(
                          record,
                          `validationErrors.businessRules[${index}].name`
                        )}
                      </div>
                      <div>{record.content}</div>
                      <div className="text-danger">
                        {_get(
                          record,
                          `validationErrors.businessRules[${index}].content`
                        )}
                      </div>
                    </>
                  ),
                },
              ]}
              pagination={false}
            />
          </div>
        )) as (record: ParsedArtefact) => React.ReactNode
      }
    />
  )
}
