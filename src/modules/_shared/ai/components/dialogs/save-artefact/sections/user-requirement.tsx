import { ParsedArtefact } from '@/modules/_shared/ai'
import { Typography } from 'antd'
import { ColumnsType } from 'antd/es/table'
import intl from '@/config/locale.config'
import { nameColumn, statusColumn } from './columns'
import React from 'react'
import { HLRSection } from './hlr'
import { formatPriority, formatScopeType } from '../format'

interface HLRSectionProps {
  section: string
  items: ParsedArtefact[]
  onChange: (items: ParsedArtefact[]) => void
  onChangeItem: (index: number, item: ParsedArtefact) => void
}

export function UserRequirementSection({
  section,
  items,
  onChange,
  onChangeItem,
}: HLRSectionProps) {
  const columns: ColumnsType<ParsedArtefact> = [
    {
      ...nameColumn,
      title: intl.formatMessage({
        id: `ai.save-artefact.${section}.name-column`,
      }),
    },
    {
      title: intl.formatMessage({
        id: `ai.save-artefact.${section}.type-column`,
      }),
      dataIndex: 'type',
      key: 'type',
      width: '15%',
      render: (value, record) => (
        <>
          <Typography.Text>{formatScopeType(value)}</Typography.Text>
          <br />
          {record.validationErrors?.type && (
            <Typography.Text italic style={{ fontSize: 11, color: 'red' }}>
              {record.validationErrors.type}
            </Typography.Text>
          )}
        </>
      ),
    },
    {
      title: intl.formatMessage({
        id: `ai.save-artefact.${section}.priority-column`,
      }),
      dataIndex: 'priority',
      key: 'priority',
      width: '15%',
      render: (value, record) => (
        <>
          <Typography.Text>{formatPriority(value)}</Typography.Text>
          <br />
          {record.validationErrors?.priority && (
            <Typography.Text italic style={{ fontSize: 11, color: 'red' }}>
              {record.validationErrors.priority}
            </Typography.Text>
          )}
        </>
      ),
    },
    {
      title: intl.formatMessage({
        id: `ai.save-artefact.${section}.description-column`,
      }),
      dataIndex: 'description',
      key: 'description',
      width: '65%',
    },
    statusColumn,
  ]

  return (
    <HLRSection
      section={section}
      columns={columns}
      onChange={onChange}
      items={items}
      onChangeItem={onChangeItem}
    />
  )
}
