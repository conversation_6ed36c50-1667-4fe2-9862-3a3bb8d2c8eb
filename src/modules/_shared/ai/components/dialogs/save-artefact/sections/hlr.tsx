import { ParsedArtefact } from '@/modules/_shared/ai'
import { Table } from 'antd'
import { ColumnsType } from 'antd/es/table'
import intl from '@/config/locale.config'
import { getCheckboxProps, nameColumn, statusColumn } from './columns'
import React from 'react'

export interface HLRSectionProps {
  columns?: ColumnsType<ParsedArtefact>
  section: string
  items: ParsedArtefact[]
  onChange: (items: ParsedArtefact[]) => void
  onChangeItem: (index: number, item: ParsedArtefact) => void
  rowExpandable?: (record: ParsedArtefact) => boolean
  expandedRowRender?: (record: ParsedArtefact) => React.ReactNode
}

export function HLRSection({
  columns,
  section,
  items,
  onChange,
  rowExpandable,
  expandedRowRender,
}: HLRSectionProps) {
  const _columns: ColumnsType<ParsedArtefact> = columns ?? [
    {
      ...nameColumn,
      title: intl.formatMessage({
        id: `ai.save-artefact.${section}.name-column`,
      }),
    },
    {
      title: intl.formatMessage({
        id: `ai.save-artefact.${section}.description-column`,
      }),
      dataIndex: 'description',
      key: 'description',
      width: '65%',
    },
    statusColumn,
  ]
  const selectedRowKeys: string[] = items
    .map((item, index) => item.isSelected && index.toString())
    .filter(Boolean) as string[]
  const [expandedRowKeys, setExpandedRowKeys] =
    React.useState<readonly React.Key[]>(selectedRowKeys)

  const onSelectChange = (selectedRowKeys: React.Key[]) => {
    const updatedItems = items.map((item, index) => ({
      ...item,
      isSelected: selectedRowKeys.includes(index.toString()),
    }))
    onChange(updatedItems)
  }

  const dataSource = items.map((item, index) => ({
    ...item,
    key: index.toString(),
  }))

  return (
    <Table
      bordered
      pagination={false}
      rowSelection={{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange,
        getCheckboxProps,
      }}
      dataSource={dataSource}
      columns={_columns}
      expandable={{
        expandedRowRender,
        rowExpandable,
        expandRowByClick: true,
        expandedRowKeys: expandedRowKeys,
        onExpandedRowsChange: (expandedKeys) =>
          setExpandedRowKeys(expandedKeys),
      }}
    />
  )
}
