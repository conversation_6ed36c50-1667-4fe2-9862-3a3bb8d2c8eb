import { HLRSection, HLRSectionProps } from './hlr'
import React from 'react'
import { ParsedArtefact, ScreenSpecification } from '@/modules/_shared/ai'
import { Table } from 'antd'

interface ScreenSpecificationSectionProps extends HLRSectionProps {}

export function ScreenSpecificationSection(
  props: ScreenSpecificationSectionProps
) {
  const nestedColumns = [
    {
      title: 'No',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      key: 'component',
    },
    {
      title: 'Component Type',
      dataIndex: 'componentType',
      key: 'componentType',
    },
    {
      title: 'Editable',
      dataIndex: 'editable',
      key: 'editable',
      render: (value: boolean) => (value ? 'Y' : 'N'),
    },
    {
      title: 'Mandatory',
      dataIndex: 'mandatory',
      key: 'mandatory',
      render: (value: boolean) => (value ? 'Y' : 'N'),
    },
    {
      title: 'Default Value',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
  ]

  return (
    <HLRSection
      {...props}
      rowExpandable={({ access, components }: any) =>
        access || components.length > 0
      }
      expandedRowRender={
        ((record: ScreenSpecification) => (
          <div>
            <div className="font-weight-bold">Screen Information</div>
            <div className="row">
              <div className="col-2">Object</div>
              <div className="col-10">{record.objects.join(', ')}</div>
            </div>
            <div className="row">
              <div className="col-2">Description</div>
              <div className="col-10">{record.description}</div>
            </div>
            <div className="row">
              <div className="col-2">Access</div>
              <div className="col-10">{record.access}</div>
            </div>
            <div className="font-weight-bold">Screen Description</div>
            <Table
              dataSource={record?.components?.map((e, index) => ({
                ...e,
                key: index + 1,
              }))}
              columns={nestedColumns}
              pagination={false}
            />
          </div>
        )) as (record: ParsedArtefact) => React.ReactNode
      }
    />
  )
}
