import { ArtefactType } from '@/modules/_shared/ai/types'



export const FIELD = {
  [ArtefactType.Actor]: 'actors',
  [ArtefactType.Object]: 'objects',
  [ArtefactType.UseCase]: 'useCases',
  [ArtefactType.Workflow]: 'workflows',
  [ArtefactType.Screen]: 'screens',
  [ArtefactType.UserRequirement]: 'userRequirements',
}

export const FIELD_IDS = {
  [ArtefactType.Actor]: 'actorIds',
  [ArtefactType.Object]: 'objectIds',
  [ArtefactType.UseCase]: 'useCaseIds',
  [ArtefactType.Workflow]: 'workflowIds',
  [ArtefactType.Screen]: 'screenIds',
  [ArtefactType.UserRequirement]: 'userRequirementIds',
}

export const TYPED_FIELD = (() => {
  const _TYPED_FIELD: Record<string, ArtefactType> = {}
  Object.entries(FIELD).forEach(([key, value]) => {
    _TYPED_FIELD[value] = key as ArtefactType
  })
  return _TYPED_FIELD
})()
