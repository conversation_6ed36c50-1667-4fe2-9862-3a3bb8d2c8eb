import {
  ArtefactType,
  ParsedArtefact,
  ParsedRequirement,
  ScreenSpecification,
  UseCaseSpecification,
} from '../../../types'
import { API_URLS, MESSAGE_TYPES, SCOPE_TYPE, STATUS } from '@/constants'
import { currentUserName } from '@/helper/share'
import { apiCall } from '@/helper/api/aloApi'
import { FIELD, FIELD_IDS } from './constant'

export const ARTEFACT_UPDATE_API: Record<ArtefactType, string> = {
  [ArtefactType.Actor]: API_URLS.ACTORS,
  [ArtefactType.Object]: API_URLS.OBJECT,
  [ArtefactType.UseCase]: API_URLS.USE_CASE,
  [ArtefactType.Workflow]: API_URLS.WORKFLOWS,
  [ArtefactType.Screen]: API_URLS.SCREENS,
  [ArtefactType.UserRequirement]: API_URLS.USER_REQUIREMENTS,
}

export class ArtifactPersistentService {
  private static _instance: ArtifactPersistentService
  private constructor() {}

  public static getInstance(): ArtifactPersistentService {
    if (!ArtifactPersistentService._instance) {
      ArtifactPersistentService._instance = new ArtifactPersistentService()
    }
    return ArtifactPersistentService._instance
  }

  public async saveArtefact(parsedArtefact: ParsedArtefact): Promise<any> {
    const payload: Record<string, any> = {
      id: parsedArtefact.id,
      name: parsedArtefact.name,
      description: parsedArtefact.description ?? '',
      author: currentUserName(),
      reviewer: '',
      customer: '',
      dueDate: new Date(),
      completeDate: null,
      status: STATUS.DRAFT,
      impacts: false,
      mentionReferences: null,
      [FIELD[ArtefactType.Actor]]:
        parsedArtefact[FIELD_IDS[ArtefactType.Actor]] ?? [],
      [FIELD[ArtefactType.Object]]:
        parsedArtefact[FIELD_IDS[ArtefactType.Object]] ?? [],
      [FIELD[ArtefactType.UseCase]]:
        parsedArtefact[FIELD_IDS[ArtefactType.UseCase]] ?? [],
      [FIELD[ArtefactType.Workflow]]:
        parsedArtefact[FIELD_IDS[ArtefactType.Workflow]] ?? [],
      [FIELD[ArtefactType.Screen]]:
        parsedArtefact[FIELD_IDS[ArtefactType.Screen]] ?? [],
      [FIELD[ArtefactType.UserRequirement]]:
        parsedArtefact[FIELD_IDS[ArtefactType.UserRequirement]] ?? [],
      messageAction: parsedArtefact.id
        ? MESSAGE_TYPES.UPDATE
        : MESSAGE_TYPES.CREATE,
    }
    switch (parsedArtefact.artefactType) {
      case ArtefactType.Actor:
        return await this.saveActor(payload, parsedArtefact)
      case ArtefactType.Object:
        return await this.saveDataObject(payload, parsedArtefact)
      case ArtefactType.UseCase:
        return await this.saveUseCase(
          payload,
          parsedArtefact as UseCaseSpecification
        )
      case ArtefactType.Workflow:
        return await this.saveWorkflow(payload, parsedArtefact)
      case ArtefactType.Screen:
        return await this.saveScreen(
          payload,
          parsedArtefact as ScreenSpecification
        )
      case ArtefactType.UserRequirement:
        return await this.saveUserRequirement(
          payload,
          parsedArtefact as ParsedRequirement
        )
      default:
        return Promise.reject(
          'Not implemented for artefact type: ' + parsedArtefact.artefactType
        )
    }
  }

  public async createDataObject(data: Record<string, any>) {
    return apiCall('POST', API_URLS.OBJECT, data).then(({ data }) => data)
  }

  public async updateDataObject(data: Record<string, any>) {
    return apiCall('PUT', `${API_URLS.OBJECT}/${data.id}`, data).then(
      ({ data }) => data
    )
  }

  public async saveDataObject(
    data: Record<string, any>,
    parsedArtefact: ParsedArtefact
  ) {
    data.userRequirementIds = parsedArtefact['userRequirementIds'] ?? []
    data.assignee = currentUserName()
    data.objectProperties = parsedArtefact['objectProperties'] ?? []

    if (data.messageAction === MESSAGE_TYPES.UPDATE) {
      return await this.updateDataObject(data)
    } else {
      return await this.createDataObject(data)
    }
  }

  public async createUserRequirement(data: Record<string, any>) {
    return apiCall('POST', API_URLS.USER_REQUIREMENTS, data).then(
      ({ data }) => data
    )
  }

  public async updateUserRequirement(data: Record<string, any>) {
    return apiCall(
      'PUT',
      `${API_URLS.USER_REQUIREMENTS}/${data.id}`,
      data
    ).then(({ data }) => data)
  }

  public async saveUserRequirement(
    data: Record<string, any>,
    parsedArtefact: ParsedRequirement
  ) {
    data.type = parsedArtefact.type || SCOPE_TYPE.ORIGINAL.value
    data.priority = parsedArtefact.priority || 1
    data.meetingMinuteIds = []
    data.referenceDocumentIds = []
    data.sourceOther = ''
    data.impacts = ''

    if (data.messageAction === MESSAGE_TYPES.UPDATE) {
      return await this.updateUserRequirement(data)
    } else {
      return await this.createUserRequirement(data)
    }
  }

  public async createUseCase(data: Record<string, any>) {
    return apiCall('POST', API_URLS.USE_CASE, data).then(({ data }) => data)
  }

  public async updateUseCase(data: Record<string, any>) {
    return apiCall('PUT', `${API_URLS.USE_CASE}/${data.id}`, data).then(
      ({ data }) => data
    )
  }

  public async saveUseCase(
    data: Record<string, any>,
    parsedArtefact: UseCaseSpecification
  ) {
    data = {
      ...data,
      assignee: currentUserName(),
      trigger: parsedArtefact.trigger,
      preCondition: parsedArtefact.preCondition ?? '',
      postCondition: parsedArtefact.postCondition ?? '',
      mainFlow: parsedArtefact.mainFlow ?? '',
      alternativeFlow: parsedArtefact.alternativeFlow ?? '',
      exceptionFlow: parsedArtefact.exceptionFlow ?? '',
      businessRules:
        parsedArtefact.businessRules?.map((rule) => ({
          ...rule,
          type: rule.name,
        })) ?? [],
    }
    if (data.messageAction === MESSAGE_TYPES.UPDATE) {
      return await this.updateUseCase(data)
    } else {
      return await this.createUseCase(data)
    }
  }

  public async createScreen(data: Record<string, any>) {
    return apiCall('POST', API_URLS.SCREENS, data).then(({ data }) => data)
  }

  public async updateScreen(data: Record<string, any>) {
    return apiCall('PUT', `${API_URLS.SCREENS}/${data.id}`, data).then(
      ({ data }) => data
    )
  }

  public async saveScreen(
    data: Record<string, any>,
    parsedArtefact: ScreenSpecification
  ) {
    data = {
      ...data,
      assignee: currentUserName(),
      access: parsedArtefact.access,
      actorIds: parsedArtefact.actorIds ?? [],
      baObjectIds: parsedArtefact.objectIds ?? [],
      functionIds: parsedArtefact.useCaseIds ?? [],
      screenComponents: parsedArtefact.components ?? [],
    }
    if (data.messageAction === MESSAGE_TYPES.UPDATE) {
      return await this.updateScreen(data)
    } else {
      return await this.createScreen(data)
    }
  }

  public async createWorkflow(data: Record<string, any>) {
    return apiCall('POST', API_URLS.WORKFLOWS, data).then(({ data }) => data)
  }

  public async updateWorkflow(data: Record<string, any>) {
    return apiCall('PUT', `${API_URLS.WORKFLOWS}/${data.id}`, data).then(
      ({ data }) => data
    )
  }

  public async saveWorkflow(
    data: Record<string, any>,
    parsedArtefact: ParsedArtefact
  ) {
    if (data.messageAction === MESSAGE_TYPES.UPDATE) {
      return await this.updateWorkflow(data)
    } else {
      return await this.createWorkflow(data)
    }
  }

  public async createActor(data: Record<string, any>) {
    return apiCall('POST', API_URLS.ACTORS, data).then(({ data }) => data)
  }

  public async updateActor(data: Record<string, any>) {
    return apiCall('PUT', `${API_URLS.ACTORS}/${data.id}`, data).then(
      ({ data }) => data
    )
  }

  public async saveActor(
    data: Record<string, any>,
    parsedArtefact: ParsedArtefact
  ) {
    data.actorType = 1
    if (data.messageAction === MESSAGE_TYPES.UPDATE) {
      return await this.updateActor(data)
    } else {
      return await this.createActor(data)
    }
  }
}
