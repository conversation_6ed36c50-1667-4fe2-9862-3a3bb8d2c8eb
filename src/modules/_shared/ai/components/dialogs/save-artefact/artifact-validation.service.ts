import { ArtefactType, ParsedArtefact } from '../../../types'
import { PIORITY_OPTIONS, SCOPE_TYPE } from '@/constants'
import {
  object,
  string,
  number,
  array,
  ObjectSchema,
  ValidationError,
} from 'yup'
import { set } from 'lodash'

const COMMON_SCHEMA = object().shape({
  name: string()
    .required('Name is required')
    .max(255, 'Name must be less than 255 characters'),
  description: string().nullable(),
})

const TYPED_SCHEMA: Record<string, ObjectSchema<any>> = {
  [ArtefactType.UserRequirement]: COMMON_SCHEMA.shape({
    type: number()
      .oneOf(
        Object.values(SCOPE_TYPE).map((option) => option.value),
        'Type is invalid'
      )
      .nullable(),
    priority: number()
      .oneOf(
        PIORITY_OPTIONS.map((option) => option.value),
        'Priority is invalid'
      )
      .nullable(),
  }),
  [ArtefactType.UseCase]: COMMON_SCHEMA.shape({
    trigger: string().nullable(),
    preCondition: string().nullable(),
    postCondition: string().nullable(),
    businessRules: array(
      object().shape({
        name: string().required('Name is required'),
        content: string().required('Content is required'),
        step: number().required('Step is required'),
      })
    ),
  }),
  [ArtefactType.Screen]: COMMON_SCHEMA.shape({
    components: array(
      object().shape({
        component: string().required('Component is required'),
        componentType: string().required('Name is required'),
        description: string().nullable(),
      })
    ),
  }),
}



export class ArtifactValidationService {
  private static _instance: ArtifactValidationService
  private constructor() {}

  public static getInstance(): ArtifactValidationService {
    if (!ArtifactValidationService._instance) {
      ArtifactValidationService._instance = new ArtifactValidationService()
    }
    return ArtifactValidationService._instance
  }

  public validate(artefactType: string, item: ParsedArtefact) {
    const errors: Record<string, string> = {}

    const parseErrors = (e: ValidationError) => {
      e.inner.forEach((e) => {
        // If nested errors
        if (e.inner.length > 0) {
          parseErrors(e)
        } else if (e.path) {
          set(errors, e.path, e.message)
        }
      })
    }

    try {
      const schema = TYPED_SCHEMA[artefactType] ?? COMMON_SCHEMA
      schema.validateSync(item, { abortEarly: false, disableStackTrace: true })
    } catch (e: unknown) {
      if (e instanceof ValidationError) {
        parseErrors(e)
      }
    }

    return errors
  }
}
