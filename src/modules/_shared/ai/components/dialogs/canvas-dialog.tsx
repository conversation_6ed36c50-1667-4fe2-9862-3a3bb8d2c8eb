import './canvas-dialog.less'
import { <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from "antd"
import { CloseOutlined, DownloadOutlined } from '@ant-design/icons'
import React, { forwardRef, useImperativeHandle, useState } from 'react'
import { CanvasEditor } from '../../../../../helper/component/tiptap'
import { SaveArtefactDialog } from './save-artefact'

type CanvasDialogProps = {
  onClose?: () => void
  onOpenChange?: (isOpen: boolean) => void
  ref: any
}

export const CanvasDialog: React.FC<CanvasDialogProps> = forwardRef(({ onClose, onOpenChange }, ref) => {
  const [isOpen, setIsOpen] = useState(false)
  const [content, setContent] = useState("")
  const saveArtefactRef = React.useRef<any>(null)

  const handleClose = () => {
    onClose?.()
    setIsOpen(false)
    onOpenChange?.(false)
  }

  const handleOpen = (initialContent: string) => {
    setContent(initialContent)
    setIsOpen(true)
    onOpenChange?.(true)
  }

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
  }

  useImperativeHandle(ref, () => ({
    open: handleOpen,
  }))

  return <Modal
    open={isOpen}
    width="100vw"
    closable={false}
    footer={null}
    style={{ top: 0, left: 0, right: 0, margin: 0, padding: 0, maxWidth: 'none' }}
    wrapClassName="canvas-dialog-modal"
  >
    <div className="canvas-dialog-content">
      <div className="canvas-dialog-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h2 style={{ margin: 0 }}>Canvas Editor</h2>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Tooltip title="Save Artefact">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => saveArtefactRef.current?.open(content)}
            />
          </Tooltip>
          <Tooltip title="Close Editor">
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleClose}
              style={{ color: '#666' }}
            />
          </Tooltip>
        </div>
      </div>
      <div className="canvas-dialog-body">
        <CanvasEditor
          content={content}
          placeholder="Type '/' for commands, or start typing..."
          onChange={handleContentChange}
          editable={true}
          className="canvas-editor"
          style={{
            minHeight: '400px',
            height: '100%',
            padding: '16px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }}
        />
      </div>
    </div>
    <SaveArtefactDialog ref={saveArtefactRef} />
  </Modal>
})