import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Card,
  Select,
  Form,
  Typography,
  Space,
  Divider,
  Tag,
  Tooltip,
  Switch,
  InputNumber,
  Button
} from 'antd'
import {
  RobotOutlined,
  SettingOutlined,
  <PERSON>boltOutlined,
  SaveOutlined
} from '@ant-design/icons'
import { AIAssistantState } from '../types'
import AppState from '../../../../store/types'
import './ai-settings.less'

const { Title, Text } = Typography
const { Option } = Select

interface AISettingsProps {
  onSettingsChange?: (settings: AISettings) => void
}

export interface AISettings {
  agentType: string
  model: string
  stream: boolean
  maxTokens: number
  temperature: number
}

export const AISettings: React.FC<AISettingsProps> = ({ onSettingsChange }) => {
  const dispatch = useDispatch()
  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState

  const [settings, setSettings] = useState<AISettings>({
    agentType: 'master_agent',
    model: 'gpt-4.1',
    stream: true,
    maxTokens: 2000,
    temperature: 0.7
  })

  const handleSettingChange = (key: keyof AISettings, value: any) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    onSettingsChange?.(newSettings)
  }

  const handleSaveSettings = () => {
    // Save settings to localStorage or dispatch to Redux
    localStorage.setItem('ai-assistant-settings', JSON.stringify(settings))
    // You could also dispatch an action to save to Redux store
  }

  const renderAgentTypeOption = (agent: any) => (
    <Option key={agent.value} value={agent.value}>
      <div className="agent-option">
        <div className="agent-header">
          <Tag color={agent.color}>{agent.label}</Tag>
        </div>
        <Text type="secondary" className="agent-description">
          {agent.description}
        </Text>
      </div>
    </Option>
  )

  const renderModelOption = (model: any) => (
    <Option key={model.value} value={model.value}>
      <div className="model-option">
        <div className="model-header">
          <span className="model-name">{model.label}</span>
          <Tag color={model.performance === 'Fast' ? 'green' : model.performance === 'Balanced' ? 'blue' : 'gold'}>
            {model.performance}
          </Tag>
        </div>
        <Text type="secondary" className="model-description">
          {model.description}
        </Text>
      </div>
    </Option>
  )

  return (
    <div className="ai-settings">
      <div className="settings-header">
        <Title level={4}>
          <SettingOutlined /> AI Assistant Settings
        </Title>
      </div>

      <Form layout="vertical" className="settings-form">
        <Form.Item label="Agent Type" className="agent-selection">
          <Select
            value={settings.agentType}
            onChange={(value) => handleSettingChange('agentType', value)}
            placeholder="Select an agent type"
            optionLabelProp="label"
          >
          </Select>
          <Text type="secondary" className="help-text">
            Choose the type of AI agent based on your analysis needs
          </Text>
        </Form.Item>

        <Form.Item label="AI Model" className="model-selection">
          <Select
            value={settings.model}
            onChange={(value) => handleSettingChange('model', value)}
            placeholder="Select an AI model"
            optionLabelProp="label"
          >
          </Select>
          <Text type="secondary" className="help-text">
            Select the AI model that best fits your task complexity
          </Text>
        </Form.Item>

        <Divider />

        <Form.Item label="Advanced Settings">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div className="setting-row">
              <div className="setting-label">
                <Text>Streaming Response</Text>
                <Tooltip title="Enable real-time streaming of AI responses">
                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                </Tooltip>
              </div>
              <Switch
                checked={settings.stream}
                onChange={(checked) => handleSettingChange('stream', checked)}
              />
            </div>

            <div className="setting-row">
              <div className="setting-label">
                <Text>Max Tokens</Text>
                <Tooltip title="Maximum number of tokens in the response">
                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                </Tooltip>
              </div>
              <InputNumber
                min={100}
                max={4000}
                value={settings.maxTokens}
                onChange={(value) => handleSettingChange('maxTokens', value || 2000)}
                style={{ width: 120 }}
              />
            </div>

            <div className="setting-row">
              <div className="setting-label">
                <Text>Temperature</Text>
                <Tooltip title="Controls randomness in responses (0.0 = deterministic, 1.0 = creative)">
                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                </Tooltip>
              </div>
              <InputNumber
                min={0}
                max={1}
                step={0.1}
                value={settings.temperature}
                onChange={(value) => handleSettingChange('temperature', value || 0.7)}
                style={{ width: 120 }}
              />
            </div>
          </Space>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveSettings}
            block
          >
            Save Settings
          </Button>
        </Form.Item>
      </Form>

      <div className="settings-info">
        <Card size="small" className="info-card">
          <Title level={5}>Current Configuration</Title>
          <Space direction="vertical" size="small">
            <div>
              <Text strong>Agent: </Text>
            </div>
            <div>
              <Text strong>Model: </Text>
              <Tag color="blue">{settings.model}</Tag>
            </div>
            <div>
              <Text strong>Streaming: </Text>
              <Tag color={settings.stream ? 'green' : 'red'}>
                {settings.stream ? 'Enabled' : 'Disabled'}
              </Tag>
            </div>
          </Space>
        </Card>
      </div>
    </div>
  )
}

export default AISettings
