import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Button, Tabs, Card, message, Tooltip } from 'antd'
import {
  RobotOutlined,
  SettingOutlined,
  CloseOutlined,
  ExpandOutlined,
  EyeOutlined,
  SaveOutlined,
  DownloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons'
import { AIAssistantState } from '../types'
import { toggleAIChatPanel, toggleAISettingsPanel, setActiveTab, closeAllPanels, enterCanvasOnlyMode, exitCanvasOnlyMode, setCurrentEditingMessage, updateMessageContentRequest } from '../actions'
import { AIChatBox } from './chatbox'
import AISettings from './ai-settings'
import { RightMenu } from './right-menu'
import { CanvasEditor } from '@/helper/component/tiptap'
import AppState from '@/store/types'
import intl from '@/config/locale.config'
import { SaveArtefactDialog } from './dialogs/save-artefact'
import { CanvasDialog } from './dialogs/canvas-dialog'
import { extractProjectCode, toast } from '@/helper/share'

const { TabPane } = Tabs

type AIAssistantProps = {
  collapsed: boolean
}

export const AIAssistant: React.FC<AIAssistantProps> = ({ collapsed }) => {
  const dispatch = useDispatch()
  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState
  const [canvasContent, setCanvasContent] = useState(aiState.canvasContent || '')
  const [isSaving, setIsSaving] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isCanvasDialogOpen, setIsCanvasDialogOpen] = useState(false)
  const saveArtefactRef = React.useRef<any>(null)
  const canvasDialogRef = React.useRef<any>(null)

  // Sync canvas content with state when it changes
  useEffect(() => {
    setCanvasContent(aiState.canvasContent || '')
  }, [aiState.canvasContent])

  const handleToggleAIChat = () => {
    if (aiState.isCanvasOnlyMode) {
      // Exit canvas-only mode and open AI chat
      dispatch(exitCanvasOnlyMode())
      dispatch(toggleAIChatPanel())
    } else {
      dispatch(toggleAIChatPanel())
    }
  }

  const handleToggleSettings = () => {
    dispatch(toggleAISettingsPanel())
  }

  const handleTabChange = (activeKey: string) => {
    dispatch(setActiveTab(activeKey as 'ai-chat' | 'settings'))
  }

  const handleCloseAll = () => {
    // Clear current editing message when closing all panels
    dispatch(setCurrentEditingMessage(null))
    dispatch(closeAllPanels())
  }

  const handleEnterCanvasOnlyMode = () => {
    // Get the content of the currently editing message
    if (aiState.currentEditingMessage && aiState.currentConversation?.messages) {
      const editingMessage = aiState.currentConversation.messages.find(
        msg => msg.id === aiState.currentEditingMessage
      )
      
      if (editingMessage) {
        const messageContent = editingMessage.content || ''
        setCanvasContent(messageContent)
        dispatch(enterCanvasOnlyMode(messageContent))
      }
    }
  }

  const handleSaveCanvasContent = async (closeCanvas: boolean = true) => {
    if (!aiState.currentEditingMessage || !aiState.currentConversation) {
      message.error('No message selected for editing')
      return
    }

    setIsSaving(true)
    try {
      // The canvasContent is already in markdown format from the canvas editor
      // We don't need to convert it again - just save it directly
      dispatch(updateMessageContentRequest({
        conversationId: aiState.currentConversation.id,
        messageId: aiState.currentEditingMessage,
        content: canvasContent
      }))
      
      toast.success('Message updated successfully')
      if (closeCanvas) {
        // Exit canvas mode after saving
        dispatch(exitCanvasOnlyMode())
        dispatch(setCurrentEditingMessage(null))
        // Open AI chat after saving
        handleToggleAIChat()
      }
    } catch (error) {
      toast.error('Failed to update message')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCanvasContentChange = (newContent: string) => {
    setCanvasContent(newContent)
  }

  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const handleOpenCanvasDialog = (content: string) => {
    canvasDialogRef.current?.open(content)
  }

  const handleCanvasDialogOpenChange = (isOpen: boolean) => {
    setIsCanvasDialogOpen(isOpen)
    // If canvas dialog is closing, open AI chat
    if (!isOpen) {
      handleToggleAIChat()
    }
  }

  if (!extractProjectCode()) {
    return <></>
  }

  return (
    <>
      {/* Right Side Menu - Always visible */}
      <RightMenu
        onToggleAIChat={handleToggleAIChat}
        onToggleSettings={handleToggleSettings}
        isAIChatOpen={aiState.isAiPanelOpen && aiState.activeTab === 'ai-chat'}
        isSettingsOpen={aiState.isAiPanelOpen && aiState.activeTab === 'settings'}
        isHidden={aiState.isCanvasOnlyMode || isCanvasDialogOpen}
      />
      {/* <CanvasEditor onClose={() => { }} onSave={async () => { }} collapsed={collapsed} /> */}
      {/* Tabbed Interface - Shows when any panel is open */}
      {aiState.isAiPanelOpen && (
        <div className={`ai-assistant-tabbed-container ${isFullscreen ? 'ai-assistant-fullscreen' : ''}`}>
          <div className="ai-assistant-tabs-header">
            <Tabs
              activeKey={aiState.activeTab || undefined}
              onChange={handleTabChange}
              type="card"
              size="small"
              tabBarExtraContent={
                <div className="tab-extra-actions">
                  {aiState.currentEditingMessage && (
                    <>
                      <Button title='Canvas Only View' type="text" onClick={handleEnterCanvasOnlyMode} icon={<EyeOutlined />} />
                      <Button 
                        title='Open Canvas Dialog (Fullscreen)' 
                        type="text" 
                        onClick={() => {
                          if (aiState.currentConversation?.messages) {
                            const editingMessage = aiState.currentConversation.messages.find(
                              msg => msg.id === aiState.currentEditingMessage
                            )
                            if (editingMessage) {
                              handleOpenCanvasDialog(editingMessage.content || '')
                            }
                          }
                        }} 
                        icon={<ExpandOutlined />} 
                      />
                    </>
                  )}
                  <Button 
                    title={isFullscreen ? 'Exit Full screen' : 'Toggle Full screen'} 
                    type="text" 
                    onClick={handleToggleFullscreen} 
                    icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
                  />
                  <Button title='Close Chat Panel' type="text" onClick={handleCloseAll} icon={<CloseOutlined />} />
                </div>
              }
            >
              <TabPane
                tab={
                  <span>
                    <RobotOutlined />
                    {intl.formatMessage({ id: 'ai.agent.name' }) }
                  </span>
                }
                key="ai-chat"
              />
              <TabPane
                disabled // TODO: Enable when settings are ready
                tab={
                  <span>
                    <SettingOutlined />
                    Settings
                  </span>
                }
                key="settings"
              />
            </Tabs>
          </div>

          <div className="ai-assistant-tab-content">
            {aiState.activeTab === 'ai-chat' && (
              <AIChatBox
                onClose={handleToggleAIChat}
              />
            )}
            {aiState.activeTab === 'settings' && (
              <AISettings />
            )}
          </div>
        </div>
      )}

      {/* Canvas Only Mode - Shows when in canvas-only mode */}
      {aiState.isCanvasOnlyMode && (
        <div className="canvas-only-container">
          <Card
            title="Canvas Editor"
            size="small"
            extra={
              <div style={{ display: 'flex', gap: '8px' }}>
                <Button
                  type="text"
                  icon={<SaveOutlined />}
                  onClick={() => handleSaveCanvasContent(true)}
                  loading={isSaving}
                  disabled={isSaving}
                />

                <Tooltip title="Save Artefact">
                  <Button
                    type="text"
                    icon={<DownloadOutlined />}
                    onClick={() => handleSaveCanvasContent(false).then(() => saveArtefactRef.current?.open(canvasContent))}
                  />
                </Tooltip>
                  
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  title="Close Canvas"
                  onClick={() => {
                    dispatch(setCurrentEditingMessage(null))
                    dispatch(closeAllPanels())
                    // Open AI chat after closing canvas
                    handleToggleAIChat()
                  }}
                />
              </div>
            }
            className="canvas-only-card"
          >
            <CanvasEditor
              content={canvasContent}
              placeholder="Edit your content here..."
              onChange={handleCanvasContentChange}
              editable={true}
              style={{
                minHeight: '60vh',
                maxHeight: '80vh',
                overflow: 'auto'
              }}
            />
          </Card>
          <SaveArtefactDialog ref={saveArtefactRef} />
        </div>
      )}

      {/* Canvas Dialog - Fullscreen modal for editing */}
      <CanvasDialog 
        ref={canvasDialogRef}
        onOpenChange={handleCanvasDialogOpenChange}
      />
    </>
  )
}
