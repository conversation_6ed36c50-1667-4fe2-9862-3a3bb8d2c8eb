@import '../../../../commons.less';

.right-side-menu {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  z-index: 1000;

  .right-menu-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e8e8e8;
    backdrop-filter: blur(10px);

    .menu-button {
      width: 48px;
      height: 48px;
      border: 1px solid #d9d9d9;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border-color: @primary-color;
      }

      &.active {
        background-color: @primary-color;
        border-color: @primary-color;
        color: white;

        &:hover {
          background-color: @primary-color;
          border-color: @primary-color;
          opacity: 0.9;
        }
      }

      .anticon {
        font-size: 18px;
      }

      // Specific button styling
      &.ai-chat-button {
        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          background-color: #1890ff;
          border-color: #1890ff;
        }
      }

      &.settings-button {
        &:hover {
          border-color: #52c41a;
          color: #52c41a;
        }

        &.active {
          background-color: #52c41a;
          border-color: #52c41a;
        }
      }
    }
  }

  // Animation for menu appearance
  animation: slideInRight 0.3s ease-out;

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateY(-50%) translateX(20px);
    }

    to {
      opacity: 1;
      transform: translateY(-50%) translateX(0);
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    right: 10px;

    .right-menu-container {
      padding: 8px;

      .menu-button {
        width: 40px;
        height: 40px;

        .anticon {
          font-size: 16px;
        }
      }
    }
  }

  // When AI panels are open, move menu to the left of the tabbed interface
  .ai-panels-open & {
    right: 470px;

    @media (max-width: 1200px) {
      right: 420px;
    }

    @media (max-width: 992px) {
      right: 370px;
    }

    @media (max-width: 768px) {
      right: 10px;
    }
  }

  // When chat is also open
  .chat-open.ai-panels-open & {
    right: 870px;

    @media (max-width: 1400px) {
      right: 820px;
    }

    @media (max-width: 1200px) {
      right: 770px;
    }

    @media (max-width: 992px) {
      right: 720px;
    }

    @media (max-width: 768px) {
      right: 10px;
    }
  }
}

// Pulse animation for active buttons
.right-side-menu .menu-button.active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}