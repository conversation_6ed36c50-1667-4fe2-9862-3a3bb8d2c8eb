import React from 'react'
import { <PERSON><PERSON>, Toolt<PERSON>, Space } from 'antd'
import { RobotOutlined, SettingOutlined } from '@ant-design/icons'
import './right-menu.less'

type RightMenuProps = {
  onToggleAIChat?: () => void
  onToggleSettings?: () => void
  isAIChatOpen?: boolean
  isSettingsOpen?: boolean
  isHidden?: boolean
}

export const RightMenu: React.FC<RightMenuProps> = ({
  onToggleAIChat,
  onToggleSettings,
  isAIChatOpen = false,
  isSettingsOpen = false,
  isHidden = false
}) => {
  if (isHidden) {
    return null
  }
  
  return (
    <div className="right-side-menu">
      <div className="right-menu-container">
        <Space direction="vertical" size="small">
          <Tooltip title="OrcaAI" placement="left">
            <Button
              type={isAIChatOpen ? "primary" : "default"}
              shape="circle"
              size="large"
              icon={<RobotOutlined />}
              onClick={onToggleAIChat}
              className={`menu-button ai-chat-button ${isAIChatOpen ? 'active' : ''}`}
            />
          </Tooltip>

          <Tooltip title="OrcaAI Settings (Not ready)" placement="left">
            <Button
              type={isSettingsOpen ? "primary" : "default"}
              shape="circle"
              size="large"
              icon={<SettingOutlined />}
              // onClick={onToggleSettings} // TODO: Enable this when settings is ready
              className={`menu-button settings-button ${isSettingsOpen ? 'active' : ''}`}
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  )
}
