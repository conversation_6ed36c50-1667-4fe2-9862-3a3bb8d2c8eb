.ai-settings {
  padding: 16px;
  height: 100%;
  overflow-y: auto;

  .settings-header {
    margin-bottom: 24px;
    
    h4 {
      margin: 0;
      color: #1890ff;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .settings-form {
    .agent-selection,
    .model-selection {
      .ant-select {
        width: 100%;
      }

      .help-text {
        display: block;
        margin-top: 4px;
        font-size: 12px;
      }
    }

    .agent-option,
    .model-option {
      padding: 4px 0;

      .agent-header,
      .model-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2px;

        .model-name {
          font-weight: 500;
        }
      }

      .agent-description,
      .model-description {
        font-size: 12px;
        line-height: 1.3;
      }
    }

    .setting-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 0;

      .setting-label {
        display: flex;
        align-items: center;
        flex: 1;

        .anticon {
          cursor: help;
        }
      }
    }
  }

  .settings-info {
    margin-top: 24px;

    .info-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;

      .ant-card-body {
        padding: 12px;
      }

      h5 {
        margin: 0 0 12px 0;
        color: #495057;
      }

      .ant-space-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}

// Dark theme support
.dark-theme .ai-settings {
  .settings-info .info-card {
    background: #1f1f1f;
    border-color: #434343;

    h5 {
      color: #e0e0e0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .ai-settings {
    padding: 12px;

    .settings-form {
      .setting-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .setting-label {
          width: 100%;
        }
      }
    }
  }
}
