import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { extractProjectCode, toast } from '@/helper/share'
import intl from '@/config/locale.config'
import { Card, Col, Popover, Row, Select, Space } from 'antd'
import aiService from '@/services/ai.service'
import { ITokenUsageResponse } from '@/modules/_shared/ai'
import AppCommonService from '@/services/app.service'
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons'

interface ProjectDashboardProps {}

const periodOptions: {
  value: string
  label: string
  getFilter: () => { from?: string; to?: string }
}[] = [
  {
    value: 'from-beginning',
    label: 'From the beginning',
    getFilter: () => ({}),
  },
  {
    value: 'previous-7d',
    label: 'Previous 7 days',
    getFilter: () => ({ from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }),
  },
  {
    value: 'previous-1m',
    label: 'Previous 1 month',
    getFilter: () => ({
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    }),
  },
  {
    value: 'previous-3m',
    label: 'Previous 3 months',
    getFilter: () => ({
      from: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
    }),
  },
  {
    value: 'previous-6m',
    label: 'Previous 6 months',
    getFilter: () => ({
      from: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
    }),
  },
]

function calculateCost(unitCostPer1m: number, tokens: number) {
  if (!unitCostPer1m || !tokens) {
    return 0
  }
  return (unitCostPer1m * tokens) / 1e6
}

function formatLocaleNumber(...numbers: (number | undefined | null)[]) {
  const validNumbers: number[] = numbers.filter(Boolean) as number[]
  return validNumbers
    .reduce((acc, cur) => acc + cur, 0)
    .toLocaleString(undefined, {
      maximumFractionDigits: 2,
    })
}

function formatTokenNumber(n: number) {
  let postfix = ''
  if (n >= 1e8) {
    n /= 1e9
    postfix = 'B'
  } else if (n >= 1e5) {
    n /= 1e6
    postfix = 'M'
  } else if (n >= 1e2) {
    n /= 1e3
    postfix = 'K'
  }
  return (
    n.toLocaleString(undefined, {
      maximumFractionDigits: 2,
    }) + postfix
  )
}

export const AIProjectDashboard: React.FC<ProjectDashboardProps> = (_props) => {
  const [loading, setLoading] = useState(false)
  const projectCode = extractProjectCode()
  const [period, setPeriod] = useState(periodOptions[0].value)
  const [userId, setUserId] = useState<string | null>(null)
  const [projectMembers, setProjectMembers] = useState<string[]>([])
  const [statistics, setStatistics] = useState<ITokenUsageResponse | null>(null)

  const getStatistics = useCallback(async () => {
    setLoading(true)
    try {
      const filter =
        periodOptions.find((item) => item.value === period)?.getFilter() ?? {}
      const response = await aiService.getTokenUsage({
        projectId: projectCode ?? undefined,
        // userId: userId ?? undefined,
        ...filter,
      })
      setStatistics(response)
    } catch (error) {
      toast.error(error)
    } finally {
      setLoading(false)
    }
  }, [projectCode, period, userId])

  const getProjectMembers = useCallback(async () => {
    AppCommonService.getAllProjectMembers()
      .then(setProjectMembers)
      .catch(toast.error)
  }, [])

  useEffect(() => {
    getStatistics().catch(console.error)
  }, [getStatistics])

  useEffect(() => {
    getProjectMembers().catch(console.error)
  }, [getProjectMembers])

  const totalPromptTokens = Object.values(
    statistics?.promptTokens ?? {}
  ).reduce((acc, cur) => acc + cur, 0)

  const totalCompletionTokens = Object.values(
    statistics?.completionTokens ?? {}
  ).reduce((acc, cur) => acc + cur, 0)

  const totalCost = useMemo(() => {
    const cost: Record<
      string,
      { prompt: number; completion: number; cached: number }
    > = {
      total: {
        prompt: 0,
        completion: 0,
        cached: 0,
      },
    }
    if (!statistics) {
      return cost
    }
    Object.keys(statistics.promptTokens).forEach((model) => {
      const modelCost = {
        prompt: calculateCost(
          statistics.costPer1mPromptTokens[model],
          statistics.promptTokens[model] - statistics.cachedPromptTokens[model]
        ),
        completion: calculateCost(
          statistics.costPer1mCompletionTokens[model],
          statistics.completionTokens[model]
        ),
        cached: calculateCost(
          statistics.costPer1mCachedTokens[model],
          statistics.cachedPromptTokens[model]
        ),
      }
      cost[model] = modelCost
      cost.total.prompt += modelCost.prompt
      cost.total.completion += modelCost.completion
      cost.total.cached += modelCost.cached
    })

    return cost
  }, [statistics])

  return (
    <Card
      title={intl.formatMessage({ id: 'dashboard.ai-usage' })}
      loading={loading}
      bodyStyle={{ paddingTop: '1rem' }}
    >
      <Row>
        <Col span={6}>
          <div className="d-flex align-items-center">
            <span className="font-weight-bold mr-2">Period</span>
            <Select value={period} onChange={setPeriod} style={{ width: 200 }}>
              {periodOptions.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </div>
        </Col>
        <Col span={6}>
          <div className="d-flex align-items-center">
            <span className="font-weight-bold mr-2">Project Member</span>
            <Select value={userId} onChange={setUserId} style={{ width: 200 }}>
              <Select.Option value={null}>All</Select.Option>
              {projectMembers.map((item) => (
                <Select.Option key={item} value={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </div>
        </Col>
      </Row>
      <Row className="mt-2">
        <Col span={6}>
          <Popover
            placement="top"
            title={<div className="h5 mt-2">Token Breakdown</div>}
            content={
              <table className="table">
                <thead>
                  <tr>
                    <th scope="col">Model</th>
                    <th scope="col">Total</th>
                    <th scope="col">Non-Cached Prompt</th>
                    <th scope="col">Cached Prompt</th>
                    <th scope="col">Completion</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.keys(statistics?.promptTokens ?? {}).map((model) => (
                    <tr key={model}>
                      <th scope="row">{model}</th>
                      <td>
                        {formatTokenNumber(
                          Number(statistics?.promptTokens[model]) +
                            Number(statistics?.completionTokens[model])
                        )}
                      </td>
                      <td>
                        {formatTokenNumber(
                          Number(statistics?.promptTokens[model]) -
                            Number(statistics?.cachedPromptTokens[model])
                        )}
                      </td>
                      <td>
                        {formatTokenNumber(
                          Number(statistics?.cachedPromptTokens[model])
                        )}
                      </td>
                      <td>
                        {formatTokenNumber(
                          Number(statistics?.completionTokens[model])
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            }
          >
            <div className="p-4">
              <div className="h5 font-weight-bold">Total Tokens Used</div>
              <div className="h4 text-primary">
                {formatTokenNumber(totalPromptTokens + totalCompletionTokens)}
              </div>
              <Space size={10}>
                <span className="text-success">
                  <ArrowUpOutlined />
                  <span className="ml-2">
                    {formatTokenNumber(totalPromptTokens)}
                  </span>
                </span>
                <span className="text-warning">
                  <ArrowDownOutlined />
                  <span className="ml-2">
                    {formatTokenNumber(totalCompletionTokens)}
                  </span>
                </span>
              </Space>
            </div>
          </Popover>
        </Col>
        <Col span={6}>
          <Popover
            placement="top"
            title={<div className="h5 mt-2">Cost Breakdown</div>}
            content={
              <table className="table">
                <thead>
                  <tr>
                    <th scope="col">Model</th>
                    <th scope="col">Total</th>
                    <th scope="col">Non-Cached Prompt</th>
                    <th scope="col">Cached Prompt</th>
                    <th scope="col">Completion</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.keys(totalCost)
                    .filter((item) => item !== 'total')
                    .map((model) => (
                      <tr key={model}>
                        <th scope="row">{model}</th>
                        <td>
                          {formatLocaleNumber(
                            totalCost[model].prompt,
                            totalCost[model].cached,
                            totalCost[model].completion
                          )}
                        </td>
                        <td>
                          <div>
                            {formatLocaleNumber(totalCost[model].prompt)}
                          </div>
                          <div className="small font-italic">
                            $
                            {formatLocaleNumber(
                              statistics?.costPer1mPromptTokens[model]
                            )}
                            /1M
                          </div>
                        </td>
                        <td>
                          <div>
                            {formatLocaleNumber(totalCost[model].cached)}
                          </div>
                          <div className="small font-italic">
                            $
                            {formatLocaleNumber(
                              statistics?.costPer1mCachedTokens[model]
                            )}
                            /1M
                          </div>
                        </td>
                        <td>
                          <div>
                            {formatLocaleNumber(totalCost[model].completion)}
                          </div>
                          <div className="small font-italic">
                            $
                            {formatLocaleNumber(
                              statistics?.costPer1mCompletionTokens[model]
                            )}
                            /1M
                          </div>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            }
          >
            <div className="p-4">
              <div className="h5 font-weight-bold">Total Cost (USD)</div>
              <div className="h4 text-warning">
                $
                {formatLocaleNumber(
                  totalCost.total.prompt,
                  totalCost.total.cached,
                  totalCost.total.completion
                )}
              </div>
            </div>
          </Popover>
        </Col>
        <Col span={6}>
          <div className="p-4">
            <div className="h5 font-weight-bold">Total Prompts</div>
            <div className="h4 text-primary">
              {formatLocaleNumber(statistics?.totalMessages)}
            </div>
          </div>
        </Col>
        <Col span={6}>
          <div className="p-4">
            <div className="h5 font-weight-bold">Total Conversations</div>
            <div className="h4 text-info">
              {formatLocaleNumber(statistics?.totalConversations)}
            </div>
          </div>
        </Col>
      </Row>
    </Card>
  )
}

export default AIProjectDashboard
