// API Schema Types based on OpenAPI specification

export enum AgentCode {
  Master = 'orca-ai',
}

export enum ModelId {
  GPT4o = 'gpt-4o',
  GPT41 = 'gpt-4.1',
  GPT41Mini = 'gpt-4.1-mini',
}

export enum ArtefactValidationActionEnum {
  VALIDATE_NAME_REQUEST = '@@ARTEFACT/VALIDATE_NAME_REQUEST',
  VALIDATE_NAME_SUCCESS = '@@ARTEFACT/VALIDATE_NAME_SUCCESS',
  VALIDATE_NAME_FAILURE = '@@ARTEFACT/VALIDATE_NAME_FAILURE',
}

export interface ArtefactValidationState {
  [artefactKey: string]: {
    isValid: boolean
    loading: boolean
    error?: string
  }
}

export interface Agent {
  id: string
  name: string
  code: string
  role: string
  description?: string
  systemPrompt?: string
  additionalPrompt?: string
  modelId: string
  tools: string[]
  project?: string
  version?: string
  isActive?: boolean // Made optional in case API doesn't always return it
  status: number
  parentId?: string | null
  createdAt?: string
  updatedAt?: string
}

export interface AgentPromptUpdate extends Record<string, any> {
  agentCode: string
  additionalPrompt?: string
}

export interface Conversation {
  id: string
  title: string
  projectId: string
  userId: string
  promptTokens: number
  completionTokens: number
  totalCost?: number
  createdAt: string | Date
  updatedAt: string | Date
  messages: Message[]
}

export interface ConversationCreateRequest extends Record<string, any> {
  projectId: string
}

export enum StreamMessageEvent {
  UserMessageCreated = 'UserMessageCreated',
  MessageCreated = 'MessageCreated',
  MessageDelta = 'MessageDelta',
  MessageError = 'MessageError',
  MessageComplete = 'MessageComplete',
  ConversationNameGenerated = 'ConversationNameGenerated',
}

export interface IMessageStep extends Record<string, any> {
  id: string
  name: string
  args: { [key: string]: any }
  result: string
  done: boolean
}

export interface Message {
  id: string
  content: string
  reasoningContent?: string
  steps?: IMessageStep[]
  step?: IMessageStep
  references?: Reference[]
  isBot: boolean
  isLoading: boolean
  promptTokens?: number
  completionTokens?: number
  userId?: string
  replyTo?: string
  conversationId?: string
  createdAt: string | Date
  updatedAt: string | Date
}

export interface SendMessageRequest {
  conversationId: string
  content: string
  agentCode?: string
  mode?: string
  sse: boolean
  references?: Reference[]
}

export interface UpdateMessageContentRequest {
  conversationId: string
  messageId: string
  content: string
}

export interface MessageEvent
  extends Partial<Message>,
    Partial<MessageEventError> {
  event: StreamMessageEvent
}

export interface MessageEventError {
  id: string
  event: StreamMessageEvent
  error: string
}

export enum ReferenceType {
  File = 'File',
  LegacyFile = 'LegacyFile',
  Actor = 'Actor',
  MeetingMinute = 'MeetingMinute',
  UserRequirement = 'UserRequirement',
  Workflow = 'Workflow',
  UseCase = 'UseCase',
  Object = 'Object',
  Screen = 'Screen',
  Literal = 'Literal',
  PreviousMessage = 'PreviousMessage',
}

export interface Reference {
  id: string // The id of the reference
  type: ReferenceType // The type of the reference
  name: string // The name of the reference
  chunkId?: string // The chunk id of the reference (optional)
  content?: string // The content of the reference (optional, default "")
}

export interface InputState {
  content: string
  references: Reference[]
  mode?: string
}

export interface AIAssistantState {
  isOpen: boolean
  isLoading: boolean
  currentConversation?: Conversation
  isTyping: boolean
  suggestions: string[]
  isAiPanelOpen: boolean
  activeTab: 'ai-chat' | 'settings' | null
  isCanvasOnlyMode: boolean
  canvasContent: string
  currentEditingMessage: string | null // Track which message is being edited
  input: InputState
}

export enum SavingStatus {
  Saving = 'saving',
  Saved = 'saved',
  Error = 'error',
}

export enum ArtefactType {
  Actor = 'Actor',
  Object = 'Object',
  UseCase = 'UseCase',
  Workflow = 'Workflow',
  Screen = 'Screen',
  UserRequirement = 'UserRequirement',
}

export interface ParseRequest extends Record<string, any> {
  content: string
  userRequest?: string
  projectId?: string | null
  context?: Record<string, any>
}

export interface ParsedArtefact {
  id?: string
  name: string
  description: string
  artefactType: ArtefactType

  /**
   * Non-persisted state for UI purposes
   */
  path?: string
  status?: SavingStatus
  error?: string
  validationErrors?: Record<string, string>
  isSelected?: boolean
  isExisted?: boolean
  isHLR?: boolean
}

export interface ParsedRequirement extends ParsedArtefact {
  type: number // UserRequirement type (1: Original, 2: Change Request)
  priority: number // UserRequirement priority (1: Low, 2: Medium, 3: High, 4: Urgent)
}

export interface ScreenSpecification extends ParsedArtefact {
  components: Record<string, string>[]
  access: string
  actors: string[]
  objects: string[]
  useCases: string[]

  actorIds?: number[]
  objectIds?: number[]
  useCaseIds?: number[]
}

export interface UseCaseSpecification extends ParsedArtefact {
  trigger: string
  preCondition: string
  postCondition: string
  mainFlow: string
  alternativeFlow: string
  exceptionFlow: string
  businessRules: {
    name: string
    code: string
    step: number
    content: string
  }[]
  actors: string[]
  objects: string[]
  userRequirements: string[]
  otherRequirements: string[]
  actorIds?: number[]
  objectIds?: number[]
}

export interface ArtefactParseResult {
  actors: ParsedArtefact[]
  objects: ParsedArtefact[]
  workflows: ParsedArtefact[]
  screens: ScreenSpecification[]
  useCases: UseCaseSpecification[]
  userRequirements: ParsedRequirement[]
}

// File Upload Types
export interface FileCreateRequest extends Record<string, any> {
  name: string
  contentType: string
  size: number
}

export interface FileCreateResponse {
  id: string
  name: string
  size?: number
  contentType?: string
  userId?: string
  status?: FileStatus
  createdAt: string
  updatedAt: string
  presignedUrl: string
}

export enum FileStatus {
  created = 'created',
  uploaded = 'uploaded',
  processing = 'processing',
  processed = 'processed',
  error = 'error',
}

export interface IFIle {
  id: string
  name: string
  size?: number
  contentType?: string
  userId?: string
  status?: FileStatus
  createdAt?: string
  updatedAt?: string
}

export interface IFileWithUploadState extends IFIle, Reference {
  uploadProgress?: number
  status?: FileStatus
  type: ReferenceType.File
  error?: string
}

export interface ITokenUsageRequest extends Record<string, any> {
  projectId?: string
  userId?: string
  startDate?: string
  endDate?: string
}

export interface ITokenUsageResponse {
  totalConversations: number
  totalMessages: number
  costPer1mPromptTokens: Record<string, number>
  costPer1mCompletionTokens: Record<string, number>
  costPer1mCachedTokens: Record<string, number>
  promptTokens: Record<string, number>
  completionTokens: Record<string, number>
  cachedPromptTokens: Record<string, number>
}

export enum AIActionTypes {
  // UI Actions
  SET_CURRENT_CONVERSATION = 'SET_CURRENT_CONVERSATION',
  SET_CURRENT_CONVERSATION_MESSAGES = 'SET_CURRENT_CONVERSATION_MESSAGES',

  FETCH_LAST_CONVERSATION_OR_CREATE = 'FETCH_LAST_CONVERSATION_OR_CREATE',

  CREATE_CONVERSATION = 'CREATE_CONVERSATION',

  // Message Actions
  SEND_MESSAGE_REQUEST = 'SEND_MESSAGE_REQUEST',
  SEND_MESSAGE_SUCCESS = 'SEND_MESSAGE_SUCCESS',

  UPDATE_MESSAGE_CONTENT_REQUEST = 'UPDATE_MESSAGE_CONTENT_REQUEST',
  UPDATE_MESSAGE_CONTENT_SUCCESS = 'UPDATE_MESSAGE_CONTENT_SUCCESS',

  RECEIVE_AI_RESPONSE = 'RECEIVE_AI_RESPONSE',

  // Streaming Actions
  START_STREAMING_RESPONSE = 'START_STREAMING_RESPONSE',
  RECEIVE_STREAMING_CHUNK = 'RECEIVE_STREAMING_CHUNK',
  END_STREAMING_RESPONSE = 'END_STREAMING_RESPONSE',
  STREAMING_ERROR = 'STREAMING_ERROR',

  // Typing Actions
  SET_AI_TYPING = 'SET_AI_TYPING',

  // Suggestions Actions
  SET_SUGGESTIONS = 'SET_SUGGESTIONS',
  CLEAR_SUGGESTIONS = 'CLEAR_SUGGESTIONS',

  // Panel Management Actions
  TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',
  TOGGLE_SETTINGS_PANEL = 'TOGGLE_SETTINGS_PANEL',
  SET_ACTIVE_TAB = 'SET_ACTIVE_TAB',
  CLOSE_ALL_PANELS = 'CLOSE_ALL_PANELS',
  ENTER_CANVAS_ONLY_MODE = 'ENTER_CANVAS_ONLY_MODE',
  EXIT_CANVAS_ONLY_MODE = 'EXIT_CANVAS_ONLY_MODE',
  SET_CURRENT_EDITING_MESSAGE = 'SET_CURRENT_EDITING_MESSAGE',
  SET_LOADING = 'SET_LOADING',
  SET_INPUT_STATE = 'SET_INPUT_STATE',
  UPDATE_FILE_STATE = 'UPDATE_FILE_STATE',
  REMOVE_FILE = 'REMOVE_FILE',
  ADD_REFERENCE = 'ADD_REFERENCE',
  REMOVE_REFERENCE = 'REMOVE_REFERENCE',
}
