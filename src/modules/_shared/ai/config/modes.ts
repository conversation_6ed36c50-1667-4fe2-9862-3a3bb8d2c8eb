/**
 * AI Chat Mode Configuration
 *
 * This file defines the available AI chat modes that users can select
 * via dropdown or slash commands. Each mode has different behavior
 * and can add prefixes to user messages.
 */

/**
 * Interface defining the structure of a chat mode
 */
export interface ChatMode {
  /** Unique identifier for UI selection and state management */
  id: string
  /** Value sent to the API (multiple modes can share the same API value) */
  value: string
  /** Display name shown to users in UI */
  label: string
  /** Description explaining what this mode does */
  description: string
  /** Text automatically prepended to user messages in this mode */
  textPrefix: string
}

/**
 * Available chat modes for the AI assistant
 *
 * Note: Multiple modes can have the same 'value' (API parameter) but different
 * 'id' (UI identifier) and 'textPrefix' (message modification)
 */
export const CHAT_MODES: ChatMode[] = [
  {
    id: 'coordinate',
    value: 'coordinate',
    label: 'coordinate',
    description: 'Default AI assistant Coordination mode',
    textPrefix: '', // No prefix for coordinate mode
  },
  {
    id: 'route',
    value: 'route',
    label: 'route',
    description: 'Receive directly result from other worker agents',
    textPrefix: '', // No prefix for general route mode
  },
  {
    id: 'route-user-requirement-agent',
    value: 'route', // Same API value as general route
    label: 'route-user-requirement-agent',
    description: 'Mention to route directly to User Requirement Agent',
    textPrefix: 'Use the User Requirement Agent,',
  },
  {
    id: 'route-high-level-requirement-agent',
    value: 'route', // Same API value as general route
    label: 'route-high-level-requirement-agent',
    description: 'Mention to route directly to High-Level Requirement Agent',
    textPrefix: 'Use the High-Level Requirement Agent,',
  },
  {
    id: 'route-use-case-specification-agent',
    value: 'route', // Same API value as general route
    label: 'route-use-case-specification-agent',
    description: 'Mention to route directly to Use Case Specification Agent',
    textPrefix: 'Use the Use Case Specification Agent,',
  },
  {
    id: 'route-screen-description-agent',
    value: 'route', // Same API value as general route
    label: 'route-screen-description-agent',
    description: 'Mention to route directly to Screen Description Agent',
    textPrefix: 'Use the Screen Description Agent,',
  },
]

/**
 * Helper function to get mode configuration by ID
 * Used for UI operations like displaying mode info or getting prefixes
 *
 * @param id - The unique mode identifier
 * @returns The mode configuration or undefined if not found
 */
export const getModeById = (id: string): ChatMode | undefined => {
  return CHAT_MODES.find(mode => mode.id === id)
}

/**
 * Helper function to get mode configuration by API value
 * Used when you need to find a mode based on the value sent to/from the API
 * Note: This returns the first mode with the matching value
 *
 * @param value - The API value to search for
 * @returns The first mode configuration with matching value or undefined
 */
export const getModeByValue = (value: string): ChatMode | undefined => {
  return CHAT_MODES.find(mode => mode.value === value)
}

/**
 * Helper function to get the text prefix for a mode
 * Useful for adding prefixes to user messages
 *
 * @param modeId - The mode ID to get prefix for
 * @returns The text prefix or empty string if mode not found
 */
export const getModeTextPrefix = (modeId: string): string => {
  const modeConfig = getModeById(modeId)
  return modeConfig?.textPrefix || ''
}

/**
 * Utility function to handle mode switching with prefix management
 * Removes any existing mode prefix from content and adds the new mode's prefix
 *
 * @param newMode - The mode to switch to
 * @param currentContent - The current text content
 * @returns Object with the new mode ID and updated content
 */
export const switchMode = (newMode: ChatMode, currentContent: string) => {
  // Remove any existing prefix from current content
  let cleanContent = currentContent
  CHAT_MODES.forEach(existingMode => {
    if (existingMode.textPrefix && cleanContent.startsWith(existingMode.textPrefix)) {
      cleanContent = cleanContent.substring(existingMode.textPrefix.length)
    }
  })

  // Add new prefix if the selected mode has one
  const newContent = newMode.textPrefix
    ? newMode.textPrefix + cleanContent
    : cleanContent

  return {
    mode: newMode.id,
    content: newContent
  }
}
