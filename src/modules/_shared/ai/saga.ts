import { Action } from '@reduxjs/toolkit'
import {
  all,
  call,
  fork,
  put,
  take,
  takeLatest,
} from 'redux-saga/effects'
import { eventChannel, EventChannel } from 'redux-saga'
import { extractProjectCode,  toast } from '@/helper/share'
import aiService from '@/services/ai.service'
import {
  createConversationRequest,
  endStreamingResponse,
  fetchLastConversationOrCreate,
  receiveAIResponse,
  receiveStreamingChunk,
  sendMessageRequest,
  sendMessageSuccess,
  setAITyping,
  setCurrentConversation,
  setCurrentConversationMessages, setLoading,
  startStreamingResponse,
  updateMessageContentRequest,
  updateMessageContentSuccess
} from './actions'
import {
  AgentCode,
  Message,
  MessageEvent,
  SendMessageRequest,
  UpdateMessageContentRequest,
  StreamMessageEvent,
} from './types'
import { preProcessUserMessage } from '@/modules/_shared/ai/utils/pre-process-user-message'
import { LAST_USER_MESSAGE } from '@/modules/_shared/ai/config'

// Default agent type and project configuration
const getDefaultAgentType = () => AgentCode.Master
enum EventType {
  Chunk,
  Complete,
  Error,
}

function createStreamingChannel(
  apiRequest: SendMessageRequest
): EventChannel<any> {
  return eventChannel((emitter) => {
    // Start the streaming request
    aiService.sendStreamingMessage(
      apiRequest,
      // onChunk callback
      (event: MessageEvent) => {
        emitter({
          type: EventType.Chunk,
          payload: event,
        })
      },
      // onComplete callback
      () => {
        emitter({
          type: EventType.Complete,
        })
      },
      // onError callback
      (error: Error) => {
        emitter({
          type: EventType.Error,
          payload: error,
        })
      }
    ).catch(() => {
      // Handle fetch event source errors silently
    })

    // Return unsubscribe function
    return () => { }
  })
}

// Streaming response handler saga
function* handleStreamingResponse(
  apiRequest: SendMessageRequest,
  streamId: string
) {
  try {
    const streamingChannel = yield call(createStreamingChannel, apiRequest)

    while (true) {
      const action = yield take(streamingChannel)

      if (action.type === EventType.Chunk) {
        const event = action.payload as MessageEvent
        yield put(receiveStreamingChunk(event))
      } else if (action.type === EventType.Complete) {
        yield put(
          endStreamingResponse({
            id: action?.payload?.id ?? streamId,
            event: StreamMessageEvent.MessageComplete,
          })
        )
        break
      } else if (action.type === EventType.Error) {
        toast.error(action.payload.error)
        break
      }
    }

    streamingChannel.close()
  } catch (error: any) {
    toast.error(error)
  }
}

function* handleCreateConversation(action: Action) {
  if (createConversationRequest.match(action)) {
    try {
      const request = action.payload
      const data = yield call(aiService.createConversation, request)
      yield put(setCurrentConversation(data))
    } catch (error: any) {
      toast.error(error)
    }
  }
}

function* handleSendMessage(action: Action) {
  if (sendMessageRequest.match(action)) {
    const request = action.payload as SendMessageRequest

    try {
      request.content = yield call(preProcessUserMessage, request.content)

      const userMessage: Message = {
        id: LAST_USER_MESSAGE,
        content: request.content,
        isBot: false,
        steps: [],
        references: request.references,
        isLoading: false,
        promptTokens: 0,
        completionTokens: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      yield put(sendMessageSuccess(userMessage))

      // Show AI typing indicator
      yield put(setAITyping(true))

      if (request.sse) {
        // Handle real streaming response with SSE
        const streamingId = `streaming_${Date.now()}`
        yield put(startStreamingResponse(streamingId))

        try {
          // Start the streaming saga
          yield fork(handleStreamingResponse, request, streamingId)
        } catch (streamError: any) {
          toast.error(streamError)
          yield put(setAITyping(false))
        }
      } else {
        // Handle regular response
        const data = yield call(aiService.sendMessage, request)
        yield put(receiveAIResponse(data))
      }
    } catch (error) {
      toast.error(error)
    }
  }
}

function* handleUpdateMessageContent(action: Action) {
  if (updateMessageContentRequest.match(action)) {
    try {
      const request = action.payload as UpdateMessageContentRequest
      const updatedMessage: Message = yield call(
        aiService.updateMessageContent,
        request.conversationId,
        request.messageId,
        request.content
      )
      yield put(updateMessageContentSuccess(updatedMessage))
    } catch (error) {
      toast.error(error)
    } finally {
      yield put(setLoading(false))
    }
  }
}

function* handleFetchLastConversationOrCreate(action: Action) {
  if (fetchLastConversationOrCreate.match(action)) {
    try {
      const { data: conversations } = yield call(
        aiService.listConversations,
        extractProjectCode(),
        50,
        0
      )

      if (conversations.length > 0) {
        yield put(setCurrentConversation(conversations[0]))
      } else {
        const request = {
          agentType: getDefaultAgentType(),
          projectId: extractProjectCode() ?? "",
        }
        yield put(createConversationRequest(request))
      }
    } catch (error) {
      toast.error(error)
    }
  }
}

function* handleSetCurrentConversation(action: Action) {
  if (setCurrentConversation.match(action)) {
    const messages: Message[] = yield call(
      aiService.listMessages,
      action.payload.id,
      50,
      0
    )
    yield put(setCurrentConversationMessages(messages))
  }
}

function* watchAIAssistantRequests() {
  yield takeLatest(
    fetchLastConversationOrCreate.type,
    handleFetchLastConversationOrCreate
  )
  yield takeLatest(createConversationRequest.type, handleCreateConversation)
  yield takeLatest(sendMessageRequest.type, handleSendMessage)
  yield takeLatest(updateMessageContentRequest.type, handleUpdateMessageContent)
  yield takeLatest(setCurrentConversation.type, handleSetCurrentConversation)
}

export default function* aiAssistantSaga() {
  yield all([fork(watchAIAssistantRequests)])
}

// export default function* artefactValidationSaga(){
//   yield takeLatest(validateArtefactName.type, validateNameExisted)
// }