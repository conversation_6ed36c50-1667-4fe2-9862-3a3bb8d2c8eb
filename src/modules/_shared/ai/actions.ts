import { createAction } from '@reduxjs/toolkit'
import {
  AIActionTypes,
  AIAssistantState,
  Conversation,
  ConversationCreateRequest,
  IFileWithUploadState,
  Message,
  MessageEvent,
  MessageEventError, Reference,
  SendMessageRequest,
  UpdateMessageContentRequest,
  ArtefactValidationActionEnum
} from './types'

export const validateArtefactName = createAction<{ artefactType: string, name: string, key: string }>(ArtefactValidationActionEnum.VALIDATE_NAME_REQUEST);
export const validateArtefactNameSuccess = createAction<{ key: string, isValid: boolean }>(ArtefactValidationActionEnum.VALIDATE_NAME_SUCCESS);
export const validateArtefactNameFailure = createAction<{ key: string, error: string }>(ArtefactValidationActionEnum.VALIDATE_NAME_FAILURE);

// UI Actions
export const setCurrentConversation = createAction<Conversation>(AIActionTypes.SET_CURRENT_CONVERSATION)
export const setCurrentConversationMessages = createAction<Message[]>(AIActionTypes.SET_CURRENT_CONVERSATION_MESSAGES)
export const fetchLastConversationOrCreate = createAction(AIActionTypes.FETCH_LAST_CONVERSATION_OR_CREATE)
export const createConversationRequest = createAction<ConversationCreateRequest>(AIActionTypes.CREATE_CONVERSATION)

// Message Actions
export const sendMessageRequest = createAction<SendMessageRequest>(AIActionTypes.SEND_MESSAGE_REQUEST)
export const sendMessageSuccess = createAction<Message>(AIActionTypes.SEND_MESSAGE_SUCCESS)

export const updateMessageContentRequest = createAction<UpdateMessageContentRequest>(AIActionTypes.UPDATE_MESSAGE_CONTENT_REQUEST)
export const updateMessageContentSuccess = createAction<Message>(AIActionTypes.UPDATE_MESSAGE_CONTENT_SUCCESS)

export const receiveAIResponse = createAction<Message>(AIActionTypes.RECEIVE_AI_RESPONSE)

// Streaming Actions
export const startStreamingResponse = createAction<string>(AIActionTypes.START_STREAMING_RESPONSE) // messageId
export const receiveStreamingChunk = createAction<MessageEvent>(AIActionTypes.RECEIVE_STREAMING_CHUNK)
export const endStreamingResponse = createAction<MessageEvent>(AIActionTypes.END_STREAMING_RESPONSE)
export const streamingError = createAction<MessageEventError>(AIActionTypes.STREAMING_ERROR)

// Typing Actions
export const setAITyping = createAction<boolean>(AIActionTypes.SET_AI_TYPING)

// Suggestions Actions
export const setSuggestions = createAction<string[]>(AIActionTypes.SET_SUGGESTIONS)
export const clearSuggestions = createAction(AIActionTypes.CLEAR_SUGGESTIONS)

// Panel Management Actions
export const toggleAIChatPanel = createAction(AIActionTypes.TOGGLE_AI_CHAT_PANEL)
export const toggleAISettingsPanel = createAction(AIActionTypes.TOGGLE_SETTINGS_PANEL)
export const setActiveTab = createAction<'ai-chat' | 'settings' | null>(AIActionTypes.SET_ACTIVE_TAB)
export const closeAllPanels = createAction(AIActionTypes.CLOSE_ALL_PANELS)
export const enterCanvasOnlyMode = createAction<string>(AIActionTypes.ENTER_CANVAS_ONLY_MODE)
export const exitCanvasOnlyMode = createAction(AIActionTypes.EXIT_CANVAS_ONLY_MODE)
export const setCurrentEditingMessage = createAction<string | null>(AIActionTypes.SET_CURRENT_EDITING_MESSAGE)
export const setLoading = createAction<boolean>(AIActionTypes.SET_LOADING)
export const setInputState = createAction<Partial<AIAssistantState['input']>>(AIActionTypes.SET_INPUT_STATE)
export const updateFileState = createAction<Partial<IFileWithUploadState>>(AIActionTypes.UPDATE_FILE_STATE)
export const removeFile = createAction<string>(AIActionTypes.REMOVE_FILE) // fileId
export const addReference = createAction<Reference>(AIActionTypes.ADD_REFERENCE)
export const removeReference = createAction<string>(AIActionTypes.REMOVE_REFERENCE)
