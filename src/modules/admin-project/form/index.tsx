import { CheckOutlined, PlusOutlined } from '@ant-design/icons'
import {
    <PERSON><PERSON>, Col, DatePicker, Form, Input, Modal, Row, Select,
    Space, Spin, Switch, Table, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { useEffect, useState } from 'react'
import intl from '../../../config/locale.config'
import { BUTTON_TYPE, DATE_FORMAT, SCREEN_MODE } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import AppCommonService from '../../../services/app.service'
import { DefaultProductData, DefaultResourceData } from '../type'
import { ShowMessgeAdditionalSubmit } from '../../../helper/share'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select


interface CommonCommitteeFormModalProps {
    code?: string
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
    onFinish?: () => void | null
    onDismiss: () => void | null
}

const AdminProjectForm = ({ code, screenMode, onFinish, onDismiss }: CommonCommitteeFormModalProps) => {
    const [form] = Form.useForm()
    const [dataSource, setDataSource] = useState<any>([])
    const [dataSourceProduct, setDataSourceProduct] = useState<any>([])
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'common_create_project.page_title' : 'common_update_project.page_title' });
        return () => {
            form.resetFields()
        }
    }, [])

    useEffect(() => {
        if (code && screenMode === SCREEN_MODE.EDIT) {
            AppCommonService.getDetailProject(code).then((e) => {
                setDataSource(e.data.resources)
                setDataSourceProduct(e.data.products)
                form.setFieldsValue({
                    "code": e.data.code,
                    "name": e.data.name,
                    "description": e.data.description,
                    "group": e.data.group,
                    "manager": e.data.manager,
                    "status": e.data.status,
                    "customer": e.data.customer,
                    "rank": e.data.rank,
                    "startDate": e.data?.startDate ? moment(new Date(e.data?.startDate)) : null,
                    "endDate": e.data?.endDate ? moment(new Date(e.data?.endDate)) : null,
                    "contractType": e.data.contractType,
                    "industry": e.data.industry,
                    "category": e.data.category,
                    "scope": e.data.scope,
                    "methodology": e.data?.methodology,
                    "resourceFromDate": e.data.resources?.map((i) => moment(new Date(i.fromDate))),
                    "resourceName": e.data.resources?.map((i) => i.username),
                    "resourcePlanned": e.data.resources?.map((i) => i.planned),
                    "resourceRole": e.data.resources?.map((i) => i.roles),
                    "resourceToDate": e.data.resources?.map((i) => moment(new Date(i.toDate))),
                    "productName": e.data.products?.map((i) => i.name),
                    "phase": e.data.products?.map((i) => i.phase),
                    "productType": e.data.products?.map((i) => i.productType),
                    "version": e.data.products?.map((i) => i.version)
                })
            })
        }
    }, [code, screenMode])

    const updateRecordResourceProperty = (order, partialRecord) => {
        const list = dataSource.map((record, index) => {
            let nxtRecord = record;

            if (index === order) {
                nxtRecord = {
                    ...nxtRecord,
                    ...partialRecord,
                };
            }
            return nxtRecord;
        })
        setDataSource(list)
    };

    const updateRecordProductProperty = (order, partialRecord) => {
        const list = dataSourceProduct.map((record, index) => {
            let nxtRecord = record;

            if (index === order) {
                nxtRecord = {
                    ...nxtRecord,
                    ...partialRecord,
                };
            }
            return nxtRecord;
        })
        setDataSourceProduct(list)
    };
    const handleChangeResourceDate = (order, property, value) => {
        const newData = Object.assign([], dataSource)
        newData.forEach((record, index) => {
            if (index === order) {
                record[property] = value._d
            }
        })
        setDataSource(newData)

    }

    const handleChangeProductDate = (order, property, value) => {
        const newData = Object.assign([], dataSourceProduct)
        newData.forEach((record, index) => {
            if (index === order) {
                record[property] = value._d
            }
        })
        setDataSourceProduct(newData)

    }

    const handleChangeResourceText = (order, prop) => ({ target }) => {
        updateRecordResourceProperty(order, { [prop]: target?.value });
    };

    const handleChangeProductText = (order, prop) => ({ target }) => {
        updateRecordProductProperty(order, { [prop]: target?.value });
    };

    const handleChangeListRole = (order, list) => {
        const newData = Object.assign([], dataSource)
        newData.forEach((record, index) => {
            if (index === order) {
                record.roles = list
            }
        })
        setDataSource(newData)
    }

    const setEditting = (order) => {
        const newData = Object.assign([], dataSource)
        newData.forEach((record, index) => {
            if (index === order) {
                record.editting = !record.editting
            }
        })
        setDataSource(newData);
    }

    const setEdittingProduct = (order) => {
        const newData = Object.assign([], dataSourceProduct)
        newData.forEach((record, index) => {
            if (index === order) {
                record.editting = !record.editting
            }
        })
        setDataSourceProduct(newData);
    }

    const deleteRow = (order) => {
        let currentSource: any = Object.assign([], dataSource)
        currentSource.splice(order, 1)
        form.getFieldValue("resourceName")?.splice(order, 1);
        form.getFieldValue("resourcePlanned")?.splice(order, 1);
        form.getFieldValue("resourceFromDate")?.splice(order, 1);
        form.getFieldValue("resourceToDate")?.splice(order, 1);
        form.getFieldValue("resourceRole")?.splice(order, 1);
        form.setFieldsValue({
            resourceName: form.getFieldValue("resourceName"),
            resourcePlanned: form.getFieldValue("resourcePlanned"),
            resourceFromDate: form.getFieldValue("resourceFromDate"),
            resourceToDate: form.getFieldValue("resourceToDate"),
            resourceRole: form.getFieldValue("resourceRole"),
        })
        setDataSource(currentSource)
    }

    const deleteRowProduct = (order) => {
        let currentSource: any = Object.assign([], dataSourceProduct)
        currentSource.splice(order, 1)
        form.getFieldValue("productName")?.splice(order, 1);
        form.getFieldValue("phase")?.splice(order, 1);
        form.getFieldValue("productType")?.splice(order, 1);
        form.getFieldValue("version")?.splice(order, 1);
        form.setFieldsValue({
            productName: form.getFieldValue("productName"),
            phase: form.getFieldValue("phase"),
            productType: form.getFieldValue("productType"),
            version: form.getFieldValue("version"),
        })
        setDataSourceProduct(currentSource)
    }

    const checkValidateFields = async (callback: () => void) => {
        try {
            await form.validateFields();
            callback();
        } catch (error) {
            // handle validate fields
        }
    }

    const columns: any = [
        {
            title: 'Name',
            dataIndex: 'username',
            width: '15%',
            render: (text, record: any, order) => {
                return record?.editting ? <>
                    <Form.Item
                        name={["resourceName", order]}
                        rules={[{ required: true, message: 'Please input name.' }]}
                    >
                        <Input value={text} width={'100%'} onChange={handleChangeResourceText(order, 'username')} maxLength={255} />

                    </Form.Item>
                </>
                    : text
            }
        },
        {
            title: 'Planned',
            dataIndex: 'planned',
            width: '7.5%',
            align: 'center',
            render: (text, record, index) => {
                return record?.editting ? <>
                    <Form.Item
                        name={["resourcePlanned", index]}
                        rules={[{ required: true, message: 'Please input planned.' }]}
                    >
                        <Input
                            style={{ textAlign: 'center' }}
                            maxLength={4}
                            value={text} onChange={
                                handleChangeResourceText(index,
                                    "planned")
                            } />
                    </Form.Item>
                </>
                    : text
            }
        },
        {
            title: 'From Date',
            dataIndex: 'fromDate',
            width: '15%',
            render: (text, record: any, order) => {
                return record?.editting ?
                    <Form.Item
                        name={["resourceFromDate", order]}
                        rules={[{ required: true, message: 'Please input from date.' }]}
                    >
                        <DatePicker style={{ width: '100%' }} value={text ? moment(new Date(text)) : null} onChange={(e: any) => handleChangeResourceDate(order, 'fromDate', e)} format={[DATE_FORMAT]} />

                    </Form.Item>
                    : (text ? moment(text).format(DATE_FORMAT) : '')
            }
        },
        {
            title: 'End Date',
            dataIndex: 'toDate',
            width: '15%',
            render: (text, record: any, order) => {
                return record?.editting ?
                    <Form.Item
                        name={["resourceToDate", order]}
                        rules={[{ required: true, message: 'Please input end date.' }]}
                    >
                        <DatePicker value={text ? moment(new Date(text)) : null} onChange={(e: any) => handleChangeResourceDate(order, 'toDate', e)} style={{ width: '100%' }} format={[DATE_FORMAT]} />

                    </Form.Item> : (text ? moment(text).format(DATE_FORMAT) : '')
            }
        },
        {
            title: 'Role',
            dataIndex: 'roles',
            render: (text, record: any, order) => {
                return record?.editting ? <Form.Item
                    name={["resourceRole", order]}
                    rules={[{ required: true, message: 'Please select role.' }]}
                >
                    <Select mode='multiple' optionLabelProp="label" style={{ width: '100%' }} value={text} onChange={(e) => { handleChangeListRole(order, e) }} defaultValue={text}>
                        <Option key={"PM"} value="PM">PM</Option>
                        <Option key={"BA"} value="BA">BA</Option>
                        <Option key={"DEV"} value="DEV">DEV</Option>
                        <Option key={"TEST"} value="TEST">TEST</Option>
                        <Option key={"QA"} value="QA">QA</Option>
                        <Option key={"Customer"} value="Customer">CUSTOMER</Option>
                        <Option key={"BA Lead"} value="BA Lead">BA Lead</Option>
                    </Select>
                </Form.Item> : text.join(', ')
            }
        },
        {
            title: intl.formatMessage({ id: 'common.table.column.action' }),
            dataIndex: 'action',
            width: '5%',
            align: 'center',
            render: (text, record: any, index: number) => {
                return (
                    <Space size='small'>
                        {
                            record?.editting ? <Button icon={<CheckOutlined name="EditCustomIcon" />} type="link" onClick={() => checkValidateFields(() => setEditting(index))}></Button>
                                : <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={() => setEditting(index)}></Button>
                        }
                        {
                            !record?.id ? <Button
                                type="text"
                                icon={<CustomSvgIcons name="DeleteCustomIcon" />}
                                onClick={() => deleteRow(index)}
                            /> : <></>
                        }
                        {
                            record?.id ?
                                <DeleteButton
                                    type={BUTTON_TYPE.ICON}
                                    content={intl.formatMessage(
                                        { id: 'CFD_7' },
                                        { artefact_type: intl.formatMessage({ id: 'project.config.project' }) }
                                    )}
                                    okCB={() => deleteRow(index)}
                                    confirmButton={intl.formatMessage({ id: 'common.action.ok' })}
                                /> : <></>
                        }
                    </Space>
                )
            },
        },

    ]

    const columnsProduct: any = [
        {
            title: 'Name',
            dataIndex: 'name',
            width: '15%',
            align: 'center',
            render: (text, record, index) => {
                return record?.editting ? <Form.Item
                    name={["productName", index]}
                    rules={[{ required: true, message: 'Please input name.' }]}
                >
                    <Input
                        maxLength={255}
                        defaultValue={text} onChange={
                            handleChangeProductText(index,
                                "name")
                        } />

                </Form.Item>
                    : text
            }
        },
        {
            title: 'Phase',
            dataIndex: 'phase',
            width: '5%',
            render: (text, record: any, order) => {
                return record?.editting ? <>
                    <Form.Item
                        name={["phase", order]}
                        rules={[{ required: true, message: 'Please input phase.' }]}
                    >
                        <Input value={text} width={'100%'} onChange={handleChangeProductText(order, 'phase')} maxLength={255} />
                    </Form.Item>
                </>
                    : text
            }
        },
        {
            title: 'Product Type',
            dataIndex: 'productType',
            width: '12%',
            render: (text, record: any, order) => {
                return record?.editting ? <>
                    <Form.Item
                        name={["productType", order]}
                        rules={[{ required: true, message: 'Please input product type.' }]}
                    >
                        <Input value={text} width={'100%'} onChange={handleChangeProductText(order, 'productType')} maxLength={255} />
                    </Form.Item>
                </>
                    : text
            }
        },
        {
            title: 'Version',
            dataIndex: 'version',
            width: '5%',
            render: (text, record: any, order) => {
                return record?.editting ? <>
                    <Form.Item
                        name={["version", order]}
                        rules={[{ required: true, message: 'Please input version.' }]}
                    >
                        <Input value={text} width={'100%'} onChange={handleChangeProductText(order, 'version')} maxLength={255} />

                    </Form.Item>
                </>
                    : text
            }
        },
        {
            title: 'Deliverable',
            dataIndex: 'isDeliverable',
            render: (text, record: any, order) => {
                return record?.editting ? <>
                    <Switch defaultChecked={text} onChange={(checked: boolean) => handleChangeProductText(order, 'isDeliverable')({ target: { value: checked } })} />
                </>
                    : <Switch defaultChecked={text} disabled />
            }
        },
        {
            title: 'Final',
            dataIndex: 'isFinal',
            render: (text, record: any, order) => {
                return record?.editting ? <>
                    <Switch defaultChecked={text} onChange={(checked: boolean) => handleChangeProductText(order, 'isFinal')({ target: { value: checked } })} />
                </>
                    : <Switch defaultChecked={text} disabled />
            }
        },
        {
            title: 'Planned Release',
            dataIndex: 'plannedRelease',
            width: '15%',
            render: (text, record: any, order) => {
                return record?.editting ? <DatePicker value={text ? moment(new Date(text)) : null} onChange={(e: any) => handleChangeProductDate(order, 'plannedRelease', e)} style={{ width: '100%' }} format={[DATE_FORMAT]} /> : (text ? moment(text).format(DATE_FORMAT) : '')
            }
        },
        {
            title: 'Re Planned',
            dataIndex: 'rePlanned',
            width: '15%',
            render: (text, record: any, order) => {
                return record?.editting ? <DatePicker value={text ? moment(new Date(text)) : null} onChange={(e: any) => handleChangeProductDate(order, 'rePlanned', e)} style={{ width: '100%' }} format={[DATE_FORMAT]} /> : (text ? moment(text).format(DATE_FORMAT) : '')
            }
        },
        {
            title: 'Actual Release',
            dataIndex: 'actualRelease',
            width: '15%',
            render: (text, record: any, order) => {
                return record?.editting ? <DatePicker value={text ? moment(new Date(text)) : null} onChange={(e: any) => handleChangeProductDate(order, 'actualRelease', e)} style={{ width: '100%' }} format={[DATE_FORMAT]} /> : (text ? moment(text).format(DATE_FORMAT) : '')
            }
        },
        {
            title: intl.formatMessage({ id: 'common.table.column.action' }),
            dataIndex: 'action',
            width: '5%',
            align: 'center',
            render: (text, record: any, index: number) => {
                return (
                    <Space size='small'>
                        {
                            record?.editting ? <Button icon={<CheckOutlined name="EditCustomIcon" />} type="link" onClick={() => checkValidateFields(() => setEdittingProduct(index))}></Button>
                                : <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={() => setEdittingProduct(index)}></Button>
                        }
                        {
                            !record?.id ? <Button
                                type="text"
                                icon={<CustomSvgIcons name="DeleteCustomIcon" />}
                                onClick={() => deleteRowProduct(index)}
                            /> : <></>
                        }
                        {
                            record?.id ?
                                <DeleteButton
                                    type={BUTTON_TYPE.ICON}
                                    content={intl.formatMessage(
                                        { id: 'CFD_7' },
                                        { artefact_type: intl.formatMessage({ id: 'project.config.product' }) }
                                    )}
                                    okCB={() => deleteRowProduct(index)}
                                    confirmButton={intl.formatMessage({ id: 'common.action.ok' })}
                                /> : <></>
                        }
                    </Space>
                )
            },
        },

    ]

    const onSubmit = debounce((values: any, st?: string) => {
        setLoading(true);
        let requestData: any = {
            "code": values.code,
            "name": values.name,
            "description": values.description,
            "group": values.group,
            "manager": values.manager,
            "status": values.status,
            "customer": values.customer,
            "rank": values.rank,
            "startDate": values?.startDate?._d,
            "endDate": values?.endDate?._d,
            "contractType": values.contractType,
            "industry": values.industry,
            "category": values.category,
            "scope": values.scope,
            "methodology": values.methodology,
            "resources": dataSource,
            "products": dataSourceProduct,
        }
        screenMode === SCREEN_MODE.CREATE ?
            AppCommonService.createProject(requestData).then((e) => {
                onDismiss();
                setLoading(false);
            }).catch((e) => {
                setLoading(false);
                ShowMessgeAdditionalSubmit('EMSG_7', 'common.action.code');
            })
            : AppCommonService.updateProject(code, requestData).then((e) => {
                onDismiss();
                setLoading(false);
            }).catch((e) => {
                setLoading(false);
            })
    }, 500)

    const handleAddmember = () => {
        const newData = Object.assign([], dataSource)
        newData.push({ ...DefaultResourceData, toDate: null, fromDate: null })
        setDataSource(newData)
    }

    const handleAddProduct = () => {
        setDataSourceProduct([...dataSourceProduct, {
            ...DefaultProductData, plannedRelease: null,
            rePlanned: null,
            actualRelease: null,
        }])
    }

    const renderLabelRequired = (fieldName: string) => {
        return (
            <div className="field-required">{fieldName}</div>
        )
    }

    // disable end date less than start date
    const disabledEndDate = (current) => {
        return current && current < moment(form.getFieldValue("startDate")).endOf('day');
    };
    

    return (
        <Spin spinning={loading}>
            <Form
                form={form}
                onFinish={onSubmit}
                labelCol={{
                    span: 2,
                    offset: 0,
                }}
                scrollToFirstError={{ block: 'center' }}
            >
                <div className='rq-modal-header'>
                    <LavPageHeader
                        showBreadcumb={false}
                        title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'common_create_project.page_title' : 'common_update_project.page_title' })}
                    >
                        <Space size="small">
                            <Button onClick={() => onDismiss()}>
                                {intl.formatMessage({ id: 'common.action.close' })}
                            </Button>

                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button
                                    className="success-btn"
                                    htmlType="submit"
                                >
                                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.create' : 'common.action.save' })}
                                </Button>
                            </Form.Item>
                        </Space>
                    </LavPageHeader>
                </div>

                <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                    <Row gutter={[10, 10]}>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Code')}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="code"
                                    rules={[{ required: true, message: 'Please input field code.' }]}
                                >
                                    <Input disabled={screenMode === SCREEN_MODE.EDIT ? true : false} maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired(intl.formatMessage({ id: 'common.label.name' }))}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="name"
                                    rules={[{ required: true, message: 'Please input field name.' }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>

                        <Col span={24}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Description')}
                                labelSpan={3}
                                controlSpan={21}
                            >
                                <Form.Item
                                    name="description"
                                    rules={[{ required: true, message: 'Please input field description.' }]}
                                >
                                    <Input.TextArea />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Group')}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="group"
                                    rules={[{ required: true, message: 'Please input field group.' }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>

                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Project Manager')}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="manager"
                                    rules={[{ required: true, message: 'Please input field project manager.' }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Status')}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="status"
                                    rules={[{ required: true, message: 'Please input field status.' }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>


                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Customer')}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="customer"
                                    rules={[{ required: true, message: 'Please input field customer.' }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Rank')}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="rank"
                                    rules={[{ required: true, message: 'Please input field rank.' }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <Row justify='space-between' gutter={[10, 10]}>
                                <Col span={12}>
                                    <FormGroup inline labelSpan={12} controlSpan={12} label={renderLabelRequired('Start Date')}>
                                        <Form.Item validateTrigger="onBlur" name="startDate" rules={[{ required: true, message: 'Please input field start date.' }]}>
                                            <DatePicker style={{ width: '100%' }} format={[DATE_FORMAT]} />
                                        </Form.Item>
                                    </FormGroup>
                                </Col>
                                <Col span={12}>
                                    <FormGroup isCustomLabel inline labelSpan={12} controlSpan={12} label={renderLabelRequired('End Date')}>
                                        <Form.Item validateTrigger="onBlur" name="endDate" rules={[{ required: true, message: 'Please input field end date.' }]}>
                                            <DatePicker disabledDate={disabledEndDate} style={{ width: '100%' }} format={[DATE_FORMAT]} />
                                        </Form.Item>
                                    </FormGroup>
                                </Col>
                            </Row>
                        </Col>
                        <Col span={12}>

                            <FormGroup
                                inline
                                label='Contract Type'
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="contractType"
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label='Industry'
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="industry"
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label='Category'
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="category"
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label='Scope'
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="scope"
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col>
                        <Col span={12}>
                            <FormGroup
                                inline
                                label={renderLabelRequired('Methodology')}
                                labelSpan={6}
                                controlSpan={18}
                            >
                                <Form.Item
                                    name="methodology"
                                    rules={[{ required: true, message: 'Please input field methodology.' }]}
                                >
                                    <Select optionLabelProp="label" style={{ width: '100%' }}>
                                        <Option key={"Agile"} value="Agile">Agile</Option>
                                        <Option key={"Waterfall"} value="Waterfall">Waterfall</Option>
                                    </Select>
                                </Form.Item>
                            </FormGroup>
                        </Col>
                    </Row>

                    <Button icon={<PlusOutlined />} type='primary' onClick={() => handleAddmember()}>Add resource</Button>
                    <Table bordered pagination={false} columns={columns} dataSource={dataSource} />

                    <Button icon={<PlusOutlined />} type='primary' onClick={() => handleAddProduct()}>Add product</Button>
                    <Table bordered pagination={false} columns={columnsProduct} dataSource={dataSourceProduct}></Table>
                </Space>
            </Form>
        </Spin>
    )
}


export default AdminProjectForm
