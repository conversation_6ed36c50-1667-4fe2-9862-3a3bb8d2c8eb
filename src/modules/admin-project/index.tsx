import { PlusOutlined, ReloadOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, DATE_FORMAT, MESSAGE_TYPE, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE } from '../../constants'
import LavTable from '../../helper/component/lav-table'
import { ShowAppMessage, getColumnSearchProps } from '../../helper/share'
import AdminProjectForm from './form'
import AppCommonService from '../../services/app.service'

const AdminProject = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [projectCode, setProjectCode] = useState('')
  useEffect(() => {
    if(screenMode ==  SCREEN_MODE.VIEW)
      document.title = "Project List";
  }, [screenMode])
  const columns = [
    {
      title: intl.formatMessage({ id: 'project.table.project-code' }),
      dataIndex: "code",
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text, record) => {
        return <Typography.Text style={{ color: '#2979FF', cursor: 'pointer' }} onClick={() => {
          setProjectCode(record.code)
          setScreenMode(SCREEN_MODE.EDIT)
        }}>{text}</Typography.Text>
      }
    },
    {
      title: intl.formatMessage({ id: 'project.table.group' }),
      dataIndex: "group",
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-management' }),
      dataIndex: "manager",
      render: (text: string, record: any) => {
        return <Link style={{ textDecoration: 'underline' }} to={`/`}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-status' }),
      dataIndex: "status",
    },
    {
      title: intl.formatMessage({ id: 'project.table.customer' }),
      dataIndex: "customer"
    },
    {
      title: intl.formatMessage({ id: 'project.table.rank' }),
      dataIndex: "rank"
    },
    {
      title: intl.formatMessage({ id: 'project.table.start-date' }),
      dataIndex: "startDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: intl.formatMessage({ id: 'project.table.end-date' }),
      dataIndex: "endDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-contract-type' }),
      dataIndex: "contractType"
    },
    {
      title: intl.formatMessage({ id: 'project.table.industry' }),
      dataIndex: "industry"
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-catagory' }),
      dataIndex: "category"
    },
  ]

  const handleSyncData = () => {
    const jobName = 'SyncDataMartJob';
    AppCommonService.syncDataMart(jobName).then((res) => {
      ShowAppMessage(null, 'EMSG_42', 'common.message.sync-data');
    }).catch((error) => {
      ShowAppMessage(error, null, 'common.message.error-description');
    })
  };

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      <Space>
        <Button ghost={true}
          type='primary'
          className='lav-btn-create'
          icon={<PlusOutlined />}
          onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >Create Project
        </Button>
        <Button ghost={true}
          type='primary'
          icon={<ReloadOutlined />}
          onClick={handleSyncData} >Sync Data Mart
        </Button>
      </Space>
    )
    // return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <MessageForm onFinish={() => handleDataChange()} /> : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ? <LavTable
        showBreadcumb={false}
        title="Project List"
        artefact_type="common.artefact.message"
        apiUrl={API_URLS.ADMIN_PROJECT}
        columns={columns}
        isCommon
        artefactType={REQ_ARTEFACT_TYPE_ID.ADMIN_PROJECT}
        createComponent={CreateComponent}
      // deleteComponent={DeleteComponent}
      /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <AdminProjectForm screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <AdminProjectForm screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} code={projectCode} /> : <></>
      }
    </Space>
  )
}

export default AdminProject