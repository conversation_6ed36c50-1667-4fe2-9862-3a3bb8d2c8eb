import { <PERSON><PERSON>, Space, Typo<PERSON>, <PERSON>, Col, Spin } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../config/locale.config'
import { SCREEN_MODE } from '../../constants'
import AdminSupervisorAgentForm from './form'
import LavPageHeader from '../../helper/component/lav-breadcumb'
import { getDetailRequest, resetState } from './action'
import './style.css'

const AdminSupervisorAgent = () => {
  const dispatch = useDispatch()
  const adminSupervisorAgentState = useSelector((state: any) => state.AdminSupervisorAgent)
  const { isLoading = false, detail = null } = adminSupervisorAgentState || {}
  
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)

  useEffect(() => {
    if (screenMode === SCREEN_MODE.VIEW) {
      document.title = 'Supervisor Agent Details';
      dispatch(getDetailRequest({ project_id: '6864b7e4ecc61ecbe4a146f6' }));
    }
  }, [screenMode, dispatch]);

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      dispatch(resetState({}));
    }
  }, [dispatch])

  // Get agent data from Redux state
  const agentData = detail ? {
    name: detail.name || 'No name',
    description: detail.description || 'No description',
    instruction: detail.systemPrompt || 'No instruction',
    model: detail.model || 'No model'
  } : {
    name: 'Loading...',
    description: 'Loading...',
    instruction: 'Loading...',
    model: 'Loading...'
  };

  return (
    
    <Space direction="vertical" size="middle" className="admin-supervisor-agent__container">
      {screenMode === SCREEN_MODE.VIEW ? (
        <>
        <div className="admin-supervisor-agent__header">
          <LavPageHeader
            showBreadcumb={false}
            title={intl.formatMessage({ id: 'admin_supervisor_agent.page_title', defaultMessage: 'Supervisor Agent Details' })}
          >
            <Space size="small">
              <Button
                loading={isLoading}
                onClick={() => setScreenMode(SCREEN_MODE.EDIT)}
              >
                {intl.formatMessage({ id: 'common.action.update', defaultMessage: 'Update' })}
              </Button>
            </Space>
          </LavPageHeader>
        </div>
        {isLoading ? (
          <div className="admin-supervisor-agent__loading">
            <Spin size="large" />
            <div className="admin-supervisor-agent__loading-text">
              <Typography.Text>Loading agent data...</Typography.Text>
            </div>
          </div>
        ) : (
        <Row gutter={24}>
          {/* Left Column */}
          <Col span={12}>
            <div className="admin-supervisor-agent__field">
              <Typography.Text strong className="admin-supervisor-agent__field-label">Name:</Typography.Text>
              <div className="admin-supervisor-agent__field-value">
                {agentData.name}
              </div>
            </div>
            <div className="admin-supervisor-agent__field">
              <Typography.Text strong className="admin-supervisor-agent__field-label">Description:</Typography.Text>
              <div className="admin-supervisor-agent__field-value admin-supervisor-agent__field-value--description">
                {agentData.description}
              </div>
            </div>
            <div className="admin-supervisor-agent__field">
              <Typography.Text strong className="admin-supervisor-agent__field-label">Instruction:</Typography.Text>
              <div className="admin-supervisor-agent__field-value admin-supervisor-agent__field-value--instruction">
                {agentData.instruction}
              </div>
            </div>
            <div className="admin-supervisor-agent__field">
              <Typography.Text strong className="admin-supervisor-agent__field-label">Model:</Typography.Text>
              <div className="admin-supervisor-agent__field-value">
                {agentData.model}
              </div>
            </div>
          </Col>

          {/* Right Column */}
          <Col span={12}>
            <Typography.Text strong>Preview Result:</Typography.Text>
            <div className="admin-supervisor-agent__preview">
              <Typography.Text type="secondary">
                Preview will be displayed here based on the agent configuration.
              </Typography.Text>
            </div>
          </Col>
        </Row>
        )}
        </>) : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <AdminSupervisorAgentForm 
                                            screenMode="EDIT" 
                                            onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} 
                                            onFinish={() => {
                                              setScreenMode(SCREEN_MODE.VIEW);
                                              dispatch(getDetailRequest({ project_id: '6864b7e4ecc61ecbe4a146f6' })); // Refresh data after edit
                                            }} 
                                             /> : <></>
      }
    </Space>
  )
}

export default AdminSupervisorAgent;