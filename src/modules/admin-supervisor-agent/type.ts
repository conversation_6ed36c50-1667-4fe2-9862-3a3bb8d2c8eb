import { Agent } from '../../modules/_shared/ai'

export interface AdminSupervisorAgentState {
  isLoading: boolean,
  updateSuccess?: boolean,
  detail?: Agent | null,
}

export const defaultState: AdminSupervisorAgentState = {
  isLoading: false,
  updateSuccess: false,
}

export enum ActionEnum {
  RESET_STATE = 'admin-supervisor-agent/RESET_STATE',
  
  GET_DETAIL_REQUEST = 'admin-supervisor-agent/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = 'admin-supervisor-agent/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = 'admin-supervisor-agent/GET_DETAIL_FAILED',
  
  UPDATE_INSTRUCTIONS_REQUEST = 'admin-supervisor-agent/UPDATE_INSTRUCTIONS_REQUEST',
  UPDATE_INSTRUCTIONS_SUCCESS = 'admin-supervisor-agent/UPDATE_INSTRUCTIONS_SUCCESS',
  UPDATE_INSTRUCTIONS_FAILED = 'admin-supervisor-agent/UPDATE_INSTRUCTIONS_FAILED',
}
