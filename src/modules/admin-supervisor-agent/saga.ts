import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { ShowAppMessage } from '../../helper/share'
import {
  getDetailFailed, getDetailRequest,
  getDetailSuccess, updateInstructionsRequest, updateInstructionsSuccess, updateInstructionsFailed
} from './action'
import AIService from '../../services/ai.service'
import { AgentCode } from '../..//modules/_shared/ai'

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const data = yield call(AIService.getAgentDetails, AgentCode.Master);
      yield put(getDetailSuccess(data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      ShowAppMessage(err, null, 'Failed to fetch supervisor agent details');
    }
  }
}

function* handleUpdateInstructions(action: Action) {
  if (updateInstructionsRequest.match(action)) {
    try {
      const data = yield call(AIService.updateAgentInstruction, AgentCode.Master, action.payload);
      
      yield put(updateInstructionsSuccess(data));
      ShowAppMessage(null, 'Success', 'Supervisor agent instructions updated successfully');
    } catch (err: any) {
      yield put(updateInstructionsFailed(null));
      ShowAppMessage(err, null, 'Failed to update supervisor agent instructions');
    }
  }
}

function* watchGetDetail() {
  yield takeLatest(getDetailRequest.type, handleGetDetail)
}

function* watchUpdateInstructions() {
  yield takeLatest(updateInstructionsRequest.type, handleUpdateInstructions)
}

export default function* AdminSupervisorAgentSaga() {
  yield all([
    fork(watchGetDetail),
    fork(watchUpdateInstructions),
  ])
}
