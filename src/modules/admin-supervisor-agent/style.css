/* Admin Supervisor Agent Page Styles */
.admin-supervisor-agent__container {
  width: 100%;
  padding: 20px;
}

.admin-supervisor-agent__header {
  border-bottom: 2px solid #ebecf0;
  margin-bottom: 10px;
  position: sticky;
  top: 0;
  z-index: 99;
  background-color: white;
}

.admin-supervisor-agent__loading {
  text-align: center;
  padding: 50px;
}

.admin-supervisor-agent__loading-text {
  margin-top: 16px;
}

.admin-supervisor-agent__field {
  margin-bottom: 16px;
}

.admin-supervisor-agent__field-label {
  font-weight: bold;
  display: block;
  margin-bottom: 4px;
}

.admin-supervisor-agent__field-value {
  margin-top: 4px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  white-space: pre-wrap;
}

.admin-supervisor-agent__field-value--instruction {
  height: 50vh;
  overflow: auto;
  border: 1px solid #d9d9d9;
}

.admin-supervisor-agent__field-value--description {
  min-height: 80px;
}

.admin-supervisor-agent__preview {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  min-height: 200px;
  background-color: #f9f9f9;
}

/* Admin Supervisor Agent Form Styles */
.admin-supervisor-agent-form__container {
  padding: 0 10px 2px 0;
}

.admin-supervisor-agent-form__guidance {
  padding: 12px 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  margin-bottom: 16px;
}

.admin-supervisor-agent-form__guidance-text {
  font-size: 13px;
  color: #42526e;
}

.admin-supervisor-agent-form__field--disabled {
  background-color: #f5f5f5 !important;
  color: #666 !important;
}

.admin-supervisor-agent-form__field--instructions {
  height: 50vh !important;
  resize: none !important;
  background-color: #f5f5f5 !important;
  color: #666 !important;
}

.admin-supervisor-agent-form__model-select {
  width: 100%;
}

.admin-supervisor-agent-form__submit-container {
  margin-bottom: 0px;
}

/* Common field required styling */
.field-required {
  color: #ff4d4f;
}

.field-required::after {
  content: " *";
}
