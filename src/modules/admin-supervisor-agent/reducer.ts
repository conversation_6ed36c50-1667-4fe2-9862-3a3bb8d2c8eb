import { createReducer } from '@reduxjs/toolkit';
import {
  getDetailFailed, getDetailRequest,
  getDetailSuccess, resetState, updateInstructionsRequest, updateInstructionsSuccess, updateInstructionsFailed
} from './action';
import { defaultState, AdminSupervisorAgentState } from './type';

const initState: AdminSupervisorAgentState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          detail: state.detail
        });
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
      })

      .addCase(updateInstructionsRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateInstructionsSuccess, (state, action) => {
        state.isLoading = false
        state.updateSuccess = true
      })
      .addCase(updateInstructionsFailed, (state, action) => {
        state.isLoading = false
        state.updateSuccess = false
      })
  );
})

export default reducer;
