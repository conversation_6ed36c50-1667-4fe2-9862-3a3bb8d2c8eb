import { Button, Col, Form, Input, Row, Select, Space, Spin } from 'antd'
import { useEffect, useState, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import { ShowAppMessage } from '../../../helper/share'
import { updateInstructionsRequest, resetState, getDetailRequest } from '../action'
import { AgentCode, ModelId } from '../../../modules/_shared/ai/types'

const { Option } = Select

interface AdminSupervisorAgentFormProps {
    screenMode: string;
    onFinish: () => void;
    onDismiss: () => void;
}

const AdminSupervisorAgentForm = ({ screenMode, onFinish, onDismiss }: AdminSupervisorAgentFormProps) => {
    const [form] = Form.useForm()
    const [loading, setLoading] = useState<boolean>(false)
    const dispatch = useDispatch()
    const adminSupervisorAgentState = useSelector((state: any) => state.AdminSupervisorAgent)

    // Available models for selection
    const models = Object.values(ModelId)

    const loadAgentData = useCallback(async () => {
        setLoading(true)
        try {
            dispatch(getDetailRequest(""))
        } catch (error) {
            ShowAppMessage(error, null, 'Failed to load supervisor agent data')
        } finally {
            setLoading(false)
        }
    }, [dispatch])

    // Load agent data when in edit mode
    useEffect(() => {
        if (screenMode === 'EDIT') {
            loadAgentData()
        }
    }, [screenMode, loadAgentData])

    useEffect(() => {
        if (adminSupervisorAgentState.detail) {
            const agentDetail = adminSupervisorAgentState.detail
            form.setFieldsValue(agentDetail)
        }
    }, [adminSupervisorAgentState.detail, form])

    useEffect(() => {
        if (adminSupervisorAgentState.updateSuccess) {
            ShowAppMessage(null, 'Success', 'Supervisor agent instructions updated successfully')
            onFinish() // Close the form after successful update
        }
    }, [adminSupervisorAgentState.updateSuccess, onFinish])

    useEffect(() => {
        if (adminSupervisorAgentState.isLoading !== undefined) {
            setLoading(adminSupervisorAgentState.isLoading)
        }
    }, [adminSupervisorAgentState.isLoading])

    useEffect(() => {
        return () => {
            dispatch(resetState(null))
        }
    }, [dispatch])

    const onSubmit = async (values: any) => {
        try {
            if (isEditMode) {
                dispatch(updateInstructionsRequest({
                  ...values,
                  agentCode: AgentCode.Master,
                }))
            } else {
                ShowAppMessage(null, 'Info', 'Supervisor agent cannot be created - it already exists')
            }
        } catch (error) {
            ShowAppMessage(error, null, 'Failed to save supervisor agent')
        }
    }

    const renderLabelRequired = (fieldName: string) => {
        return <div className="field-required">{fieldName}</div>
    }

    const isEditMode = screenMode === 'EDIT'

    return (
        <Spin spinning={loading}>
            <Form
                form={form}
                onFinish={onSubmit}
                labelCol={{
                    span: 2,
                    offset: 0,
                }}
                scrollToFirstError={{ block: 'center' }}
            >
                <div className='rq-modal-header'>
                    <LavPageHeader
                        showBreadcumb={false}
                        title={intl.formatMessage({ 
                            id: 'admin_supervisor_agent.form_title',
                            defaultMessage: 'Update Supervisor Agent'
                        })}
                    >
                        <Space size="small">
                            <Button onClick={() => onDismiss()}>
                                {intl.formatMessage({ id: 'common.action.close' })}
                            </Button>

                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button
                                    className="success-btn"
                                    htmlType="submit"
                                    loading={loading}
                                >
                                    {intl.formatMessage({ 
                                        id: 'common.action.save' 
                                    })}
                                </Button>
                            </Form.Item>
                        </Space>
                    </LavPageHeader>
                </div>

                <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                    <Row gutter={24}>
                        <Col span={24}>
                            {/* Agent Name Field - Disabled */}
                            <FormGroup
                                inline
                                label="Agent Name"
                                labelSpan={3}
                                controlSpan={21}
                            >
                                <Form.Item
                                    name="name"
                                >
                                    <Input 
                                        maxLength={255} 
                                        disabled={true}
                                        placeholder="Agent name"
                                        style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                                    />
                                </Form.Item>
                            </FormGroup>

                            {/* Description Field - Disabled */}
                            <FormGroup
                                inline
                                label="Description"
                                labelSpan={3}
                                controlSpan={21}
                            >
                                <Form.Item
                                    name="description"
                                >
                                    <Input 
                                        maxLength={500} 
                                        disabled={true}
                                        placeholder="Agent description"
                                        style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                                    />
                                </Form.Item>
                            </FormGroup>

                            {/* Instructions Field - Display Only (Disabled) */}
                            <FormGroup
                                inline
                                label="Instructions"
                                labelSpan={3}
                                controlSpan={21}
                            >
                                <Form.Item
                                    name="systemPrompt"
                                >
                                    <Input.TextArea 
                                        placeholder="System instructions (cannot be modified)"
                                        style={{ 
                                            backgroundColor: '#f5f5f5', 
                                            color: '#666',
                                            height: '50vh',
                                            resize: 'none'
                                        }}
                                    />
                                </Form.Item>
                            </FormGroup>

                            {/* Additional Context Field - Editable and Optional */}
                            <FormGroup
                                inline
                                label="Additional Context"
                                labelSpan={3}
                                controlSpan={21}
                            >
                                <Form.Item
                                    name="additionalPrompt"
                                >
                                    <Input.TextArea 
                                        rows={4}
                                        placeholder="Enter additional context for the supervisor agent (optional)"
                                        maxLength={2000}
                                        showCount
                                    />
                                </Form.Item>
                            </FormGroup>

                            {/* Model Selection Field */}
                            <FormGroup
                                inline
                                label={renderLabelRequired('Model')}
                                labelSpan={3}
                                controlSpan={21}
                            >
                                <Form.Item
                                    name="modelId"
                                    rules={[{ required: true, message: 'Please select a model.' }]}
                                >
                                    <Select 
                                        placeholder="Select a model" 
                                        style={{ width: '100%' }}
                                        showSearch
                                        optionFilterProp="children"
                                    >
                                        {models.map((model) => (
                                            <Option key={model} value={model}>
                                                {model}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </FormGroup>
                        </Col>
                    </Row>
                </Space>
            </Form>
        </Spin>
    )
}

export default AdminSupervisorAgentForm
