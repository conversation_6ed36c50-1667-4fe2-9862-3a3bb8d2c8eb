import { Agent } from '../_shared/ai/types'

export interface ProjectWorkerAgentState {
  isLoading: boolean,
  isLoadingList: boolean,
  listData: Agent[],
  detail: Agent | null,
  updateInstructionsSuccess?: boolean,
}

export const defaultState: ProjectWorkerAgentState = {
  isLoading: false,
  isLoadingList: false,
  listData: [],
  detail: null,
  updateInstructionsSuccess: false,
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/PROJECT_WORKER_AGENT/RESET_STATE',

  GET_LIST_REQUEST = '@@MODULES/PROJECT_WORKER_AGENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/PROJECT_WORKER_AGENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/PROJECT_WORKER_AGENT/GET_LIST_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/PROJECT_WORKER_AGENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/PROJECT_WORKER_AGENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/PROJECT_WORKER_AGENT/GET_DETAIL_FAILED',

  UPDATE_INSTRUCTIONS_REQUEST = '@@MODULES/PROJECT_WORKER_AGENT/UPDATE_INSTRUCTIONS_REQUEST',
  UPDATE_INSTRUCTIONS_SUCCESS = '@@MODULES/PROJECT_WORKER_AGENT/UPDATE_INSTRUCTIONS_SUCCESS',
  UPDATE_INSTRUCTIONS_FAILED = '@@MODULES/PROJECT_WORKER_AGENT/UPDATE_INSTRUCTIONS_FAILED',
}
