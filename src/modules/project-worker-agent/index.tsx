import React, { useEffect, useState } from 'react'
import { Space, Typography, Card, Table, Button } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import LavPageHeader from '../../helper/component/lav-breadcumb'
import { getColumnSearchProps, extractProjectCode } from '../../helper/share'
import { SCREEN_MODE, SEARCH_TYPE } from '../../constants'
import { getListRequest, resetState } from './action'
import ProjectWorkerAgentForm from './form/index'
import './style.css'

const { Title } = Typography

const ProjectWorkerAgent = () => {
  const dispatch = useDispatch()
  const projectWorkerAgentState = useSelector((state: any) => state.ProjectWorkerAgent)
  const { isLoadingList = false, listData = [] } = projectWorkerAgentState || {}
  
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [agentCode, setAgentCode] = useState<string>('')

  useEffect(() => {
    if (screenMode === SCREEN_MODE.VIEW) {
      document.title = extractProjectCode() + " - Project Worker Agents"
    } else {
      document.title = extractProjectCode() + " - Project Worker Agent Details"
    }
  }, [screenMode])

  useEffect(() => {
    // Fetch data on component mount when in VIEW mode
    if (screenMode === SCREEN_MODE.VIEW) {
      const projectCode = extractProjectCode()
      if (projectCode) {
        dispatch(getListRequest(projectCode))
      }
    }
  }, [dispatch, screenMode])

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      dispatch(resetState({}))
    }
  }, [dispatch])

  const columns = [
    {
      title: 'Agent Code',
      dataIndex: 'code',
      width: '120px',
      sorter: true,
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        return (
          <Button 
            type="link" 
            style={{ padding: 0, height: 'auto' }}
            onClick={() => {
              setAgentCode(record.code)
              setScreenMode(SCREEN_MODE.EDIT)
            }}
          >
            {text}
          </Button>
        )
      },
    },
    {
      title: 'Agent Name',
      dataIndex: 'name',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      sorter: true,
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      render: (text: string) => {
        return text || '-' // Handle undefined/null descriptions
      },
    },
    {
      title: 'Mode',
      dataIndex: 'project',
      width: '100px',
      render: (project: string) => {
        return project || '-' // Show project name or dash if empty
      },
    },
  ]

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ? (
        <>
          <LavPageHeader
            showBreadcumb={false}
            title="Project Worker Agents"
          />
          <Card>
            <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4} style={{ margin: 0 }}>Worker Agent Management</Title>
            </div>
            
            <Table
              dataSource={listData}
              columns={columns}
              rowKey="id"
              loading={isLoadingList}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
              }}
              scroll={{ x: true }}
            />
          </Card>
        </>
      ) : null}
      
      {screenMode === SCREEN_MODE.EDIT ? (
        <ProjectWorkerAgentForm 
          agentCode={agentCode}
          onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} 
          onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} 
        />
      ) : null}
    </Space>
  )
}

export default ProjectWorkerAgent
