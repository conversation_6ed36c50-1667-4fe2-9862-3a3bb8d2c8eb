import { createReducer } from '@reduxjs/toolkit'
import {
  getListRequest,
  getListSuccess,
  getListFailed,
  getDetailRequest,
  getDetailSuccess,
  getDetailFailed,
  updateInstructionsRequest,
  updateInstructionsSuccess,
  updateInstructionsFailed,
  resetState,
} from './action'
import { ProjectWorkerAgentState, defaultState } from './type'

const initState: ProjectWorkerAgentState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state) => {
        Object.assign(state, defaultState)
      })

      .addCase(getListRequest, (state) => {
        state.isLoadingList = true
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state) => {
        state.isLoadingList = false
        state.listData = []
      })

      .addCase(getDetailRequest, (state) => {
        state.isLoading = true
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
      })
      .addCase(getDetailFailed, (state) => {
        state.isLoading = false
        state.detail = null
      })

      .addCase(updateInstructionsRequest, (state) => {
        state.isLoading = true
        state.updateInstructionsSuccess = false
      })
      .addCase(updateInstructionsSuccess, (state, action) => {
        state.isLoading = false
        state.updateInstructionsSuccess = true
        state.detail = action.payload
      })
      .addCase(updateInstructionsFailed, (state) => {
        state.isLoading = false
        state.updateInstructionsSuccess = false
      })
  )
})

export type { ProjectWorkerAgentState } from './type'
export default reducer
