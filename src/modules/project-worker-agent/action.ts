import { createAction } from '@reduxjs/toolkit';
import { ActionEnum } from './type';
import { Agent } from '../_shared/ai/types';

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<string>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<Agent[]>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<{projectCode: string, agentCode: string}>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<Agent>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const updateInstructionsRequest = createAction<{projectCode: string, agentCode: string, systemPrompt: string, additionalPrompt?: string}>(ActionEnum.UPDATE_INSTRUCTIONS_REQUEST);
export const updateInstructionsSuccess = createAction<Agent>(ActionEnum.UPDATE_INSTRUCTIONS_SUCCESS);
export const updateInstructionsFailed = createAction<any>(ActionEnum.UPDATE_INSTRUCTIONS_FAILED);
