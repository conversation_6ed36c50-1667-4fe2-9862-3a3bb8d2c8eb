import { Button, Col, Form, Input, Row, Select, Space, Spin } from 'antd'
import { useEffect, useState, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import { ShowAppMessage, extractProjectCode } from '../../../helper/share'
import { updateInstructionsRequest, resetState, getDetailRequest } from '../action'
import { ModelId } from '../../../modules/_shared/ai'

const { Option } = Select

interface ProjectWorkerAgentFormProps {
  agentCode: string
  onFinish: () => void
  onDismiss: () => void
}

const ProjectWorkerAgentForm = ({ agentCode, onFinish, onDismiss }: ProjectWorkerAgentFormProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState<boolean>(false)
  const dispatch = useDispatch()
  const projectWorkerAgentState = useSelector((state: any) => state.ProjectWorkerAgent)

  // Available models for selection
  const models = Object.values(ModelId)

  const loadAgentData = useCallback(async () => {
    console.log('Loading agent data for agentCode:', agentCode)
    try {
      const projectCode = extractProjectCode()
      console.log('Project code extracted:', projectCode)
      
      if (!projectCode) {
        throw new Error('Project code not found')
      }

      // Dispatch action to get agent details by project code and agent code
      dispatch(getDetailRequest({ projectCode, agentCode }))
      
    } catch (error) {
      console.error('Error loading agent data:', error)
      ShowAppMessage(error, null, 'Failed to load agent data')
      setLoading(false)
    }
  }, [agentCode, dispatch])

  useEffect(() => {
    if (agentCode) {
      loadAgentData()
    }
    
    return () => {
      dispatch(resetState({}))
    }
  }, [agentCode, loadAgentData, dispatch])

  // Listen for agent detail data and populate form
  useEffect(() => {
    console.log('Current Redux state:', { 
      detail: projectWorkerAgentState.detail, 
      screenMode: projectWorkerAgentState.screenMode, 
      agentCode,
      isLoading: projectWorkerAgentState.isLoading,
      fullState: projectWorkerAgentState
    })
    
    // Check if we have data and are not loading
    if (projectWorkerAgentState.detail) {
      const agentDetail = projectWorkerAgentState.detail
      console.log('Agent detail received:', agentDetail)
      
      // Map API response fields to form fields exactly as they come from API
      const formValues = {
        code: agentDetail.code || '',
        name: agentDetail.name || '',
        description: agentDetail.description || '',
        systemPrompt: agentDetail.systemPrompt || '',
        additionalPrompt: agentDetail.additionalPrompt || '',
        modelId: agentDetail.modelId || ModelId.GPT4o, // Default to GPT4o if not specified
      }
      
      console.log('Setting form values:', formValues)

      // Use setTimeout to ensure form is ready
      setTimeout(() => {
        form.setFieldsValue(formValues)
        console.log('Form values set, current form values:', form.getFieldsValue())
      }, 100) // Increased timeout to ensure proper setting
    }
  }, [projectWorkerAgentState.detail, form, agentCode]) // Focus on detail changes

  // Listen for update instructions success and errors
  useEffect(() => {
    if (projectWorkerAgentState.updateInstructionsSuccess) {
      ShowAppMessage(null, 'Success', 'Agent instructions updated successfully')
      onFinish() // Close the form after successful update
    }
  }, [projectWorkerAgentState.updateInstructionsSuccess, onFinish])

  // Handle loading state from Redux
  useEffect(() => {
    if (projectWorkerAgentState.isLoading !== undefined) {
      setLoading(projectWorkerAgentState.isLoading)
    }
  }, [projectWorkerAgentState.isLoading])

  const onSubmit = async (values: any) => {
    try {
      const projectCode = extractProjectCode()
      if (!projectCode) {
        throw new Error('Project code not found')
      }

      dispatch(updateInstructionsRequest({
        projectCode,
        agentCode,
        systemPrompt: values.systemPrompt,
        additionalPrompt: values.additionalPrompt || ''
      }))
    } catch (error) {
      console.error('Submit error:', error)
      ShowAppMessage(error, null, 'Failed to update agent')
    }
  }

  const renderLabelRequired = (label: string) => (
    <span>
      {label} <span style={{ color: 'red' }}>*</span>
    </span>
  )

  const renderActionButtons = () => {
    const cancelButton = (
      <Button onClick={onDismiss}>
        {intl.formatMessage({ id: 'common.action.cancel' })}
      </Button>
    )

    return (
      <Space size="small">
        {cancelButton}
        <Form.Item style={{ marginBottom: '0px' }}>
          <Button
            className="success-btn"
            htmlType="submit"
            loading={loading}
          >
            {intl.formatMessage({ id: 'common.action.save' })}
          </Button>
        </Form.Item>
      </Space>
    )
  }

  return (
    <Spin spinning={loading} tip="Loading agent details...">
      <Form
        form={form}
        onFinish={onSubmit}
        labelCol={{
          span: 2,
          offset: 0,
        }}
        scrollToFirstError={{ block: 'center' }}
        key={projectWorkerAgentState.detail?.id || 'no-data'}
      >
        <div className='rq-modal-header'>
          <LavPageHeader
            showBreadcumb={false}
            title={`Project Worker Agent Details - ${agentCode || 'Loading...'}`}
          >
            {renderActionButtons()}
          </LavPageHeader>
        </div>

        <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
          <Row gutter={24}>
            <Col span={24}>
              {/* Agent Code Field - Disabled */}
              <FormGroup
                inline
                label="Agent Code"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="code">
                  <Input
                    maxLength={255}
                    disabled={true}
                    placeholder={projectWorkerAgentState.detail?.code || "Loading agent code..."}
                    value={projectWorkerAgentState.detail?.code || ''}
                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                  />
                </Form.Item>
              </FormGroup>

              {/* Agent Name Field - Disabled */}
              <FormGroup
                inline
                label="Agent Name"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="name">
                  <Input 
                    maxLength={255} 
                    disabled={true}
                    placeholder={projectWorkerAgentState.detail?.name || "Loading agent name..."}
                    value={projectWorkerAgentState.detail?.name || ''}
                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                  />
                </Form.Item>
              </FormGroup>

              {/* Description Field - Disabled */}
              <FormGroup
                inline
                label="Description"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="description">
                  <Input.TextArea 
                    rows={2}
                    disabled={true}
                    placeholder={projectWorkerAgentState.detail?.description || "Loading description..."}
                    value={projectWorkerAgentState.detail?.description || ''}
                    style={{ backgroundColor: '#f5f5f5', color: '#666', resize: 'none' }}
                  />
                </Form.Item>
              </FormGroup>

              {/* Model Selection Field - Disabled */}
              <FormGroup
                inline
                label="Model"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="modelId">
                  <Select 
                    placeholder={projectWorkerAgentState.detail?.modelId || "Loading model information..."} 
                    style={{ width: '100%' }}
                    showSearch
                    optionFilterProp="children"
                    disabled={true}
                    value={projectWorkerAgentState.detail?.modelId || undefined}
                  >
                    {models.map((model) => (
                      <Option key={model} value={model}>
                        {model}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </FormGroup>

              {/* Instructions Field - Editable */}
              <FormGroup
                inline
                label={renderLabelRequired('Instructions')}
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item
                  name="systemPrompt"
                  rules={[
                    { required: true, message: 'Please enter system instructions.' },
                    { min: 10, message: 'Instructions must be at least 10 characters long.' }
                  ]}
                >
                  <Input.TextArea 
                    placeholder="Enter system instructions for this agent..."
                    style={{ 
                      height: '45vh',
                      resize: 'none'
                    }}
                    showCount
                    maxLength={10000}
                  />
                </Form.Item>
              </FormGroup>

              {/* Additional Context Field - Editable and Optional */}
              <FormGroup
                inline
                label="Additional Context"
                labelSpan={3}
                controlSpan={21}
              >
                <Form.Item name="additionalPrompt">
                  <Input.TextArea 
                    rows={6}
                    placeholder="Enter additional context for the agent (optional)"
                    maxLength={5000}
                    showCount
                    style={{ resize: 'none' }}
                  />
                </Form.Item>
              </FormGroup>
            </Col>
          </Row>
        </Space>
      </Form>
    </Spin>
  )
}

export default ProjectWorkerAgentForm
