import { Action } from '@reduxjs/toolkit'
import { all, call, put, takeLatest } from 'redux-saga/effects'
import { ShowAppMessage } from '../../helper/share'
import AIService from '../../services/ai.service'
import { Agent, AgentCode } from '../../modules/_shared/ai'
import {
  getListFailed,
  getListRequest,
  getListSuccess,
  getDetailFailed,
  getDetailRequest,
  getDetailSuccess,
  updateInstructionsRequest,
  updateInstructionsSuccess,
  updateInstructionsFailed
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const projectCode = action.payload
      const data: Agent[] = yield call(AIService.getListProjectAgents, projectCode)
      
      // Filter out Master agent like in admin-worker-agent
      const filteredData = data.filter((agent: Agent) => agent.code !== AgentCode.Master)
      yield put(getListSuccess(filteredData))
    } catch (err: any) {
      yield put(getListFailed(err))
      ShowAppMessage(err, null, 'Failed to fetch project worker agents')
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const { projectCode, agentCode } = action.payload
      console.log('Fetching agent details for:', { projectCode, agentCode })
      
      // Use the new project-specific agent details endpoint
      const data: Agent = yield call(AIService.getProjectAgentDetails, projectCode, agentCode)
      console.log('Received agent data from API:', data)
      
      yield put(getDetailSuccess(data))
    } catch (err: any) {
      console.error('Error in handleGetDetail:', err)
      yield put(getDetailFailed(err))
      ShowAppMessage(err, null, 'Failed to fetch project agent details')
    }
  }
}

function* handleUpdateInstructions(action: Action) {
  if (updateInstructionsRequest.match(action)) {
    try {
      const { projectCode, agentCode, systemPrompt, additionalPrompt } = action.payload
      
      // Use the new project-specific agent update endpoint
      const data: Agent = yield call(AIService.updateProjectAgentInstructions, projectCode, agentCode, {
        systemPrompt,
        additionalPrompt
      })
      yield put(updateInstructionsSuccess(data))
      ShowAppMessage(null, 'Success', 'Agent instructions updated successfully')
    } catch (err: any) {
      yield put(updateInstructionsFailed(err))
      ShowAppMessage(err, null, 'Failed to update agent instructions')
    }
  }
}

function* watchGetList() {
  yield takeLatest(getListRequest.type, handleGetList)
}

function* watchGetDetail() {
  yield takeLatest(getDetailRequest.type, handleGetDetail)
}

function* watchUpdateInstructions() {
  yield takeLatest(updateInstructionsRequest.type, handleUpdateInstructions)
}

export default function* projectWorkerAgentSaga() {
  yield all([
    watchGetList(),
    watchGetDetail(),
    watchUpdateInstructions(),
  ])
}
