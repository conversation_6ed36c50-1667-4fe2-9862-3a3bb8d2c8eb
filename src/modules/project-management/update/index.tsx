import {
  Button, Col, Form,
  Input, Modal, Row, Select,
  Space, Spin
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { BUTTON_TYPE, DATE_FORMAT } from '../../../constants'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomModal from '../../../helper/component/custom-modal'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import AppState from '../../../store/types'
import { initScreenUpdate, resetUpdateProjectState, updateProjectRequest } from '../action'
import { UpdateProjectState } from '../type'
const { confirm } = Modal
const { Option } = Select

interface UpdateProjectProps {
  projectCode: string
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT
  onChange: (boolean?) => void | null
}
interface UpdateProjectModalProps {
  projectCode: string
  onChange: (boolean?) => void | null
  onDismiss: () => void | null
}
const UpdateProjectModal = ({ projectCode, onChange, onDismiss }: UpdateProjectModalProps) => {
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const state = useSelector<AppState | null>(
    (s) => s?.Project?.update
  ) as UpdateProjectState
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()

  // Destroy
  useEffect(() => {
    dispatch(initScreenUpdate(projectCode))
    return () => {
      form.resetFields()
    }
  }, [])

  useEffect(() => {
    if (state.getDataStatus) {
      form.setFieldsValue({
        projectCode: state.projectData.code,
        methodology: state.projectData.methodology || 'Waterfall',
      })
    }
  }, [state.getDataStatus])

  useEffect(() => {
    if (state.updateStatus === true) {
      dispatch(resetUpdateProjectState())
      onChange(true)
      onDismiss();
    }
  }, [state.updateStatus])

  const onFinish = debounce((values: any) => {
    let requestData = {
      "methodology": values.methodology
    };
    dispatch(updateProjectRequest({ projectCode: projectCode, requestData: requestData }))
  }, 500)

  const cfCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_3' })}`,
      onOk() {
        onChange()
        dispatch(resetUpdateProjectState())
        onDismiss();
      },
      onCancel() { },
    })
  }

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    visible={true}
    footer={null}
  >
    <Spin spinning={state?.isLoading}>
      <Form
        name='frmProject'
        form={form}
        onFinish={onFinish}
        labelCol={{
          span: 2,
          offset: 0,
        }}
        scrollToFirstError={{ block: 'center' }}
      >
        <div className='rq-modal-header'>
          <Row>
            <Col span={10}>
              <Form.Item
                name="projectCode"
                className='full-width'
              >
                <Input
                  size="large"
                  readOnly
                  className="modal-create-name-input-field"
                />
              </Form.Item>
            </Col>

            <Col span={14}>
              <Row justify="end">
                <Space size="small">
                  <Button onClick={cfCancel}>
                    {intl.formatMessage({ id: 'common.action.close' })}
                  </Button>
                  <Form.Item style={{ marginBottom: '0px' }}>
                    <Button
                      className="success-btn"
                      htmlType="submit"
                    >
                      {intl.formatMessage({ id: 'common.action.update' })}
                    </Button>
                  </Form.Item>
                </Space>
              </Row>
            </Col>
          </Row>
        </div>

        <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 300}>
          <Space direction="vertical" size="small" className='rq-form-group' style={{ padding: '0 10px 2px 0' }}>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.group' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.group}</div></Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-manager' })}</div></Col>
              <Col span={16}>
                <div className='rq-form-value'>
                  <a href='#'>{state?.projectData?.manager}</a>
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-status' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.status}</div></Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.customer' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.customer}</div></Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.rank' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.rank}</div></Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.start-date' })}</div></Col>
              <Col span={16}>
                <div className='rq-form-value'>
                  {/* lavdate */}
                  {state?.projectData?.startDate ? moment(state.projectData.startDate).format(DATE_FORMAT) : ''}
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.end-date' })}</div></Col>
              <Col span={16}>
                <div className='rq-form-value'>
                  {/* lavdate */}
                  {state?.projectData?.endDate ? moment(state.projectData.endDate).format(DATE_FORMAT) : ''}
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-contract-type' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.contractType}</div></Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.industry' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.industry}</div></Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-category' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.category}</div></Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-scope' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.scope}</div></Col>
            </Row>
            <Row>
              <Col span={8}>
                <div className='rq-form-label'>
                  {intl.formatMessage({ id: 'project.info.requirement-methodology' })}
                  <span className='rq-required'>*</span>
                </div>
              </Col>
              <Col span={16}>
                <Form.Item
                  name="methodology"
                  className='full-width'
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({ id: 'IEM_1' }),
                    },
                    {
                      validator: async (rule, value) => {
                        if (value != 'Waterfall' && value != 'Agile') {
                          throw new Error(
                            `${intl.formatMessage({ id: 'ESMG_99' })}`
                          )
                        }
                      },
                    },
                  ]}>
                  <Select optionLabelProp="label" style={{ maxWidth: 220 }}>
                    <Option value="Waterfall">Waterfall</Option>
                    <Option value="Agile">Agile</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={8}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-description' })}</div></Col>
              <Col span={16}><div className='rq-form-value'>{state?.projectData?.description}</div></Col>
            </Row>
          </Space>
        </Scrollbars>
      </Form>
    </Spin>
  </CustomModal>
}
const UpdateProject = ({ projectCode, buttonType, onChange }: UpdateProjectProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  // useEffect(() => {
  //   if (isModalVisible !== null) {
  //     dispatch(setModalVisible(isModalVisible))
  //   }
  // }, [isModalVisible])

  return <>

    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button onClick={() => setIsModalVisible(true)} className="success-btn">
          {intl.formatMessage({ id: 'common.action.update' })}
        </Button> :
        <Button
          type="text"
          icon={<CustomSvgIcons name="EditCustomIcon" />}
          onClick={() => setIsModalVisible(true)}
        />
    }
    {isModalVisible === true ? <UpdateProjectModal projectCode={projectCode} onChange={onChange} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}
export default UpdateProject
