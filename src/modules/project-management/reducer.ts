import { createReducer } from '@reduxjs/toolkit'
import { ProjectState } from './type'
import { initScreen, initScreenSuccess, initScreenFailure, updateProjectRequest, resetUpdateProjectState, initScreenUpdate, initScreenUpdateSuccess, initScreenUpdateFailure, updateProjectSuccess, updateProjectFailure, initScreenDetail, initScreenDetailSuccess, initScreenDetailFailure, resetScreenDetailState, initScreenMenu, initScreenMenuSuccess, initScreenMenuFailure, initScreenProjectMembers, initScreenProjectMembersSuccess, initScreenProjectMembersFailure,initScreenProjectMembersStakeholders, initScreenProjectMembersStakeholdersSuccess, initScreenProjectMembersStakeholdersFailure, initScreenProjectProductsSuccess, initScreenProjectProducts, initScreenProjectProductsFailure, initMemberScreen, initMemberScreenSuccess, initMemberScreenFailure, updateMemberRequest, updateMemberSuccess, updateMemberFailure, resetMemberState, getListGroup, getListGroupSuccess, getListStatus, getListStatusSuccess, getConfig, getConfigSuccess, saveConfig, saveConfigSuccess, verifyConfluence, verifyConfluenceDone, syncConfluence, syncConfluenceDone } from './action'

const initState: ProjectState = {
  isLoading: false,
  response: {},
  group: [],
  status: [],
  total: -1,
  skip: -1,
  take: -1,
  code: '',
  currentPage: -1,
  update: {
    isLoading: false,
    projectData: {
      projectCode: '',
      methodology: ''
    },
    error: null,
    message: '',
    updateStatus: false,
    getDataStatus: false,
  },
  detail: {
    isLoadingMenu: false,
    allProjects: null,
    isLoading: false,
    projectData: null,
    isLoadingMembers: false,
    allMembers: null,
    isLoadingMembersStakeholders: false,
    allMembersStakeholders: null,
    isLoadingProducts: false,
    allProducts: null,
    error: ''
  },
  updateMember: {
    isLoading: false,
    memberData: {},
    error: null,
    message: '',
    updateStatus: false,
    getDataStatus: false,
  },
  config: {
    id: -1,
    code: '',
    name: '',
    srsGenerationType: 1,
    defaultPaging: 20,
    overview: '',
    abbreviations: '',
    messageAction: '',
    confluenceSpaceKey: '',
    confluenceDestinationPage: '',
  }
}

const reducer = createReducer(initState, (builder) => {
  return builder
    // Project List
    .addCase(initScreen, (state, action?) => {
      state.isLoading = true
    })
    .addCase(initScreenSuccess, (state, action) => {
      state.isLoading = false
      state.response = action.payload
    })
    .addCase(initScreenFailure, (state, action) => {
      state.isLoading = false
      state.response = action.payload
    })
    // Project Detail Menu
    .addCase(initScreenMenu, (state, action) => {
      state.detail.isLoadingMenu = true
      state.detail.allProjects = null
    })
    .addCase(initScreenMenuSuccess, (state, action) => {
      state.detail.isLoadingMenu = false
      state.detail.allProjects = action.payload
    })
    .addCase(initScreenMenuFailure, (state, action) => {
      state.detail.isLoadingMenu = false
      state.detail.allProjects = null
    })
    // Project Detail
    .addCase(initScreenDetail, (state, action) => {
      state.detail.isLoading = true
      state.detail.error = ''
    })
    .addCase(initScreenDetailSuccess, (state, action) => {
      state.detail.isLoading = false
      state.detail.projectData = action.payload
    })
    .addCase(initScreenDetailFailure, (state, action) => {
      state.detail.isLoading = false
      state.detail.error = action.payload
    })
    // Project Members
    .addCase(initScreenProjectMembers, (state, action) => {
      state.detail.isLoadingMembers = true
      state.detail.allMembers = null
    })
    .addCase(initScreenProjectMembersSuccess, (state, action) => {
      state.detail.isLoadingMembers = false
      state.detail.allMembers = action.payload
    })
    .addCase(initScreenProjectMembersFailure, (state, action) => {
      state.detail.isLoadingMembers = false
      state.detail.allMembers = null
    })
    // Project Members Stack
    .addCase(initScreenProjectMembersStakeholders, (state, action) => {
      state.detail.isLoadingMembersStakeholders = true
      state.detail.allMembersStakeholders = null
    })
    .addCase(initScreenProjectMembersStakeholdersSuccess, (state, action) => {
      state.detail.isLoadingMembersStakeholders = false
      state.detail.allMembersStakeholders = action.payload
    })
    .addCase(initScreenProjectMembersStakeholdersFailure, (state, action) => {
      state.detail.isLoadingMembersStakeholders = false
      state.detail.allMembersStakeholders = null
    })
    // Project Products
    .addCase(initScreenProjectProducts, (state, action) => {
      state.detail.isLoadingProducts = true
      state.detail.allProducts = null
    })
    .addCase(initScreenProjectProductsSuccess, (state, action) => {
      state.detail.isLoadingProducts = false
      state.detail.allProducts = action.payload
    })
    .addCase(initScreenProjectProductsFailure, (state, action) => {
      state.detail.isLoadingProducts = false
      state.detail.allProducts = null
    })

    .addCase(resetScreenDetailState, (state, action) => {
      state.detail.isLoadingMenu = false
      state.detail.allProjects = null
      state.detail.isLoadingMembers = false
      state.detail.allMembers = null
      state.detail.isLoading = false
      state.detail.error = ''
    })
    // Project Update
    .addCase(initScreenUpdate, (state, action) => {
      state.update.isLoading = true
      state.update.getDataStatus = false
    })
    .addCase(initScreenUpdateSuccess, (state, action) => {
      state.update.isLoading = false
      state.update.getDataStatus = true
      state.update.projectData = action.payload
    })
    .addCase(initScreenUpdateFailure, (state, action) => {
      state.update.isLoading = false
    })
    .addCase(updateProjectRequest, (state, action) => {
      state.update.isLoading = true
    })
    .addCase(updateProjectSuccess, (state, action) => {
      state.update.isLoading = false
      state.update.updateStatus = true
    })
    .addCase(updateProjectFailure, (state, action) => {
      state.update.isLoading = false
      state.update.error = action.payload
    })
    .addCase(resetUpdateProjectState, (state, action) => {
      state.update.getDataStatus = false
      state.update.updateStatus = false
    })
    // Project Member Update
    .addCase(initMemberScreen, (state, action) => {
      state.updateMember.isLoading = true
      state.updateMember.getDataStatus = false
    })
    .addCase(initMemberScreenSuccess, (state, action) => {
      state.updateMember.isLoading = false
      state.updateMember.getDataStatus = true
      state.updateMember.memberData = action.payload
    })
    .addCase(initMemberScreenFailure, (state, action) => {
      state.updateMember.isLoading = false
    })
    .addCase(updateMemberRequest, (state, action) => {
      state.updateMember.isLoading = true
    })
    .addCase(updateMemberSuccess, (state, action) => {
      state.updateMember.isLoading = false
      state.updateMember.updateStatus = true
    })
    .addCase(updateMemberFailure, (state, action) => {
      state.updateMember.isLoading = false
      state.updateMember.error = action.payload
    })
    .addCase(resetMemberState, (state, action) => {
      state.updateMember.memberData = {}
      state.updateMember.isLoading = false
      state.updateMember.error = ''
      state.updateMember.message = ''
      state.updateMember.getDataStatus = false
      state.updateMember.updateStatus = false
    })
    .addCase(getListGroup, (state, action) => {
      state.isLoading = true
    })
    .addCase(getListGroupSuccess, (state, action) => {
      state.isLoading = false
      state.group = action.payload
    })
    .addCase(getListStatus, (state, action) => {
      state.isLoading = true
    })
    .addCase(getListStatusSuccess, (state, action) => {
      state.isLoading = false
      state.status = action.payload
    })
    .addCase(getConfig, (state, action) => {
      state.isLoading = true
    })
    .addCase(getConfigSuccess, (state, action) => {
      state.isLoading = false
      state.config = action.payload
    })

    .addCase(saveConfig, (state, action) => {
      state.isLoading = true
    })
    .addCase(saveConfigSuccess, (state, action) => {
      state.isLoading = false
      state.config = action.payload
    })

    .addCase(verifyConfluence, (state, action) => {
      state.isLoading = true
    })
    .addCase(verifyConfluenceDone, (state, action) => {
      state.isLoading = false
    })

    .addCase(syncConfluence, (state, action) => {
      state.isLoading = true
    })
    .addCase(syncConfluenceDone, (state, action) => {
      state.isLoading = false
    })
})

export default reducer
export { initState as ProjectState }