import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { extractProjectCode, ShowAppMessage } from '../../helper/share'
import {
  getConfig,
  getConfigSuccess,
  getListGroupSuccess,
  getListStatusSuccess, initMemberScreen, initMemberScreenSuccess, initScreen, initScreenDetail,
  initScreenDetailFailure,
  initScreenDetailSuccess, initScreenFailure, initScreenMenu, initScreenMenuFailure, initScreenMenuSuccess, initScreenProjectMembers, initScreenProjectMembersFailure, initScreenProjectMembersSuccess, initScreenProjectProducts, initScreenProjectProductsFailure, initScreenProjectProductsSuccess, initScreenSuccess, initScreenUpdate,
  initScreenUpdateSuccess, saveConfig, saveConfigSuccess, syncConfluence, syncConfluenceDone, updateMemberFailure, updateMemberRequest, updateMemberSuccess, updateProjectFailure, updateProjectRequest,
  updateProjectSuccess,
  verifyConfluence,
  verifyConfluenceDone,
  initScreenProjectMembersStakeholders, initScreenProjectMembersStakeholdersSuccess, initScreenProjectMembersStakeholdersFailure
} from './action'
import END_POINT from './url'

//Init project list
function* initScreenFlow(action: Action) {
  if (initScreen.match(action)) {
    try {
      const take = action.payload.take
      const skip = (action.payload.skip - 1) * take
      const code = action.payload.code
      const status = action.payload.projectStatus
      const group = action.payload.group
      //Get list group
      const listGroups = [] as any
      const url = `${END_POINT.PROJECTS}/Groups`
      const res = yield call(apiCall, 'GET', url)
      res.data?.map((a: string) => listGroups.push({
        text: a,
        value: res.data.indexOf(a)
      }))
      yield put(getListGroupSuccess(listGroups))

      //Get list project status
      const listStatus = [] as any
      const url1 = `${END_POINT.PROJECTS}/Statuses`
      const res1 = yield call(apiCall, 'GET', url1)
      res1.data?.map((a: string) => listStatus.push({
        text: a,
        value: res1.data.indexOf(a)
      }))

      yield put(getListStatusSuccess(listStatus))
      let filter = ''
      if (code) filter += filter + `&code=${code}`
      if (status) {
        for (let i = 0; i < status.length; i++) {
          filter = filter + `&statuses=${status[i]}`
        }
      }
      if (group) {
        for (let i = 0; i < group[0].length; i++) {
          filter = filter + `&groups=${res.data[group[0][i]]}`
        }
      }
      const url2 = `${END_POINT.PROJECTS}?Take=${take}&Skip=${skip}` + `${filter}&SortField=projectStatus&SortDir=asc`
      const res2 = yield call(apiCall, 'GET', url2)
      yield put(initScreenSuccess(res2.data))
    } catch (err) {
      yield put(initScreenFailure(''))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}


// Project Menu
function* initScreenMenuFlow(action: Action) {
  if (initScreenMenu.match(action)) {
    try {
      const take = action.payload.take
      const skip = (action.payload.skip - 1) * take
      const url = `${END_POINT.PROJECTS}?Take=${take}&Skip=${skip}`
      const res = yield call(apiCall, 'GET', url)
      if (res.data) {
        yield put(
          initScreenMenuSuccess(res.data)
        )
      }
    } catch (err) {
      yield put(initScreenMenuFailure(''))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

// Project Details
function* initScreenDetailFlow(action: Action) {
  if (initScreenDetail.match(action)) {
    try {
      const params = action.payload
      const url = END_POINT.PROJECTS + `/${params}`
      const res = yield call(apiCall, 'GET', url)

      if (res.data) {
        yield put(
          initScreenDetailSuccess(res.data)
        )
      }
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(initScreenDetailFailure(''))
    }
  }
}

// Project Members
function* initScreenProjectMembersFlow(action: Action) {
  if (initScreenProjectMembers.match(action)) {
    try {
      const take = action.payload.take
      const skip = (action.payload.skip - 1) * take
      const username = action.payload.username
      let filter = ''
      if (username) filter += filter + `&Username=${username}`
      const url = `${END_POINT.PROJECTS}/${action.payload.projectCode}/resources?Take=${take}&Skip=${skip}${filter}`
      const stakeholdersUrl = `${END_POINT.PROJECTS}/${action.payload.projectCode}/stakeholders?Take=${take}&Skip=${skip}${filter}`
      const res = yield call(apiCall, 'GET', url)
      const resStakeholders = yield call(apiCall, 'GET', stakeholdersUrl)
      yield put(initScreenProjectMembersSuccess(res.data))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(initScreenProjectMembersFailure('requestFail'))
    }
  }
}
function* initScreenProjectMembersStakeholdersFlow(action: Action) {
  if (initScreenProjectMembersStakeholders.match(action)) {
    try {
      const take = action.payload.take
      const skip = (action.payload.skip - 1) * take
      const username = action.payload.username
      let filter = ''
      if (username) filter += filter + `&Username=${username}`
      const url = `${END_POINT.PROJECTS}/${action.payload.projectCode}/stakeholders?Take=${take}&Skip=${skip}${filter}`
      const res = yield call(apiCall, 'GET', url)
      yield put(initScreenProjectMembersStakeholdersSuccess(res.data))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(initScreenProjectMembersStakeholdersFailure('requestFail'))
    }
  }
}

// Project Products
function* initScreenProjectProductsFlow(action: Action) {
  if (initScreenProjectProducts.match(action)) {
    try {
      const take = action.payload.take
      const skip = (action.payload.skip - 1) * take
      const name = action.payload.name
      let filter = ''
      if (name) filter += filter + `&Name=${name}`
      const url = `${END_POINT.PROJECTS}/${action.payload.projectCode}/Products?Take=${take}&Skip=${skip}${filter}`
      const res = yield call(apiCall, 'GET', url)
      yield put(initScreenProjectProductsSuccess(res.data))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(initScreenProjectProductsFailure('requestFail'))
    }
  }
}

// Update Project
function* initScreenUpdateFlow(action: Action) {
  if (initScreenUpdate.match(action)) {
    try {
      const params: any = action.payload
      const url = END_POINT.PROJECTS + '/' + params;
      const reqGetProject = yield call(apiCall, 'GET', url)
      const res = reqGetProject.data
      yield put(initScreenUpdateSuccess(res))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* updateProjectFlow(action: Action) {
  if (updateProjectRequest.match(action)) {
    try {
      const params = action.payload
      const url = END_POINT.PROJECTS + '/' + params.projectCode + '/methodology';
      const res = yield call(apiCall, 'PUT', url, params.requestData as any)
      //checkdone
      ShowAppMessage(null, params.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.project')
      yield put(updateProjectSuccess(''))
    } catch (err) {
      updateProjectFailure("")
      ShowAppMessage(err, null, 'common.artefact.project')
    }
  }
}

// Update Mmber
function* initMemberScreenFlow(action: Action) {
  if (initMemberScreen.match(action)) {
    try {
      debugger
      const params: any = action.payload
      const url = END_POINT.PROJECTS + '/' + params.projectCode + '/resources/' + params.memberId;
      const reqGetMember = yield call(apiCall, 'GET', url)
      const res = reqGetMember.data
      yield put(initMemberScreenSuccess(res))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* initConfigScreenFlow(action: Action) {
  if (getConfig.match(action)) {
    try {
      const params: any = action.payload
      const url = END_POINT.PROJECTS + '/' + params + '/configuration';
      const reqGetMember = yield call(apiCall, 'GET', url)
      const res = reqGetMember.data
      yield put(getConfigSuccess(res))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* saveConfigScreenFlow(action: Action) {
  if (saveConfig.match(action)) {
    try {
      const url = END_POINT.BASE_URL + `${action.payload.projectCode ? 'projects/' + action.payload?.projectCode + '/' : ''}` + 'configuration';
      const reqGetMember = yield call(apiCall, 'PUT', url, action.payload?.config)
      const res = reqGetMember.data
      yield put(saveConfigSuccess(res))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* verifyConfluenceScreenFlow(action: Action) {
  if (verifyConfluence.match(action)) {
    try {
      const url = END_POINT.BASE_URL + `${action.payload.projectCode ? 'projects/' + action.payload?.projectCode + '/' : ''}` + 'confluence/verify' + `?space=${action.payload?.confluenceSpaceKey}&page=${action.payload?.confluenceDestinationPage}`;
      const reqGetMember = yield call(apiCall, 'GET', url, action.payload?.config)
      const res = reqGetMember.data
      if(reqGetMember.status === 200 && action.payload.sync) {
        yield put(syncConfluence(action.payload.projectCode))
      }
      yield put(verifyConfluenceDone(res))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(verifyConfluenceDone(null))
    }
  }
}

function* syncConfluenceScreenFlow(action: Action) {
  if (syncConfluence.match(action)) {
    try {
      const url = END_POINT.BASE_URL + `${'projects/' + action.payload + '/' + 'confluence/sync'}`;
      const reqGetMember = yield call(apiCall, 'POST', url, action.payload?.config)
      const res = reqGetMember.data
      yield put(syncConfluenceDone(res))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(syncConfluenceDone(null))
    }
  }
}

function* updateMemberFlow(action: Action) {
  if (updateMemberRequest.match(action)) {
    try {
      const params = action.payload
      const username = action.payload.username
      const url = `${END_POINT.PROJECTS}/${params.projectCode}/resources${params.memberId ? `/${params.memberId}` : ''}`;
      const res = yield call(apiCall, params.memberId ? 'PUT' : 'POST', url, params.requestData as any)
      if (res.data) {
        // If member is current User
        const currentUserStored = localStorage.getItem('currentUserProjects') || '';
        let currentUser: any = '';
        currentUser = JSON.parse(currentUserStored).userName;

        if (username.toLowerCase() === currentUser.toLowerCase()) {
          const res2 = yield call(apiCall, 'GET', END_POINT.AUTH)
          if (res2) {
            localStorage.setItem('currentUserProjects', JSON.stringify(res2.data))
          }
        }
        //checkdone
        ShowAppMessage(null, params.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.project')
        yield put(updateMemberSuccess('done'))
      }
    } catch (err) {
      updateMemberFailure('')
      ShowAppMessage(err, null, 'common.artefact.project')
    }
  }
}


function* watchFetchRequest() {
  yield takeLatest(initScreen.type, initScreenFlow)
  yield takeLatest(initScreenUpdate.type, initScreenUpdateFlow)
  yield takeLatest(updateProjectRequest.type, updateProjectFlow)
  yield takeLatest(initScreenDetail.type, initScreenDetailFlow)
  yield takeLatest(initScreenMenu.type, initScreenMenuFlow)
  yield takeLatest(initScreenProjectMembers.type, initScreenProjectMembersFlow)
  yield takeLatest(initScreenProjectMembersStakeholders.type, initScreenProjectMembersStakeholdersFlow)
  yield takeLatest(initScreenProjectProducts.type, initScreenProjectProductsFlow)
  yield takeLatest(initMemberScreen.type, initMemberScreenFlow)
  yield takeLatest(updateMemberRequest.type, updateMemberFlow)
  yield takeLatest(getConfig.type, initConfigScreenFlow)
  yield takeLatest(saveConfig.type, saveConfigScreenFlow)
  yield takeLatest(verifyConfluence.type, verifyConfluenceScreenFlow)
  yield takeLatest(syncConfluence.type, syncConfluenceScreenFlow)
}

export default function* ProjectSaga() {
  yield all([fork(watchFetchRequest)])
}






