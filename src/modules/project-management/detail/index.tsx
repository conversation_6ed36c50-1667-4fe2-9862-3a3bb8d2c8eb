import AppState from '../../../store/types'
import { Row, Col, Pagination } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { initScreenDetail, initScreenMenu, resetScreenDetailState } from '../action'
import LeftControl from './component/leftControl'
import RightControl from './component/rightControl'
import { ViewProjectState } from '../type'
import { DEFAULT_PAGE_SIZE } from '../../../constants'
import intl from '../../../config/locale.config'

const ProjectDetails = (props) => {
  const state = useSelector<AppState | null>(
    (s) => s?.Project?.detail
  ) as ViewProjectState
  const dispatch = useDispatch()
  const [projectList, setProjectList] = useState([]) as any
  const [totalRecord, setTotalRecord] = useState(1)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(DEFAULT_PAGE_SIZE)

  useEffect(() => {
    if (state.allProjects) {
      setProjectList(state.allProjects.data)
      setTotalRecord(state.allProjects.total)
    }
  }, [state.allProjects])

  useEffect(() => {
    dispatch(initScreenMenu({ skip: currentPage, take: currentPageSize }))
    return () => {
      dispatch(resetScreenDetailState())
    }
  }, [])

  useEffect(() => {    
    document.title = props.match.params.projectCode + "-" + intl.formatMessage({ id: 'project.detail.tabs.information' });
    if (props?.match?.params?.projectCode) {
      dispatch(initScreenDetail(props.match.params.projectCode))
    }
  }, [props])

  const handleReloadData = () => {
    dispatch(initScreenMenu({ skip: currentPage, take: currentPageSize }))
    dispatch(initScreenDetail(props.match.params.projectCode))
  }

  const handlePageChange = (pageI, pageS) => {
    setCurrentPage(pageI);
    setCurrentPageSize(pageS);
    dispatch(initScreenMenu({ skip: pageI, take: pageS }))
  }

  return (
    <>
      <Row>
        <Col span={4}>
          <LeftControl
            isLoading={state?.isLoading}
            projectList={projectList}
            activeId={props.match.params.projectCode} />
          <div style={{ padding: '0 1rem' }}>
            <Pagination onChange={handlePageChange} size='small' pageSize={currentPageSize} defaultCurrent={currentPage} total={totalRecord} />
          </div>
        </Col>
        <Col span={20}>
          <RightControl
            onChange={handleReloadData}
            isLoading={state?.isLoading}
            data={state?.projectData}
            projectCode={props.match.params.projectCode} />
        </Col>
      </Row>
    </>
  )
}

export default ProjectDetails
