import intl from '../../../../../config/locale.config'
import useWindowDimensions from '../../../../../helper/hooks/useWindowDimensions'
import {
  Typography,
  Row,
  Button,
  Space,
  Breadcrumb,
  Divider,
  Tabs,
  Spin,
} from 'antd'
import { Scrollbars } from 'react-custom-scrollbars'
import UpdateProject from '../../../update'
import ProjectInformation from '../information'
import ProjectMembers from '../members'
import ProjectProducts from '../products'
import { useEffect, useState } from 'react'
import { Link, useHistory } from 'react-router-dom'
import { APP_ROLES, APP_ROUTES, BUTTON_TYPE, PROJECT_PREFIX } from '../../../../../constants'
import { extractProjectCode, hasRole } from '../../../../../helper/share'
import ProjectConfig from '../project-config'

const { Title } = Typography
const { TabPane } = Tabs;

const RightControl = (props: any) => {
  const { height: windowHeight } = useWindowDimensions()
  const [selectedTabIndex, setSelectedTabIndex] = useState('1');
  const history = useHistory();
  const [showUpdateButton, setShowUpdateButton] = useState(true);

  const handleTabClick = (e) => {
    setShowUpdateButton(e == '1');
    if (e == '4') {
      const dashboardPath = `${PROJECT_PREFIX}${props.projectCode}${APP_ROUTES.DASHBOARD}`;
      history.push(dashboardPath); 
      document.title = props.projectCode + "-" + intl.formatMessage({ id: 'dashboard.page-title' });
      return;
    }
    if (e == '5') {
      return;
    }
    if(e == '1')      
      document.title = props.projectCode + "-" + intl.formatMessage({ id: 'project.detail.tabs.information' });
    else if(e == '2')      
      document.title = props.projectCode + "-" + intl.formatMessage({ id: 'project.detail.tabs.members' });
    else if(e == '3')      
      document.title = props.projectCode + "-" + intl.formatMessage({ id: 'project.detail.tabs.products' });
    else if(e == '6')      
      document.title = props.projectCode + "-" + intl.formatMessage({ id: 'project.detail.tabs.project-config' });
    setSelectedTabIndex(e);
  }

  useEffect(() => {
    if (props.projectCode) {
      setSelectedTabIndex('1');
    }
  }, [props.projectCode])

  return (
    <Spin spinning={props.isLoading}>
      <Space
        direction="vertical"
        size="middle"
        className="record-detail-right-control-container p-1rem"
      >
        <Row align="middle" justify="space-between" style={{ minHeight: 32 }}>
          <div>
            <Breadcrumb className='rq-breadcrumb' separator=">">
              <Breadcrumb.Item>
                <Link to={`${APP_ROUTES.PROJECTS}`}>
                  {intl.formatMessage({ id: 'project.breadcrumb' })}
                </Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item>
                <Button className="breadcrumb-link-btn" type="link">{intl.formatMessage({ id: 'project.detail.breadcrumb' })}</Button>
              </Breadcrumb.Item>
            </Breadcrumb>
          </div>
          {
            (
              showUpdateButton &&
              hasRole(APP_ROLES.PM, props?.data?.code) &&
              (
                props?.data?.projectStatus?.toLowerCase() == intl.formatMessage({ id: 'common.status.on-going' })?.toLowerCase() ||
                props?.data?.projectStatus?.toLowerCase() == intl.formatMessage({ id: 'common.status.tentative' })?.toLowerCase()
              )
            ) ? <Space size="small">
              <UpdateProject
                buttonType={BUTTON_TYPE.TEXT}
                projectCode={props.projectCode}
                onChange={(res) => {
                  if (res) props.onChange();
                }}
              />
            </Space> : <></>
          }

        </Row>
        <Divider className="mt-0 mb-0" />

        <Scrollbars
          autoHide
          autoHeight
          autoHeightMin={windowHeight - 215}
          autoHeightMax={windowHeight - 215}
        >
          <Space direction="vertical">
            <Tabs tabPosition="left" activeKey={selectedTabIndex} onTabClick={handleTabClick}>
              <TabPane tab={intl.formatMessage({ id: 'project.detail.tabs.information' })} key="1">
                <ProjectInformation data={props.data} projectCode={props?.data?.code} />
              </TabPane>
              <TabPane tab={intl.formatMessage({ id: 'project.detail.tabs.members' })} key="2">
                <ProjectMembers projectCode={props?.data?.code} projectStatus={props?.data?.status} />
              </TabPane>
              <TabPane tab={intl.formatMessage({ id: 'project.detail.tabs.products' })} key="3">
                <ProjectProducts projectCode={props?.data?.code} />
              </TabPane>

              <TabPane tab={intl.formatMessage({ id: 'project.detail.tabs.requirement-document' })} key="4"></TabPane>
              <TabPane tab={intl.formatMessage({ id: 'project.detail.tabs.project-config' })} key="6">
                <ProjectConfig projectCode={props?.data?.code}></ProjectConfig>
              </TabPane>
              <TabPane tab={intl.formatMessage({ id: 'project.detail.tabs.requirement-dashboard' })} key="5"></TabPane>
            </Tabs>
          </Space>
        </Scrollbars>
      </Space>
    </Spin>
  )
}

export default RightControl
