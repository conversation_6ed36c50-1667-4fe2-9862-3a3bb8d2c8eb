import AppState from '@/store/types'
import {
    Button,
    Col, Collapse, Form, Input, Row, Select, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation } from 'react-router-dom'
import intl from '../../../../../config/locale.config'
import { defaultPagingConfig, fileConfig } from '../../../../../constants'
import Ckeditor from '../../../../../helper/component/ckeditor'
import FormGroup from '../../../../../helper/component/form-group'
import { getConfig, saveConfig, syncConfluence, verifyConfluence } from '../../../action'
import { ProjectState } from '../../../type'

const { Panel } = Collapse
const { Title } = Typography

const ProjectConfig = (props: any) => {
    const [isVerify, setIsVerify] = useState<boolean>(false)
    const [isSync, setIsSync] = useState<boolean>(false)
    const [isNormal, setIsNormal] = useState<boolean>(false)
    const overviewRef: any = createRef()
    const abbreviationsRef: any = createRef()
    const location = useLocation()
    const spaceKeyRef = useRef<any>()
    const state = useSelector<AppState | null>(
        (s) => s?.Project
    ) as ProjectState
    const dispatch = useDispatch()
    const [form] = Form.useForm()
    const [generateSelected, setGenerateSelected] = useState<number>(1)
    const [defaultPaging, setDefaultPaging] = useState<number>(10)


    useEffect(() => {
        dispatch(getConfig(props.projectCode))
    }, [])

    useEffect(() => {
        const pathName = location.pathname;
        if (pathName.split('/')[2] !== props.projectCode) {
            dispatch(getConfig(pathName.split('/')[2]))
        }

    }, [location])

    useEffect(() => {
        setGenerateSelected(state.config?.srsGenerationType)
        setDefaultPaging(state.config?.defaultPaging)
        form.setFieldsValue({
            generateSelected: state.config?.srsGenerationType,
            defaultPaging: state.config?.defaultPaging,
            overview: state.config?.overview,
            abbreviations: state.config?.abbreviations,
            confluenceSpaceKey: state.config?.confluenceSpaceKey,
            confluenceDestinationPage: state.config?.confluenceDestinationPage
        })
    }, [state.config])
    const onChangeGenerateFile = (e) => {
        setGenerateSelected(e)
    }
    const onChangeDefaultPaging = (e) => {
        setDefaultPaging(e)
    }
    const onOverviewChange = (e) => {
        form.setFieldsValue({
            overview: e
        })
    }

    const onAbbreviationsChange = (e) => {
        form.setFieldsValue({
            abbreviations: e
        })
    }
    const onSubmit = debounce((values: any, st?: string) => {
        if (isVerify) {
            const requestData = {
                sync: isSync,
                projectCode: props.projectCode,
                confluenceSpaceKey: values.confluenceSpaceKey,
                confluenceDestinationPage: values.confluenceDestinationPage,
            }
            dispatch(verifyConfluence(requestData))
            setIsVerify(false)
        }
        if (isNormal) {
            const requestData = {
                srsGenerationType: values.generateSelected,
                defaultPaging: values.defaultPaging,
                overview: values.overview,
                abbreviations: values.abbreviations,
                confluenceLink: values.confluenceLink,
                confluenceSpaceKey: values.confluenceSpaceKey,
                confluenceDestinationPage: values.confluenceDestinationPage,
            }
            dispatch(saveConfig({
                projectCode: props.projectCode,
                config: requestData
            }))
            setIsNormal(false)
        }
    }, 500)
    return (
        <Spin spinning={state?.isLoading}>
            <Space size="large" direction='vertical'>
                <Form form={form} onFinish={onSubmit} scrollToFirstError={{ block: 'center' }}>
                    <Space direction="vertical" size="small" className='rq-form-group rq-project-info' style={{ padding: '0 10px 2px 0' }}>
                        <Row gutter={[0, 10]}>
                            <Col span={16} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                <Button className="success-btn" htmlType='submit' onClick={() => {
                                    setIsSync(false)
                                    setIsVerify(false)
                                    setIsNormal(true)
                                }
                                }>Submit</Button>
                            </Col>
                            <Col span={8}></Col>
                            <Col span={3}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.config.generate-srs' })}</div></Col>
                            <Col span={6}>
                                <Form.Item style={{ margin: 0 }} name='generateSelected'>
                                    <Select style={{ width: '80%' }} value={generateSelected} onChange={onChangeGenerateFile}>
                                        {
                                            fileConfig.map(e => (
                                                <Select.Option key={e.id} value={e.id}>{e.name}</Select.Option>
                                            ))
                                        }
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={3}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.config.default-paging' })}</div></Col>
                            <Col span={6}>
                                <Form.Item style={{ margin: 0 }} name='defaultPaging'>
                                    <Select style={{ width: '30%' }} value={defaultPaging} onChange={onChangeDefaultPaging}>
                                        {
                                            defaultPagingConfig.map(e => (
                                                <Select.Option key={e.id} value={e.id}>{e.name}</Select.Option>
                                            ))
                                        }
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={24}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.config.project-overview' })}</div></Col>
                            <Form.Item style={{ margin: 0, width: '100%' }} name='overview'>
                                <Col span={16}><Ckeditor ref={overviewRef} data={state.config?.overview || ''} onChange={onOverviewChange} /></Col>
                            </Form.Item>
                        </Row>
                        <Row>
                            <Col span={24}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.config.abbreviations' })}</div></Col>
                            <Form.Item style={{ margin: 0, width: '100%' }} name='abbreviations'>
                                <Col span={16}><Ckeditor ref={abbreviationsRef} data={state.config?.abbreviations || ''} onChange={onAbbreviationsChange} /></Col>
                            </Form.Item>
                        </Row>

                        <Row>
                            <Col span={16}>
                                <Collapse bordered={true} className="rq-audit-trail" style={{ width: '100%' }}>
                                    <Panel
                                        className="description"
                                        header={<Title level={5}>{intl.formatMessage({ id: 'project.config.confluence' })}</Title>}
                                        key="1"
                                    >
                                        <Row>
                                            <Col span={24}>
                                                <div ref={spaceKeyRef}>
                                                    <FormGroup label={intl.formatMessage({ id: 'project.config.confluence-space-key' })} required>
                                                        <Form.Item style={{ margin: 0 }} name='confluenceSpaceKey' rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                                            <Input maxLength={255} />
                                                        </Form.Item>
                                                    </FormGroup>
                                                </div>
                                            </Col>
                                            <Col span={24}>
                                                <FormGroup label={intl.formatMessage({ id: 'project.config.destination-page' })} required>
                                                    <Form.Item rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]} name='confluenceDestinationPage'>
                                                        <Input maxLength={255} />
                                                    </Form.Item>
                                                </FormGroup>
                                            </Col>
                                            <Space>
                                                <Button type='primary' htmlType='submit' onClick={() => {
                                                    setIsSync(false)
                                                    setIsVerify(true)
                                                }}>Verify</Button>
                                                <Button className='success-btn' htmlType='submit' onClick={() => {
                                                    setIsSync(true)
                                                    setIsVerify(true)
                                                }
                                                }>Sync</Button>
                                            </Space>
                                        </Row>

                                    </Panel>
                                </Collapse>
                            </Col>
                        </Row>
                    </Space>
                </Form >
            </Space >
        </Spin >
    )
}

export default ProjectConfig
