import intl from '../../../../../config/locale.config'
import {
  Typography,
  Row,
  Space,
  Button,
  Col,
  Input,
} from 'antd'
import RqTable from '../../../../../helper/component/edit-table-fe'
import { Link } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { initScreenProjectProducts } from '../../../action'
import { DATE_FORMAT, DEFAULT_PAGE_SIZE } from '../../../../../constants'
import AppState from '../../../../../store/types'
import { ViewProjectState } from '../../../type'
import moment from 'moment'
import { SearchOutlined } from '@ant-design/icons'
const { Title } = Typography

const ProjectProducts = (props: any) => {
  const dispatch = useDispatch();
  const state = useSelector<AppState | null>(
    (s) => s?.Project?.detail
  ) as ViewProjectState
  const [productList, setProductList] = useState([]) as any
  const [totalRecord, setTotalRecord] = useState(1)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(DEFAULT_PAGE_SIZE)

  useEffect(() => {
    if (props.projectCode) {
      dispatch(initScreenProjectProducts({ projectCode: props.projectCode, skip: currentPage, take: currentPageSize }))
    }
  }, [props.projectCode])

  useEffect(() => {
    if (state.allProducts) {
      setProductList(state.allProducts.data)
      setTotalRecord(state.allProducts.total)
    }
  }, [state.allProducts])

  const onTableChange = (pagination, filters, sorter, extra) => {
    setCurrentPage(pagination.current)
    setCurrentPageSize(pagination.pageSize)
    dispatch(initScreenProjectProducts({ projectCode: props.projectCode, skip: pagination.current, take: pagination.pageSize, name: filters.name }))
  }
  const handleSearch = (selectedKeys: string[], confirm, dataIndex: string) => {
    confirm()
    // if (dataIndex !== 'status') {
    //   dispath(
    //     searchItem({
    //       searchText: selectedKeys[0],
    //       searchedColumn: dataIndex,
    //     })
    //   )
    // }
  }

  const columns = [
    {
      title: "Product Name",
      dataIndex: "name",
      filterIcon: (filtered) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => {
        // if (filterFirst === false && dataIndex === 'status') {
    
        //   setFilterFirst(true)
        //   setSelectedKeys([[0, 1, 2, 3]])
        //   confirm()
        // }
        return (
          <Row
            style={{
              padding: 8,
              width: '10vw',
            }}
          >
            <Col span={24}>
              {' '}
              <Input
                placeholder={`${intl.formatMessage({
                  id: 'common.text.search',
                })} ${"productname"}`}
                value={selectedKeys[0]}
                onChange={(e) =>
                  setSelectedKeys(e.target.value ? [e.target.value] : [])
                }
                onPressEnter={() =>
                  handleSearch(selectedKeys, confirm, "name")
                }
                style={{ marginBottom: 8, display: 'block' }}
              />
            </Col>
            <Col
          style={{ paddingTop:'16px'}}
          span={24}
        >
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, "name")}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            {intl.formatMessage({ id: 'common.action.search' })}
          </Button>
        </Col>
      </Row>)
      }
    },
    {
      title: "Phase",
      dataIndex: "phase"
    },
    {
      title: "Product Type",
      dataIndex: "productType"
    },
    {
      title: "Version",
      dataIndex: "version"
    },
    {
      title: "Is Deliverable",
      dataIndex: "isDeliverable",
    },
    {
      title: "Milestone",
      dataIndex: "milestone"
    },
    {
      title: "Final",
      dataIndex: "isFinal",
    },
    {
      title: "Status",
      dataIndex: "status"
    },
    {
      title: "Planned Release",
      dataIndex: "plannedRelease",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: "Re-planned",
      dataIndex: "rePlanned",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: "Actual Release",
      dataIndex: "actualRelease",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    }
  ];

  return (
    <>
      <Row align="middle" justify="space-between" style={{ minHeight: 32 }}>
        <Title level={4} className='rq-page-title'>{props?.projectCode || ''}</Title>
      </Row>
      <Space direction="vertical" size="small" className='rq-form-group' style={{ padding: '0 10px 2px 0' }}>
        <RqTable
          key="id"
          dataSource={productList}
          columns={columns}
          isLoading={state.isLoadingProducts}
          onChangeHandle={onTableChange}
          pagination={{
            position: ['topRight'],
            total: totalRecord,
            size: 'small',
            showLessItems: true,
            showSizeChanger: true,
            showTotal: (total, range) => {
              return `${range[0]}-${range[1]} ${intl.formatMessage({
                id: 'project.pagination.of',
              })} ${total} ${intl.formatMessage({
                id: 'project.pagination.items',
              })}`
            }
          }}
        ></RqTable>
      </Space>
    </>
  )
}

export default ProjectProducts
