import intl from '../../../../../config/locale.config'
import {
  Typography,
  Row,
  Col,
  Space,
  Button,
  Select,
  Input,
} from 'antd'
import RqTable from '../../../../../helper/component/edit-table-fe'
import { Link } from 'react-router-dom'
import ProjectMemberForm from './form'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {  initScreenProjectMembers, initScreenProjectMembersStakeholders } from '../../../action'
import { APP_ROLES, DATE_FORMAT, DEFAULT_PAGE_SIZE } from '../../../../../constants'
import AppState from '../../../../../store/types'
import { ViewProjectState } from '../../../type'
import moment from 'moment'
import { SearchOutlined } from '@ant-design/icons'
import { hasRole } from '../../../../../helper/share'
const { Title, Text } = Typography

const ProjectMembers = (props: any) => {
  const dispatch = useDispatch();
  const state = useSelector<AppState | null>(
    (s) => s?.Project?.detail
  ) as ViewProjectState
  const [memberList, setMemberList] = useState([]) as any
  const [totalRecord, setTotalRecord] = useState(1)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [stakeholdersList, setstakeholdersList] = useState([]) as any
  const [totalStakeholdersRecord, setTotalStakeholdersRecord] = useState(1)
  const [currentStakeholdersPage, setCurrentStakeholdersPage] = useState(1)
  const [currentStakeholdersPageSize, setCurrentStakeholdersPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [toggleEdit, setToggleEdit] = useState(false)
  useEffect(() => {
    if (props.projectCode) {
      dispatch(initScreenProjectMembers({ projectCode: props.projectCode, skip: currentPage, take: currentPageSize }))
      dispatch(initScreenProjectMembersStakeholders({ projectCode: props.projectCode, skip: currentStakeholdersPage, take: currentStakeholdersPageSize }))
    }
  }, [props.projectCode])

  useEffect(() => {
    if (state.allMembers) {
      setMemberList(state.allMembers.data)
      setTotalRecord(state.allMembers.total)
    }
  }, [state.allMembers])

  useEffect(() => {
    if (state.allMembersStakeholders) {
      setstakeholdersList(state.allMembersStakeholders.data)
      setTotalStakeholdersRecord(state.allMembersStakeholders.total)
    }
  }, [state.allMembersStakeholders])

  const onTableChange = (pagination, filters, sorter, extra) => {
    setCurrentPage(pagination.current)
    setCurrentPageSize(pagination.pageSize)
    dispatch(initScreenProjectMembers({ projectCode: props.projectCode, skip: pagination.current, take: pagination.pageSize, username: filters.username }))
  }

  const onStakeholdersTableChange = (pagination, filters, sorter, extra) => {
    setCurrentStakeholdersPage(pagination.current)
    setCurrentStakeholdersPageSize(pagination.pageSize)
    dispatch(initScreenProjectMembersStakeholders({ projectCode: props.projectCode, skip: pagination.current, take: pagination.pageSize, username: filters.username }))
  }

  const handleSearch = (selectedKeys: string[], confirm, dataIndex: string) => {
    confirm()
    // if (dataIndex !== 'status') {
    //   dispath(
    //     searchItem({
    //       searchText: selectedKeys[0],
    //       searchedColumn: dataIndex,
    //     })
    //   )
    // }
  }

  const columns = [
    {
      title: "Project Member",
      dataIndex: "username",
      render: (text: any, record) => {
        return <Link to={'/'}>{text}</Link>
      },
      filterIcon: (filtered) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => {
        // if (filterFirst === false && dataIndex === 'status') {

        //   setFilterFirst(true)
        //   setSelectedKeys([[0, 1, 2, 3]])
        //   confirm()
        // }
        return (
          <Row
            style={{
              padding: 8,
              width: '10vw',
            }}
          >
            <Col span={24}>
              {' '}
              <Input
                placeholder={`${intl.formatMessage({
                  id: 'common.text.search',
                })} ${"username"}`}
                value={selectedKeys[0]}
                onChange={(e) =>
                  setSelectedKeys(e.target.value ? [e.target.value] : [])
                }
                onPressEnter={() =>
                  handleSearch(selectedKeys, confirm, "username")
                }
                style={{ marginBottom: 8, display: 'block' }}
              />
            </Col>
            <Col
              style={{ paddingTop: '16px' }}
              span={24}
            >
              <Button
                type="primary"
                onClick={() => handleSearch(selectedKeys, confirm, "username")}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                {intl.formatMessage({ id: 'common.action.search' })}
              </Button>
            </Col>
          </Row>)
      }
    },
    {
      title: "Planned (h)",
      dataIndex: "planned"
    },
    {
      title: "From",
      dataIndex: "fromDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: "To",
      dataIndex: "toDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: "Role",
      dataIndex: "roles",
      render: (text: any, record) => {
        return (
          hasRole(APP_ROLES.PM, props?.projectCode) &&
          (
            props?.projectStatus?.toLowerCase() == intl.formatMessage({ id: 'common.status.on-going' })?.toLowerCase() ||
            props?.projectStatus?.toLowerCase() == intl.formatMessage({ id: 'common.status.tentative' })?.toLowerCase()
          )
        ) ? 
        <ProjectMemberForm type="EDIT" projectCode={props.projectCode} memberId={record.id} default={text} onChange={handleUpdateSuccess} username={record.username} skip= {currentPage} take= {currentPageSize}/> 
        : (text ? text.join(', ') : '')
      }
    },
  ];
  const columnsStakeholders = [
    {
      title: "Project Stakeholder",
      dataIndex: "username"
    },
    {
      title: "Role",
      dataIndex: "role",
    },
    {
      title: "From",
      dataIndex: "startDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
      width: '20%'
    },
    {
      title: "To",
      dataIndex: "endDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
      width: '20%'
    },
  ];

  const handleUpdateSuccess = (e) => {
    if (e)
      dispatch(initScreenProjectMembers({ projectCode: props.projectCode, skip: currentPage, take: currentPageSize }))
  }

  return (
    <>
      <Row align="middle" justify="space-between" style={{ minHeight: 32 }}>
        <Title level={4} className='rq-page-title'>{props?.projectCode || ''}</Title>
      </Row>
      <Space direction="vertical" size="small" className='rq-form-group' style={{ padding: '0 10px 2px 0' }}>
        <RqTable
          key="id"
          dataSource={memberList}
          columns={columns}
          isLoading={state.isLoadingMembers}
          onChangeHandle={onTableChange}
          pagination={{
            position: ['topRight'],
            total: totalRecord,
            size: 'small',
            showLessItems: true,
            showSizeChanger: true,
            showTotal: (total, range) => {
              return `${range[0]}-${range[1]} ${intl.formatMessage({
                id: 'project.pagination.of',
              })} ${total} ${intl.formatMessage({
                id: 'project.pagination.items',
              })}`
            }
          }}
        ></RqTable>
        <RqTable
          key="id"
          dataSource={stakeholdersList}
          columns={columnsStakeholders}
          isLoading={state.isLoadingMembersStakeholders}
          onChangeHandle={onStakeholdersTableChange}
          pagination={{
            position: ['topRight'],
            total: totalStakeholdersRecord,
            size: 'small',
            showLessItems: true,
            showSizeChanger: true,
            showTotal: (total, range) => {
              return `${range[0]}-${range[1]} ${intl.formatMessage({
                id: 'project.pagination.of',
              })} ${total} ${intl.formatMessage({
                id: 'project.pagination.items',
              })}`
            }
          }}
        ></RqTable>
      </Space>
    </>
  )
}

export default ProjectMembers
