import React, { useEffect, useState } from 'react'
import {
  Form,
  Modal,
  Select,
  Typography,
} from 'antd'
import debounce from 'lodash.debounce'
import intl from '../../../../../config/locale.config'
import useModalConfirmationConfig from '../../../../../helper/hooks/useModalConfirmationConfig'
import { useDispatch, useSelector } from 'react-redux'
import AppState from '../../../../../store/types'
import { ProjectMemberState } from '../../../type'
import { resetMemberState, updateMemberRequest } from '../../../action'
import { ShowAppMessage } from '../../../../../helper/share'
import { MESSAGE_TYPE } from '../../../../../constants'

const { confirm } = Modal
const { Option } = Select
const { Text } = Typography

const ProjectMemberForm = (props: any) => {
  const state = useSelector<AppState | null>(
    (s) => s?.Project?.updateMember
  ) as ProjectMemberState
  const dispatch = useDispatch()
  const [form] = Form.useForm()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isModalVisible1, setIsModalVisible1] = useState(false)
  const modalConfirmConfig = useModalConfirmationConfig()
  const [listRole, setListRole] = useState([])
  const [currentRole, setCurrentRole] = useState([])

  useEffect(() => {
    setListRole(props.default)
    setCurrentRole(props.default)
  }, [])
  useEffect(() => {
    if (state.getDataStatus) {
      form.setFieldsValue({
        roles: state.memberData.roles
      })
    }
  }, [state.getDataStatus])

  const onFinish = debounce((values: any) => {
    let requestData: any = {
      "roles": values.roles
    };
    dispatch(updateMemberRequest({ projectCode: props.projectCode, memberId: props.memberId, requestData: requestData }))
  }, 500)

  const cfCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_3' })}`,
      onOk() {
        props.onChange()
        dispatch(resetMemberState())
        setIsModalVisible(false)
      },
      onCancel() { },
    })
  }

  const handleChangeRoleList = (selectedItems) => {
    if (selectedItems.length == 0) {
      setIsModalVisible1(true)
      setListRole(currentRole)
    } else {
      setIsModalVisible(true)
      setListRole(selectedItems)
    }
    //dispatch(updateMemberRequest({ projectCode: props.projectCode, memberId: props.memberId, requestData: requestData, username: props.username}))
  }
  const handleOk = () => {
    try {
      let requestData: any = {
        "roles": listRole
      };
      dispatch(updateMemberRequest({ projectCode: props.projectCode, memberId: props.memberId, requestData: requestData, username: props.username }))
      setIsModalVisible(false)
      setCurrentRole(listRole)
    } catch {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      setListRole(currentRole)
    }
  }
  const handleCancel = () => {
    setIsModalVisible(false)
    setIsModalVisible1(false)
    setListRole(currentRole)
  }


  const handleOk1 = () => {
    setIsModalVisible1(false)
  }
  const handleCancel1 = () => {
    setIsModalVisible1(false)
  }
  return (
    <>
      <Select mode='multiple' optionLabelProp="label" value={listRole} onChange={handleChangeRoleList} style={{ width: 250 }} defaultValue={listRole}>
        <Option key={"PM"} value="PM">PM</Option>
        <Option key={"BA"} value="BA">BA</Option>
        <Option key={"DEV"} value="DEV">DEV</Option>
        <Option key={"TEST"} value="TEST">TEST</Option>
        <Option key={"QA"} value="QA">QA</Option>
        <Option key={"Customer"} value="Customer">CUSTOMER</Option>
        <Option key={"BA Lead"} value="BA Lead">BA Lead</Option>
      </Select>
      <>
        <Modal maskClosable={false} title={`${intl.formatMessage({
          id: 'project.title.confirm',
        })}`} visible={isModalVisible} onOk={handleOk} onCancel={handleCancel} okText={`${intl.formatMessage({
          id: 'project.label.confirm',
        })}`} closable={false} centered>
          <Text>{intl.formatMessage({
            id: 'project.message.confirm',
          })}</Text>
        </Modal>
      </>
      <>
        <Modal title={`${intl.formatMessage({
          id: 'project.title.warning',
        })}`} visible={isModalVisible1} onOk={handleOk1} onCancel={handleCancel1} closable={false} centered okButtonProps={{ style: { display: 'none' } }}>
          <Text>{intl.formatMessage({
            id: 'project.message.warning',
          })}</Text>
        </Modal>
      </>
    </>
  )
}

export default ProjectMemberForm
