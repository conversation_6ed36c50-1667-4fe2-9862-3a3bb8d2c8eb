import intl from '../../../../../config/locale.config'
import {
  Typography,
  Row,
  Col,
  Space,
} from 'antd'
import moment from 'moment'
import { DATE_FORMAT } from '../../../../../constants'
const { Title } = Typography

const ProjectInformation = (props: any) => {
  return (
    <Space size="large" direction='vertical'>
      <Row align="middle" justify="space-between" style={{ minHeight: 32 }}>
        <Title level={4} className='rq-page-title'>{props?.projectCode || ''}</Title>
      </Row>
      <Space direction="vertical" size="small" className='rq-form-group rq-project-info' style={{ padding: '0 10px 2px 0' }}>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.group' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.group}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-manager' })}</div></Col>
          <Col span={20}>
            <div className='rq-form-value'>
              <a href='#'>{props?.data?.manager}</a>
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-status' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.status}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.customer' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.customer}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.rank' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.rank}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.start-date' })}</div></Col>
          <Col span={20}>
            <div className='rq-form-value'>
              {/* lavdate */}
              {props?.data?.startDate ? moment(props.data.startDate).format(DATE_FORMAT) : ''}
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.end-date' })}</div></Col>
          <Col span={20}>
            <div className='rq-form-value'>
              {/* lavdate */}
              {props?.data?.endDate ? moment(props.data.endDate).format(DATE_FORMAT) : ''}
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-contract-type' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.contractType}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.industry' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.industry}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-category' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.category}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-scope' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.scope}</div></Col>
        </Row>
        <Row>
          <Col span={4}>
            <div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.requirement-methodology' })}</div>
          </Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.methodology}</div></Col>
        </Row>
        <Row>
          <Col span={4}><div className='rq-form-label'>{intl.formatMessage({ id: 'project.info.project-description' })}</div></Col>
          <Col span={20}><div className='rq-form-value'>{props?.data?.description}</div></Col>
        </Row>
      </Space>
    </Space>
  )
}

export default ProjectInformation
