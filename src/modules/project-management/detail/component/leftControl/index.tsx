import React, { useEffect } from 'react'
import { Menu, Row, Col, Typography, Space, Button, Spin } from 'antd'
import { Scrollbars } from 'react-custom-scrollbars'
import { Link, useHistory } from 'react-router-dom'
import { ArrowLeftOutlined } from '@ant-design/icons'
import useWindowDimensions from '../../../../../helper/hooks/useWindowDimensions'
import intl from '../../../../../config/locale.config'
import { APP_ROUTES } from '../../../../../constants'

const { Title, Text } = Typography

const LeftControl = (props) => {
  const { height: windowHeight } = useWindowDimensions();
  const history = useHistory()

  const changeMenu = (code: string) => {
    if (props.activeId !== code) {
      history.push(APP_ROUTES.PROJECT_DETAIL + code)
    }
  }

  useEffect(() => {
    window.setTimeout(() => {
      document.getElementById(props.activeId)?.focus()
    }, 500)
  }, [props.activeId])

  return (
    <>
      <div className="record-detail-left-control-container">
        <Spin spinning={props.isLoading}>
          <Space direction="vertical" className="full-width">
            <div className='rq-panel-heading' style={{ padding: '1rem 1rem 0' }}>
              <Title className='rq-panel-title' level={3}>
                {intl.formatMessage({ id: 'project.detail.project-list' })}
              </Title>
              <Link to={APP_ROUTES.PROJECTS}>
                <Button
                  size="middle"
                  type="primary"
                  ghost
                  icon={<ArrowLeftOutlined />}
                >
                  {intl.formatMessage({ id: 'project.detail.back' })}
                </Button>
              </Link>
            </div>
            {props?.projectList &&
              <Scrollbars
                autoHide
                autoHeight
                autoHeightMin={windowHeight - 210}
                autoHeightMax={windowHeight - 210}
              >
                <Menu
                  style={{ borderRight: 0 }}
                  selectedKeys={[props.activeId]}
                  mode="inline"
                  theme="light"
                  className="detail-menu"
                >
                  {
                    props.projectList.map((item: any) => (
                      <Menu.Item
                        id={item.code}
                        className="detail-menu-item"
                        key={item.code}
                        onClick={() => changeMenu(item.code)}
                      >
                        <Row>
                          <Col span={24}>{`${item.code || ''}`}</Col>
                          <Col span={24}>{`${item.methodology || ''}`}</Col>
                          <Col span={24}>
                            <Text type="secondary">{item.projectStatus || ''}</Text>
                          </Col>
                        </Row>
                      </Menu.Item>
                    ))}
                </Menu>
              </Scrollbars>
            }
          </Space>
        </Spin>
      </div>
    </>
  )
}

export default React.memo(LeftControl)
