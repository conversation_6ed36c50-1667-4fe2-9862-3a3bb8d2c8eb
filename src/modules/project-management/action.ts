import { createAction } from '@reduxjs/toolkit'
import { ProjectEnum } from './type'

export const initScreen = createAction<any>(ProjectEnum.INIT_SCREEN)
export const initScreenSuccess = createAction<any>(ProjectEnum.INIT_SCREEN_SUCCESS)
export const initScreenFailure = createAction<any>(ProjectEnum.INIT_SCREEN_FAILURE)

export const initScreenUpdate = createAction<any>(ProjectEnum.INIT_SCREEN_UPDATE)
export const initScreenUpdateSuccess = createAction<any>(ProjectEnum.INIT_SCREEN_UPDATE_SUCCESS)
export const initScreenUpdateFailure = createAction<any>(ProjectEnum.INIT_SCREEN_UPDATE_FAILURE)

export const updateProjectRequest = createAction<any>(ProjectEnum.UPDATE_PROJECT_REQUEST)
export const updateProjectSuccess = createAction<any>(ProjectEnum.UPDATE_PROJECT_SUCCESS)
export const updateProjectFailure = createAction<any>(ProjectEnum.UPDATE_PROJECT_FAILURE)

export const resetUpdateProjectState = createAction(ProjectEnum.RESET_UPDATE_STATE)

export const initScreenMenu = createAction<any>(ProjectEnum.INIT_SCREEN_MENU)
export const initScreenMenuSuccess = createAction<any>(ProjectEnum.INIT_SCREEN_MENU_SUCCESS)
export const initScreenMenuFailure = createAction<any>(ProjectEnum.INIT_SCREEN_MENU_FAILURE)

export const initScreenDetail = createAction<any>(ProjectEnum.INIT_SCREEN_DETAIL)
export const initScreenDetailSuccess = createAction<any>(ProjectEnum.INIT_SCREEN_DETAIL_SUCCESS)
export const initScreenDetailFailure = createAction<any>(ProjectEnum.INIT_SCREEN_DETAIL_FAILURE)
export const resetScreenDetailState = createAction(ProjectEnum.RESET_SCREEN_DETAIL_STATE)

export const initScreenProjectMembers = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_MEMBERS)
export const initScreenProjectMembersSuccess = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_MEMBERS_SUCCESS)
export const initScreenProjectMembersFailure = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_MEMBERS_FAILURE)

export const initScreenProjectMembersStakeholders = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS)
export const initScreenProjectMembersStakeholdersSuccess = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS_SUCCESS)
export const initScreenProjectMembersStakeholdersFailure = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS_FAILURE)

export const initScreenProjectProducts = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_PRODUCTS)
export const initScreenProjectProductsSuccess = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_PRODUCTS_SUCCESS)
export const initScreenProjectProductsFailure = createAction<any>(ProjectEnum.INIT_SCREEN_PROJECT_PRODUCTS_FAILURE)

export const initMemberScreen = createAction<any>(ProjectEnum.INIT_MEMBER_SCREEN)
export const initMemberScreenSuccess = createAction<any>(ProjectEnum.INIT_MEMBER_SCREEN_SUCCESS)
export const initMemberScreenFailure = createAction<any>(ProjectEnum.INIT_MEMBER_SCREEN_FAILURE)

export const updateMemberRequest = createAction<any>(ProjectEnum.UPDATE_MEMBER_REQUEST)
export const updateMemberSuccess = createAction<any>(ProjectEnum.UPDATE_MEMBER_SUCCESS)
export const updateMemberFailure = createAction<any>(ProjectEnum.UPDATE_MEMBER_FAILURE)
export const resetMemberState = createAction(ProjectEnum.RESET_MEMBER_STATE)

export const getListGroup = createAction<void>(ProjectEnum.GET_GROUP_LIST)
export const getListGroupSuccess = createAction<any>(ProjectEnum.GET_GROUP_LIST_SUCCESS)
export const getListGroupFailure = createAction<any>(ProjectEnum.GET_GROUP_LIST_FAILURE)

export const getListStatus = createAction<void>(ProjectEnum.GET_STATUS_LIST)
export const getListStatusSuccess = createAction<any>(ProjectEnum.GET_STATUS_LIST_SUCCESS)
export const getListStatusFailure = createAction<any>(ProjectEnum.GET_STATUS_LIST_FAILURE)

export const getConfig = createAction<any>(ProjectEnum.GET_CONFIG)
export const getConfigSuccess = createAction<any>(ProjectEnum.GET_CONFIG_SUCCESS)
export const getConfigFailure = createAction<any>(ProjectEnum.GET_CONFIG_FAILURE)

export const saveConfig = createAction<any>(ProjectEnum.SAVE_CONFIG)
export const saveConfigSuccess = createAction<any>(ProjectEnum.SAVE_CONFIG_SUCCESS)
export const saveConfigFailure = createAction<any>(ProjectEnum.SAVE_CONFIG_FAILURE)


export const verifyConfluence = createAction<any>(ProjectEnum.VERIFY_CONFLUENCE)
export const verifyConfluenceDone = createAction<any>(ProjectEnum.VERIFY_CONFLUENCE_DONE)

export const syncConfluence = createAction<any>(ProjectEnum.SYNC_CONFLUENCE)
export const syncConfluenceDone = createAction<any>(ProjectEnum.SYNC_CONFLUENCE_DONE)

