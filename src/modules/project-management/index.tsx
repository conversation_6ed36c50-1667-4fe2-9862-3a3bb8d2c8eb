import {
  Row,
  <PERSON>,
  Typography,
  Col,
  Checkbox,
  Button,
} from 'antd'
import { Scrollbars } from 'react-custom-scrollbars'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import AppState from '../../store/types'
import intl from '../../config/locale.config'
import { initScreen } from './action'
import { useDispatch, useSelector } from 'react-redux'
import RqTable from '../../helper/component/edit-table-fe'
import { DataSourceType } from './type'
import { ProjectState } from './type'
import UpdateProject from './update'
import { APP_ROLES, APP_ROUTES, DATE_FORMAT, DEFAULT_PAGE_SIZE, PROJECT_STATUS, SEARCH_TYPE } from '../../constants'
import moment from 'moment'
import { getColumnSearchProps, hasRole } from '../../helper/share'
import { SearchOutlined } from '@ant-design/icons'
import useWindowDimensions from '../../helper/hooks/useWindowDimensions'
const { Title } = Typography
const ViewProject = () => {
  const [projectList, setProjectList] = useState([]) as any
  const [totalRecord, setTotalRecord] = useState(1)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [prjStatus, setPrjStatus] = useState([])
  const [groups, setGroups] = useState([]) as any
  const [prjCode, setPrjCode] = useState([])
  const dispatch = useDispatch()
  const { height: windowHeight } = useWindowDimensions()
  const state = useSelector<AppState | null>(
    (s) => s?.Project
  ) as ProjectState

  const [indeterminateStatus, setIndeterminateStatus] = useState(false);
  const [checkAllStatus, setCheckAllStatus] = useState(false);

  useEffect(() => {
    document.title = intl.formatMessage({
      id: 'project.table.title',
    });
    dispatch(initScreen({ skip: currentPage, take: currentPageSize }))
  }, [])

  useEffect(() => {
    if (state.update.updateStatus) {
      dispatch(initScreen({ skip: currentPage, take: currentPageSize }))
    }
  }, [state.update.updateStatus])

  useEffect(() => {
    if (state) {
      setProjectList(state.response.data)
      setTotalRecord(state.response.total)
    }
  }, [state.response])

  const handleSearch = (selectedKeys: string[], confirm, dataIndex: string) => {
    confirm()
    initScreen({ group: selectedKeys })

  }

  const columns = [
    {
      title: intl.formatMessage({ id: 'project.table.project-code' }),
      dataIndex: "code",
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: DataSourceType) => {
        return <Link to={`${APP_ROUTES.PROJECT_DETAIL}${record.code}`}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'project.table.group' }),
      dataIndex: "group",
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => {
        return (
          <Row
            style={{
              padding: 8,
              width: '12vw',
            }}
          >
            <Col span={24}>
              <Checkbox.Group
                onChange={(e) => {
                  setSelectedKeys(e ? [e] : [])
                }}
              >
                <Scrollbars
                  autoHide
                  autoHeight
                  autoHeightMin={windowHeight * 0.30}
                  autoHeightMax={windowHeight * 0.05}
                >
                  <Row gutter={[16, 4]}>
                    {state.group?.map((item) => (
                      <Col key={item.value} span={24}>
                        <Checkbox id={item.value} value={item.value}>{item.text || intl.formatMessage({ id: 'common.enum.blank-value' })}</Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Scrollbars>
              </Checkbox.Group>
            </Col>
            <Col
              style={{
                paddingTop: '16px'
              }}
              span={24}
            >
              <Button
                type="primary"
                onClick={() => handleSearch(selectedKeys, confirm, "group")}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                {intl.formatMessage({ id: 'common.action.search' })}
              </Button>
            </Col>
          </Row>)
      }
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-management' }),
      dataIndex: "manager",
      render: (text: string, record: DataSourceType) => {
        return <Link style={{ textDecoration: 'underline' }} to={`/`}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-status' }),
      dataIndex: "status",
      filterMode: 'tree',
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => {

        const onCheckAllChange = (e: any) => {
          setSelectedKeys(e.target.checked ? PROJECT_STATUS : []);
          setIndeterminateStatus(false);
          setCheckAllStatus(e.target.checked);
        };

        return (
          <Row
            style={{
              padding: 8,
              width: '12vw',
            }}
          >
            <Col span={24}>
              <Checkbox indeterminate={indeterminateStatus} onChange={onCheckAllChange} checked={checkAllStatus}>All</Checkbox>
              <Checkbox.Group
                onChange={(e) => {
                  setSelectedKeys(e || [])
                  setIndeterminateStatus(!!e.length && e.length < PROJECT_STATUS.length);
                  setCheckAllStatus(e.length === PROJECT_STATUS.length);
                }}
                value={selectedKeys}
              >
                <Scrollbars
                  autoHide
                  autoHeight
                  autoHeightMin={windowHeight * 0.30}
                  autoHeightMax={windowHeight * 0.05}
                >
                  <Row gutter={[16, 4]}>
                    {
                      PROJECT_STATUS.map((e, idx) => {
                        return <Col key={idx} span={24}>
                          <Checkbox id={`chk_${idx}`} value={e}>{e}</Checkbox>
                        </Col>
                      })
                    }
                  </Row>
                </Scrollbars>
              </Checkbox.Group>
            </Col>
            <Col
              style={{
                paddingTop: '16px'
              }}
              span={24}
            >
              <Button
                type="primary"
                onClick={() => handleSearch(selectedKeys, confirm, "group")}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                {intl.formatMessage({ id: 'common.action.search' })}
              </Button>
            </Col>
          </Row>)
      }
    },
    {
      title: intl.formatMessage({ id: 'project.table.customer' }),
      dataIndex: "customer"
    },
    {
      title: intl.formatMessage({ id: 'project.table.rank' }),
      dataIndex: "rank"
    },
    {
      title: intl.formatMessage({ id: 'project.table.start-date' }),
      dataIndex: "startDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: intl.formatMessage({ id: 'project.table.end-date' }),
      dataIndex: "endDate",
      render: (text: any, record) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      }
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-contract-type' }),
      dataIndex: "contractType"
    },
    {
      title: intl.formatMessage({ id: 'project.table.industry' }),
      dataIndex: "industry"
    },
    {
      title: intl.formatMessage({ id: 'project.table.project-catagory' }),
      dataIndex: "category"
    },
    {
      title: intl.formatMessage({ id: 'project.table.action' }),
      dataIndex: 'operation',
      className: 'rq-action',
      render: (_: any, record) => {
        return (
          hasRole(APP_ROLES.PM, record?.code) &&
          (
            record?.status?.toLowerCase() == intl.formatMessage({ id: 'common.status.on-going' })?.toLowerCase() ||
            record?.status?.toLowerCase() == intl.formatMessage({ id: 'common.status.tentative' })?.toLowerCase()
          )
        ) ? <UpdateProject projectCode={record.code} onChange={handleUpdateSuccess} /> : <></>
      },
    },
  ]

  const handleUpdateSuccess = (e) => {
    if (e)
      dispatch(initScreen({ skip: currentPage, take: currentPageSize, status: prjStatus, group: groups, code: prjCode }))
  }

  const onTableChange = (pagination, filters, sorter, extra) => {
    setCurrentPage(pagination.current)
    setCurrentPageSize(pagination.pageSize)
    setPrjStatus(filters.status)
    setGroups(filters.group)
    setPrjCode(filters.code)
    dispatch(initScreen({ skip: pagination.current, take: pagination.pageSize, code: filters.code, status: filters.status, group: filters.group }))
  }
  return (
    <>
      <Space direction="vertical" size="middle" className="full-width p-20px">
        <div className='rq-page-heading'>
          <Row align="middle" justify="space-between">
            <div>
              <Title level={3} className='rq-page-title'>
                {intl.formatMessage({
                  id: 'project.table.title',
                })}
              </Title>
            </div>
          </Row>
        </div>
        <RqTable
          key="id"
          dataSource={projectList}
          columns={columns}
          isLoading={state.isLoading}
          onChangeHandle={onTableChange}
          pagination={{
            position: ['topRight'],
            total: totalRecord,
            size: 'small',
            showLessItems: true,
            showSizeChanger: true,
            showTotal: (total, range) => {
              return `${range[0]}-${range[1]} ${intl.formatMessage({
                id: 'project.pagination.of',
              })} ${total} ${intl.formatMessage({
                id: 'project.pagination.items',
              })}`
            }
          }}
        ></RqTable>
      </Space>
    </>
  )
}

export default ViewProject