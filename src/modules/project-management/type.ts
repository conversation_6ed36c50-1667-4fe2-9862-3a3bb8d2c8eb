import { Pagination } from '../../helper/share/type'

// export interface Item {
//   id: number
//   code: string
//   group: string
//   manager: string
//   projectStatus: string
//   customer: string
//   rank: string
//   startDate: any
//   endDate: any
//   contractType: string
//   industry: string
//   category: string
//   scope: string
//   methodology: string
//   description: string
//   auditTrailDescription: string
// }

export interface ProjectState {
  isLoading: boolean
  response: any
  total: number
  skip: number
  currentPage: number
  take: number
  update: UpdateProjectState
  detail: ViewProjectState
  updateMember: ProjectMemberState
  config: ConfigProjectState
  group: any
  status: any
  code: string
}
export interface ProjectMemberState {
  memberData: any
  isLoading: boolean
  error: any
  message: ''
  updateStatus: boolean
  getDataStatus: boolean
}
export interface UpdateProjectState {
  projectData: any
  isLoading: boolean
  error: any
  message: ''
  updateStatus: boolean
  getDataStatus: boolean
}

export interface ConfigProjectState {
  id: number | null,
  code: string,
  name: string,
  srsGenerationType: number,
  defaultPaging: number,
  overview: string,
  abbreviations: string
  messageAction: string
  confluenceSpaceKey: string
  confluenceDestinationPage: string
}
export interface ViewProjectState {
  isLoadingMenu: boolean
  allProjects: any
  isLoading: boolean
  projectData: any
  isLoadingMembers: boolean
  allMembers: any
  isLoadingProducts: boolean
  allProducts: any
  error: string,
  isLoadingMembersStakeholders: boolean,
  allMembersStakeholders: any,
}
export enum ProjectStatusEnum {
  DRAFT,
  SUMITTED,
  CANCELLED,
  CLOSED,
  DELETED,
}

export type ProjectModalProps = {
  isVisible?: boolean
  mode: 'NONE' | 'UPDATE'
  data?: DataSourceType
  isVisibleActorDetail?: boolean
  isCreateAnother?: boolean
}

export type DataSourceType = {
  id: number
  dateCreated: Date
  dateUpdate: Date
  dateDeleted: Date
  description: string
  createdBy: string
  updatedBy: string
  deletedBy: string
  activeStatus: number
  name: string
  order: number
  status: ProjectStatusEnum
  code: string
}

export enum Status {
  ONGOING = 0,
  TENTATIVE = 1,
  CANCELLED = 2,
  CHECK_IN = 3,
  DELETED = 4,
  CLOSED = 5
}
export enum ProjectEnum {
  INIT_SCREEN = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN',
  INIT_SCREEN_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_SUCCESS',
  INIT_SCREEN_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_FAILURE',

  INIT_SCREEN_UPDATE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_UPDATE',
  INIT_SCREEN_UPDATE_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_UPDATE_SUCCESS',
  INIT_SCREEN_UPDATE_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_UPDATE_FAILURE',

  UPDATE_PROJECT_REQUEST = '@@MODULES/PROJECTMANAGEMENT/UPDATE_PROJECT_REQUEST',
  UPDATE_PROJECT_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/UPDATE_PROJECT_SUCCESS',
  UPDATE_PROJECT_FAILURE = '@@MODULES/PROJECTMANAGEMENT/UPDATE_PROJECT_FAILURE',
  RESET_UPDATE_STATE = '@@MODULES/PROJECTMANAGEMENT/RESET_UPDATE_STATE',

  INIT_SCREEN_MENU = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_MENU',
  INIT_SCREEN_MENU_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_MENU_SUCCESS',
  INIT_SCREEN_MENU_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_MENU_FAILURE',

  INIT_SCREEN_DETAIL = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_DETAIL',
  INIT_SCREEN_DETAIL_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_DETAIL_SUCCESS',
  INIT_SCREEN_DETAIL_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_DETAIL_FAILURE',
  RESET_SCREEN_DETAIL_STATE = '@@MODULES/PROJECTMANAGEMENT/RESET_SCREEN_DETAIL_STATE',

  INIT_SCREEN_PROJECT_MEMBERS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_MEMBERS',
  INIT_SCREEN_PROJECT_MEMBERS_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_MEMBERS_SUCCESS',
  INIT_SCREEN_PROJECT_MEMBERS_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_MEMBERS_FAILURE',

  INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS',
  INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS_SUCCESS',
  INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_MEMBERS_STAKEHOLDERS_FAILURE',

  INIT_SCREEN_PROJECT_PRODUCTS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_PRODUCTS',
  INIT_SCREEN_PROJECT_PRODUCTS_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_PRODUCTS_SUCCESS',
  INIT_SCREEN_PROJECT_PRODUCTS_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_SCREEN_PROJECT_PRODUCTS_FAILURE',


  INIT_MEMBER_SCREEN = '@@MODULES/PROJECTMANAGEMENT/INIT_MEMBER_SCREEN',
  INIT_MEMBER_SCREEN_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/INIT_MEMBER_SCREEN_SUCCESS',
  INIT_MEMBER_SCREEN_FAILURE = '@@MODULES/PROJECTMANAGEMENT/INIT_MEMBER_SCREEN_FAILURE',
  UPDATE_MEMBER_REQUEST = '@@MODULES/PROJECTMANAGEMENT/UPDATE_MEMBER_REQUEST',
  UPDATE_MEMBER_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/UPDATE_MEMBER_SUCCESS',
  UPDATE_MEMBER_FAILURE = '@@MODULES/PROJECTMANAGEMENT/UPDATE_MEMBER_FAILURE',
  RESET_MEMBER_STATE = '@@MODULES/PROJECTMANAGEMENT/RESET_MEMBER_STATE',

  GET_GROUP_LIST = '@@MODULES/PROJECTMANAGEMENT/GET_GROUP_LIST',
  GET_GROUP_LIST_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/GET_GROUP_LIST_SUCCESS',
  GET_GROUP_LIST_FAILURE = '@@MODULES/PROJECTMANAGEMENT/GET_GROUP_LIST_FAILURE',

  GET_STATUS_LIST = '@@MODULES/PROJECTMANAGEMENT/GET_STATUS_LIST',
  GET_STATUS_LIST_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/GET_STATUS_LIST_SUCCESS',
  GET_STATUS_LIST_FAILURE = '@@MODULES/PROJECTMANAGEMENT/GET_STATUS_LIST_FAILURE',

  GET_CONFIG = '@@MODULES/PROJECTMANAGEMENT/GET_CONFIG',
  GET_CONFIG_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/GET_CONFIG_SUCCESS',
  GET_CONFIG_FAILURE = '@@MODULES/PROJECTMANAGEMENT/GET_CONFIG_FAILURE',

  SAVE_CONFIG = '@@MODULES/PROJECTMANAGEMENT/SAVE_CONFIG',
  SAVE_CONFIG_SUCCESS = '@@MODULES/PROJECTMANAGEMENT/SAVE_CONFIG_SUCCESS',
  SAVE_CONFIG_FAILURE = '@@MODULES/PROJECTMANAGEMENT/SAVE_CONFIG_FAILURE',
  VERIFY_CONFLUENCE = '@@MODULES/PROJECTMANAGEMENT/VERIFY_CONFLUENCE',
  VERIFY_CONFLUENCE_DONE = '@@MODULES/PROJECTMANAGEMENT/VERIFY_CONFLUENCE_DONE',

  SYNC_CONFLUENCE = '@@MODULES/PROJECTMANAGEMENT/SYNC_CONFLUENCE',
  SYNC_CONFLUENCE_DONE = '@@MODULES/PROJECTMANAGEMENT/SYNC_CONFLUENCE_DONE',
}









