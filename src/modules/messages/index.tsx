import CustomSvgIcons from '../../helper/component/custom-icons'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import LavTable from '../../helper/component/lav-table'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import MessageFormPage from './form/form'
import { MessageCategory } from './type'
const { Text } = Typography

const Message = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  useEffect(() => {       
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'mess.header.title'}); 
  }, [screenMode])

  const columns = [
    {
      title: intl.formatMessage({ id: 'mess.column.mess-code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MESSAGE_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'mess.column.mess-category' }),
      dataIndex: 'category',
      sorter: true,
      ...getColumnSearchProps(
        'category',
        SEARCH_TYPE.SINGLE_CHOICE,
        MessageCategory
      ),
      render: (category, record) => {
        const data = MessageCategory.filter((item) => item.id === category)
        if (data && data.length === 1) {
          return <Text>{data[0].name}</Text>
        } else {
          return <Text></Text>
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'mess.column.message' }),
      dataIndex: 'content',
      sorter: true,
      ...getColumnSearchProps('content', SEARCH_TYPE.TEXT),
    },
  ]
  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common-mess.label.create-mess' })}
      </Button> : <></>
    // return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <MessageForm onFinish={() => handleDataChange()} /> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
    // return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && record.status !== STATUS.CANCELLED && record.status !== STATUS.DELETE) ?
    //   <MessageForm onFinish={() => handleDataChange()} screenMode={SCREEN_MODE.EDIT} messID={record.id} buttonType={BUTTON_TYPE.ICON} /> : <></>
  }
  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (record.status !== STATUS.DELETE && (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? children : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ? <LavTable
        title="mess.header.title"
        artefact_type="common.artefact.message"
        apiUrl={API_URLS.MESSAGES}
        columns={columns}
        artefactType={REQ_ARTEFACT_TYPE_ID.MESSAGE}
        updateComponent={UpdateComponent}
        createComponent={CreateComponent}
        deleteComponent={DeleteComponent}
      /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <MessageFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <MessageFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} id={id} /> : <></>
      }
    </Space>
  )
}

export default Message
