import LavImpact from '../../../helper/component/lav-impact'
import AppState from '@/store/types'
import {
  <PERSON>read<PERSON><PERSON>b, Button, Card, Col, Divider, Modal, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, STATUS } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavEffortEstimation from '../../../helper/component/lav-efffort-estimation'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { deleteRequest, updateRequest } from '../action'
import { MessageCategory } from '../type'

const { Title, Text } = Typography
const { confirm } = Modal

interface RightControlProps {
  data: any | [],
  messID: string,
  onChange: () => void,
  isLoading: boolean,
  isModalShow?: boolean,
  setScreenMode: any
}
const RightControl = ({ data, messID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
  const { height: windowHeight } = useWindowDimensions()
  const dispatch = useDispatch();
  const modalConfirmConfig = useModalConfirmationConfig()

  const projectCode = extractProjectCode();
  const projectName = getProjectName(projectCode);

  const handleCancelRecord = () => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage(
        { id: 'CFD_6_4' },
        {
          Artefact: `${intl.formatMessage({
            id: 'common.artefact.message',
          })}`,
        }
      )}`,
      onOk() {
        dispatch(updateRequest({ ...data, status: STATUS.CANCELLED, messageAction: MESSAGE_TYPES.CANCEL }))
      },
      onCancel() { },
    })
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;


  useEffect(() => {
    if(data)
        document.title = intl.formatMessage({ id: `create-mess.label.message` }) + "-" + data?.code;

    const getCoString = localStorage.getItem('comment')
    if (getCoString != null) {
      const co = JSON.parse(getCoString || '')
      if (commentState.fields && co?.itemId === data?.id) {
        const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.MESSAGE);
        if (fieldObj) {
          const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
          dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
        }
      }
    }
  }, [commentState.fields, data])
  useEffect(() => {
    if (!data?.id || commentState.isLoading || isModalShow) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'category', title: intl.formatMessage({ id: 'create-mess.label.category' }), },
      { field: 'message', title: intl.formatMessage({ id: 'create-mess.label.message' }), },
      { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
      { field: 'screen', title: intl.formatMessage({ id: 'view-screen-list.label.screen' }), },
      { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
      { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

    const payload = {
      projectId: data.projectId,
      itemId: data.id,
      artefact: ARTEFACT_COMMENT.MESSAGE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [data, isModalShow])

  //#endregion COMMENT INIT

  return data ? (
    <Space
      direction="vertical"
      size="middle"
      className="record-detail-right-control-container p-1rem"
    >
      <Row align="middle" justify="space-between">
        <div>
          <Breadcrumb className='rq-breadcrumb' separator=">">
            <Breadcrumb.Item>
              <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
            </Breadcrumb.Item>
          </Breadcrumb>
          <Title level={3} className='rq-page-title'>
            {intl.formatMessage({ id: `create-mess.label.message` })} - {data?.code}
          </Title>
        </div>

        <Space size="small">
          {/* Delete record */}
          {((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE) ? <DeleteButton
            type={BUTTON_TYPE.TEXT}
            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.message' }) })}
            okCB={() => dispatch(deleteRequest(messID))}
            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
          }

          {/* Update record */}
          {((((hasRole(APP_ROLES.BA) && data.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer)
            && (data?.status === STATUS.SUBMITTED || data?.status === STATUS.REJECT || data?.status === STATUS.REJECT_CUSTOMER || data?.status === STATUS.APPROVE))) &&
            data.status !== STATUS.CANCELLED &&
            data.status !== STATUS.DELETE &&
            data.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && data.status !== STATUS.CANCELLED &&
            data.status !== STATUS.DELETE
          ) && (
              <Button
                type='primary'
                className='lav-btn-create'
                onClick={() => {
                  setScreenMode()
                }} >{intl.formatMessage({ id: 'common.action.update' })}</Button>
            )}

          {/* Cancel record */}
          {(hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && (data?.status === STATUS.SUBMITTED) ? <Button className='cancel-btn' onClick={() => handleCancelRecord()}>
            {intl.formatMessage({ id: `common.action.cancel` })}</Button> : <></>
          }
        </Space>
      </Row>
      <Divider className="mt-0 mb-0" />
      <Spin spinning={isLoading}>
        <Scrollbars
          autoHide
        >
          <Space direction="vertical" size="middle">
            <Card
              title={
                <Title level={5}>
                  {`${intl.formatMessage({
                    id: 'create-mess.card.message-infomation',
                  })}`}
                </Title>
              }
              bordered={true}
            >
              <Row gutter={[16, 4]}>
                <Col span={3}>
                  <TriggerComment field="category">
                    <Text type="secondary">
                      {intl.formatMessage({
                        id: 'create-mess.label.category',
                      })}:
                    </Text>
                  </TriggerComment>
                </Col>
                <Col span={21}>
                  {
                    MessageCategory.filter(
                      (item: any) => item.id === data?.category
                    )[0]?.name
                  }
                </Col>

                <Col span={3}>
                  <TriggerComment field="message">
                    <Text type="secondary">
                      {intl.formatMessage({
                        id: 'create-mess.label.message',
                      })}:
                    </Text>
                  </TriggerComment>
                </Col>
                <Col span={21}>{data?.content}</Col>
              </Row>
            </Card>

            <LavReferences data={data} />
            {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.MESSAGE} onChange={() => { }} isViewMode={true} />}
            {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.MESSAGE} onChange={() => { }} isViewMode={true} /> : <></>} */}

            <Row justify="space-between">
              <Col span={8}>
                <LavEffortEstimation data={data} />
              </Col>

              <Col span={15}>
                <LavRelatedLinks data={data} />
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <LavAuditTrail data={data?.auditTrail} />
              </Col>
            </Row>
          </Space>
        </Scrollbars>
      </Spin>
    </Space>
  ) : <></>
}

export default RightControl
