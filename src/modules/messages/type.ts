
export interface MessagesState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: MessageDetail | null,
  selectedData?: MessageDetail | null,
  isModalShow?:boolean
}
export interface MessageDetail {
  id?: number | null,
  code: string,
  status: number,
  category: number | null,
  content: string,
  storage: string
  jira: string
  confluence: string
  reqElicitation: string,
  documentation: number | null
  assignee: string
  reviewer: string
  customer: string
  dueDate: any
  completeDate: any
  projectId?: number,
  impacts: string

}

export const defaultState = {
  detail: {
    id: null,
    code: '',
    status: 0,
    category: null,
    content: '',
    storage: '',
    jira: '',
    confluence: '',
    reqElicitation: '',
    documentation: 0,
    assignee: '',
    reviewer: '',
    dueDate: '',
    customer: '',
    completeDate: '',
    impacts: '',
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
}

export const MessageCategory = [
  { id: 1, name: 'Error Message' },
  { id: 2, name: 'Inline Error Message' },
  { id: 3, name: 'Confirmation Dialog' },
  { id: 4, name: 'Success Dialog' }
]

export enum ActionEnum {
  RESET_STATE = '@@MODULES/MESSAGE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/MESSAGE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/MESSAGE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/MESSAGE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/MESSAGE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/MESSAGE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/MESSAGE/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/MESSAGE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/MESSAGE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/MESSAGE/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/MESSAGE/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/MESSAGE/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/MESSAGE/GET_LIST_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/MESSAGE/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/MESSAGE/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/MESSAGE/GET_LIST_OBJECTS_FAILED',

  DELETE_REQUEST = '@@MODULES/MESSAGE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/MESSAGE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/MESSAGE/DELETE_FAILED',
  SET_MODAL_VISIBLE = '@@MODULES/MESSAGE/SET_MODAL_VISIBLE',
}
