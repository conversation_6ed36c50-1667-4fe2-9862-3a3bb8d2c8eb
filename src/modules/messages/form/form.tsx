import AppState from '@/store/types'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import FormGroup from '../../../helper/component/form-group'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { MessageCategory, MessagesState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text, Title } = Typography
const { confirm } = Modal
const { Option } = Select

interface MessageFormModalProps {
  id?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const MessageFormPage = ({ id, screenMode, onFinish, onDismiss }: MessageFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.Messages) as MessagesState
  const [impacts, setImpacts] = useState<any>(false)
  const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()

  useBeforeUnload()
  // Destroy
  useEffect(() => {
    return () => {
      dispatch(resetState(null));
      resetForm();
    }
  }, [])


  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
    document.title = intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'update-mess.label.update-mess' : 'create-mess.label.create-mess' });
  }, [screenMode, id])

  const isJsonString = (data) => {
    try {
      JSON.parse(data);
    } catch (e) {
      return '';
    }
    return JSON.parse(data);
  }

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      const storage = isJsonString(state.detail?.storage);
      const jira = isJsonString(state.detail?.jira);
      const confluence = isJsonString(state.detail?.confluence);

      form.setFieldsValue({
        ...state.detail,
        storageLinkText: storage ? storage?.textToDisplay : storage,
        storageWebLink: storage ? storage?.address : storage,
        jiraLinkText: jira ? jira?.textToDisplay : jira,
        jiraWebLink: jira ? jira?.address : jira,
        confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
        confluenceWebLink: confluence ? confluence?.address : confluence,
        req: state.detail.reqElicitation
      })
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm();
        form.setFieldsValue({
          assignee: currentUserName(),
          dueDate: moment(new Date()),
        })
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss();
      }
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])
  const onChange = (e) => {
    setImpacts(JSON.stringify(e))
  }
  const onSubmit = debounce(async (values: any, st?: string) => {
    const requestData: any = {
      ...values,
      id: id || null,
      reqElicitation: values.req,
      impacts: impacts,
      storage: JSON.stringify({
        textToDisplay: values?.storageLinkText || '',
        address: values?.storageWebLink || '',
      }),
      jira: JSON.stringify({
        textToDisplay: values?.jiraLinkText || '',
        address: values?.jiraWebLink || '',
      }),
      confluence: JSON.stringify({
        textToDisplay: values?.confluenceLinkText || '',
        address: values?.confluenceWebLink || '',
      }),
    }
    setIsCreateMore(values.createMore);
    requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    form.resetFields([
      'version',
      'code',
      'category',
      'content',
      'storageLinkText',
      'storageWebLink',
      'jiraLinkText',
      'jiraWebLink',
      'confluenceLinkText',
      'confluenceWebLink',
      'req',
      'documentation',
      'reviewer',
      'customer',
      'dueDate',
      'completeDate'
    ])
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'category', title: intl.formatMessage({ id: 'create-mess.label.category' }), },
      { field: 'message', title: intl.formatMessage({ id: 'create-mess.label.message' }), },
      { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
      { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.MESSAGE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={10}>
            <Space size="large">
              <Title level={4}>{intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'update-mess.label.update-mess' : 'create-mess.label.create-mess' })}</Title>
              {/* {screenMode === SCREEN_MODE.EDIT ? renderStatusBadge(state.detail?.status) : <></>} */}
            </Space>
          </Col>

          <Col span={14}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                <Button onClick={() => {
                  setIsSubmitForm(true)
                }} className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: 'common.action.save' })}
                </Button>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Card className='rq-form-block' title={intl.formatMessage({ id: 'create-mess.card.message-infomation' })}>
          {
            screenMode === SCREEN_MODE.EDIT ? <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
              <Form.Item rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                <Input maxLength={255} value={state.detail?.code} disabled></Input>
              </Form.Item>
            </FormGroup> : <></>
          }
          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" required label={
            <TriggerComment screenMode={screenMode} field="category">
              {intl.formatMessage({ id: 'create-mess.label.category' })}
            </TriggerComment>
          }>
            <Form.Item
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                }]}
              name="category"
            >
              <Select
                filterOption={(input, option: any) => option.children?.toLowerCase().indexOf(input?.toLowerCase()) >= 0}
                showSearch
              >
                {MessageCategory.map((item: any) => (
                  <Option key={item.id} value={item.id}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </FormGroup>
          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" required label={
            <TriggerComment screenMode={screenMode} field="message">
              {intl.formatMessage({ id: 'create-mess.label.message' })}
            </TriggerComment>}>
            <Form.Item
              name="content"
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }
              ]}
            >
              <Input maxLength={255} />
            </Form.Item>
          </FormGroup>
        </Card>
        {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.MESSAGE} onChange={onChange} isSubmitForm={isSubmitForm} />}

        <LavEffortEstimationForm
          screenMode={screenMode}
          hasDevelopment={state?.detail?.hasOwnProperty('development')}
          hasImplementation={state?.detail?.hasOwnProperty('implementation')}
        />
        {/* <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.effort' })}>
            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="req-elicitation">
                {intl.formatMessage({ id: 'createscreen.label.req' })}
              </TriggerComment>}>
              <Form.Item name="reqElicitation">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>

            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="documentation">
                {intl.formatMessage({ id: 'createscreen.label.documentation' })}
              </TriggerComment>}>
              <Form.Item name="documentation">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>
          </Card> */}

        <LavRelatedLinksForm form={form} screenMode={screenMode} />
      </Space>
    </Form>
  </Spin>
}
// const MessageForm = ({ id, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: MessageFormProps) => {
//   const dispatch = useDispatch();
//   const [isModalVisible, setIsModalVisible] = useState<any>(null)

//   useEffect(() => {
//     if (isModalVisible !== null) {
//       dispatch(setModalVisible(isModalVisible))
//     }
//   }, [isModalVisible])

//   return <>
//     {
//       buttonType === BUTTON_TYPE.TEXT ?
//         <Button
//           ghost={screenMode === SCREEN_MODE.CREATE}
//           type='primary'
//           className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
//           onClick={() => setIsModalVisible(true)}
//           icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
//         >
//           {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'create-mess.label.create-mess' : 'common.action.update' })}
//         </Button> :
//         buttonType === BUTTON_TYPE.ICON ?
//           <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
//           <></>
//     }
//     {isModalVisible === true ? <MessageFormModal id={id} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
//   </>
// }
export default MessageFormPage
