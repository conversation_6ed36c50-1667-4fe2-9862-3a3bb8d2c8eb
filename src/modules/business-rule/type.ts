import { Reference } from '../../constants'

export interface Details {
  id: number | null
  name: string
  status: number
  code: string
  diagram: string
  description: string
  userRequirement: Reference
  reqElicitation: number
  documentation: number
  storage: string
  jira: string
  confluence: string
  assignee: string
  reviewer: string
  customer: string
  completeDate: any
  dueDate: any
  projectId?: number
  impacts: string,

  
}
export interface BusinessRuleState {
  isLoading: boolean
  sourceUserRequirement: Reference[]
  imgID: number
  details: Details | null
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  isLoadingList?: boolean
  listData?: any
  selectedData?: Details | null,

  listUserRequirements?: any[],
  isLoadingUserRequirements?: boolean,
  isModalShow?:boolean,
}

export const defaultState: BusinessRuleState = {
  sourceUserRequirement: [],
  isLoading: false,
  imgID: -1,
  details: {
    id: null,
    name: '',
    status: -1,
    code: '',
    diagram: '',
    description: '',
    userRequirement: {
      id: -1,
      name: '',
    },
    reqElicitation: -1,
    documentation: -1,
    storage: '',
    jira: '',
    confluence: '',
    assignee: '',
    reviewer: '',
    customer: '',
    completeDate: '',
    dueDate: '',
    impacts: '',

  },
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  listUserRequirements: [],
  isLoadingUserRequirements: false,
}
export enum ActionEnum {
  RESET_STATE = '@@MODULES/BUSINESSRULE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/BUSINESSRULE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/BUSINESSRULE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/BUSINESSRULE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/BUSINESSRULE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/BUSINESSRULE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/BUSINESSRULE/UPDATE_FAILED',

  DELETE_REQUEST = '@@MODULES/BUSINESSRULE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/BUSINESSRULE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/BUSINESSRULE/DELETE_FAILED',

  SAVE_IMG_ID = '@@MODULES/BUSINESSRULE/SAVE_IMG_ID',

  GET_DETAIL_REQUEST = '@@MODULES/BUSINESSRULE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/BUSINESSRULE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/BUSINESSRULE/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/BUSINESSRULE/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/BUSINESSRULE/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/BUSINESSRULE/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/BUSINESSRULE/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/BUSINESSRULE/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/BUSINESSRULE/GET_LIST_FAILED',

  GET_LIST_UR_REQUEST = '@@MODULES/BUSINESSRULE/GET_LIST_UR_REQUEST',
  GET_LIST_UR_SUCCESS = '@@MODULES/BUSINESSRULE/GET_LIST_UR_SUCCESS',
  GET_LIST_UR_FAILED = '@@MODULES/BUSINESSRULE/GET_LIST__URFAILED',

  SET_MODAL_VISIBLE = '@@MODULES/BUSINESSRULE/SET_MODAL_VISIBLE',
}
