import { Card, Col, Row, Space, Typography } from 'antd'
import intl from '../../../config/locale.config'
import LavReferences from '../../../helper/component/lav-references'
import { renderStatusBadge } from '../../../helper/share'

const { Title, Text } = Typography

const CBRDetailInfo = ({ data }) => {
    return (
        <Space direction="vertical">
            <Space size="large">
                {/* <span>

                    <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}

                </span> */}
                {renderStatusBadge(data?.status)}
            </Space>

            <Card
                title={
                    <Title level={5}>
                        {`${intl.formatMessage({
                            id: 'cbr.column.cbr-info',
                        })}`}
                    </Title>
                }
                bordered={true}
            >
                <Row gutter={[16, 4]}>
                    <Col span={24}>

                        <Text type="secondary">
                            {intl.formatMessage({
                                id: 'cbr.column.description',
                            })}
                        </Text>

                    </Col>
                    <Col span={24}>
                        <div
                            className="tableDangerous"
                            dangerouslySetInnerHTML={{
                                __html: data?.description,
                            }}
                        ></div>
                    </Col>
                </Row>
            </Card>

            <LavReferences data={data} />
        </Space>
    )
}

export default CBRDetailInfo
