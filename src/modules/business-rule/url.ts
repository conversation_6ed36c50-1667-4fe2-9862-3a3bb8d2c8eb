const baseUrl = process.env.REACT_APP_API_BACKEND

export default {
  GET_BUSINESS_RULES: `${baseUrl}CommonBusinessrules`,
  GET_SOURCE_USERREQUIREMENT: `${baseUrl}references/userrequirements`,
  ADD_BUSINESS_RULE: `${baseUrl}CommonBusinessrules`,
  // DELETE_BUSINESS_RULE: `${baseUrl}CommonBusinessRule/Delete`,
  DELETE_BUSINESS_RULE: `${baseUrl}CommonBusinessRules`,
  GET_BUSINESS_RULE_DETAIL: `${baseUrl}CommonBusinessRules`,
  UPDATE_BUSINESS_RULE: `${baseUrl}CommonBusinessRules`,
  SUBMIT_BUSINESS_RULE: `${baseUrl}CommonBusinessRule/Submitted`,
  CHECK_BUSINESS_RULE: `${baseUrl}CommonBusinessRule/CheckDelete`
}
