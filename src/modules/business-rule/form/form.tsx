import AppState from '@/store/types'
import { Button, Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag, Typography } from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, getListUrRequest, resetState, updateRequest } from '../action'
import { BusinessRuleState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select

interface BusinessRuleFormModalProps {
    id?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}

const BusinessRuleFormPage = ({ id, screenMode, onFinish, onDismiss }: BusinessRuleFormModalProps) => {
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>(
        (s) => s?.BusinessRule
    ) as BusinessRuleState
    const dispatch = useDispatch()
    const [isCreateMore, setIsCreateMore] = useState(false);
    const [isDraft, setIsDraft] = useState<any>(null);
    const getCkeditorData: any = createRef()
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const [impacts, setImpacts] = useState<any>(false)
    const { height: windowHeight } = useWindowDimensions()
    const modalConfirmConfig = useModalConfirmationConfig()
    // Destroy
    useEffect(() => {
        dispatch(getListUrRequest(null))
        return () => {
            dispatch(resetState(null));
            resetForm()
        }
    }, [])

    const resetForm = () => {
        setIsDraft(null);
        form.resetFields([
            'businessruleName',
            'version',
            'img',
            'businessruleExplanation',
            'userRequirement',
            'req',
            'documentation',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
            'reviewer',
            'dueDate',
            'customer',
            'completeDate',
            'version'
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })
    }

    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(id))
        }
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createcbr.header.title' : 'updatecbr.header.title' });
    }, [screenMode, id])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }


    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT && state.details?.id) {

            const storage = isJsonString(state.details?.storage);
            const jira = isJsonString(state.details?.jira);
            const confluence = isJsonString(state.details?.confluence);
            form.setFieldsValue({
                code: state.details?.code,
                businessruleName: state.details?.name,
                userRequirement: state.details?.userRequirement?.id,
                req: state.details?.reqElicitation,
                documentation: state.details?.documentation,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
            })
        }
    }, [state.details])
    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            if (isCreateMore) {
                resetForm();
                form.setFieldsValue({
                    assignee: currentUserName(),
                    dueDate: moment(new Date()),
                })
            } else {
                if (onFinish) {
                    onFinish();
                }
                onDismiss();
            }
            setIsDraft(null);
            setIsCreateMore(false);
        }
    }, [state.createSuccess, state.updateSuccess])

    useBeforeUnload()

    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }
    const onSubmit = debounce(async (values: any, st?: string) => {
        const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
        const requestData: any = {
            id: id || null,
            name: values.businessruleName,
            description: getCkeditorData.current?.props?.data,
            status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.details?.status === STATUS.APPROVE || state.details?.status === STATUS.REJECT_CUSTOMER || state.details?.status === STATUS.REJECT || state.details?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.details?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            version: values.version,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText || '',
                address: values.storageWebLink || '',
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText || '',
                address: values.jiraWebLink || '',
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText || '',
                address: values.confluenceWebLink || '',
            }),
            reqElicitation: values.req,
            documentation: values.documentation,
            userRequirement: values.userRequirement,

            // Apply assign task - step 1
            author: ((state?.details?.status === STATUS.REJECT || state?.details?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
            reviewer: values.reviewer || '',
            customer: values.customer || '',
            dueDate: values.dueDate ? values.dueDate?.toDate() : null,
            completeDate: values.completeDate ? values.completeDate?.toDate() : null,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
            impacts: impacts
        }
        setIsCreateMore(values.createMore);
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.business-rule' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {
                },
            })
        }
    }, 500)

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!state.details?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'description', title: intl.formatMessage({ id: 'cbr.column.description' }), },
            { field: 'user-requirement', title: intl.formatMessage({ id: 'cbr.user-requirement' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.details.projectId, itemId: state.details.id, fields }));

        const payload = {
            projectId: state.details.projectId,
            itemId: state.details.id,
            artefact: ARTEFACT_COMMENT.COMMON_BUSINESS_RULE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.details])


    const tagRender = (props) => {
        const { label, name, value, closable, onClose } = props;


        return (
            <Tag
                closable={closable}
                onClose={onClose}
                style={{
                    marginRight: 3,
                    border: 'none',
                }}
                title={label}
            >
                {label.length > 20 ? label.substring(0, 20) + '...' : label}
            </Tag>
        );
    };

    //#endregion COMMENT INIT

    return <Spin spinning={state.isLoading}>
        <Form
            form={form}
            name="crearebusinessrule"
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createcbr.header.title' : 'updatecbr.header.title' })}
                >
                    <Space size="small">
                        {screenMode == SCREEN_MODE.CREATE ?
                            (<Form.Item
                                style={{ marginBottom: '0px' }}
                                valuePropName="checked"
                                name="createMore"
                                wrapperCol={{ span: 24 }}
                            >
                                <Checkbox disabled={state.isLoading}>
                                    {intl.formatMessage({
                                        id: 'createobject.checkbox.create-another',
                                    })}
                                </Checkbox>
                            </Form.Item>) : <></>
                        }

                        <Button onClick={debounce(confirmCancel, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>

                        <Form.Item style={{ marginBottom: '0px' }}>
                            {screenMode == SCREEN_MODE.CREATE || state.details?.status == STATUS.DRAFT || state.details?.status == STATUS.REJECT || state.details?.status == STATUS.REJECT_CUSTOMER || (state.details?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.details?.customer)) ?
                                <Button type="primary" ghost htmlType="submit" onClick={() => {
                                    setIsDraft(false)
                                    setIsSubmitForm(true)
                                }}>
                                    {intl.formatMessage({ id: 'common.action.submit' })}
                                </Button> : <></>
                            }
                        </Form.Item>

                        <Form.Item style={{ marginBottom: '0px' }}>
                            <Button
                                onClick={() => {
                                    setIsDraft(true)
                                    setIsSubmitForm(true)
                                }}
                                className="success-btn"
                                htmlType="submit"
                            >
                                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.update' })}
                            </Button>
                        </Form.Item>
                    </Space>
                </LavPageHeader>
            </div >


            <Row align="middle">
                {/* <Col span={2}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={14} controlSpan={10} label={
                                <TriggerComment screenMode={screenMode} field='actor'>
                                    {intl.formatMessage({ id: 'createobject.place-holder.version' })}
                                </TriggerComment>}>
                                <Form.Item
                                    name="version"
                                    rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col> */}
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderStatusBadge(state.details?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>
            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'cbr.column.cbr-info' })}>
                    {
                        screenMode === SCREEN_MODE.EDIT ? <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                            <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                <Input maxLength={255} disabled />
                            </Form.Item>
                        </FormGroup> : <></>
                    }
                    <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                        <Form.Item
                            name="businessruleName"
                            rules={[
                                {
                                    required: true,
                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                },
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        >
                            <Input
                                placeholder={`${intl.formatMessage({
                                    id: `cbr.place-holder.cbr`,
                                })}${intl.formatMessage({
                                    id: `common.mandatory.*`,
                                })}`}
                                maxLength={255}
                            />
                        </Form.Item>
                    </FormGroup>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="description">
                            {intl.formatMessage({ id: 'cbr.column.description' })}
                        </TriggerComment>}>
                        <Form.Item name="businessruleExplanation">
                            <CkeditorMention
                                ref={getCkeditorData}
                                data={screenMode == SCREEN_MODE.EDIT ? state.details?.description : ''}
                            />
                        </Form.Item>
                    </FormGroup>
                </Card>

                <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.reference' })}>
                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="user-requirement">
                            {intl.formatMessage({ id: 'cbr.user-requirement' })}
                        </TriggerComment>}>
                        <Form.Item name="userRequirement">
                            <Select
                                filterOption={(input, option: any) =>
                                    option.children
                                        .toLowerCase()
                                        .indexOf(input.toLowerCase()) >= 0
                                }
                                showSearch
                                optionLabelProp="label"
                                tagRender={tagRender}
                            >
                                {state?.listUserRequirements?.map(
                                    (item: any) =>
                                        item.status !== STATUS.CANCELLED &&
                                        item.status !== STATUS.DELETE && (
                                            <Option key={item.id} value={item.id} label={item.name}>
                                                {item.name}
                                            </Option>
                                        )
                                )}
                            </Select>
                        </Form.Item>
                    </FormGroup>
                </Card>
                <AssignTaskComponent form={form} data={screenMode == SCREEN_MODE.EDIT ? state.details : null} isSubmit={isDraft == false} screenMode={screenMode} />
                {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.details} artefactType={REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE} onChange={onChange} isSubmitForm={isSubmitForm} />}

                <LavEffortEstimationForm
                    screenMode={screenMode}
                    hasDevelopment={state?.details?.hasOwnProperty('development')}
                    hasImplementation={state?.details?.hasOwnProperty('implementation')}
                />

                {/* <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.effort' })}>
            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="req-elicitation">
                {intl.formatMessage({ id: 'createscreen.label.req' })}
              </TriggerComment>}>
              <Form.Item name="req">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>

            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="documentation">
                {intl.formatMessage({ id: 'createscreen.label.documentation' })}
              </TriggerComment>}>
              <Form.Item name="documentation">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>
          </Card> */}

                <LavRelatedLinksForm form={form} screenMode={screenMode} />
            </Space>
        </Form >
    </Spin>
}

export default BusinessRuleFormPage
