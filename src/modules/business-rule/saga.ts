import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  createFailed, createRequest, createSuccess, deleteRequest, getDetailFailed, getDetailRequest, getDetailSuccess, updateSuccess, updateFailed, updateRequest, getListRequest, getListSuccess, getListFailed, getListUrRequest, getListUrSuccess, getListUrFailed, deleteSuccess, deleteFailed
} from './action'

function* addNewBusinessRuleFlow(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.BUSINESS_RULES
      const res = yield call(apiCall, 'POST', url, request)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.business-rule')
      yield put(createSuccess({ createAnother: action.payload.createAnother }))
    } catch (err) {
      yield put(createFailed(null))
      ShowAppMessage(err, null, 'common.artefact.business-rule')
    }
  }
}

function* deleteBusinessRuleFlow(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const params = action.payload
      const url = API_URLS.BUSINESS_RULES + `/${params}`
      const res = yield call(apiCall, 'DELETE', url)
      yield put(deleteSuccess(null))
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.business-rule');
    } catch (err) {
      yield put(deleteFailed(null))
      ShowAppMessage(err, null, 'common.artefact.business-rule')
    }
  }
}

function* getBusinessRuleDetailFlow(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const params = action.payload
      const url = API_URLS.BUSINESS_RULES + `/${params}`
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data))
    } catch (err) {
      yield put(getDetailFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* updateBusinessRuleDetailFlow(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.BUSINESS_RULES + `/${request.id}`
      const res = yield call(apiCall, 'PUT', url, request)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.business-rule');
      yield put(updateSuccess({ createAnother: false }))
    } catch (err) {
      yield put(updateFailed(null))
      ShowAppMessage(err, null, 'common.artefact.business-rule')
    }
  }
}

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.BUSINESS_RULES}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListUr(action: Action) {
  if (getListUrRequest.match(action)) {
    try {
      const url = `${API_URLS.REFERENCES_USER_REQUIREMENTS}`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListUrSuccess(res.data));
    } catch (err) {
      yield put(getListUrFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getListUrRequest.type, handleGetListUr)
  yield takeLatest(createRequest.type, addNewBusinessRuleFlow)
  yield takeLatest(deleteRequest.type, deleteBusinessRuleFlow)
  yield takeLatest(getDetailRequest.type, getBusinessRuleDetailFlow)
  yield takeLatest(updateRequest.type, updateBusinessRuleDetailFlow)
}

export default function* BusinessRuleSaga() {
  yield all([fork(watchFetchRequest)])
}
