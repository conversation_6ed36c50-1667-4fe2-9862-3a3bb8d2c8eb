import React, { createRef, useState } from "react";
import { Modal, Form } from "antd";
import intl from '../../config/locale.config'
import Ckeditor from '../../helper/component/ckeditor'
import { CloseCircleFilled } from "@ant-design/icons";
import { ShowAppMessage } from "../../helper/share";
import { API_URLS, MESSAGE_TYPES, SCREEN_MODE } from "../../constants";
import AppCommonService from "../../services/app.service";

interface ConfirmWithInputProps {
    isModalVisible: boolean;
    setIsModalVisible: (value: boolean) => void;
    restoredRowData: any;
    setScreenMode: (mode: SCREEN_MODE) => void
    screenArtefact: any;
    artefactType: any;
    artefactId: any;
    setIsReload: any;
    onFinish: () => void | null;
    isCommon?: boolean
};

const ConfirmWithInput = ({isModalVisible, isCommon, setIsModalVisible,  onFinish, setIsReload, restoredRowData, setScreenMode, screenArtefact, artefactType, artefactId} : ConfirmWithInputProps) => {
  //const [isModalVisible, setIsModalVisible] = useState(false);
  const [note, setNote] = useState("");
  const [form] = Form.useForm();
  const noteRef: any = createRef()

  /* const showModal = () => {
    setIsModalVisible(true);
  }; */

  const handleOk = (value : any) => {
    setIsModalVisible(false);
    let restoreUpdateRequest : any = {
      version : restoredRowData.version
    }
    AppCommonService.restoreVersionHistory(isCommon ? API_URLS.COMMON_RESTORE_VERSION_HISTORY : API_URLS.RESTORE_VERSION_HISTORY, restoreUpdateRequest, artefactType, artefactId).then((e) => {    
      setNote(""); // Clear the input after submission
      form.resetFields(['note']);

      setScreenMode(SCREEN_MODE.HISTORY);
      setIsReload(true);
      onFinish();
      ShowAppMessage(null, MESSAGE_TYPES.RESTORE, screenArtefact)      
    }).catch(err => {
      console.log(err);  
    })
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setNote(""); // Clear the input on cancel
    
    form.resetFields(['note']);
  };

  const noteChange = (e) => {
    form.setFieldsValue({ note: e })
  }

  return (
    <div>
      <Modal
        title="Confirmation"
        visible={isModalVisible}
        //onOk={handleOk}
        onCancel={handleCancel}
        closeIcon={
          <CloseCircleFilled style={{ color: 'white', fontSize: '1.5rem' }} />
        }
        okButtonProps={{htmlType: 'submit', form: 'restoreVersion'}}
      >
        <p>Are you sure you want to restore this version?</p>
        <Form form={form} name="restoreVersion" onFinish={handleOk}>
            <Form.Item name="note" labelAlign="left" wrapperCol={{ span: 24 }} rules={[{max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' })}]}>
                <Ckeditor ref={noteRef} data={note} onChange={noteChange} placeholder="Add note..." />
            </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ConfirmWithInput;
