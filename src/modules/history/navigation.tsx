import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Typography } from "antd";
import ConfirmWithInput from "./confirmwithinput.";
import moment from "moment";
import { REQ_ARTEFACT_TYPE_ID } from "../../constants";
import { useState } from "react";
import intl from "../../config/locale.config";
import { LeftOutlined, RightOutlined, WarningFilled } from "@ant-design/icons";
const { Title, Text } = Typography

interface HistoryNavigationProps{
    data: any
    onChange: () => void,
    setScreenMode: any,
    setSelectedRowVersion: (version: string) => void, 
    screenArtefact: any,
    artefactType: any,
    isCommon? : boolean
}

const HistoryNavigation = ({data, onChange, isCommon, setSelectedRowVersion, setScreenMode, screenArtefact, artefactType} : HistoryNavigationProps) => {    
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [restoredRowData, setRestoredRowData] = useState<any>(null); 
    const [isReload, setIsReload] = useState<boolean>(false); 

    const onModalShow = (record) => {
        setIsModalVisible(true);
        setRestoredRowData(data);
    }

    return(
        <>
            <ConfirmWithInput isCommon = {isCommon} isModalVisible={isModalVisible} setIsReload={setIsReload} onFinish={onChange} setIsModalVisible={setIsModalVisible} artefactId={data?.id} artefactType={artefactType} setScreenMode={setScreenMode} restoredRowData={restoredRowData} screenArtefact={screenArtefact} /> 
            <Row style={{ marginBottom: '15px', marginTop :'-10px', lineHeight: "25px" }}>
                <Col span={1} style={{marginRight: "-2%"}}>
                    <WarningFilled style={{color: "orange", fontSize: "20px"}}/>
                </Col>
                <Col span={23}>
                    <Col span={23}>
                        <Text>You are viewing an old version of this record. View the <a onClick={()=> {setSelectedRowVersion(data?.nextPrevious.latestVersion)}}>current version</a>.</Text>
                    </Col>
                    {
                        data?.updatedBy && data?.dateUpdated ?  
                        <>           
                            <Col span={23}>
                                <Text>Updated by {data.updatedBy} on {(moment(data.dateUpdated).format('DD/MM/YYYY HH:mm'))}.</Text>
                            </Col> 
                        </>      
                        :
                        <></>
                    }                         
                    <Col span={23}>                                
                        <Text><a onClick={onModalShow}>Restore this version</a>.</Text>
                    </Col>
                    <Col span={23}>
                    {
                        data?.nextPrevious ?
                            <>        
                                {
                                    data?.nextPrevious.previousVersion ?
                                        <><Button 
                                            onClick={() => setSelectedRowVersion(data?.nextPrevious.previousVersion)}
                                            type='primary'
                                            className='lav-btn-create'>
                                            <LeftOutlined />Previous
                                        </Button>&nbsp;&nbsp;&nbsp;</>
                                        :
                                        <></>
                                }                           
                                <Text>{intl.formatMessage({ id: `common.label.version` })} {data?.version}</Text>
                                {
                                    data?.nextPrevious.nextVersion?
                                        <>&nbsp;&nbsp;&nbsp;<Button onClick={() => setSelectedRowVersion(data?.nextPrevious.nextVersion)}
                                                type='primary'
                                                className='lav-btn-create'>
                                            Next<RightOutlined />
                                        </Button></>
                                        :
                                        <></>
                                }                                         
                            </>
                        :
                        <></>
                    }
                    </Col>
                </Col>
            </Row>
            <Divider className="mt-0 mb-0" />
        </>
    );
}

export default HistoryNavigation