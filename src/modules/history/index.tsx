import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Spin, Table, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import { Scrollbars } from 'react-custom-scrollbars'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, getProjectName, hasRole, renderCommonStatusBadge, renderStatusBadge } from '../../helper/share'
import debounce from 'lodash.debounce'
import LavTable from '../../helper/component/lav-table'
import CustomSvgIcons from '../../helper/component/custom-icons'
import ConfirmWithInput from './confirmwithinput.'
import moment from 'moment'
import LavHistoryTable from '../../helper/component/lav-history-table'


const { Title, Text } = Typography
interface HistoryScreenProps {
  data: any | [],
  screenID?: number
  screenMode?: SCREEN_MODE.HISTORY,
  setScreenMode: (mode: SCREEN_MODE) => void
  onFinish: () => void | null
  onDismiss: () => void | null,
  setHistorySelectedRowKeys : (selectedRowKeys: React.Key[]) => void
  setSelectedRowVersion: (version: string) => void,
  apiURL: string,
  artefactType: number,
  artefact_type: string,
  pageTitle: string,  
  isCommon?: boolean,
}

const HistoryScreen = ({ onDismiss, isCommon, data, setScreenMode, onFinish, setSelectedRowVersion, setHistorySelectedRowKeys, apiURL, artefactType, artefact_type, pageTitle }: HistoryScreenProps) => {
  const [columns, setColumns] = useState<any>(null)
  const projectCode = extractProjectCode();
  const projectName = getProjectName(projectCode);
  const title = data?.code + "-" + data?.name;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [restoredRowData, setRestoredRowData] = useState<any>(null)
  const [isReload, setIsReload] = useState<boolean>(false);

  const rowSelection = {
    selectedRowKeys,
    onSelect: (record: any, selected: boolean, selectedRows: any, nativeEvent: any) => {
      if(selected){        
        setSelectedRowKeys([...selectedRowKeys, record.version]);
        //console.log(`selectedRowKeys: ${selectedRowKeys}`, `selectedRows:${JSON.stringify(selectedRows)}`);
      }
      else{        
        setSelectedRowKeys((prevItems) =>
          prevItems.filter((item) => item !== record.version));
        //console.log(`selectedRowKeys: ${selectedRowKeys}`, `selectedRows: ${JSON.stringify(selectedRows)}`);
      }
    },
    getCheckboxProps: (record: any) => ({
      disabled: !selectedRowKeys.includes(record.version) && selectedRowKeys.length == 2,
    })    
  };

  useEffect(() => {
      setColumns(configColumns)
      setSelectedRowVersion('')
  }, [])  

  const onModalShow = (record) => {
    setIsModalVisible(true);
    setRestoredRowData(record);
  }  

  const configColumns = (objs) => [
    {
      title: intl.formatMessage({ id: 'view-version-history.column.version' }),
      dataIndex: 'version',
      width: '85px',
      /* sorter: true,
      sortOrder: 'descend', */
      //...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any, index) => {       
        const currentPage = localStorage.getItem('currentHistoryPageIndex') || '' 
        return (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM) || isCommon) ? <a onClick={() => {
          setScreenMode(SCREEN_MODE.VERSION);
          setSelectedRowVersion(record.version);
        }}>{index === 0 && currentPage === "1" ?
           "Current (v. " + text + ")": "v. " + text} </a> : "v. " + text 
      },
    },
    {
      title: intl.formatMessage({ id: 'view-version-history.column.updatedat' }),
      dataIndex: 'updatedAt',
      width: '20%',
      //...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
      render:(text: string) => {
        if(text)
          return (moment(text).format('MMM DD, YYYY HH:mm'))

        return text;
      }
    },
    {
      title: intl.formatMessage({ id: 'view-version-history.column.updatedby' }),
      dataIndex: 'updatedBy',
      width: '45%',
      //...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      /* render: (text: string) => {
        return <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
      }, */
    },
    {
      title: intl.formatMessage({ id: 'view-version-history.column.status' }),
      dataIndex: 'status',
      width: '80px',
      //...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      render: (record) => isCommon? renderCommonStatusBadge(record) : renderStatusBadge(record),
    },
  ]  

  const RestoreComponent: React.FC<any> = ({ record, handleDataChange, order }) => {    
    const currentPage = localStorage.getItem('currentHistoryPageIndex') || ''   
    if(order === 0 && currentPage === "1")
      return <></>
   
    return (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM) || isCommon) ?
    <a title='Restore' onClick={() => {onModalShow(record)}}>Restore</a> : <></>
  }

  return (
    <Space
        direction="vertical"
        size="middle"
        className="record-detail-right-control-container p-1rem"
    >
        <Row align="middle" justify="space-between">
            <div>
              {
                isCommon ? <></> 
                : 
                <Breadcrumb className='rq-breadcrumb' separator=">">
                    <Breadcrumb.Item>
                        <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                    </Breadcrumb.Item>
                </Breadcrumb>
              }                
              <Title level={3} className='rq-page-title'>
                  {pageTitle}
              </Title>
            </div>

            <Space size="small">
              <Button onClick={debounce(onDismiss, 500)}>
                {intl.formatMessage({ id: 'common.action.close' })}
              </Button>
            </Space>
        </Row>        
        <Divider className="mt-0 mb-0" />
        <Row style={{fontWeight:"bold", marginTop: "-10px"}}>
          <Text>Only 2 versions can be compared at a time</Text>
        </Row>
        <Row>
          <Button
            disabled={selectedRowKeys.length !== 2}
            type='primary'
            className='lav-btn-create'
            /*onClick={() => {
              if(selectedRowKeys.length === 2) {                      
                  setScreenMode(SCREEN_MODE.COMPARE)
                  setHistorySelectedRowKeys(selectedRowKeys);
                }
            }} */>Compare Selected Versions</Button>
        </Row> 
        <Divider className="mt-0 mb-0" />             
        <Scrollbars
            autoHide
        >      
          <ConfirmWithInput isModalVisible={isModalVisible} isCommon = {isCommon} onFinish={onFinish} setIsReload={setIsReload} setIsModalVisible={setIsModalVisible} artefactType={artefactType} artefactId={data.id} setScreenMode={setScreenMode} restoredRowData={restoredRowData} screenArtefact={artefact_type} /> 
          <LavHistoryTable
              rowSelection={{
                type: "checkbox",
                columnTitle: 'Select',
                ...rowSelection,
                
              }}
              title= {title}
              showHeader={false}
              artefact_type={artefact_type}
              apiUrl= {`${apiURL}/${artefactType}/${data.id}/history`}
              columns={columns}
              artefactType={artefactType}
              rowKey="version"
              isReload={isReload}
              showRestore={hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM) || isCommon}
              restoreComponent={RestoreComponent}
            /> 
        </Scrollbars>
    </Space>
  )
}

export default HistoryScreen
