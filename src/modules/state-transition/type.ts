import { VersionType } from "../../constants"

export interface StateTransitionState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: StateTransitionDetail | null
  selectedData?: StateTransitionDetail | null,
  listObjects?: any,
  isLoadingObjects?: boolean,
  listWorkflows?: any,
  isLoadingWorkflows?: boolean,
  listUsecases?: any,
  isLoadingUsecases?: boolean,
  isModalShow?:boolean
}
export interface StateTransitionDetail {
  id?: number | null,
  code: string,
  name: string,
  diagram: number | null,
  description: string,
  storage: string
  jira: string
  confluence: string
  reqElicitation: string,
  documentation: string,
  implementation: number | null,
  object: any | null,
  status: number,
  useCases: any | null,
  workflows: any | null,
  author: string,
  reviewer: string,
  customer: string
  dueDate: string,
  completeDate: string
  projectId: number | null,
  impacts: string
  versionHistories?: VersionType[]
}

export const defaultState: StateTransitionState = {
  detail: {
    id: null,
    code: '',
    name: '',
    diagram: null,
    description: '',
    storage: '',
    jira: '',
    confluence: '',
    reqElicitation: '',
    documentation: '',
    status: 0,
    author: '',
    reviewer: '',
    customer: '',
    dueDate: '',
    completeDate: '',
    object: null,
    implementation: null,
    useCases: null,
    workflows: null,
    projectId: null,
    impacts: '',
    versionHistories: []
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  isLoadingObjects: false,
  listObjects: [],
  isLoadingWorkflows: false,
  listWorkflows: [],
  isLoadingUsecases: false,
  listUsecases: [],
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/STATE_TRANSITION/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/STATE_TRANSITION/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/STATE_TRANSITION/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/STATE_TRANSITION/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/STATE_TRANSITION/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/STATE_TRANSITION/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/STATE_TRANSITION/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/STATE_TRANSITION/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/STATE_TRANSITION/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/STATE_TRANSITION/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/STATE_TRANSITION/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/STATE_TRANSITION/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/STATE_TRANSITION/GET_LIST_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/STATE_TRANSITION/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/STATE_TRANSITION/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/STATE_TRANSITION/GET_LIST_OBJECTS_FAILED',

  GET_LIST_WORKFLOW_REQUEST = '@@MODULES/STATE_TRANSITION/GET_LIST_WORKFLOW_REQUEST',
  GET_LIST_WORKFLOW_SUCCESS = '@@MODULES/STATE_TRANSITION/GET_LIST_WORKFLOW_SUCCESS',
  GET_LIST_WORKFLOW_FAILED = '@@MODULES/STATE_TRANSITION/GET_LIST_WORKFLOW_FAILED',

  GET_LIST_USECASES_REQUEST = '@@MODULES/STATE_TRANSITION/GET_LIST_USECASES_REQUEST',
  GET_LIST_USECASES_SUCCESS = '@@MODULES/STATE_TRANSITION/GET_LIST_USECASES_SUCCESS',
  GET_LIST_USECASES_FAILED = '@@MODULES/STATE_TRANSITION/GET_LIST_USECASES_FAILED',

  DELETE_REQUEST = '@@MODULES/STATE_TRANSITION/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/STATE_TRANSITION/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/STATE_TRANSITION/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/STATE_TRANSITION/SET_MODAL_VISIBLE',
}
