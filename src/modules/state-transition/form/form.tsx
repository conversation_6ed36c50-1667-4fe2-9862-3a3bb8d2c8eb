import AppState from '@/store/types'
import {
    Button,
    Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavAttachmentUpload from '../../../helper/component/lav-attachment-upload'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import LavVersion from '../../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import AppCommonService from '../../../services/app.service'
import { createRequest, getDetailRequest, getListObjectsRequest, getListUsecasesRequest, getListWorkflowsRequest, resetState, updateRequest } from '../action'
import { StateTransitionState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { confirm } = Modal
const { Option } = Select


interface StateTransitionFormModalProps {
    stateTransitionID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}

const StateTransitionFormPage = ({ stateTransitionID, screenMode, onFinish, onDismiss }: StateTransitionFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.StateTransition) as StateTransitionState
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const modalConfirmConfig = useModalConfirmationConfig()
    const [attachment, setAttachment] = useState(null) as any
    const attachmentRef = useRef<any>()
    const getCkeditorData: any = createRef()
    const [useCaseList, setUseCaseList] = useState([])
    const [workFlowList, setworkFlowList] = useState([])
    const [description, setDesciption] = useState('')

    const location = useLocation();
    // Destroy
    useEffect(() => {
        dispatch(getListObjectsRequest(null))
        dispatch(getListUsecasesRequest(null))
        dispatch(getListWorkflowsRequest(null))
        form.setFieldsValue({
            assignee: currentUserName()
        })
        return () => {
            dispatch(resetState(null));
            resetForm();
            form.resetFields(['createMore']);
            setAttachment(null)
        }
    }, [])


    useEffect(() => {
        if (stateTransitionID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(stateTransitionID))
        }
        
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'state.title-create' : 'state.title-update' }); 
    }, [screenMode, stateTransitionID])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }

    useEffect(() => {
        if (stateTransitionID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            setAttachment(state.detail?.diagram)
            setDesciption(state.detail?.description)
            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);

            form.setFieldsValue({
                code: state.detail?.code,
                stateTransition: state.detail?.name,
                object: state.detail?.object?.id,
                useCase: state.detail?.useCases?.map(e => e.id),
                workFlow: state.detail?.workflows?.map(e => e.id),
                req: state.detail?.reqElicitation,
                documentation: state.detail?.documentation,
                implementation: state.detail?.implementation,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
            })
            setAttachment(state.detail?.diagram)
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            const version = form.getFieldValue('version')
            const changeDescription = form.getFieldValue('changeDescription')

            if (version && version !== '') {
                const payload = {
                    version: version.substring(version.length - 1) === "." ? `${version}0` : version,
                    description: changeDescription,
                    artefactCode: state.detail?.code,
                }
                AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION, state.detail?.id).then((e) => {
                    if (isCreateMore) {
                        resetForm();
                        form.setFieldsValue({
                            assignee: currentUserName(),
                            dueDate: moment(new Date()),
                        })
                    } else {
                        if (onFinish) {
                            onFinish();
                        }
                        onDismiss();
                    }
                    setIsDraft(null);
                    setIsCreateMore(false);
                })
            } else {
                if (isCreateMore) {
                    resetForm();
                    form.setFieldsValue({
                        assignee: currentUserName(),
                        dueDate: moment(new Date()),
                    })
                } else {
                    if (onFinish) {
                        onFinish();
                    }
                    onDismiss();
                }
                setIsDraft(null);
                setIsCreateMore(false);
            }
        }
    }, [state.createSuccess, state.updateSuccess])

    useEffect(() => {
        setUseCaseList(state.listUsecases || [])
    }, [state.listUsecases])

    useEffect(() => {
        setworkFlowList(state.listWorkflows || [])
    }, [state.listWorkflows])

    useBeforeUnload()

    const changeObject = (e) => {
        form.setFieldsValue({
            [`useCase`]: undefined,
            [`workFlow`]: undefined,
        })
        dispatch(getListUsecasesRequest(e))
        dispatch(getListWorkflowsRequest(e))
    }

    const validateStateTrandisitionExplanation = (e) => {
        const err: any[] = []
        if (e && e.length > 0) {
            err.push({
                name: 'stateTrandisitionDescription',
                errors: null,
            })
        } else {
            err.push({
                name: 'stateTrandisitionDescription',
                errors: [` ${intl.formatMessage({ id: `IEM_1` })}`],
            })
        }
        form.setFields(err)
    }

    const onSubmit = debounce(async (values: any, st?: string) => {
        // if (
        //   getCkeditorData.current?.props?.data &&
        //   getCkeditorData.current?.props?.data?.length > 0
        // ) {
        const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
        const requestData: any = {
            id: stateTransitionID || null,
            name: values.stateTransition,
            version: values.version,
            diagram: attachment?.id,
            description: getCkeditorData.current?.props?.data,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
            reqElicitation: values.req,
            documentation: values.documentation,
            implementation: values.implementation,
            object: values.object,
            status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            useCases: values.useCase,
            workflows: values.workFlow,
            author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
            reviewer: values.reviewer || '',
            customer: values.customer || '',
            dueDate: values.dueDate?.toDate(),
            completeDate: values.completeDate?.toDate(),
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
            impacts: impacts
        }
        setIsCreateMore(values.createMore);
        if (requestData.status === STATUS.SUBMITTED || requestData.status === STATUS.ENDORSE) {
            if (!attachment?.id) {
                attachmentRef.current.scrollIntoView('img')
                ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.state-transition')
                return
            }
        }
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.state-transition' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
        // } else {
        //   validateStateTrandisitionExplanation(getCkeditorData.current?.props?.data)
        // }
    }, 500)

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        setAttachment(null)
        setIsDraft(null);
        setDesciption('');
        form.resetFields([
            'stateTransition',
            'version',
            'img',
            'stateTrandisitionDescription',
            'object',
            'useCase',
            'workFlow',
            'req',
            'documentation',
            'implementation',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
            'reviewer',
            'customer',
            'dueDate',
            'completeDate'
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })

    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'diagram', title: intl.formatMessage({ id: 'state.state-transition-diagram' }), },
            { field: 'description', title: intl.formatMessage({ id: 'state.state-transition-description' }), },
            { field: 'object', title: intl.formatMessage({ id: 'state.object' }), },
            { field: 'workflow', title: intl.formatMessage({ id: 'state.workflow' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
            { field: 'screen', title: intl.formatMessage({ id: 'view-use-case-details.label.screen' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'createscreen.label.req' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'createscreen.label.documentation' }), },
            { field: 'implementation', title: intl.formatMessage({ id: 'view-screen-list.label.implementation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.STATE_TRASITION,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])


    const tagRender = (props) => {
        const { label, name, value, closable, onClose } = props;


        return (
            <Tag
                // color={value}
                // onMouseDown={onPreventMouseDown}
                closable={closable}
                onClose={onClose}
                style={{
                    marginRight: 3,
                    border: 'none',
                }}
                title={label}
            >
                {label.length > 20 ? label.substring(0, 20) + '...' : label}
            </Tag>
        );
    };

    //#endregion COMMENT INIT


    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'state.title-create' : 'state.title-update' })}
                >
                    <Space size="small">
                        {screenMode == SCREEN_MODE.CREATE ?
                            (<Form.Item
                                style={{ marginBottom: '0px' }}
                                valuePropName="checked"
                                name="createMore"
                                wrapperCol={{ span: 24 }}
                            >
                                <Checkbox disabled={state.isLoading}>
                                    {intl.formatMessage({
                                        id: 'createobject.checkbox.create-another',
                                    })}
                                </Checkbox>
                            </Form.Item>) : <></>
                        }

                        <Button onClick={debounce(confirmCancel, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>

                        {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button type="primary" ghost htmlType="submit" onClick={() => {
                                    setIsDraft(false)
                                    setIsSubmitForm(true)
                                }}>
                                    {intl.formatMessage({ id: 'common.action.submit' })}
                                </Button>
                            </Form.Item> : <></>
                        }
                        {
                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button
                                    onClick={() => {
                                        setIsDraft(true)
                                        setIsSubmitForm(true)
                                    }}
                                    className="success-btn"
                                    htmlType="submit"
                                >
                                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                                </Button>
                            </Form.Item>
                        }
                    </Space>
                </LavPageHeader>
            </div>
            <Row align="middle">
                {/* <Col span={2}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={14} controlSpan={10} label={
                                <TriggerComment screenMode={screenMode} field='actor'>
                                    {intl.formatMessage({ id: 'createobject.place-holder.version' })}
                                </TriggerComment>}>
                                <Form.Item
                                    name="version"
                                    rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col> */}
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderStatusBadge(state.detail?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>
            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'state.state-transition-info' })}>
                    {
                        screenMode === SCREEN_MODE.EDIT ?

                            <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                                <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                    <Input disabled maxLength={255} />
                                </Form.Item>
                            </FormGroup> : <></>
                    }

                    <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                        <Form.Item
                            name="stateTransition"
                            rules={[
                                {
                                    required: true,
                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                },
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        >
                            <Input
                                placeholder={`${intl.formatMessage({
                                    id: `state.place-holder.state`,
                                })}${intl.formatMessage({
                                    id: `common.mandatory.*`,
                                })}`}
                                maxLength={255}
                            />
                        </Form.Item>
                    </FormGroup>
                    <div ref={attachmentRef}>
                        <FormGroup label={<TriggerComment screenMode={screenMode} field="diagram">
                            {intl.formatMessage({ id: 'state.state-transition-diagram' })}
                        </TriggerComment>}>
                            <Form.Item name="img">
                                <LavAttachmentUpload artefactType={REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION} attachment={attachment} isCommon={false} onChange={setAttachment} name="file" supportPDF />
                            </Form.Item>
                        </FormGroup>
                    </div>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="description">
                        {intl.formatMessage({ id: 'state.state-transition-description' })}
                    </TriggerComment>}>
                        <Form.Item
                            name="stateTrandisitionDescription"
                            labelAlign="left"
                            wrapperCol={{ span: 24 }}
                        >
                            <CkeditorMention
                                // validate={validateStateTrandisitionExplanation}
                                ref={getCkeditorData}
                                data={description}
                            />
                        </Form.Item>
                    </FormGroup>
                </Card>

                <Card className='rq-form-block' title={intl.formatMessage({ id: 'state.reference' })}>
                    <FormGroup inline labelSpan={3} controlSpan={21} label={<TriggerComment screenMode={screenMode} field="object">
                        {intl.formatMessage({ id: 'state.object' })}
                    </TriggerComment>}>
                        <Form.Item name="object">
                            <Select
                                showSearch
                                optionFilterProp="children"
                                filterOption={(input, option: any) => {
                                    return option?.children?.toLowerCase()?.includes(input?.toLowerCase())
                                }}
                                style={{ width: '100%' }}
                                onChange={changeObject}
                                optionLabelProp="label"
                                tagRender={tagRender}
                            >
                                {state.listObjects?.map(
                                    (item: any) =>
                                        item.status !== STATUS.DELETE &&
                                        item.status !== STATUS.CANCELLED && (
                                            <Option key={item.id} value={item.id} label={item.name}>
                                                {item.name}
                                            </Option>
                                        )
                                )}
                            </Select>
                        </Form.Item>
                    </FormGroup>
                    <FormGroup inline labelSpan={3} controlSpan={21} label={<TriggerComment screenMode={screenMode} field="workflow">
                        {intl.formatMessage({ id: 'state.workflow' })}
                    </TriggerComment>}>
                        <Form.Item name="workFlow">
                            <Select
                                optionLabelProp="label"
                                mode="multiple"
                                showSearch
                                optionFilterProp="children"
                                style={{ width: '100%' }}
                                filterOption={(input, option: any) => {
                                    return option?.name?.toLowerCase()?.includes(input?.toLowerCase())
                                }}
                                tagRender={tagRender}
                            >
                                {workFlowList?.map(
                                    (item: any) =>
                                        item.status !== STATUS.DELETE &&
                                        item.status !== STATUS.CANCELLED && (
                                            <Option key={item.id} value={item.id} label={item.name}>
                                                {item.name}
                                            </Option>
                                        )
                                )}
                            </Select>
                        </Form.Item>
                    </FormGroup>
                    {/* view-screen-list.label.use-case */}
                    <FormGroup inline labelSpan={3} controlSpan={21} label={<TriggerComment screenMode={screenMode} field="use-case">
                        {intl.formatMessage({ id: 'view-screen-list.label.use-case' })}
                    </TriggerComment>}>
                        <Form.Item name="useCase">
                            <Select
                                optionLabelProp="label"
                                mode="multiple"
                                showSearch
                                optionFilterProp="children"
                                style={{ width: '100%' }}
                                filterOption={(input, option: any) => {
                                    return option?.name?.toLowerCase()?.includes(input?.toLowerCase())
                                }}
                                tagRender={tagRender}
                            >
                                {useCaseList?.map(
                                    (item: any) =>
                                        item.status !== STATUS.DELETE &&
                                        item.status !== STATUS.CANCELLED && (
                                            <Option key={item.id} value={item.id} label={item.name}>
                                                {item.name}
                                            </Option>
                                        )
                                )}
                            </Select>
                        </Form.Item>
                    </FormGroup>
                </Card>
                <AssignTaskComponent form={form} data={screenMode == SCREEN_MODE.EDIT ? state.detail : null} isSubmit={isDraft == false} screenMode={screenMode} />
                {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION} onChange={onChange} isSubmitForm={isSubmitForm} />}
                <LavEffortEstimationForm
                    screenMode={screenMode}
                    hasDevelopment={state?.detail?.hasOwnProperty('development')}
                    hasImplementation={state?.detail?.hasOwnProperty('implementation')}
                />
                <LavRelatedLinksForm form={form} screenMode={screenMode} />

                {
                    screenMode === SCREEN_MODE.EDIT ?
                        <LavVersion screenMode={screenMode} data={state?.detail?.versionHistories} form={form}/> : <></>
                }
            </Space>
        </Form>
    </Spin>
}


export default StateTransitionFormPage
