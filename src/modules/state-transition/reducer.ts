import { createReducer } from '@reduxjs/toolkit';
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListObjectsFailed, getListObjectsRequest, getListObjectsSuccess, getListRequest,
  getListSuccess, getListUsecasesFailed, getListUsecasesRequest, getListUsecasesSuccess, getListWorkflowsFailed, getListWorkflowsRequest, getListWorkflowsSuccess, resetState, setModalVisible, updateFailed, updateRequest,
  updateSuccess
} from './action';
import { defaultState, StateTransitionState } from './type';

const initState: StateTransitionState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getListObjectsRequest, (state, action?) => {
        state.isLoadingObjects = true;
      })
      .addCase(getListObjectsSuccess, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = action.payload
      })
      .addCase(getListObjectsFailed, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = null
      })

      .addCase(getListWorkflowsRequest, (state, action?) => {
        state.isLoadingWorkflows = true;
      })
      .addCase(getListWorkflowsSuccess, (state, action) => {
        state.isLoadingWorkflows = false
        state.listWorkflows = action.payload
      })
      .addCase(getListWorkflowsFailed, (state, action) => {
        state.isLoadingWorkflows = false
        state.listWorkflows = null
      })

      .addCase(getListUsecasesRequest, (state, action?) => {
        state.isLoadingUsecases = true;
      })
      .addCase(getListUsecasesSuccess, (state, action) => {
        state.isLoadingUsecases = false
        state.listUsecases = action.payload
      })
      .addCase(getListUsecasesFailed, (state, action) => {
        state.isLoadingUsecases = false
        state.listUsecases = null
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })

      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })


      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if(!action.payload){
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })
  )
})

export default reducer
export { initState as StateTransitionState };

