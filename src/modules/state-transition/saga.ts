import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListObjectsFailed, getListObjectsRequest, getListObjectsSuccess, getListRequest,
  getListSuccess, getListUsecasesFailed, getListUsecasesRequest, getListUsecasesSuccess, getListWorkflowsFailed, getListWorkflowsRequest, getListWorkflowsSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.STATE_TRANSITIONS}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.STATE_TRANSITIONS + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
      if (res.data?.object?.id) {
        yield put(getListUsecasesRequest(res.data.object.id))
        yield put(getListWorkflowsRequest(res.data.object.id))
      }
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.STATE_TRANSITIONS + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.state-transition')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.state-transition')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.STATE_TRANSITIONS, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.state-transition')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.state-transition', err)
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.STATE_TRANSITIONS + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.state-transition')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.state-transition')
    }
  }
}

function* handleGetListObjects(action: Action) {
  if (getListObjectsRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_OBJECTS
      const res = yield call(apiCall, 'GET', url)
      yield put(getListObjectsSuccess(res.data));
    } catch (err) {
      yield put(getListObjectsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListWorkflows(action: Action) {
  if (getListWorkflowsRequest.match(action)) {
    try {
      const url = action.payload ? API_URLS.REFERENCES_OBJECTS + `/${action.payload}/workflows` : API_URLS.REFERENCES_WORKFLOWS
      const res = yield call(apiCall, 'GET', url)
      yield put(getListWorkflowsSuccess(res.data));
    } catch (err) {
      yield put(getListWorkflowsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListUsecases(action: Action) {
  if (getListUsecasesRequest.match(action)) {
    try {
      const url = action.payload ? API_URLS.REFERENCES_OBJECTS + `/${action.payload}/functions` : API_URLS.REFERENCES_FUNCTIONS
      const res = yield call(apiCall, 'GET', url)
      yield put(getListUsecasesSuccess(res.data));
    } catch (err) {
      yield put(getListUsecasesFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getListObjectsRequest.type, handleGetListObjects)
  yield takeLatest(getListWorkflowsRequest.type, handleGetListWorkflows)
  yield takeLatest(getListUsecasesRequest.type, handleGetListUsecases)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
}
export default function* StateTransitionSaga() {
  yield all([fork(watchFetchRequest)])
}
