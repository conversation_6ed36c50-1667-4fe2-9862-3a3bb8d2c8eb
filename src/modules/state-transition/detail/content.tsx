import AppState from '@/store/types'
import {
    Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTaskDetail from '../../../helper/component/assign-task-detail'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAttachmentPreview from '../../../helper/component/lav-attachment-preview'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavButtons from '../../../helper/component/lav-buttons'
import LavEffortEstimation from '../../../helper/component/lav-efffort-estimation'
import LavImpact from '../../../helper/component/lav-impact'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import LavVersion from '../../../helper/component/lav-version/form'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../_shared/comment/action'
import TriggerComment from '../../_shared/comment/trigger-comment'
import { CommentState } from '../../_shared/comment/type'
import { deleteRequest } from '../action'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    stateTransitionID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}
const RightControl = ({ data, stateTransitionID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;


    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + data?.name; 
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id && co?.artefactType == REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'diagram', title: intl.formatMessage({ id: 'state.state-transition-diagram' }), },
            { field: 'description', title: intl.formatMessage({ id: 'state.state-transition-description' }), },
            { field: 'object', title: intl.formatMessage({ id: 'view-use-case-details.label.object' }), },
            { field: 'workflow', title: intl.formatMessage({ id: 'view-use-case-details.label.workflow' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'view-use-case-details.label.use-case' }), },
            { field: 'actor', title: intl.formatMessage({ id: 'view-screen-list.label.actor' }), },
            { field: 'screen', title: intl.formatMessage({ id: 'view-use-case-details.label.screen' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'implementation', title: intl.formatMessage({ id: 'view-screen-list.label.implementation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.STATE_TRASITION,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])
    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE && (
            <DeleteButton
                type={BUTTON_TYPE.TEXT}
                content={`${intl.formatMessage(
                    { id: 'CFD_7' },
                    {
                        artefact_type: `${intl.formatMessage({
                            id: 'common.artefact.state-transition',
                        })}`,
                    }
                )}`}
                okCB={() => dispatch(deleteRequest(stateTransitionID))}
                confirmButton={`${intl.formatMessage({
                    id: 'common.action.delete',
                })}`}
            ></DeleteButton>
        )
    }
    //#endregion COMMENT INIT
    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code}-{data?.name}
                    </Title>
                </div>

                <Space size="small">
                    <LavButtons
                        url={`${API_URLS.STATE_TRANSITIONS}/${data?.id}`}
                        reviewer={`${data?.reviewer}`}
                        customer={`${data?.customer}`}
                        artefact_type="common.artefact.state-transition"
                        status={data?.status}
                        isHasReject={true}
                        isHasCancel={true}
                        isHasApprove={true}
                        isHasEndorse={true}
                        artefactType={REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION}
                        id={stateTransitionID}
                        deleteButton={DeleteComponent}
                        changePage={() => onChange()}>

                        {((((hasRole(APP_ROLES.BA) && data.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer)
                            && (data?.status === STATUS.SUBMITTED || data?.status === STATUS.REJECT || data?.status === STATUS.REJECT_CUSTOMER || data?.status === STATUS.APPROVE))) &&
                            data.status !== STATUS.CANCELLED &&
                            data.status !== STATUS.DELETE &&
                            data.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && data.status !== STATUS.CANCELLED &&
                            data.status !== STATUS.DELETE
                        ) && (
                                <Button
                                    type='primary'
                                    className='lav-btn-create'
                                    onClick={() => {
                                        setScreenMode(SCREEN_MODE.EDIT)
                                    }} >{intl.formatMessage({ id: 'common.action.update' })}</Button>
                            )}
                    </LavButtons>
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical">
                        <Space size="large">                                                             
                            <span>          
                                <TriggerComment field='version'>                               
                                    <a onClick={() => {
                                        setScreenMode(SCREEN_MODE.HISTORY)
                                    }}>
                                        {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                    </a>
                                </TriggerComment>
                            </span>
                            {renderStatusBadge(data?.status)}
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'state.state-transition-info',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={24}>
                                    <TriggerComment field="diagram">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'state.state-transition-diagram',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <LavAttachmentPreview isCommon={false} attachment={data?.diagram} />
                                </Col>
                                <Col span={24}>
                                    <TriggerComment field="description">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'state.state-transition-description',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <div
                                        className="discription"
                                        dangerouslySetInnerHTML={{
                                            __html: data?.description,
                                        }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>

                        <LavReferences data={data} />
                        <AssignTaskDetail data={data} />
                        {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION} onChange={() => { }} isViewMode={true} />}
                        {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION} onChange={() => { }} isViewMode={true} /> : <></>} */}
                        <Row justify="space-between">
                            <Col span={8}>
                                <LavEffortEstimation data={data} />
                            </Col>
                            <Col span={15}>
                                <LavRelatedLinks data={data} />
                            </Col>
                        </Row>

                        <Col span={24}>
                            <LavVersion screenMode={SCREEN_MODE.VIEW} data={data.versionHistories} />
                        </Col>
                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
