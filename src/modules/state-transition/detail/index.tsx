import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import StateTransitionFormPage from '../form/form'
import { StateTransitionState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'
import HistoryScreen from '../../../modules/history'
import AppCommonService from '../../../services/app.service'
import StateTransitionVersionDetails from './history/details'

const StateTransitionDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.StateTransition) as StateTransitionState;
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.stateTransitionID) {
      dispatch(getDetailRequest(props.match.params.stateTransitionID))
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])  

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.STATE_TRANSITIONS + '/version/' + props.match.params.stateTransitionID +  '/' + selectedRowVersion).then((e) => {
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);    
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.STATE_TRANSITION}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.stateTransitionID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.STATE_TRANSITION_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col span={5}>
            <LavLeftControl
              activeId={props.match.params.stateTransitionID}
              apiUrl={API_URLS.REFERENCES_STATE_TRANSITIONS}
              route={APP_ROUTES.STATE_TRANSITION_DETAIL}
              artefactType={REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION}
              title='state.header.title'
              reload={reload}
              reloadSuccess={() => setReload(false)}
              handleCreate={handleCreate}
            >
              {
                hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA) ? <Button ghost={true}
                type='primary'
                className='lav-btn-create'
                icon={<PlusOutlined />}
                onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'state.create.state' })}
              </Button> : <></>
              }
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>            
            <Col span={19}>
              <RightControl setScreenMode={setScreenMode} onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} stateTransitionID={props.match.params.stateTransitionID} isModalShow={state?.isModalShow} />
            </Col>
          </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <StateTransitionFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <StateTransitionFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
            }} stateTransitionID={props.match.params.stateTransitionID} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.state-transition"
                            apiURL = {API_URLS.HISTORY}
                            artefactType = {REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + state?.selectedData?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
              <StateTransitionVersionDetails stateTransitionID={props.match.params.stateTransitionID} setSelectedRowVersion = {setSelectedRowVersion} isModalShow={state?.isModalShow} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
    </Row>
  )
}

export default StateTransitionDetail
