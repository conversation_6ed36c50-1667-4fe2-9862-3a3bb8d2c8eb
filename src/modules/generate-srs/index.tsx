import {
  Button,
  Checkbox,
  Col,
  Modal,
  Radio,
  Row,
  Space,
  Table,
  Typography,
} from 'antd'
import { FC, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../config/locale.config'
import {
  ARTEFACT_NAME_GENERATE,
  REQ_ARTEFACT_TYPE_ID,
  STATUS,
  API_URLS,
  APP_ROLES,
  DATETIME_FORMAT,
} from '../../constants'
import CustomModal from '../../helper/component/custom-modal'
import useModalConfirmationConfig from '../../helper/hooks/useModalConfirmationConfig'
import AppState from '../../store/types'
import { generatesrs, generatesrsFailure } from './action'
import { GenerateSrsProps, GenerateSrsState } from './type'
import LavTable from '../../helper/component/lav-table'
import { extractProjectCode, hasRole } from '../../helper/share'
import GenerateModal from './modal'
import moment from 'moment'
import { DownloadOutlined } from '@ant-design/icons'
import AppCommonService from '../../services/app.service'
import { render } from '@testing-library/react'
const GenerateMode = {
  genAllArtefact: 1,
  genSelectArtefact: 2,
}
const GenerateSrsModal: FC<GenerateSrsProps> = ({ onDismiss }) => {
  const dispatch = useDispatch()
  const [generateMode, setGenerateMode] = useState(GenerateMode.genAllArtefact)
  const [listStatus, setListStatus] = useState<number[]>([])
  const [listArtefact, setListArtefact] = useState<number[]>([])
  const [warningMissingArtefact, setWarningMissingArtefact] = useState(false)
  const modalConfirmConfig = useModalConfirmationConfig()
  const state = useSelector<AppState | null>(
    (s) => s?.generateSrs
  ) as GenerateSrsState

  useEffect(() => {
    // if (state.isGenerateSuccess) {
    //   onDismiss()
    //   dispatch(generatesrsFailure())
    // }
  }, [state])

  useEffect(() => {    
        document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'generatesrs.list'}); 
  }, []);

  const columns = [
    {
      title: 'Name',
      dataIndex: 'value',
      render: (text: string) => <Typography.Text>{text}</Typography.Text>,
    },
  ]

  const [showGenerateModal, setShowGenerateModal] = useState(false)
  const [screenMode, setScreenMode] = useState<any>(0)
  const columnsTable = [
    {
      title: intl.formatMessage({ id: 'generatesrs.column.name' }),
      dataIndex: 'name',
      width: '70%',      
    },
    {
      title: intl.formatMessage({ id: 'generatesrs.column.size' }),
      dataIndex: 'size',
      render: (byteLength, record: any, order) => {
        return byteLength ? formatBytes(byteLength) : null
      },
      width: '12%',
    },
    {
      title: intl.formatMessage({ id: 'generatesrs.column.modified' }),
      dataIndex: 'createdAt',
      render: (text, record: any, order) => {
        return text ? moment(text).format(DATETIME_FORMAT) : null
      },
      width: '12%',      
    },    
  ]

  const ImportComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && (
        <GenerateModal onDismiss={() => handleDataChange()} />
      )
    )
  }

  const handelDownloadSRS = (fileName: string) => {
    const url = `${API_URLS.DOWNLOAD_SRS}?fileName=${fileName}`
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    link.parentNode?.removeChild(link)
  }
  const DownloadSRSComponent: React.FC<any> = ({
    record,
    handleDataChange,
  }) => {
    const xxx = record.name
    return (
      <>
        <Button
          style={{ border: 'none' }}
          icon={<DownloadOutlined />}
          onClick={() => {
            handelDownloadSRS(xxx)
          }}
        />
      </>
    )
  }

  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      if (selectedRowKeys.includes(REQ_ARTEFACT_TYPE_ID.USECASE)) {
        const newListArtefact = [
          ...selectedRowKeys,
          REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE,
        ]
        setListArtefact(newListArtefact)
      } else {
        setListArtefact(selectedRowKeys)
      }
      if (selectedRowKeys.length !== 0) {
        setWarningMissingArtefact(false)
      } else {
        setWarningMissingArtefact(true)
      }
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    }),
  }
  const GenerateConfirm = () => {
    Modal.confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_11' })}`,
      onOk: () => {
        const payload = {
          listStatus,
          listArtefact,
        }
        dispatch(generatesrs(payload))
      },
    })
  }

  const handleChangeStatus = (status: number) => {
    if (listStatus.indexOf(status) === -1) {
      let newListStatus: number[] = [status, ...listStatus]
      setListStatus(newListStatus)
    } else {
      let newListStatus: number[] = [...listStatus]
      newListStatus.splice(listStatus.indexOf(status), 1)
      setListStatus(newListStatus)
    }
  }

  const formatBytes = (byteLength, decimals = 2) => {
    if (!+byteLength) return '0 Bytes'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

    const i = Math.floor(Math.log(byteLength) / Math.log(k))

    return `${parseFloat((byteLength / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
  }

  const Footer = () => {
    return (
      <Space>
        <Button onClick={() => onDismiss()}>
          {intl.formatMessage({ id: 'common.action.close' })}
        </Button>

        <Button
          onClick={() => {
            if (
              listArtefact.length === 0 &&
              generateMode === GenerateMode.genSelectArtefact
            ) {
              setWarningMissingArtefact(true)
            } else {
              GenerateConfirm()
            }
          }}
          className="success-btn"
        >
          {intl.formatMessage({ id: 'common.action.generate' })}
        </Button>
      </Space>
    )
  }
  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === 0 ? (
        <LavTable
          title="generatesrs.list"
          artefact_type="common.artefact.epic"
          apiUrl={API_URLS.GET_LIST_GENERATE_SRS}
          columns={columnsTable}
          artefactType={REQ_ARTEFACT_TYPE_ID.EPIC}
          importComponent={ImportComponent}
          updateComponent={DownloadSRSComponent}
        />
      ) : (
        <></>
      )}
    </Space>
  )
}
export default GenerateSrsModal
