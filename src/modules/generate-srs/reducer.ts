import { createReducer } from '@reduxjs/toolkit'
import { stat } from 'fs'
import {
generatesrs,
generatesrsSuccess,
generatesrsFailure
} from './action'
import { GenerateSrsState } from './type'

const initState: GenerateSrsState = {
  isLoading: false,
  isGenerateSuccess: false,
}

const reducer = createReducer(initState, (builder) => {
  return builder
    .addCase(generatesrs, (state, action) => {
      state.isLoading = true
    })
    .addCase(generatesrsFailure, (state, action) => {
      state.isLoading = false
      state.isGenerateSuccess = false
    })
    .addCase(generatesrsSuccess,(state,action)=>{
      state.isLoading= false
      state.isGenerateSuccess = true
    })
})

export default reducer
export { initState as GenerateSrsInitState }
