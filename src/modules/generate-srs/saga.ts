import { Action } from '@reduxjs/toolkit'
import { saveAs } from 'file-saver'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import { generatesrs, generatesrsFailure, generatesrsSuccess } from './action'

function* GenerateSrsFlow(action: Action) {
  if (generatesrs.match(action)) {
    try {
      let params = '?'
      action.payload?.listArtefact.forEach((ele, index) => {
        if (index === 0) {
          params += `ArtefactType=${ele}`
        } else {
          params += `&ArtefactType=${ele}`
        }
      })

      action.payload?.listStatus.forEach((ele, index) => {
        if (action.payload?.listArtefact.length === 0 && index === 0) {
          params += `Status=${ele}`
        } else if ((action.payload?.listArtefact.length > 0 && index === 0) || index !== 0) {
          params += `&Status=${ele}`
        }
      })
      const url = API_URLS.GENERATE_SRS + params
      yield call(apiCall, 'GET', url)
      ShowAppMessage(null, MESSAGE_TYPES.EXPORT)
      // const urlDownLoad = `${API_URLS.DOWNLOAD_SRS}?fileName=${resGenerate.data}`
      // const resDownload = yield call(apiCall, 'GET', urlDownLoad, undefined, undefined, true)
      // var blob = new Blob([resDownload.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
      // saveAs(blob, resGenerate.data + '.docx')
      yield put(generatesrsSuccess());
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(generatesrsFailure())
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(generatesrs.type, GenerateSrsFlow)
}
export default function* GeneratesrsSaga() {
  yield all([fork(watchFetchRequest)])
}
