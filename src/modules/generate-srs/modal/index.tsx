import { Button, Checkbox, Col, Modal, Radio, Row, Space, Table, Typography } from 'antd'
import { FC, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { ARTEFACT_NAME_GENERATE, REQ_ARTEFACT_TYPE_ID, STATUS } from '../../../constants'
import CustomModal from '../../../helper/component/custom-modal'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import AppState from '../../../store/types'
import { generatesrs, generatesrsFailure } from './../action'
import { GenerateSrsProps, GenerateSrsState } from './../type'
import { UploadOutlined } from '@ant-design/icons'

const GenerateMode = {
  genAllArtefact: 1,
  genSelectArtefact: 2
}
const GenerateModal: FC<GenerateSrsProps> = ({ onDismiss }) => {
  const dispatch = useDispatch()
  const [step, setStep] = useState(0)
  const [generateMode, setGenerateMode] = useState(GenerateMode.genAllArtefact)
  const [listStatus, setListStatus] = useState<number[]>([])
  const [listArtefact, setListArtefact] = useState<number[]>([])
  const [warningMissingArtefact, setWarningMissingArtefact] = useState(false)
  const modalConfirmConfig = useModalConfirmationConfig()
  const state = useSelector<AppState | null>(
    (s) => s?.generateSrs
  ) as GenerateSrsState

  useEffect(() => {
    if (state.isGenerateSuccess) {
      onDismiss()
      dispatch(generatesrsFailure())
    }
  }, [state])
  const columns = [
    {
      title: 'Name',
      dataIndex: 'value',
      render: (text: string) => <Typography.Text>{text}</Typography.Text>,
    },

  ]

  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      if(selectedRowKeys.includes(REQ_ARTEFACT_TYPE_ID.USECASE)) {
        const newListArtefact = [...selectedRowKeys, REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE]
        setListArtefact(newListArtefact)
      } else {
        setListArtefact(selectedRowKeys)
      }
      if (selectedRowKeys.length !== 0) {
        setWarningMissingArtefact(false)
      } else {
        setWarningMissingArtefact(true)
      }
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    }),
  };
  const GenerateConfirm = () => {
    Modal.confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_11' })}`,
      onOk: () => {
        const payload = {
          listStatus,
          listArtefact,
        }
        dispatch(generatesrs(payload))
      },
    })
  }

  const handleChangeStatus = (status: number) => {
    if (listStatus.indexOf(status) === -1) {
      let newListStatus: number[] = [status, ...listStatus]
      setListStatus(newListStatus)
    } else {
      let newListStatus: number[] = [...listStatus]
      newListStatus.splice(listStatus.indexOf(status), 1)
      setListStatus(newListStatus)
    }
  }

  const Footer = () => {
    return <Space >
      <Button onClick={() => handleCancel()}>
        {intl.formatMessage({ id: 'common.action.close' })}
      </Button>

      <Button onClick={() => {
        if (listArtefact.length === 0 && generateMode === GenerateMode.genSelectArtefact) {
          setWarningMissingArtefact(true)
        } else {
          GenerateConfirm()
        }
      }} className="success-btn">
        {intl.formatMessage({ id: 'common.action.generate' })}
      </Button>
    </Space>
  }

  const handleOpenModal = () => {
    setStep(1)
  }
  const handleCancel = () => {
    setStep(0)
  }
  const handleBack = () => {
    setStep(0)
  }
  
  return (
    <>
    <Space>
    <Button
      size="middle"
      type="primary"
      ghost
      onClick={handleOpenModal}
    >
      {intl.formatMessage({
        id: 'project.config.generate-srs',
      })}
    </Button>
  </Space>
  { step === 1 && <CustomModal
    closable={false}
    title={intl.formatMessage({ id: 'app.menu.utilities.generate_srs' })}
    size="medium"
    visible={true}
    footer={<Footer />}
  >
    <Row gutter={[10, 10]}>
      <Col span={16}>
        <div>
          <Typography.Title level={4}>{intl.formatMessage({ id: 'generatesrs.by-artefact' })}</Typography.Title>
        </div>
        <div>
          <Radio.Group value={generateMode} onChange={(e) => {
            e.preventDefault()
            if (e.target.value === GenerateMode.genAllArtefact) {
              setWarningMissingArtefact(false)
            }
            setGenerateMode(e.target.value)
          }}>
            <Space direction="vertical">
              <Radio value={GenerateMode.genAllArtefact}>{intl.formatMessage({ id: 'generatesrs.by-all-artefact' })}</Radio>
              <Radio value={GenerateMode.genSelectArtefact}>{intl.formatMessage({ id: 'generatesrs.by-selected-artefact' })}</Radio>
            </Space>
          </Radio.Group>
        </div>
        {
          warningMissingArtefact ? <Typography.Text type="danger">You must select at least one artefact</Typography.Text> : <></>
        }
        {
          generateMode === GenerateMode.genSelectArtefact ? <div>
            <Table
              rowSelection={{
                type: 'checkbox',
                ...rowSelection,
              }}
              columns={columns}
              bordered
              pagination={false}
              dataSource={ARTEFACT_NAME_GENERATE}
              style={{ padding: '10px 0' }}
            />
          </div> : <></>
        }
      </Col>
      <Col span={8}>
        <div>
          <Typography.Title level={4}>{intl.formatMessage({ id: 'generatesrs.by-status' })}</Typography.Title>
        </div>
        <div>
          <Typography.Text italic>{intl.formatMessage({ id: 'generatesrs.by-status-tip' })}</Typography.Text>
        </div>

        <div>
          <Space direction="vertical" style={{ padding: '10px 0' }}>
            <Checkbox value={STATUS.DRAFT} onClick={() => handleChangeStatus(STATUS.DRAFT)} checked={listStatus?.includes(STATUS.DRAFT)}>{intl.formatMessage({ id: 'common.status.draft' })}</Checkbox>
            <Checkbox value={STATUS.SUBMITTED} onClick={(e) => handleChangeStatus(STATUS.SUBMITTED)} checked={listStatus?.includes(STATUS.SUBMITTED)}>{intl.formatMessage({ id: 'common.status.submitted' })}</Checkbox>
            <Checkbox value={STATUS.ENDORSE} onClick={(e) => handleChangeStatus(STATUS.ENDORSE)} checked={listStatus?.includes(STATUS.ENDORSE)}>{intl.formatMessage({ id: 'common.status.endorsed' })}</Checkbox>
            <Checkbox value={STATUS.APPROVE} onClick={(e) => handleChangeStatus(STATUS.APPROVE)} checked={listStatus?.includes(STATUS.APPROVE)}>{intl.formatMessage({ id: 'common.status.approve' })}</Checkbox>
            <Checkbox value={STATUS.CANCELLED} onClick={(e) => handleChangeStatus(STATUS.CANCELLED)} checked={listStatus?.includes(STATUS.CANCELLED)}>{intl.formatMessage({ id: 'common.status.cancelled' })}</Checkbox>
            <Checkbox value={STATUS.REJECT} onClick={(e) => handleChangeStatus(STATUS.REJECT)} checked={listStatus?.includes(STATUS.REJECT)}>{intl.formatMessage({ id: 'common.status.rejected-by-reviewer' })}</Checkbox>
            <Checkbox value={STATUS.REJECT_CUSTOMER} onClick={(e) => handleChangeStatus(STATUS.REJECT_CUSTOMER)} checked={listStatus?.includes(STATUS.REJECT_CUSTOMER)}>{intl.formatMessage({ id: 'common.status.rejected-by-customer' })}</Checkbox>
          </Space>
        </div>
      </Col>
    </Row>
  </CustomModal> }
  </>
  )
}
export default GenerateModal
