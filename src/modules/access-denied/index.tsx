
import { APP_ROUTES } from '../../constants';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { Link } from 'react-router-dom';
import AccessDeniedImage from '../../assets/images/403.png';
import { useEffect } from 'react';

const AccessDeniedPage = () => {
    useEffect(() => {
        document.title = "Page not found";
    }, [])
    return (
        <div className="rq-access-denied" style={{ height: 'calc(100vh - 45px)', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
            <div>
                <img src={AccessDeniedImage} height={150} alt="403" />
            </div>
            <h2 style={{ fontSize: '36px', marginBottom: 0 }}>Access Denied</h2>
            <p style={{ marginBottom: 30 }}>You are not allowed to access this page at this time</p>
            <Link to={APP_ROUTES.PROJECTS}>
                <Button type='primary' size='large'>
                    <ArrowLeftOutlined />
                    <span>Back to project list</span>
                </Button>
            </Link>
        </div>
    )
}
export default AccessDeniedPage