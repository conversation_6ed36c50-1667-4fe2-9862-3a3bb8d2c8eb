import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import { generatewbs, generatewbsFailure, generatewbsSuccess } from './action'

function* GenerateWbsFlow(action: Action) {
  if (generatewbs.match(action)) {
    try {
      const url = API_URLS.GENERATE_WBS
      yield call(apiCall, 'GET', url, action.payload)
      ShowAppMessage(null, MESSAGE_TYPES.EXPORT)
      yield put(generatewbsSuccess());
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(generatewbsFailure())
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(generatewbs.type, GenerateWbsFlow)
}
export default function* GenerateWbsSaga() {
  yield all([fork(watchFetchRequest)])
}
