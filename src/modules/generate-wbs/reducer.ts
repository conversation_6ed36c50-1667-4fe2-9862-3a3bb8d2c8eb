import { createReducer } from '@reduxjs/toolkit'
import {
generatewbs,
generatewbsSuccess,
generatewbsFailure
} from './action'
import { GenerateWbsState } from './type'

const initState: GenerateWbsState = {
  isLoading: false,
  isGenerateSuccess: false,
}

const reducer = createReducer(initState, (builder) => {
  return builder
    .addCase(generatewbs, (state, action) => {
      state.isLoading = true
    })
    .addCase(generatewbsFailure, (state, action) => {
      state.isLoading = false
      state.isGenerateSuccess = false
    })
    .addCase(generatewbsSuccess,(state,action)=>{
      state.isLoading= false
      state.isGenerateSuccess = true
    })
})

export default reducer
export { initState as GenerateWbsInitState }
