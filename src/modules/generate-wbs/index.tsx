import {
  Button,
  Space,
} from 'antd'
import { FC } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../config/locale.config'
import {
  REQ_ARTEFACT_TYPE_ID,
  API_URLS,
  DATETIME_FORMAT,
} from '../../constants'
import AppState from '../../store/types'
import { generatewbs } from './action'
import { GenerateWbsState } from './type'
import LavTable from '../../helper/component/lav-table'
import moment from 'moment'
import { DownloadOutlined } from '@ant-design/icons'
import { ShowAppMessage } from '../../helper/share';


const GenerateWbs: FC = () => {
  const dispatch = useDispatch()
  const state = useSelector<AppState | null>(
    (s) => s?.generateWbs
  ) as GenerateWbsState



  const columnsTable: any = [
    {
      title: intl.formatMessage({ id: 'generatewbs.column.name' }),
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <span style={{ fontWeight: 'bold' }}>{record.name}</span>
      ),
    },
    {
      title: intl.formatMessage({ id: 'generatewbs.column.size' }),
      dataIndex: 'size',
      key: 'size',
      render: (text, record) => <span>{formatBytes(record.size)}</span>,
    },
    {
      title: intl.formatMessage({ id: 'generatewbs.column.createdAt' }),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text, record) => 
        {
          const date = moment(record.createdAt);
          return (
            <span>
              { date.format(DATETIME_FORMAT) ?? record.createdAt }
            </span>
          )
        }
      ,
    },
  ]

  const GenerateWBS: React.FC<any> = () => {
    const handleGenerateWBS = async () => {
      try {
        //await apiCall('GET', API_URLS.GENERATE_WBS);
        dispatch(generatewbs())
        ShowAppMessage(null, "WBS are being generated, document will be ready in minutes");
      } catch (error) {
        ShowAppMessage(error, "Oops! Something went wrong while generating WBS");
      }
    };
    return (
      <Button
        onClick={handleGenerateWBS}
        className="success-btn"
      >
        {intl.formatMessage({ id: 'common.action.generate' })}
      </Button>
    );
  } 

  const handelDownloadWBS = (fileName: string) => {
    const url = `${API_URLS.DOWNLOAD_WBS}?fileName=${fileName}`
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    link.parentNode?.removeChild(link)
  }

  const DownloadWBSComponent: React.FC<any> = ({
    record,
    handleDataChange,
  }) => {
    const fileName = record.name
    return (
      <>
        <Button
          style={{ border: 'none' }}
          icon={<DownloadOutlined />}
          onClick={() => {
            handelDownloadWBS(fileName)
          }}
        />
      </>
    )
  }


  const formatBytes = (byteLength, decimals = 2) => {
    if (!+byteLength) return '0 Bytes'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

    const i = Math.floor(Math.log(byteLength) / Math.log(k))

    return `${parseFloat((byteLength / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      <LavTable
        title="generatewbs.list"
        artefact_type="common.artefact.epic"
        apiUrl={API_URLS.GET_LIST_GENERATE_WBS}
        columns={columnsTable}
        artefactType={REQ_ARTEFACT_TYPE_ID.EPIC}
        importComponent={GenerateWBS}
        updateComponent={DownloadWBSComponent}
      />
    </Space>
  )
}

export default GenerateWbs
