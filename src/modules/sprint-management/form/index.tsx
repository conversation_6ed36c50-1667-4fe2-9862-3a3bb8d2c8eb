import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
    Breadcrumb,
    Button, Col, DatePicker, Form, Input, Modal, Row, Select, Space, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROUTES, BUTTON_TYPE, DATE_FORMAT, DURATION_SPRINT, MESSAGE_TYPES, PROJECT_PREFIX, SCREEN_MODE } from '../../../constants'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomModal from '../../../helper/component/custom-modal'
import FormGroup from '../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, extractProjectCode, getProjectName, getReferencesFromEditor } from '../../../helper/share'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { SprintManagementState } from '../type'


const { Text, Title } = Typography
const { confirm } = Modal
const { Option } = Select

interface SprintManagementFormProps {
    sprintId?: number,
    onFinish?: () => void | null,
    buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface SprintManagementFormModalProps {
    sprintId?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}
const SprintManagementFormModal = ({ sprintId, screenMode, onFinish, onDismiss }: SprintManagementFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.Sprints) as SprintManagementState
    const [isDraft, setIsDraft] = useState<any>(null);
    const [goals, setGoals] = useState('') as any
    const [scope, setScope] = useState('') as any
    const [story, setStory] = useState([]) as any
    const [isCreateMore, setIsCreateMore] = useState(false);
    const { height: windowHeight } = useWindowDimensions()
    const modalConfirmConfig = useModalConfirmationConfig()
    const getCkeditorData: any = createRef()
    const getCkeditorDataScope: any = createRef()
    const projectCode = extractProjectCode()
    const projectName = getProjectName(projectCode)
    const [disableEndDate, setDisableEndDate] = useState(false)
    const defaultDuration = DURATION_SPRINT[0].values


    // Destroy
    useEffect(() => {
        form.setFieldsValue({
            duration: defaultDuration,
            'startDate': moment(new Date(), DATE_FORMAT),
            'endDate': moment(new Date(), DATE_FORMAT)
        })
        return () => {
            dispatch(resetState(null));
            resetForm();
            form.resetFields(['createMore']);
        }
    }, [])

    useEffect(() => {
        if (sprintId && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(sprintId))
        }
    }, [screenMode, sprintId])


    useEffect(() => {
        if (sprintId && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            form.setFieldsValue({
                // ...state.detail,
                code: state.detail.code,
                name: state.detail.name,
                duration: state.detail.duration,
                startDate: state.detail.startDate ? moment(new Date(state.detail.startDate)) : null,
                endDate: state.detail.endDate ? moment(new Date(state.detail.endDate)) : null,

            })
            if (state.detail.duration !== 1) {
                setDisableEndDate(true)
            }
            setStory(state.detail?.userStorySprintResponses)
            setGoals(state.detail?.goals)
            setScope(state.detail?.scope)
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            if (onFinish) {
                onFinish();

            }
            if (isCreateMore) {
                resetForm();
                form.setFieldsValue({
                    createMore: isCreateMore,
                    duration: defaultDuration,
                    'startDate': moment(new Date(), DATE_FORMAT),
                    'endDate': moment(new Date(), DATE_FORMAT)
                })
            } else {
                onDismiss();
            }
            setIsDraft(null);
            setIsCreateMore(false);
        }
    }, [state.createSuccess, state.updateSuccess])

    const onSubmit = debounce( async (values: any, st?: string) => {
        let mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data);
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(getCkeditorDataScope.current?.props?.data))
        const requestData: any = {
            "id": sprintId || null,
            "code": values.code,
            "name": values.name,
            "goals": getCkeditorData?.current?.props?.data,
            "scope": getCkeditorDataScope?.current?.props?.data,
            "duration": values.duration,
            "startDate": values.startDate,
            "endDate": values.endDate,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,

        }
        setIsCreateMore(values.createMore);
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            //   if (requestData.status === STATUS.SUBMITTED) {

            //     if (!requestData.functionIds || requestData.functionIds?.length === 0) {
            //       useCaseRef.current.scrollIntoView('functions')
            //       ShowMessgeAdditionalSubmit('EMSG_14');
            //       return
            //     }
            //     if (!requestData.screenComponents || requestData.screenComponents?.length === 0) {
            //       tableUpdateRef.current.scrollIntoView('updatetablescreen')
            //       ShowMessgeAdditionalSubmit('EMSG_15')
            //       return
            //     }
            //   }
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.sprint' }) }
                ),
                onOk() {
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
    }, 500)

    const handleDueDateChange = (e) => {
        if (form.getFieldValue('startDate') && form.getFieldValue('endDate') && form.getFieldValue('startDate')?.startOf('day') > form.getFieldValue('endDate')?.startOf('day')) {
            form.resetFields(['endDate']);
        }
        const duration = form.getFieldValue('duration')
        if (e) {
            switch (duration) {
                case 1: form.resetFields(['endDate']);
                    break;
                case 2:
                    form.setFieldsValue({
                        'endDate': moment(e).add(7, 'days')
                    })
                    break;
                case 3:
                    form.setFieldsValue({
                        'endDate': moment(e).add(14, 'days')
                    })
                    break;
                case 4:
                    form.setFieldsValue({
                        'endDate': moment(e).add(21, 'days')
                    })
                    break;
                case 5:
                    form.setFieldsValue({
                        'endDate': moment(e).add(28, 'days')
                    })
                    break;
            }
        } else {
            form.resetFields(['endDate']);
        }


    }

    const disabledCompleteDate = (current) => {
        return current && form.getFieldValue('startDate') && current < form.getFieldValue('startDate')?.startOf('day') && current < moment().startOf('day');
    }

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        setIsDraft(null);
        setGoals('')
        setScope('')
        form.resetFields([
            'name',
            'createMore',
            'code',

        ])
        form.setFieldsValue({
            duration: defaultDuration,
            'startDate': moment(new Date(), DATE_FORMAT),
            'endDate': moment(new Date(), DATE_FORMAT)
        })
    }
    const handleDuration = (e) => {
        if (e !== 1) {
            setDisableEndDate(true)
            switch (e) {
                case 2:
                    form.getFieldValue('startDate') !== null ?
                        form.setFieldsValue({
                            'endDate': moment(form.getFieldValue('startDate')).add(7, 'days')
                        })
                        :
                        form.resetFields(['endDate']);
                    break;
                case 3:
                    form.getFieldValue('startDate') !== null ?
                        form.setFieldsValue({
                            'endDate': moment(form.getFieldValue('startDate')).add(14, 'days')
                        })
                        :
                        form.resetFields(['endDate']);
                    break;
                case 4:
                    form.getFieldValue('startDate') !== null ?
                        form.setFieldsValue({
                            'endDate': moment(form.getFieldValue('startDate')).add(21, 'days')
                        })
                        :
                        form.resetFields(['endDate']);
                    break;
                case 5:
                    form.getFieldValue('startDate') !== null ?
                        form.setFieldsValue({
                            'endDate': moment(form.getFieldValue('startDate')).add(28, 'days')
                        })
                        :
                        form.resetFields(['endDate']);
                    break;
            }
        } else {
            setDisableEndDate(false)
        }
    }

    return <CustomModal
        isLoading={state.isLoading}
        closable={false}
        size="medium"
        visible={true}
        footer={null}
    >
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <Row>
                    <Col span={10}>
                        <Space size="large">
                            <Form.Item
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: intl.formatMessage({ id: 'IEM_1' }),
                                    },
                                    { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                    {
                                        validator: async (rule, value) => {
                                            if (value && value.trim().length === 0) {
                                                throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                            }
                                        },
                                    },
                                ]}
                            >
                                <Input
                                    size="large"
                                    className="modal-create-name-input-field"
                                    placeholder={`${intl.formatMessage({
                                        id: `createsprint.place-holder.sprint-name`,
                                    })}${intl.formatMessage({
                                        id: `common.mandatory.*`,
                                    })}`}
                                    maxLength={255}
                                />
                            </Form.Item>
                        </Space>
                    </Col>

                    <Col span={14}>
                        <Row justify="end">
                            <Space size="small">
                            <Button onClick={debounce(confirmCancel, 500)} >
                                    {intl.formatMessage({ id: 'sprint-management.cancel' })}
                                </Button>
                                <Button onClick={() => {
                                    setIsDraft(true)
                                }}
                                    className="success-btn"
                                    htmlType="submit"
                                >
                                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'sprint-management.save' : 'sprint-management.update' })}
                                </Button>
                               
                            </Space>
                        </Row>
                    </Col>
                </Row>
            </div>
            <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 280}>
                <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                    <Row align="middle">
                        <Col span={2}>
                            <Text>
                                {intl.formatMessage({
                                    id: 'sprint-management.project',
                                })}
                            </Text>
                        </Col>
                        <Col span={4}>
                            {projectCode}
                        </Col>
                    </Row>
                    {
                        screenMode === SCREEN_MODE.EDIT ? <Row>
                            <Col span={12} style={{marginLeft: '-8px'}}>
                                <FormGroup inline label={intl.formatMessage({ id: 'sprint-management.code' })}>
                                    <Form.Item rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                        <Input value={state.detail?.code} disabled maxLength={255} />
                                    </Form.Item>
                                </FormGroup>
                            </Col>
                        </Row> : <></>
                    }
                    <FormGroup className="rq-fg-comment" label={intl.formatMessage({ id: 'sprint-management.goals' })}>
                        <Form.Item name="goals">
                            <CkeditorMention
                                ref={getCkeditorData}
                                data={goals || ''}
                            />
                        </Form.Item>
                    </FormGroup>
                    <FormGroup className="rq-fg-comment" label={intl.formatMessage({ id: 'sprint-management.scope' })}>
                        <Form.Item name="scope">
                            <CkeditorMention
                                ref={getCkeditorDataScope}
                                data={scope || ''}
                            />
                        </Form.Item>
                    </FormGroup>
                    <Row gutter={30} justify='space-between'>
                        <Col span={6}>
                            <FormGroup>
                                <Row>
                                    <Col span={10} className="sprint">
                                        {intl.formatMessage({
                                            id: 'sprint-management.duration',
                                        })}
                                        <Text type="danger">
                                            {intl.formatMessage({
                                                id: `common.mandatory.*`,
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={14}>
                                        <Form.Item
                                            name='duration'
                                            rules={[
                                                {
                                                    required: true,
                                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                                },
                                            ]}
                                        >
                                            <Select onChange={handleDuration}>
                                                {DURATION_SPRINT.map((item) => (
                                                    <Option key={item.id} value={item.id}>{item.values}</Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </FormGroup>
                        </Col>
                        <Col span={8}>
                            <FormGroup >
                                <Row>
                                    <Col span={10} className="sprint">
                                        {intl.formatMessage({
                                            id: 'sprint-management.start-date',
                                        })}
                                        <Text type="danger">
                                            {intl.formatMessage({
                                                id: `common.mandatory.*`,
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={14}>
                                        <Form.Item name='startDate' rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                            <DatePicker onChange={handleDueDateChange} className='full-width' format={DATE_FORMAT} />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </FormGroup>
                        </Col>
                        <Col span={8}>
                            <FormGroup>
                                <Row>
                                    <Col span={10} className="sprint">
                                        {intl.formatMessage({
                                            id: 'sprint-management.end-date',
                                        })}
                                        <Text type="danger">
                                            {intl.formatMessage({
                                                id: `common.mandatory.*`,
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={14}>
                                        <Form.Item name='endDate' rules={[
                                            { required: true, message: intl.formatMessage({ id: 'IEM_1' }) },
                                        ]}>
                                            <DatePicker disabled={disableEndDate} className='full-width' format={DATE_FORMAT} disabledDate={disabledCompleteDate} />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </FormGroup>
                        </Col>
                    </Row>
                    {screenMode === SCREEN_MODE.EDIT ?
                        <Row>
                            <Col span={4}>
                                <Text>
                                    {intl.formatMessage({ id: 'sprint-management.story' })}
                                </Text>
                            </Col>
                            <Col span={20}>
                                {
                                    // story?.map((e) => <p key={e?.userStoryId}>{e?.userStoryCode} - {e?.userStoryName}</p>)
                                    story?.map((item) => (
                                        <Link key={item.userStoryId} to={`${PROJECT_PREFIX}${projectCode}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}${item.userStoryId}`}>
                                            <div>
                                                {item.userStoryCode} {item.userStoryName}
                                            </div>
                                        </Link>
                                    ))
                                }
                            </Col>
                        </Row>
                        : <></>
                    }
                </Space>
            </Scrollbars>
        </Form>
    </CustomModal>
}
const SprintManagementForm = ({ sprintId, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: SprintManagementFormProps) => {
    const dispatch = useDispatch();
    const [isModalVisible, setIsModalVisible] = useState<any>(null)

    useEffect(() => {
        if (isModalVisible !== null) {
            dispatch(setModalVisible(isModalVisible))
        }
    }, [isModalVisible])

    return <>
        {
            buttonType === BUTTON_TYPE.TEXT ?
                <Button
                    ghost={screenMode === SCREEN_MODE.CREATE}
                    type='primary'
                    className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
                    onClick={() => setIsModalVisible(true)}
                    icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
                >
                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'sprint-management.create' : 'sprint-management.update' })}
                </Button> :
                buttonType === BUTTON_TYPE.ICON ?
                    <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
                    <></>
        }
        {isModalVisible === true ? <SprintManagementFormModal sprintId={sprintId} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}    </>
}
export default SprintManagementForm
