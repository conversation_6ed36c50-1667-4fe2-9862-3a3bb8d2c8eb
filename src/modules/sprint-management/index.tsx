import { PlusOutlined } from '@ant-design/icons'
import CustomSvgIcons from '../../helper/component/custom-icons'

import { Button, Space, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import {
    API_URLS,
    APP_ROLES,
    APP_ROUTES,
    BUTTON_TYPE,
    PROJECT_PREFIX,
    REQ_ARTEFACT_TYPE_ID,
    SCREEN_MODE,
    SEARCH_TYPE
} from '../../constants'
import LavTable from '../../helper/component/lav-table'
import {
    extractProjectCode,
    getColumnSearchProps,
    getProjectName,
    hasRole
} from '../../helper/share'
import SprintManagementForm from './form'
import SprintManagementFormPage from './form/form'

const { Title, Text } = Typography

const SprintManagement = () => {
    const [screenMode, setScreenMode] = useState<any>(0)

    useEffect(() => {       
      if(screenMode === 0)
        document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'sprint-management.title'}); 
    }, [screenMode])
  
    const [id, setId] = useState<number>(0)
    const columns = [
        {
            title: intl.formatMessage({ id: 'sprint-management.code' }),
            dataIndex: 'code',
            width: '85px',
            sorter: true,
            sortOrder: 'descend',
            ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
            render: (text: string, record: any) => {
                const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SPRINT_MANAGEMENT_DETAIL}` + record.id
                return <Link to={href}>{text}</Link>
            },
        },
        {
            title: intl.formatMessage({ id: 'sprint-management.name' }),
            dataIndex: 'name',
            sorter: true,
            width: '47%',
            ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
            render: (text: string) => {
                return <Text className="description">{text}</Text>
            },
        },
        {
            title: intl.formatMessage({ id: 'sprint-management.goals' }),
            dataIndex: 'goals',
            width: '47%',
            ...getColumnSearchProps('goals', SEARCH_TYPE.TEXT),
            render: (text) => (
                <div
                    className="tableDangerous"
                    dangerouslySetInnerHTML={{ __html: text }}
                ></div>
            ),
        },

    ]
    const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
        return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
        <Button ghost={true}
          type='primary'
          className='lav-btn-create'
          icon={<PlusOutlined />}
          onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'sprint-management.create' })}
        </Button> : <></>
        // return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <SprintManagementForm onFinish={() => handleDataChange()} /> : <></>
    }
    const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
        return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
        // return <SprintManagementForm onFinish={() => handleDataChange()} buttonType={BUTTON_TYPE.ICON} sprintId={record.id} screenMode={SCREEN_MODE.EDIT} />
    }

    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return children
    }
    return (
        <Space direction="vertical" size="middle" className="full-width p-20px">
             {screenMode === 0 ? <LavTable
                title="sprint-management.title"
                artefact_type="common.artefact.sprint-management"
                apiUrl={API_URLS.SPRINTS_MANAGEMENT}
                columns={columns}
                artefactType={REQ_ARTEFACT_TYPE_ID.SPRINT}
                createComponent={CreateComponent}
                updateComponent={UpdateComponent}
                deleteComponent={DeleteComponent}
            />: <></>
        }
        {
          screenMode === SCREEN_MODE.CREATE ? <SprintManagementFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(0)} onFinish={() => setScreenMode(0)} /> : <></>
        }
        {
          screenMode === SCREEN_MODE.EDIT ? <SprintManagementFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(0)} onFinish={() => setScreenMode(0)} id={id} /> : <></>
        }
        </Space>
    )
}

export default SprintManagement
