import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import SprintManagementForm from '../form'
import SprintManagementFormPage from '../form/form'
import { SprintManagementState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'
import intl from '../../../config/locale.config'

const SprintDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.Sprints) as SprintManagementState;
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.sprintId) {
      dispatch(getDetailRequest(props.match.params.sprintId))
    }
  }, [props])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SPRINT_MANAGEMENT}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.sprintId))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)


  const handleCreate = (items) => {
    if(isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SPRINT_MANAGEMENT_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
          {
        screenMode === SCREEN_MODE.VIEW ? 
        <>
      <Col span={5}>
        <LavLeftControl
          hideStatus={true}
          activeId={props.match.params.sprintId}
          apiUrl={API_URLS.REFERENCES_SPRINT}
          route={APP_ROUTES.SPRINT_MANAGEMENT_DETAIL}
          artefactType={REQ_ARTEFACT_TYPE_ID.SPRINT}
          title='sprint-management.title'
          reload={reload}
          reloadSuccess={() => setReload(false)}
          handleCreate={handleCreate}
        >
          {/* {
            (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <SprintManagementForm onFinish={() => {setReload(true); setIsCreate(true)}} /> : <></>
          } */}
            {
            (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) &&
            <Button ghost={true}
              type='primary'
              className='lav-btn-create'
              icon={<PlusOutlined />}
              onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'sprint-management.create' })}
            </Button> 
          }
        </LavLeftControl>
      </Col>
      <Col span={19}>
        <RightControl setScreenMode={()=> setScreenMode(SCREEN_MODE.EDIT)} onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} sprintId={props.match.params.sprintId} isModalShow={state?.isModalShow} />
      </Col>
     </> : <></>
}
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <SprintManagementFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <SprintManagementFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
              }} id={props.match.params.sprintId} />
          </Col> : <></>
      }
    </Row>
  )
}

export default SprintDetail
