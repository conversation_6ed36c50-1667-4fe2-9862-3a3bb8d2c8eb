import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Row, Space, Spin, Typography
} from 'antd'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch } from 'react-redux'
import { <PERSON>, useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, BUTTON_TYPE, DURATION_SPRINT, PROJECT_PREFIX, SCREEN_MODE } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavButtons from '../../../helper/component/lav-buttons'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, getProjectName, hasR<PERSON> } from '../../../helper/share'
import { deleteRequest } from '../action'
import SprintManagementForm from '../form'
import CloseButton from './../../../helper/component/commonButton/CloseButton/index'
import LavReferences from '../../../helper/component/lav-references'
import { useEffect } from 'react'

const { Title, Text } = Typography
interface RightControlProps {
    data: any | [],
    sprintId: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any

}
const RightControl = ({ data, sprintId, onChange, isLoading, isModalShow,setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const history = useHistory()
    const dispatch = useDispatch();

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    useEffect(() => {        
        if(data)
            document.title = data?.code + "-" + data?.name; 
    }, [data])

    const duration = DURATION_SPRINT.find(
        (duration: any) => duration.id === data?.duration
    )

    const closeSprintDetail = () => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SPRINT_MANAGEMENT}`
        history.push(href)
    }
    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code} - {data?.name}
                    </Title>
                </div>

                <Space size="small">
                    <LavButtons
                        url={`${API_URLS.SPRINTS_MANAGEMENT}/${data?.id}`}
                        reviewer={`${data?.reviewer}`}
                        customer={`${data?.customer}`}
                        artefact_type="common.artefact.sprint"
                        status={data?.status}
                        changePage={() => onChange()}>

                        {/* Update record */}

                        {(hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) ?
                             <Button
                             type='primary'
                             className='lav-btn-create'
                             onClick={() => {
                               setScreenMode()
                             }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                        }
                        {/*Close record */}
                        <CloseButton
                            content={intl.formatMessage({ id: 'CFD_3' })}
                            okCB={closeSprintDetail}
                            confirmButton={intl.formatMessage({ id: 'common.action.close' })}
                        />
                        {/* Delete record */}
                        {((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <DeleteButton
                            type={BUTTON_TYPE.TEXT}
                            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.sprint' }) })}
                            okCB={() => dispatch(deleteRequest(sprintId))}
                            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
                        }

                    </LavButtons>
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size='middle'>
                        <Space size="large">
                            <Text>
                                {intl.formatMessage({
                                    id: 'sprint-management.project',
                                })}
                            </Text>
                            {projectCode}
                        </Space>
                        <Space size="large">
                            <Text>
                                {intl.formatMessage({
                                    id: 'sprint-management.code',
                                })}
                            </Text>
                            <Text>
                                {data?.code}
                            </Text>
                        </Space>
                        <Card title={<Title level={5}>{intl.formatMessage({ id: 'view-sprint-details.sprint-info' })}</Title>} bordered={true}>
                            <Row gutter={[16, 4]} className='rq-card-infor'>
                                <Col span={3}>
                                    <Text type='secondary' >
                                        {intl.formatMessage({
                                            id: 'sprint-management.goals',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={20} className="sprint">
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.goals }}
                                    ></div>
                                </Col>
                                <Col span={3}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'sprint-management.scope',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={20} className="sprint">
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.scope }}
                                    ></div>
                                </Col>
                                <Col span={3} >
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'sprint-management.duration',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={20} className="sprint">
                                    {duration?.values}
                                </Col>
                                <Col span={3}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'sprint-management.start-date',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={20} className="sprint">
                                    {data?.formattedStartDate}
                                </Col>
                                <Col span={3}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'sprint-management.end-date',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={20} className="sprint">
                                    {data?.formattedEndDate}
                                </Col>
                                <Col span={3}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'sprint-management.story',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={20} className="sprint">
                                    {
                                        data?.userStorySprintResponses?.map((item) => (
                                            <Link key={item.userStoryId} to={`${PROJECT_PREFIX}${projectCode}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}${item.userStoryId}`}>
                                                <div>
                                                    {item.userStoryCode} {item.userStoryName}
                                                </div>
                                            </Link>
                                        ))
                                    }
                                </Col>
                            </Row>
                        </Card>
                        <LavReferences data={data} />
                        <LavAuditTrail data={data?.auditTrailDescription} />
                    </Space>
                </Scrollbars>
            </Spin>
        </Space >
    ) : <></>
}

export default RightControl
