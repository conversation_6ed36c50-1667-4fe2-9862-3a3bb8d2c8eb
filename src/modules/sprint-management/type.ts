export interface SprintManagementState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: SprintManagementDetail | null,
  selectedData?: SprintManagementDetail | null,
  isModalShow?: boolean
}

export interface SprintManagementDetail {
  id?: number | null,
  code: string,
  name: string,
  goals: string,
  scope: string,
  duration: number | null,
  startDate: string,
  endDate: string,
  userStorySprintResponses?: any [],
  projectId?: number
}

export const defaultState: SprintManagementState = {
  detail: {
    id: null,
    code: '',
    name: '',
    goals: '',
    scope: '',
    duration: null,
    startDate: '',
    endDate: '',
    userStorySprintResponses: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/SPRINT_MANAGEMENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/SPRINT_MANAGEMENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/SPRINT_MANAGEMENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/SPRINT_MANAGEMENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/SPRINT_MANAGEMENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/SPRINT_MANAGEMENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/SPRINT_MANAGEMENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/SPRINT_MANAGEMENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/SPRINT_MANAGEMENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/SPRINT_MANAGEMENT/GET_DETAIL_FAILED',


  GET_LIST_REQUEST = '@@MODULES/SPRINT_MANAGEMENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/SPRINT_MANAGEMENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/SPRINT_MANAGEMENT/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/SPRINT_MANAGEMENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/SPRINT_MANAGEMENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/SPRINT_MANAGEMENT/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/SPRINT_MANAGEMENT/SET_MODAL_VISIBLE',
}
