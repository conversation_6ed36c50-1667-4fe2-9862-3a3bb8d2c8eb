import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import EpicForm from '../form'
import EpicFormPage from '../form/form'
import { EpicManagementState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'
import intl from '../../../config/locale.config'

const EpicManagemrntDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.Epic) as EpicManagementState;
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.epicId) {
      dispatch(getDetailRequest(props.match.params.epicId))
    }
  }, [props])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.epicId))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if(isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
        {
        screenMode === SCREEN_MODE.VIEW ? 
        <> 
      <Col span={5}>
        <LavLeftControl
          hideStatus={true}
          activeId={props.match.params.epicId}
          apiUrl={API_URLS.REFERENCES_EPIC}
          route={APP_ROUTES.EPIC_MANAGEMENT_DETAIL}
          artefactType={REQ_ARTEFACT_TYPE_ID.EPIC}
          title='epic.list'
          reload={reload}
          reloadSuccess={() => setReload(false)}
          handleCreate={handleCreate}
        >
          {/* {
            ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <EpicForm onFinish={() => {setReload(true); setIsCreate(true)}} /> : <></>
          } */}
           {
            (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) &&
            <Button ghost={true}
              type='primary'
              className='lav-btn-create'
              icon={<PlusOutlined />}
              onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'epic.create' })}
            </Button> 
          }
        </LavLeftControl>
      </Col>
      <Col span={19}>
        <RightControl setScreenMode={()=> setScreenMode(SCREEN_MODE.EDIT)} onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} epicId={props.match.params.epicId} isModalShow={state?.isModalShow} />
      </Col>
      </> : <></>
}
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <EpicFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <EpicFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
              }} id={props.match.params.epicId} />
          </Col> : <></>
      }
    </Row>
  )
}

export default EpicManagemrntDetail
