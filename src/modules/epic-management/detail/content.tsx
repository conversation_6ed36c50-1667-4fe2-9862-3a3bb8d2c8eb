import LavReferences from '../../../helper/component/lav-references'
import {
    Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch } from 'react-redux'
import { Link, useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, BUTTON_TYPE, PROJECT_PREFIX, SCREEN_MODE, STATUS } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavButtons from '../../../helper/component/lav-buttons'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, getProjectName, hasRole } from '../../../helper/share'
import { deleteRequest } from '../action'
import EpicForm from '../form'
import CloseButton from './../../../helper/component/commonButton/CloseButton/index'
import { useEffect } from 'react'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    epicId: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any
}
const RightControl = ({ data, epicId, onChange, isLoading,isModalShow,setScreenMode}: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const history = useHistory()

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    const closeEpicDetail = () => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT}`
        history.push(href)
    }

    useEffect(() => {        
        if(data)
            document.title = data?.code + "-" + data?.name; 
    }, [data])

    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {`${data?.code || ''} - ${data?.name || ''}`}
                    </Title>
                </div>
                <LavButtons
                    url={`${API_URLS.EPIC_MANAGEMENT}/${data?.id}`}
                    reviewer={`${data?.reviewer}`}
                    artefact_type="common.artefact.object"
                    status={data?.status}
                    changePage={() => onChange()}>
                    {/* Update record */}

                    {/* {(hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && (data?.status !== STATUS.DELETE && data?.status !== STATUS.CANCELLED) ?
                        <EpicForm
                            screenMode={SCREEN_MODE.EDIT}
                            buttonType={BUTTON_TYPE.TEXT}
                            onFinish={() => onChange()}
                            epicId={parseInt(epicId)} /> : <></>
                    } */}
                    {((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE)&& (
              <Button
                type='primary'
                className='lav-btn-create'
                onClick={() => {
                  setScreenMode()
                }} >{intl.formatMessage({ id: 'common.action.update' })}</Button>
            )
                }
                    {/*Close record */}
                    <CloseButton
                        content={intl.formatMessage({ id: 'CFD_3' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.epic' }) })}
                        okCB={closeEpicDetail}
                        confirmButton={intl.formatMessage({ id: 'common.action.close' })}
                    />
                    {/* Delete record */}
                    {((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE) ? <DeleteButton
                        type={BUTTON_TYPE.TEXT}
                        content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.epic' }) })}
                        okCB={() => dispatch(deleteRequest(epicId))}
                        confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
                    }

                </LavButtons>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical">
                    <Card title={<Title level={5}>{intl.formatMessage({ id: 'epic.information' })}</Title>} bordered={true}>
                            <Row gutter={[16, 4]}>
                                <Col span={4}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'epic.colum.code',
                                        })}:
                                    </Text>
                                </Col>
                                <Col span={20}>
                                    {data?.code}
                                </Col>

                                <Col span={4}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'epic.colum.description',
                                        })}:
                                    </Text>
                                </Col>
                                <Col span={20}>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: data?.description,
                                        }}
                                    ></div>
                                </Col>

                                <Col span={4}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'epic.colum.product',
                                        })}:
                                    </Text>
                                </Col>
                                <Col span={20}>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: data?.product?.name,
                                        }}
                                    ></div>
                                </Col>

                                <Col span={4}>
                                    <Text type="secondary">
                                        {intl.formatMessage({
                                            id: 'epic.colum.user.story.include',
                                        })}:
                                    </Text>
                                </Col>
                                <Col span={20} className="include">
                                    {
                                        data?.userStoryEpicResponses?.map((item) => (
                                            <Link key={item.userStoryId} to={`${PROJECT_PREFIX}${projectCode}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}${item.userStoryId}`}>
                                                <div>
                                                    {item.userStoryCode} {item.userStoryName}
                                                </div>
                                            </Link>
                                        ))
                                    }
                                </Col>
                                <Col span={20}>
                                    {data?.include}
                                </Col>
                            </Row>
                        </Card>
                        <LavReferences data={data} />
                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrailDescription} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
