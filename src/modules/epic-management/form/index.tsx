import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button, Col, Form, Input, Modal, Row, Space
} from 'antd'
import Text from 'antd/lib/typography/Text'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROUTES, BUTTON_TYPE, MESSAGE_TYPES, PROJECT_PREFIX, SCREEN_MODE } from '../../../constants'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomModal from '../../../helper/component/custom-modal'
import FormGroup from '../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, extractProjectCode, getProjectName, getReferencesFromEditor } from '../../../helper/share'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { EpicManagementState } from '../type'

const { confirm } = Modal

interface EpicManagementFormProps {
  epicId?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface EpicFormModalProps {
  epicId?: number,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const EpicFormModal = ({ epicId, screenMode, onFinish, onDismiss }: EpicFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorData: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.Epic) as EpicManagementState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const modalConfirmConfig = useModalConfirmationConfig()
  const [description, setDescription] = useState('');
  const [story, setStory] = useState([]) as any
  const projectCode = extractProjectCode()
  const projectName = getProjectName(projectCode)

  // Destroy
  useEffect(() => {
    form.setFieldsValue({
      assignee: currentUserName()
    })
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (epicId && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(epicId))
    }
  }, [screenMode, epicId])

  useEffect(() => {
    if (epicId && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        name: state.detail.name,
      })
      setStory(state.detail?.userStoryEpicResponses)
      setDescription(state.detail.description);
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm();
      } else {
        onDismiss();
      }
      setIsDraft(null);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce((values: any, st?: string) => {
    let mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data);
    const requestData: any = {
      id: epicId || null,
      name: values.name,
      description: getCkeditorData.current?.props?.data,
      status: isDraft,
      type: values.type,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.epic' }) }
        ),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(null);
    setDescription('')
    form.resetFields([
      'remarks',
    ])
    form.setFieldsValue({
      assignee: currentUserName()
    })
  }

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    // size="smalls"
    visible={true}
    footer={null}
  >
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={12}>
            <Space size="large">
              <Form.Item
                name="name"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <Input
                  size="large"
                  className="modal-create-name-input-field"
                  placeholder={`${intl.formatMessage({
                    id: `epic.create.name`,
                  })}${intl.formatMessage({
                    id: `common.mandatory.*`,
                  })}`}
                  maxLength={255}
                />
              </Form.Item>
              {/* {screenMode === SCREEN_MODE.EDIT ? (
                renderCommonStatusBadge(state.detail?.status)
              ) : (
                <></>
              )} */}
            </Space>
          </Col>

          <Col span={12}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >

                </Form.Item> : <></>}
                <Button onClick={confirmCancel}>
                  {intl.formatMessage({ id: 'common.action.cancel' })}
                </Button>
                <Form.Item style={{ marginBottom: '0px' }}>
                  <Button
                    onClick={() => setIsDraft(true)}
                    className="success-btn"
                    htmlType="submit"
                  >
                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save' : 'common.action.update' })}
                  </Button>
                </Form.Item>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <FormGroup inline className="rq-fg-comment" label={
        <Text>
          {intl.formatMessage({ id: 'epic.description' })}
        </Text>}>

        <Form.Item
          name="description"
          labelAlign="left"
          wrapperCol={{ span: 24 }}
          className="height-default"
        >
          <CkeditorMention
            ref={getCkeditorData}
            data={description || ''}
          />
        </Form.Item>
      </FormGroup>
      {screenMode === SCREEN_MODE.EDIT ?
        <Row>
          <Col span={4}>
            <Text>
              {intl.formatMessage({ id: 'sprint-management.story' })}
            </Text>
          </Col>
          <Col span={20}>
            {
              // story?.map((e) => <p key={e?.userStoryId}>{e?.userStoryCode} - {e?.userStoryName}</p>)
              story?.map((item) => (
                <Link key={item.userStoryId} to={`${PROJECT_PREFIX}${projectCode}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}${item.userStoryId}`}>
                  <div>
                    {item.userStoryCode} {item.userStoryName}
                  </div>
                </Link>
              ))
            }
          </Col>
        </Row>
        : <></>
      }
    </Form>
  </CustomModal>
}
const EpicForm = ({ epicId, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: EpicManagementFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return (
    <>
      {
        buttonType === BUTTON_TYPE.TEXT ?
          <Button
            ghost={screenMode === SCREEN_MODE.CREATE}
            type='primary'
            className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
            onClick={() => setIsModalVisible(true)}
            icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
          >
            {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'epic.create' : 'epic.update' })}
          </Button> :
          buttonType === BUTTON_TYPE.ICON ?
            <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
            <></>
      }
      {isModalVisible === true ? <EpicFormModal epicId={epicId} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
    </>
  )
}
export default EpicForm
