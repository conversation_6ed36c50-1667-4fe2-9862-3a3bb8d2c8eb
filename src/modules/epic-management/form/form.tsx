

import LavPageHeader from '../../../helper/component/lav-breadcumb'
import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button, Col, Form, Input, Modal, Row, Select, Space, Spin
} from 'antd'
import Text from 'antd/lib/typography/Text'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useHistory, useLocation } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROUTES, BUTTON_TYPE, MESSAGE_TYPES, PROJECT_PREFIX, SCREEN_MODE } from '../../../constants'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomModal from '../../../helper/component/custom-modal'
import FormGroup from '../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, extractProjectCode, getProjectName, getReferencesFromEditor } from '../../../helper/share'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { EpicManagementState } from '../type'
import TriggerComment from '@/modules/_shared/comment/trigger-comment'
import AppCommonService from '../../../services/app.service'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { confirm } = Modal
const { Option } = Select
interface EpicManagementFormProps {
  id?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface EpicFormModalProps {
  id?: number,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const EpicFormPage = ({ id, screenMode, onFinish, onDismiss }: EpicFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorData: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.Epic) as EpicManagementState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const modalConfirmConfig = useModalConfirmationConfig()
  const [description, setDescription] = useState('');
  const [story, setStory] = useState([]) as any
  const projectCode = extractProjectCode()
  const projectName = getProjectName(projectCode)
  const history = useHistory()
  const [products, setProducts] = useState<any[]>([]);

  useBeforeUnload();
  // Destroy
  useEffect(() => {
    form.setFieldsValue({
      assignee: currentUserName()
    });
    AppCommonService.getReferenceProduct().then(res => setProducts(res.data)).catch((err) => setProducts([]));
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
    document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'epic.title-create' : 'epic.title-update' });
  }, [screenMode, id])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        name: state.detail.name,
        productId: state.detail?.product?.id || null
      })
      setStory(state.detail?.userStoryEpicResponses)
      setDescription(state.detail.description);
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm();
      } else {
        onDismiss();
      }
      setIsDraft(null);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce((values: any, st?: string) => {
    let mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data);
    const requestData: any = {
      id: id || null,
      name: values.name,
      description: getCkeditorData.current?.props?.data,
      productId: values.productId,
      status: isDraft,
      type: values.type,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.epic' }) }
        ),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        /* const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT}`
        history.push(href) */
        onDismiss()
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(null);
    setDescription('')
    form.resetFields([
      'remarks',
    ])
    form.setFieldsValue({
      assignee: currentUserName()
    })
  }

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <LavPageHeader
          showBreadcumb={true}
          title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'epic.title-create' : 'epic.title-update' })}
        >
          <Space size="small">
            {screenMode === SCREEN_MODE.CREATE ? <Form.Item
              style={{ marginBottom: '0px' }}
              valuePropName="checked"
              name="createMore"
              wrapperCol={{ span: 24 }}
            >

            </Form.Item> : <></>}
            <Button onClick={confirmCancel}>
              {intl.formatMessage({ id: 'common.action.cancel' })}
            </Button>
            <Form.Item style={{ marginBottom: '0px' }}>
              <Button
                onClick={() => setIsDraft(true)}
                className="success-btn"
                htmlType="submit"
              >
                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save' : 'common.action.update' })}
              </Button>
            </Form.Item>
          </Space>
        </LavPageHeader>
      </div>

      <FormGroup
        inline
        required
        label={`${intl.formatMessage({
          id: `common.label.name`,
        })}`}
        labelSpan={3}
        controlSpan={21}
      >
        <Form.Item
          name="name"
          rules={[
            {
              required: true,
              message: intl.formatMessage({ id: 'IEM_1' }),
            },
            { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
            {
              validator: async (rule, value) => {
                if (value && value.trim().length === 0) {
                  throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                }
              },
            },
          ]}
        >
          <Input
            maxLength={255}
          />
        </Form.Item>
      </FormGroup>

      <FormGroup inline className="rq-fg-comment" label={`${intl.formatMessage({ id: 'epic.description' })}`}
        labelSpan={3}
        controlSpan={21}
      >

        <Form.Item
          name="description"
          labelAlign="left"
          wrapperCol={{ span: 24 }}
        >
          <CkeditorMention
            ref={getCkeditorData}
            data={description || ''}
          />
        </Form.Item>
      </FormGroup>

      <FormGroup required inline label={`${intl.formatMessage({ id: 'epic.product' })}`}
        labelSpan={3}
        controlSpan={21}>
        <Form.Item name="productId" rules={[
          {
            required: true,
            message: intl.formatMessage({ id: 'IEM_1' }),
          }
        ]}>
          <Select
            filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            showSearch
            allowClear
            className='full-width'>
            {
              products.map((products, idx) => (
                <Option key={idx} value={products.id}>{products.name}</Option>
              ))
            }
          </Select>
        </Form.Item>
      </FormGroup>

      {screenMode === SCREEN_MODE.EDIT ?
        <Row>
          <Col span={4}>
            <Text>
              {intl.formatMessage({ id: 'sprint-management.story' })}
            </Text>
          </Col>
          <Col span={20}>
            {
              // story?.map((e) => <p key={e?.userStoryId}>{e?.userStoryCode} - {e?.userStoryName}</p>)
              story?.map((item) => (
                <Link key={item.userStoryId} to={`${PROJECT_PREFIX}${projectCode}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}${item.userStoryId}`}>
                  <div>
                    {item.userStoryCode} {item.userStoryName}
                  </div>
                </Link>
              ))
            }
          </Col>
        </Row>
        : <></>
      }
    </Form>
  </Spin>
}
// const EpicForm = ({ id, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: EpicManagementFormProps) => {
//   const dispatch = useDispatch();
//   const [isModalVisible, setIsModalVisible] = useState<any>(null)

//   useEffect(() => {
//     if (isModalVisible !== null) {
//       dispatch(setModalVisible(isModalVisible))
//     }
//   }, [isModalVisible])

//   return (
//     <>
//       {
//         buttonType === BUTTON_TYPE.TEXT ?
//           <Button
//             ghost={screenMode === SCREEN_MODE.CREATE}
//             type='primary'
//             className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
//             onClick={() => setIsModalVisible(true)}
//             icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
//           >
//             {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'epic.create' : 'epic.update' })}
//           </Button> :
//           buttonType === BUTTON_TYPE.ICON ?
//             <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
//             <></>
//       }
//       {isModalVisible === true ? <EpicFormModal id={id} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
//     </>
//   )
// }
export default EpicFormPage
