export interface EpicManagementState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: EpicManagementDetail | null,
  selectedData?: EpicManagementDetail | null,
  isModalShow?:boolean
}
export interface EpicManagementDetail {
  id?: number | null,
  name: string,
  description: string,
  product: any,
  userStoryEpicResponses?: any [],
}

export const defaultState: EpicManagementState = {
  detail: {
    id: null,
    name: '',
    description: '',
    product: {},
    userStoryEpicResponses: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
}


export enum ActionEnum {
  RESET_STATE = '@@MODULES/EPIC_MANAGEMENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/EPIC_MANAGEMENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/EPIC_MANAGEMENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/EPIC_MANAGEMENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/EPIC_MANAGEMENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/EPIC_MANAGEMENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/EPIC_MANAGEMENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/EPIC_MANAGEMENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/EPIC_MANAGEMENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/EPIC_MANAGEMENT/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/EPIC_MANAGEMENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/EPIC_MANAGEMENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/EPIC_MANAGEMENT/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/EPIC_MANAGEMENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/EPIC_MANAGEMENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/EPIC_MANAGEMENT/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/EPIC_MANAGEMENT/SET_MODAL_VISIBLE',
}
