import CustomSvgIcons from '../../helper/component/custom-icons'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import React, { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_COMMON } from '../../constants'
import LavTable from '../../helper/component/lav-table'
import { currentUserName, extractProjectCode, getColumnSearchProps, hasRole } from '../../helper/share'
import EpicForm from '../epic-management/form'
import EpicFormPage from './form/form'

const EpicManagement = () => {
  const [screenMode, setScreenMode] = useState<any>(0)
  const [id, setId] = useState<number>(0)

  useEffect(() => {       
    if(screenMode === 0)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'epic-management.list'}); 
  }, [screenMode])

  const columns = [
    {
      title: intl.formatMessage({ id: 'common.action.code' }),
      dataIndex: 'code',
      width: '85px',
      editable: true,
      sortOrder: 'descend',
      sorter: true,
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'epic.column.name' }),
      dataIndex: 'name',
      width: '35%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: intl.formatMessage({ id: 'epic.column.description' }),
      dataIndex: 'description',
      width: '60%',
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      render: text => <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return  (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
    <Button ghost={true}
      type='primary'
      className='lav-btn-create'
      icon={<PlusOutlined />}
      onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'epic.create' })}
    </Button> : <></>
    // return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <EpicForm onFinish={() => handleDataChange()} /> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))
    ?
    <Button ghost={screenMode === SCREEN_MODE.EDIT}
      style={{ border: 'none' }}
      icon={<CustomSvgIcons name="EditCustomIcon" />}
      onClick={() => {
        setScreenMode(SCREEN_MODE.EDIT)
        setId(record.id)
      }} /> : <></>
    // return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && record.status !== STATUS.CANCELLED && record.status !== STATUS.DELETE) ?
    //   <EpicForm onFinish={() => handleDataChange()} screenMode={SCREEN_MODE.EDIT} epicId={record.id} buttonType={BUTTON_TYPE.ICON} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (record.status !== STATUS.DELETE && (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? children : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
       {screenMode === 0 ? <LavTable
        title="epic-management.list"
        artefact_type="common.artefact.epic"
        apiUrl={API_URLS.EPIC_MANAGEMENT}
        columns={columns}
        artefactType={REQ_ARTEFACT_TYPE_ID.EPIC}
        createComponent={CreateComponent}
        updateComponent={UpdateComponent}
        deleteComponent={DeleteComponent}
      /> : <></>
       }
        {
        screenMode === SCREEN_MODE.CREATE ? <EpicFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(0)} onFinish={() => setScreenMode(0)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <EpicFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(0)} onFinish={() => setScreenMode(0)} id={id} /> : <></>
      }
    </Space>
  )
}

export default EpicManagement
