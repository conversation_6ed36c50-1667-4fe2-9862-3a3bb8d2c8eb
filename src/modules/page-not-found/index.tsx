import { APP_ROUTES } from '../../constants';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { Link } from 'react-router-dom';
import NotFoundImage from '../../assets/images/404.jpg';
import { useEffect } from 'react';
const NotFoundPage = () => {
    useEffect(() => {
        document.title = "Page not found";
    }, [])
    return (
        <div className="rq-not-found" style={{ height: 'calc(100vh - 45px)', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
            <div>
                <img src={NotFoundImage} alt="" height={150} />
            </div>
            <h2 style={{ fontSize: '36px', marginBottom: 0 }}>Page Not Found</h2>
            <p style={{ marginBottom: 30 }}>The requested page was not found</p>
            <Link to={APP_ROUTES.PROJECTS}>
                <Button type='primary' size='large'>
                    <ArrowLeftOutlined />
                    <span>Back to project list</span>
                </Button>
            </Link>
        </div>
    )
}
export default NotFoundPage