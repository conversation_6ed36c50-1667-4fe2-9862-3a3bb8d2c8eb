import { REQ_ARTEFACT_TYPE_ID } from '@/constants';
import { createReducer } from '@reduxjs/toolkit';
import {
  getDetailFailed,
  getDetailRequest,
  getDetailSuccess,
  getListRefsFailed, getListRefsRequest,
  getListRefsSuccess, getMissingFailed, getMissingRequest, getMissingSuccess, getWarningMessageFailed, getWarningMessageRequest, getWarningMessageSuccess, recommendFailed, recommendRequest,
  recommendSuccess, resetState, updateFailed, updateRequest, updateSuccess
} from './action';
import { defaultState, RecommendCommonComponentState } from './type';

const initState: RecommendCommonComponentState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
        });
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.details = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.details = null
        state.selectedData = null
      })

      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })

      .addCase(recommendRequest, (state, action?) => {
        state.isLoading = true;
        state.recommendSuccess = false;
      })
      .addCase(recommendSuccess, (state, action) => {
        state.isLoading = false;
        state.recommendSuccess = true;
      })
      .addCase(recommendFailed, (state, action) => {
        state.isLoading = false;
        state.recommendSuccess = false;
      })

      .addCase(getListRefsRequest, (state, action?) => {
        state.isLoadingRefs = true;
      })
      .addCase(getListRefsSuccess, (state, action) => {
        state.isLoadingRefs = false
        state.listRefs = action.payload
      })
      .addCase(getListRefsFailed, (state, action) => {
        state.isLoadingRefs = false
        state.listRefs = []
      })

      .addCase(getMissingRequest, (state, action?) => {
        state.isLoadingMissing = true;
      })
      .addCase(getMissingSuccess, (state, action) => {
        state.isLoadingMissing = false
        state.missing = action.payload
      })
      .addCase(getMissingFailed, (state, action) => {
        state.isLoadingMissing = false
        state.missing = null
      })
      .addCase(getWarningMessageRequest, (state, action?) => {
        state.isLoadingWarningMessage = true;
      })
      .addCase(getWarningMessageSuccess, (state, action) => {
        state.isLoadingWarningMessage = false
        state.warningMessage = action.payload
      })
      .addCase(getWarningMessageFailed, (state, action) => {
        state.isLoadingWarningMessage = false
        state.warningMessage = null
      })
  )
})

export default reducer
export { initState as RecommendCommonComponentState };

