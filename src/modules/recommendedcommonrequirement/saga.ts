import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import { getDetailFailed, getDetailRequest, getDetailSuccess, getListRefsFailed, getListRefsRequest, getListRefsSuccess, getMissingFailed, getMissingRequest, getMissingSuccess, getWarningMessageFailed, getWarningMessageRequest, getWarningMessageSuccess, recommendFailed, recommendRequest, recommendSuccess, updateFailed, updateRequest, updateSuccess } from './action'

function* handleCreate(action: Action) {
  if (recommendRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.RECOMMEND_COMMON_COMPONENT, request as any)
      yield put(recommendSuccess(null));
    } catch (err: any) {
      yield put(recommendFailed(null));
      ShowAppMessage(err, null, 'common.artefact.recommend-common-component', 'recommend_common_component.column.name')
    }
  }
}

function* handleGetListRefs(action: Action) {
  if (getListRefsRequest.match(action)) {
    try {
      const url = API_URLS.RECOMMEND_COMMON_REFERENCES_ARTEFACT + '?search=' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getListRefsSuccess(res.data));
    } catch (err) {
      yield put(getListRefsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListMissing(action: Action) {
  if (getMissingRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'POST', API_URLS.REFERENCES_ARTEFACT_MISSING, action.payload)
      yield put(getMissingSuccess(res.data));
    } catch (err) {
      yield put(getMissingFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}


function* handleGetListWarningMessage(action: Action) {
  if (getWarningMessageRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'POST', API_URLS.REFERENCES_ARTEFACT_MISSING, action.payload)
      yield put(getWarningMessageSuccess(res.data));
    } catch (err) {
      yield put(getWarningMessageFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* getDetailFlow(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const params = action.payload
      const url = API_URLS.RECOMMENDED_COMMON_REQ + `/${params}`
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data))
    } catch (err) {
      yield put(getDetailFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.RECOMMENDED_COMMON_REQ + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.recommended-common-requiremnt')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.recommended-common-requiremnt')
    }
  }
}
function* watchFetchRequest() {
  yield takeLatest(recommendRequest.type, handleCreate)
  yield takeLatest(getListRefsRequest.type, handleGetListRefs)
  yield takeLatest(getMissingRequest.type, handleGetListMissing)
  yield takeLatest(getWarningMessageRequest.type, handleGetListWarningMessage)
  yield takeLatest(getDetailRequest.type, getDetailFlow)
  yield takeLatest(updateRequest.type, handleUpdate)
}

export default function* RecommendCommonComponentSaga() {
  yield all([fork(watchFetchRequest)])
}
