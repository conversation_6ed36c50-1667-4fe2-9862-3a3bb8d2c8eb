import AppCommonService from '../../services/app.service'
import { Space } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import LavTable from '../../helper/component/lav-table'
import { extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import RecommendForm from './form'
// import RecommendForm from './form'

const Recommended = () => {
  const [columns, setColumns] = useState<any>(null)
  useEffect(() => {
    document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'recommended-common-requirement.header.title'}); 

    AppCommonService.getData3List(API_URLS.REFERENCES_SCREENS, API_URLS.REFERENCES_OBJECTS, API_URLS.REFERENCES_FUNCTIONS).then((res) => {
      const lstScreens = res[0].map(e => { return { value: e.id, text: e.name } })
      const lstObjects = res[1].map(e => { return { value: e.id, text: e.name } })
      const lstUseCases = res[2].map(e => { return { value: e.id, text: e.name } })

      setColumns(configColumns(lstObjects, lstScreens, lstUseCases));
    }).catch(err => {
      setColumns(configColumns([], [], []))
    })
  }, [])
  const configColumns = (objs, scrs, ucs) => [
    {
      title: intl.formatMessage({ id: 'common.table.header.code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.RECOMMENED_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.component-name' }),
      dataIndex: 'name',
      width: '15%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.description' }),
      dataIndex: 'description',
      width: '45%',
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      render: (text) => {
        return text ? <div
          className="tableDangerous"
          dangerouslySetInnerHTML={{
            __html: text,
          }}
        ></div> : <></>
      }
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.object' }),
      dataIndex: 'objects',
      width: '10%',
      ...getColumnDropdownFilterProps(objs, 'ObjectId', true),
      render: (listObject: any, record) => {
        return (
          <>
            {listObject?.map((e: any, index) => (
              <Link key={e.code} to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_DETAIL}${e.id}`}>
                {index !== 0 ? <br /> : ``} {e.name}
              </Link>
            ))}
          </>
        )
      },
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.usecase' }),
      dataIndex: 'useCases',
      width: '10%',
      ...getColumnDropdownFilterProps(ucs, 'UseCaseId', true),
      render: (listUseCases: any, record) => {
        return (
          <>
            {listUseCases?.map((e: any, index) => (
              <Link key={e.code} to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DETAIL}${e.id}`}>
                {index !== 0 ? <br /> : ``} {e.name}
              </Link>
            ))}
          </>
        )
      },
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.screen' }),
      dataIndex: 'screens',
      width: '10%',
      ...getColumnDropdownFilterProps(scrs, 'ScreenId', true),
      render: (listScreens: any, record) => {
        return (
          <>
            {listScreens?.map((e: any, index) => (
              <Link key={e.code} to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SCREEN_DETAIL}${e.id}`}>
                {index !== 0 ? <br /> : ``} {e.name}
              </Link>
            ))}
          </>
        )
      },
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.status' }),
      dataIndex: 'status',
      width: '80px',
      ...getColumnDropdownFilterProps([...STATUS_FILTER, {
        text: 'Rejected',
        value: STATUS.REJECTED,
      }], 'Statuses'),

      sorter: true,
      render: (record) => renderStatusBadge(record),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return <RecommendForm onFinish={() => handleDataChange()} screenMode={SCREEN_MODE.CREATE} />
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (record.status === STATUS.CANCELLED) ? <RecommendForm onFinish={() => handleDataChange()} screenMode={SCREEN_MODE.EDIT} buttonType={BUTTON_TYPE.ICON} id={record.id}></RecommendForm> : <></>
  }
  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return <></>
  }


  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {columns ?
        <LavTable
          title="recommended-common-requirement.header.title"
          artefact_type="common.artefact.recommended-common-requiremnt"
          apiUrl={API_URLS.RECOMMENDED_COMMON_REQ}
          columns={columns}
          isHasAction={false}
          artefactType={REQ_ARTEFACT_TYPE_ID.RECOMMENDED_COMMON_REQ}
          updateComponent={UpdateComponent}
          createComponent={CreateComponent}
          deleteComponent={DeleteComponent}
        />
        : <></>
      }
    </Space>
  )
}

export default Recommended
