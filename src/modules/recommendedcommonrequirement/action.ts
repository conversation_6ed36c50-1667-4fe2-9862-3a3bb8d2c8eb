import { createAction } from '@reduxjs/toolkit';
import { ActionEnum } from './type';

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<any>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<any>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<any>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<any>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const recommendRequest = createAction<any>(ActionEnum.RECOMMEND_REQUEST);
export const recommendSuccess = createAction<any>(ActionEnum.RECOMMEND_SUCCESS);
export const recommendFailed = createAction<any>(ActionEnum.RECOMMEND_FAILED);

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST);
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS);
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED);

export const deleteRequest = createAction<any>(ActionEnum.DELETE_REQUEST);
export const deleteSuccess = createAction<any>(ActionEnum.DELETE_SUCCESS);
export const deleteFailed = createAction<any>(ActionEnum.DELETE_FAILED);

export const getListRefsRequest = createAction<any>(ActionEnum.GET_LIST_REFS_REQUEST);
export const getListRefsSuccess = createAction<any>(ActionEnum.GET_LIST_REFS_SUCCESS);
export const getListRefsFailed = createAction<any>(ActionEnum.GET_LIST_REFS_FAILED);

export const getMissingRequest = createAction<any>(ActionEnum.GET_MISSTING_REQUEST);
export const getMissingSuccess = createAction<any>(ActionEnum.GET_MISSTING_SUCCESS);
export const getMissingFailed = createAction<any>(ActionEnum.GET_MISSTING_FAILED);

export const getWarningMessageRequest = createAction<any>(ActionEnum.GET_WARNING_MESSAGE_REQUEST);
export const getWarningMessageSuccess = createAction<any>(ActionEnum.GET_WARNING_MESSAGE_SUCCESS);
export const getWarningMessageFailed = createAction<any>(ActionEnum.GET_WARNING_MESSAGE_FAILED);

export const setModalVisible = createAction<any>(ActionEnum.SET_MODAL_VISIBLE)