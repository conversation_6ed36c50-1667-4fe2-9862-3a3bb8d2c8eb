import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, Card, Col, Divider, Row, Space, Spin, Table, Typography
} from 'antd'
import { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROUTES, COMPONENT_SCOPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID } from '../../../constants'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, getProjectName, renderStatusBadge } from '../../../helper/share'

const { Title, Text } = Typography
interface RightControlProps {
  data: any | [],
  recommendId: string,
  onChange: () => void,
  isLoading: boolean,
  isModalShow?: boolean
}
const RightControl = ({ data, recommendId, onChange, isLoading, isModalShow }: RightControlProps) => {
  const { height: windowHeight } = useWindowDimensions()
  const [dataSource, setDataSource] = useState<any>([])
  const projectCode = extractProjectCode();
  const projectName = getProjectName(projectCode);

  //#region COMMENT INIT
  useEffect(() => {
    if(data)
      document.title = data?.code + "-" + data?.name;
    
    const obj = initType(data?.objects, REQ_ARTEFACT_TYPE_ID.OBJECT)
    const uc = initType(data?.useCases, REQ_ARTEFACT_TYPE_ID.USECASE)
    const sc = initType(data?.screens, REQ_ARTEFACT_TYPE_ID.SCREEN)
    const email = initType(data?.emailTemplates, REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE)
    const br = initType(data?.commonBusinessRules, REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE)
    const msg = initType(data?.messages, REQ_ARTEFACT_TYPE_ID.MESSAGE)
    const nf = initType(data?.nonFunctionalRequirements, REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT)
    const wf = initType(data?.workflows, REQ_ARTEFACT_TYPE_ID.WORKFLOW)

    const dataS = [
      ...obj,
      ...uc,
      ...sc,
      ...email,
      ...br,
      ...msg,
      ...nf,
      ...wf
    ]
    setDataSource(dataS)
  }, [data])

  const initType = (data, type) => {
    return data ? data?.map((item) => ({
      ...item,
      type: type,
    })) : [];
  }
  //#endregion COMMENT INIT
  const columns = [
    {
      title: intl.formatMessage({ id: 'common.table.header.code' }),
      dataIndex: 'code',
      width: '85px',
      render: (text: string, record: any) => {
        if (record?.type == REQ_ARTEFACT_TYPE_ID.OBJECT) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
        if (record?.type == REQ_ARTEFACT_TYPE_ID.USECASE) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
        if (record?.type == REQ_ARTEFACT_TYPE_ID.SCREEN) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SCREEN_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
        if (record?.type == REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MAIL_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
        if (record?.type == REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
        if (record?.type == REQ_ARTEFACT_TYPE_ID.MESSAGE) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MESSAGE_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
        if (record?.type == REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.NONFUNTIONAL_REQ_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
        if (record?.type == REQ_ARTEFACT_TYPE_ID.WORKFLOW) {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.WORKFLOW_DETAIL}` + record.id
          return <Link to={href}>{text}</Link>
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.name' }),
      dataIndex: 'name',
    },
    {
      title: intl.formatMessage({ id: 'recommended-common-requirement.column.description' }),
      dataIndex: 'description',
      render: (text) => {
        return text ? <div
          className="tableDangerous"
          dangerouslySetInnerHTML={{
            __html: text,
          }}
        ></div> : <></>
      }
    },
  ]

  return (
    <Space
      direction="vertical"
      size="middle"
      className="record-detail-right-control-container p-1rem"
    >
      <Row align="middle" justify="space-between">
        <div>
          <Breadcrumb className='rq-breadcrumb' separator=">">
            <Breadcrumb.Item>
              <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
            </Breadcrumb.Item>
          </Breadcrumb>
          <Title level={3} className='rq-page-title'>
            {data?.code} - {data?.name}
          </Title>
        </div>

        <Space size="small">

          {/* Delete record */}
          {/* {(data?.status !== STATUS.DELETE) ? <DeleteButton
            type={BUTTON_TYPE.TEXT}
            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.object' }) })}
            okCB={() => dispatch(deleteRequest(recommendId))}
            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
          } */}

          {/* Update record */}
          {/* {(data?.status !== STATUS.DELETE && data?.status !== STATUS.CANCELLED) ?
            <RecommendForm
              screenMode={SCREEN_MODE.EDIT}
              buttonType={BUTTON_TYPE.TEXT}
              onFinish={() => onChange()}
              id={parseInt(recommendId)} /> : <></>
          } */}
        </Space>
      </Row>
      <Divider className="mt-0 mb-0" />
      <Spin spinning={isLoading}>
        <Scrollbars
          autoHide
        >
          <Space direction="vertical" size="middle">
            <Space size="large">
              {renderStatusBadge(data?.status)}
            </Space>
            <Card
              title={
                <Title level={5}>
                  {`${intl.formatMessage({
                    id: 'recommended-common-requirement-infomation',
                  })}`}
                </Title>
              }
              bordered={true}
            >
              <Row gutter={[16, 4]}>
                <Col span={24}>
                    <Text type="secondary">
                      {intl.formatMessage({ id: 'state.scope', })}: {intl.formatMessage({
                    id: data?.scope == COMPONENT_SCOPE[0].id ? 'common_component.scope.all_projects' :
                      'common_component.scope.current_customer'
                  })}
                    </Text>
                </Col>
                <Col span={24}>
                  <Text type="secondary">
                    {intl.formatMessage({ id: 'createobject.label.description' })}:
                  </Text>
                </Col>
                <Col className="description" span={24}>
                  {data?.description ?
                    <div
                      className="tableDangerous"
                      dangerouslySetInnerHTML={{
                        __html: data.description,
                      }}
                    ></div>
                    : <></>
                  }
                </Col>
              </Row>
            </Card>

            <Card
              title={
                <Title level={5}>
                  {`${intl.formatMessage({
                    id: 'recommended-common-requirement.components.properties',
                  })}`}
                </Title>
              }
              bordered={true}
            >
              <Table
                bordered={true}
                dataSource={dataSource}
                columns={columns}
              />
            </Card>

            <Row>
              <Col span={24}>
                <LavAuditTrail data={data?.auditTrail} />
              </Col>
            </Row>
          </Space>
        </Scrollbars>
      </Spin>
    </Space >
  )
}

export default RightControl
