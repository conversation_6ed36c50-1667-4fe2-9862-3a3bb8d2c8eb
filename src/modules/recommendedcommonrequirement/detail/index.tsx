import { Col, Row } from 'antd'
import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { APP_ROUTES, PROJECT_PREFIX } from '../../../constants'
import { extractProjectCode } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import { RecommendCommonComponentState } from '../type'
import RightControl from './content'

const RecommendedDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.RecommendCommonComponent) as RecommendCommonComponentState;

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.recommendId) {
      dispatch(getDetailRequest(props.match.params.recommendId))
    }
  }, [props])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.RECOMMENED}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    dispatch(getDetailRequest(props.match.params.recommendId))
  }

  return (
    <Row className='antRowHeight'>
      <Col span={24}>
        <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} recommendId={props.match.params.recommendId} isModalShow={state.isModalShow} />
      </Col>
    </Row>
  )
}

export default RecommendedDetail
