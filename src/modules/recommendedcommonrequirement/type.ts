export interface RecommendCommonComponentState {
  isLoading: boolean
  details: RecommendCommonComponentDetail | null
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  isLoadingList?: boolean
  listData?: any
  selectedData?: RecommendCommonComponentDetail | null,
  recommendSuccess?: boolean,

  listObjects?: any[],
  isLoadingObjects?: boolean,

  listFunctions?: any[],
  isLoadingFunctions?: boolean,

  listScreens?: any[],
  isLoadingScreens?: boolean,

  listRefs?: any[],
  isLoadingRefs?: boolean,

  missing?: any | null,
  isLoadingMissing?: boolean,

  warningMessage?: any,
  isLoadingWarningMessage?: boolean,

  isModalShow: boolean
}
export interface RecommendCommonComponentDetail {
  id?: number | null,
  name: string,
  status: number,
  code: string,
  description: string,
  warningMessage: string,
  auditTrails: any[]
  objects: any[],
  screens: any[],
  useCases: any[],
  messages: any[],
  nonFunctionalRequirements: any[],
  emailTemplates: any[],
  commonBusinessRules: any[],
  scope: number,
}

export const defaultState: RecommendCommonComponentState = {
  details: {
    id: -1,
    name: '',
    status: -1,
    code: '',
    description: '',
    warningMessage: '',
    auditTrails: [],
    objects: [],
    screens: [],
    useCases: [],
    messages: [],
    nonFunctionalRequirements: [],
    emailTemplates: [],
    commonBusinessRules: [],
    scope: -1,
  },
  isModalShow: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: '',
  selectedData: {
    id: -1,
    name: '',
    status: -1,
    code: '',
    description: '',
    warningMessage: '',
    auditTrails: [],
    objects: [],
    screens: [],
    useCases: [],
    messages: [],
    nonFunctionalRequirements: [],
    emailTemplates: [],
    commonBusinessRules: [],
    scope: -1,
  },
  isLoading: false,
  recommendSuccess: false,
  listObjects: [],
  isLoadingObjects: false,
  listFunctions: [],
  isLoadingFunctions: false,
  listScreens: [],
  isLoadingScreens: false,
  listRefs: [],
  isLoadingRefs: false,
  missing: null,
  isLoadingMissing: false,
  warningMessage: null,
  isLoadingWarningMessage: false,
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/RECOMMEND_COMMON_COMPONENT/RESET_STATE',

  RECOMMEND_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/RECOMMEND_REQUEST',
  RECOMMEND_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/RECOMMEND_SUCCESS',
  RECOMMEND_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/RECOMMEND_FAILED',

  GET_LIST_REFS_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_LIST_REFS_REQUEST',
  GET_LIST_REFS_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_LIST_REFS_SUCCESS',
  GET_LIST_REFS_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_LIST_REFS_FAILED',

  GET_MISSTING_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_MISSTING_REQUEST',
  GET_MISSTING_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_MISSTING_SUCCESS',
  GET_MISSTING_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_MISSTING_FAILED',

  GET_WARNING_MESSAGE_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_WARNING_MESSAGE_REQUEST',
  GET_WARNING_MESSAGE_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_WARNING_MESSAGE_SUCCESS',
  GET_WARNING_MESSAGE_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_WARNING_MESSAGE_FAILED',

  UPDATE_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/UPDATE_FAILED',

  DELETE_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/DELETE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/RECOMMEND_COMMON_COMPONENT/GET_LIST_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/RECOMMEND_COMMON_COMPONENT/SET_MODAL_VISIBLE',
}
