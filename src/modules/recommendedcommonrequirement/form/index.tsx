import AppState from "@/store/types";
import { PlusOutlined } from "@ant-design/icons";
import { AutoComplete, Button, Card, Checkbox, Col, Form, Input, Modal, notification, Row, Select, Space, Spin } from "antd";
import debounce from 'lodash.debounce';
import { createRef, useEffect, useState } from "react";
import { Scrollbars } from 'react-custom-scrollbars';
import { useDispatch, useSelector } from "react-redux";
import intl from "../../../config/locale.config";
import { API_URLS, BUTTON_TYPE, COMPONENT_SCOPE, LIST_NFR_CATEGORY, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS_COMMON } from "../../../constants";
import CkeditorMention from "../../../helper/component/ckeditor-mention";
import CustomSvgIcons from "../../../helper/component/custom-icons";
import CustomModal from "../../../helper/component/custom-modal";
import FormGroup from "../../../helper/component/form-group";
import useModalConfirmationConfig from "../../../helper/hooks/useModalConfirmationConfig";
import useWindowDimensions from "../../../helper/hooks/useWindowDimensions";
import { commonPrefixType } from "../../../helper/share";
import CBRDetailInfo from "../../../modules/business-rule/detail/content-preview";
import EmailDetailInfo from "../../../modules/email-templates/detail/content-preview";
import MessDetailInfo from "../../../modules/messages/detail/content-preview";
import ScreenDetailInfo from "../../../modules/mockup-screen/detail/content-preview";
import NonFunctonalDetailInfo from "../../../modules/non-functional-requirement/detail/content-preview";
import ObjectDetailInfo from "../../../modules/objects/detail/content-preview";
import UseCaseDetailInfo from "../../../modules/usecase/detail/content-preview";
import WorkflowDetailInfo from "../../../modules/workflow/detail/content-preview";
import AppCommonService from "../../../services/app.service";
import { getDetailRequest, getListRefsRequest, getMissingRequest, getWarningMessageRequest, recommendRequest, resetState, setModalVisible, updateRequest } from "../action";
import { RecommendCommonComponentState } from "../type";
import RecommendCommonComponentFunction from "./components/function";
import RecommendCommonComponentMissingMentioned from "./components/missing-mentioned";
import RecommendCommonComponentMissingReferenced from "./components/missing-referenced";
import RecommendComponentObjects from "./components/objects";
import RecommendCommonComponentOthers from "./components/others";
import RecommendCommonComponentScreens from "./components/screens";

const { Option } = Select
const { confirm } = Modal
const reqNoti = notification

interface RecommendCommonComponentModalProps {
    id?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}
const RecommendCommonComponentModal = ({ onDismiss, screenMode, id, onFinish }: RecommendCommonComponentModalProps) => {
    const dispatch = useDispatch();
    const { height: windowHeight } = useWindowDimensions();
    const modalConfirmConfig = useModalConfirmationConfig();
    const [form] = Form.useForm();
    const getCkeditorData: any = createRef()
    const state = useSelector<AppState | null>((s) => s?.RecommendCommonComponent) as RecommendCommonComponentState
    const [isCreateMore, setIsCreateMore] = useState(false);
    const [inputSearch, setInputSearch] = useState('');
    const [selectedArtefact, setSelectedArtefact] = useState<any>(null);

    const [objects, setObjects] = useState<any>([]);
    const [functions, setFunctions] = useState<any>([]);
    const [screens, setScreens] = useState<any>([]);
    const [others, setOthers] = useState<any>([]);
    const [options, setOptions] = useState<any>([]);
    const [selectedInput, setSelectedInput] = useState<any>(null);
    const [missing, setMissing] = useState<any>(null);
    const [warningMessage, setWarningMessage] = useState<any>(null);
    const categoryName = (catId) => {
        const cat = LIST_NFR_CATEGORY.find(
            (category: any) => category.id === catId
        )
        return cat?.name || ''
    }

    useEffect(() => {
        form.setFieldsValue({
            'scope': 0,
        })
        setWarningMessage(null);
        setMissing(null);
        setInputSearch('');
        setOptions([]);
        setObjects([]);
        setFunctions([]);
        setScreens([]);
        setOthers([]);
        resetForm();
        dispatch(resetState(null));
        return () => {
            setWarningMessage(null);
            setMissing(null);
            dispatch(resetState(null));
            resetForm()
        }
    }, [])


    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(id))
        }
    }, [screenMode, id])
    const initType = (data, type) => {
        return data.map((item) => ({
            ...item,
            type: type,
        }));
    }
    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT && state.details?.id) {
            form.setFieldsValue({
                code: state.details?.code,
                name: state.details?.name,
                scope: state.details?.scope,
            })
            const obj = initType(state.details.objects, REQ_ARTEFACT_TYPE_ID.OBJECT)
            const uc = initType(state.details.useCases, REQ_ARTEFACT_TYPE_ID.USECASE)
            const sc = initType(state.details.screens, REQ_ARTEFACT_TYPE_ID.SCREEN)

            setObjects(obj)
            setFunctions(uc)
            setScreens(sc)
        }
    }, [state.details])
    useEffect(() => {
        if (state.recommendSuccess || state.updateSuccess) {
            reqNoti['success']({
                description: intl.formatMessage({ id: 'SCD_9' }),
                message: intl.formatMessage({ id: 'common.dialog.success' }),
                placement: 'bottomRight',
            })
            if (onFinish) {
                onFinish();
            }
            if (isCreateMore) {
                resetForm(true);
            } else {
                resetForm();
                onDismiss()
            }
        }
    }, [state.recommendSuccess])

    const onSearch = (searchText: string) => {
        setInputSearch(searchText);
        dispatch(getListRefsRequest(searchText))
    };

    useEffect(() => {
        let allOptions = state.listRefs?.map((e) => { return { ...e, value: e.type + '_' + e.id, label: e.code + (e.name ? ' - ' + e.name : '') } }) || [];
        let optList: any[] = [];
        let selectedItems: any[] = [];
        if (objects && Array.isArray(objects)) {
            selectedItems = selectedItems.concat(objects)
        }
        if (functions && Array.isArray(functions)) {
            selectedItems = selectedItems.concat(functions)
        }
        if (screens && Array.isArray(screens)) {
            selectedItems = selectedItems.concat(screens)
        }
        if (others && Array.isArray(others)) {
            selectedItems = selectedItems.concat(others)
        }
        allOptions.forEach(opt => {
            const existsIndex = selectedItems.findIndex((e) => e.id === opt.id && e.type === opt.type);
            if (existsIndex === -1) {
                opt.rowId = opt.type + '_' + opt.id;
                optList.push(opt)
            }
        });
        setOptions(optList)

        if (inputSearch === '') {
            setOptions([])
        }
    }, [state.listRefs, objects, functions, screens, others, inputSearch])


    const onSelect = (onSelect: string, option: any) => {
        setInputSearch(option.label);
        setSelectedInput(option);
    };


    const onSubmit = debounce((values: any, st?: string) => {
        const othersData = extractOtherList(others);
        let inputObjects = objects?.map((e) => {
            return { id: e.id, objectPropertyIds: e.objectPropertyIds }
        })
        let inputScreens = screens?.map((e) => {
            return { id: e.id, screenComponentIds: e.screenComponentIds }
        })
        const requestData: any = {
            id: id || null,
            name: values.name,
            status: STATUS_COMMON.SUBMITTED,
            description: getCkeditorData.current?.props?.data,
            objects: inputObjects, // objects?.map((e) => e.id)
            screens: inputScreens, // screens?.map((e) => e.id)
            useCaseIds: functions?.map((e) => e.id),
            commonBusinessRuleIds: othersData.lstCBR?.map((e) => e.id),
            emailTemplateIds: othersData.lstEmails?.map((e) => e.id),
            workflowIds: othersData.lstWorkflows?.map((e) => e.id),
            messageIds: othersData.lstMessages?.map((e) => e.id),
            nonFunctionalRequirementIds: othersData.lstNFR?.map((e) => e.id),
            scope: values.scope
        }
        setIsCreateMore(values.createMore);
        if ((!objects || objects.length <= 0) && (!screens || screens.length <= 0) && (!functions || functions.length <= 0) && (!others || others.length <= 0)) {
            reqNoti['error']({
                description: intl.formatMessage({ id: 'EMSG_5' }),
                message: intl.formatMessage({ id: 'common.message.error' }),
                placement: 'bottomRight',
            })
            return;
        }
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage(
                { id: 'CFD_8' },
                { Artefact: intl.formatMessage({ id: 'common.artefact.recommend-common-component' }) }
            ),
            onOk() {
                dispatch(screenMode == SCREEN_MODE.CREATE ? recommendRequest(requestData) : updateRequest(requestData));
            },
            onCancel() {

            },
        })
    }, 500)

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss()
            },
            onCancel() { },
        })
    }

    const resetForm = (createMore = false) => {
        setOptions([]);
        setObjects([]);
        setFunctions([]);
        setOthers([]);
        setScreens([]);
        setSelectedArtefact(null);
        setMissing(null);
        setWarningMessage(null);
        form.resetFields([
            'code',
            'name',
            'description',
        ])
        if (!createMore) {
            setIsCreateMore(false);
        }
    }

    const addData = (item, fromMissing = false) => {
        let lstObjects: any[] = Object.assign([], objects);
        let lstScreens: any[] = Object.assign([], screens);
        let lstUsecases: any[] = Object.assign([], functions);
        let lstOthers: any[] = Object.assign([], others);
        switch (item.type) {
            case REQ_ARTEFACT_TYPE_ID.OBJECT:
                if (lstObjects.findIndex(e => e.rowId === item.rowId) === -1) {
                    lstObjects.push(item);
                    lstObjects.sort((a, b) => a.name.localeCompare(b.name))
                    setObjects(lstObjects)
                }
                break;
            case REQ_ARTEFACT_TYPE_ID.SCREEN:
                if (lstScreens.findIndex(e => e.rowId === item.rowId) === -1) {
                    lstScreens.push(item);
                    lstScreens.sort((a, b) => a.name.localeCompare(b.name))
                    setScreens(lstScreens)
                }
                break;
            case REQ_ARTEFACT_TYPE_ID.USECASE:
                if (lstUsecases.findIndex(e => e.rowId === item.rowId) === -1) {
                    lstUsecases.push(item);
                    lstUsecases.sort((a, b) => a.name.localeCompare(b.name))
                    setFunctions(lstUsecases)
                }
                break;
            default:
                if (lstOthers.findIndex(e => e.rowId === item.rowId) === -1) {
                    const prefix = commonPrefixType(item.type, false)
                    lstOthers.push({
                        ...item,
                        name: prefix + ' - ' + (item.name ? item.name : item.code)
                    });
                    lstOthers.sort((a, b) => a.name.localeCompare(b.name))
                    setOthers(lstOthers)
                }
                break;
        }
        const postData = selectedData(null, null, lstObjects, lstScreens, lstUsecases, lstOthers);
        dispatch(getWarningMessageRequest(postData));
        if (fromMissing) {
            const postData = selectedData(selectedArtefact.artefactType, selectedArtefact.id, lstObjects, lstScreens, lstUsecases, lstOthers);
            dispatch(getMissingRequest(postData));
        } else {
            setInputSearch('');
        }
    }

    useEffect(() => {
        setMissingData(state.missing);
    }, [state.missing])

    const setMissingData = (data) => {
        let lstMissingMentions: any[] = [];
        let lstMissingReferences: any[] = [];
        data?.missingMentions?.forEach((element: any) => {
            lstMissingMentions.push({
                ...element,
                rowId: element.type + '_' + element.id
            })
        });
        data?.missingReferences?.forEach((element: any) => {
            lstMissingReferences.push({
                ...element,
                rowId: element.type + '_' + element.id
            })
        });
        setMissing({
            missingMentions: lstMissingMentions,
            missingReferences: lstMissingReferences
        });
    }

    useEffect(() => {
        setWarningMessage(state.warningMessage);
        if (!selectedArtefact) {
            setMissingData(state.warningMessage);
        }
    }, [state.warningMessage])

    const handleAdd = () => {
        if (!options || options.length <= 0) {
            return
        }
        addData(selectedInput);
    }

    const handleAddMissing = (e) => {
        addData(e, true);
    }

    const extractOtherList = (lstOthers) => {
        let lstCBR: any[] = [];
        let lstNFR: any[] = [];
        let lstMessages: any[] = [];
        let lstEmails: any[] = [];
        let lstWorkflows: any[] = [];
        lstOthers?.forEach(element => {
            switch (element.type) {
                case REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE: {
                    lstCBR.push(element)
                    break
                }
                case REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT: {
                    lstNFR.push(element)
                    break
                }
                case REQ_ARTEFACT_TYPE_ID.MESSAGE: {
                    lstMessages.push(element)
                    break
                }
                case REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE: {
                    lstEmails.push(element)
                    break
                }
                case REQ_ARTEFACT_TYPE_ID.WORKFLOW: {
                    lstWorkflows.push(element)
                    break
                }
            }
        });
        return { lstCBR, lstNFR, lstMessages, lstEmails, lstWorkflows }
    }
    const selectedData = (artefactType, artefactId, lstObjects, lstScreens, lstUsecases, lstOthers) => {
        const othersData = extractOtherList(lstOthers);
        return {
            "artefactType": artefactType,
            "artefactId": artefactId,
            "objectIds": lstObjects.map(e => e.id),
            "screenIds": lstScreens.map(e => e.id),
            "useCaseIds": lstUsecases.map(e => e.id),
            "commonBusinessRuleIds": othersData.lstCBR.map(e => e.id),
            "nonFunctionalRequirementIds": othersData.lstNFR.map(e => e.id),
            "messageIds": othersData.lstMessages.map(e => e.id),
            "emailTemplateIds": othersData.lstEmails.map(e => e.id),
            "workflowIds": othersData.lstWorkflows.map(e => e.id),
        }
    }
    const handleUpdateScreenComponents = (e) => {
        let lstScreens = Object.assign([], screens);
        lstScreens.forEach(o => {
            if (o.id === selectedArtefact.id) {
                o.screenComponentIds = e;
            }
        });
        setScreens(lstScreens);
    }
    const handleUpdateObjProperties = (e) => {
        let lstObjects = Object.assign([], objects);
        lstObjects.forEach(o => {
            if (o.id === selectedArtefact.id) {
                o.objectPropertyIds = e;
            }
        });
        setObjects(lstObjects);
    }
    const handleViewDetail = (e) => {
        if (e.type === selectedArtefact?.artefactType && e.id === selectedArtefact?.id) {
            setSelectedArtefact(null);
            setMissingData(state.warningMessage);
            return
        }
        const postData = selectedData(e.type, e.id, objects, screens, functions, others);
        dispatch(getMissingRequest(postData));
        // Load detail
        let url = '';
        switch (e.type) {
            case REQ_ARTEFACT_TYPE_ID.OBJECT: {
                url = API_URLS.OBJECT
                break;
            }
            case REQ_ARTEFACT_TYPE_ID.SCREEN: {
                url = API_URLS.SCREENS
                break;
            }
            case REQ_ARTEFACT_TYPE_ID.USECASE: {
                url = API_URLS.USE_CASE
                break;
            }
            case REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT: {
                url = API_URLS.NON_FUNCTIONS
                break;
            }
            case REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE: {
                url = API_URLS.BUSINESS_RULES
                break;
            }
            case REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE: {
                url = API_URLS.EMAILS
                break;
            }
            case REQ_ARTEFACT_TYPE_ID.MESSAGE: {
                url = API_URLS.MESSAGES
                break;
            }
            case REQ_ARTEFACT_TYPE_ID.WORKFLOW: {
                url = API_URLS.WORKFLOWS
                break;
            }
        }
        AppCommonService.viewDetailCommonArtefact(url, e.id).then(res => {
            let artefact: any = {
                ...e,
                ...res.data,
                artefactType: e.type
            }
            if (e.type === REQ_ARTEFACT_TYPE_ID.OBJECT && !e.objectPropertyIds) {
                artefact.objectPropertyIds = res.data.objectProperties?.map((e) => e.id)
            }
            if (e.type === REQ_ARTEFACT_TYPE_ID.SCREEN && !e.screenComponentIds) {
                artefact.screenComponentIds = res.data.screenComponents?.map((e) => e.id)
            }
            setSelectedArtefact(artefact);
        }).catch(err => {
            setSelectedArtefact(null);
        }).finally(() => {
        })
    }
    const updateDataSource = (lstObjects, lstScreens, lstUsecases, lstOthers, updateType) => {
        if (
            (updateType === 1 && lstObjects.findIndex((e) => e.id === selectedArtefact?.id) === -1) ||
            (updateType === 2 && lstScreens.findIndex((e) => e.id === selectedArtefact?.id) === -1) ||
            (updateType === 3 && lstUsecases.findIndex((e) => e.id === selectedArtefact?.id) === -1) ||
            (updateType === 4 && lstOthers.findIndex((e) => e.id === selectedArtefact?.id) === -1)
        ) {
            setMissing(null);
            setSelectedArtefact(null)
        }
        setWarningMessage(null)
        const postData = selectedData(null, null, lstObjects, lstScreens, lstUsecases, lstOthers);
        dispatch(getWarningMessageRequest(postData));
    }

    return <CustomModal
        isLoading={state.isLoading}
        closable={false}
        size="extra"
        visible={true}
        footer={null}
    >
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header' style={{ marginBottom: 16 }}>
                <Row>
                    <Col span={16}>
                        <Space size="large">
                            <Form.Item
                                style={{ minWidth: 450 }}
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: intl.formatMessage({ id: 'IEM_1' }),
                                    },
                                    { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                    {
                                        validator: async (rule, value) => {
                                            if (value && value.trim().length === 0) {
                                                throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                            }
                                        },
                                    },
                                ]}
                            >
                                <Input
                                    size="large"
                                    className="modal-create-name-input-field"
                                    placeholder={`${intl.formatMessage({ id: `recommend_common_component.column.name` })}${intl.formatMessage({ id: `common.mandatory.*` })}`}
                                    maxLength={255}
                                />
                            </Form.Item>
                        </Space>
                    </Col>

                    <Col span={8}>
                        <Row justify="end">
                            <Space size="small">
                                {screenMode == SCREEN_MODE.CREATE ?
                                    (<Form.Item
                                        style={{ marginBottom: '0px' }}
                                        valuePropName="checked"
                                        name="createMore"
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Checkbox disabled={state.isLoading}>
                                            {intl.formatMessage({
                                                id: 'createobject.checkbox.create-another',
                                            })}
                                        </Checkbox>
                                    </Form.Item>) : <></>
                                }

                                <Button onClick={debounce(confirmCancel, 500)}>
                                    {intl.formatMessage({ id: 'common.action.close' })}
                                </Button>

                                <Button type="primary" htmlType="submit">
                                    {intl.formatMessage({ id: 'common.action.recommend' })}
                                </Button>
                            </Space>
                        </Row>
                    </Col>
                </Row>
            </div>

            <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 225}>
                <Spin spinning={state.isLoading}>
                    <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                        <Card className='rq-form-block' title={intl.formatMessage({ id: 'recommend_common_component.card.general_information' })}>
                            <FormGroup className="rq-fg-comment" label={intl.formatMessage({ id: 'recommended-common-requirement.column.scope' })}>
                                <Form.Item
                                    validateTrigger="onBlur"
                                    name="scope"
                                    style={{ width: '30%' }}
                                >
                                    <Select allowClear>
                                        {COMPONENT_SCOPE.map((item: any) =>
                                            <Option key={item.id} value={item.id}>
                                                {item.name}
                                            </Option>
                                        )}
                                    </Select>
                                </Form.Item>
                            </FormGroup>

                            <FormGroup label={intl.formatMessage({ id: 'recommend_common_component.column.description' })}>
                                <Form.Item name="description" labelAlign="left" wrapperCol={{ span: 24 }}>
                                    <CkeditorMention ref={getCkeditorData} data={screenMode == SCREEN_MODE.EDIT ? state.details?.description : ''} />
                                </Form.Item>
                            </FormGroup>
                            <FormGroup label={intl.formatMessage({ id: 'recommend_common_component.column.warning_message' })}>
                                <div style={{ background: '#f7f7f7', minHeight: 80, border: '1px solid #d9d9d9', padding: 16 }}>
                                    {
                                        warningMessage ? <>
                                            {
                                                (warningMessage.missingMentions && warningMessage.missingMentions.length > 0) ? <>
                                                    <div>Missing Mentioned Items:</div>
                                                    <ul>
                                                        {warningMessage.missingMentions?.map((mm, idx) => {
                                                            return <li key={idx}>{mm.code}{mm.name ? ': ' + mm.name : ''}</li>
                                                        })
                                                        }
                                                    </ul>
                                                </> : <></>
                                            }
                                            {
                                                (warningMessage.missingReferences && warningMessage.missingReferences.length > 0) ? <>
                                                    <div>Missing Referenced Items:</div>
                                                    <ul>
                                                        {warningMessage.missingReferences?.map((mr, idx) => {
                                                            return <li key={idx}>{mr.code}{mr.name ? ': ' + mr.name : ''}</li>
                                                        })
                                                        }
                                                    </ul>
                                                </> : <></>
                                            }
                                        </> : <></>
                                    }
                                </div>
                            </FormGroup>
                        </Card>

                        <Card className='rq-form-block' title={intl.formatMessage({ id: 'recommend_common_component.card.component_detail' })}>
                            <Space direction='vertical'>
                                <Space>
                                    <AutoComplete
                                        options={options}
                                        style={{ width: 400 }}
                                        onSelect={onSelect}
                                        onSearch={onSearch}
                                        value={inputSearch}
                                        notFoundContent={inputSearch ? 'No data' : ''}
                                        size='middle'
                                    />
                                    <Button onClick={handleAdd} type='link' size='middle' icon={<PlusOutlined />}>{intl.formatMessage({ id: 'recommend_common_component.action.add' })}</Button>
                                </Space>

                                <Row gutter={10}>
                                    <Col span={18}>
                                        <Row gutter={10} style={{ marginBottom: 8 }}>
                                            <Col span={6}>
                                                <RecommendComponentObjects dataSource={objects} onChange={(e) => { setObjects(e); updateDataSource(e, screens, functions, others, 1) }} onSelect={handleViewDetail} selectedArtefact={selectedArtefact} />
                                            </Col>
                                            <Col span={6}>
                                                <RecommendCommonComponentFunction dataSource={functions} onChange={(e) => { setFunctions(e); updateDataSource(objects, screens, e, others, 1) }} onSelect={handleViewDetail} selectedArtefact={selectedArtefact} />
                                            </Col>
                                            <Col span={6}>
                                                <RecommendCommonComponentScreens dataSource={screens} onChange={(e) => { setScreens(e); updateDataSource(objects, e, functions, others, 1) }} onSelect={handleViewDetail} selectedArtefact={selectedArtefact} />
                                            </Col>
                                            <Col span={6}>
                                                <RecommendCommonComponentOthers dataSource={others} onChange={(e) => { setOthers(e); updateDataSource(objects, screens, functions, e, 1) }} onSelect={handleViewDetail} selectedArtefact={selectedArtefact} />
                                            </Col>
                                        </Row>
                                        {
                                            selectedArtefact ? <Card className='rq-form-block' title={
                                                selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT ? selectedArtefact.code + ' - ' + categoryName(selectedArtefact.category) :
                                                    selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE ? selectedArtefact.code + ' - ' + (selectedArtefact.subject ? selectedArtefact.subject : intl.formatMessage({ id: 'common_component.card.email-template' })) :
                                                        selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.MESSAGE ? selectedArtefact.code + ' - ' + (selectedArtefact.content ? selectedArtefact.content : intl.formatMessage({ id: 'common_component.card.message' })) :
                                                            selectedArtefact.code + ' - ' + selectedArtefact.name
                                            }>
                                                {/* Workflow */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.WORKFLOW ? <WorkflowDetailInfo data={selectedArtefact} /> : <></>}
                                                {/* Object */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.OBJECT ? <ObjectDetailInfo onChange={handleUpdateObjProperties} data={selectedArtefact} /> : <></>}
                                                {/* Usercase */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.USECASE ? <UseCaseDetailInfo data={selectedArtefact} /> : <></>}
                                                {/* Screen */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.SCREEN ? <ScreenDetailInfo onChange={handleUpdateScreenComponents} data={selectedArtefact} /> : <></>}
                                                {/* Email */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE ? <EmailDetailInfo data={selectedArtefact} /> : <></>}
                                                {/* Message */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.MESSAGE ? <MessDetailInfo data={selectedArtefact} /> : <></>}
                                                {/* Non functional Business */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT ? <NonFunctonalDetailInfo data={selectedArtefact} /> : <></>}
                                                {/* Common Business Rule */}
                                                {selectedArtefact.artefactType === REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE ? <CBRDetailInfo data={selectedArtefact} /> : <></>}
                                            </Card> : <></>
                                        }
                                    </Col>
                                    <Col span={6}>
                                        <div style={{ marginBottom: 8 }}>
                                            <RecommendCommonComponentMissingMentioned onAdd={handleAddMissing} dataSource={missing?.missingMentions || []} />
                                        </div>
                                        <RecommendCommonComponentMissingReferenced onAdd={handleAddMissing} dataSource={missing?.missingReferences || []} />
                                    </Col>
                                </Row>
                            </Space>
                        </Card>
                    </Space>
                </Spin>
            </Scrollbars>
        </Form>
    </CustomModal>
}

interface RecommendedCommonReqFormProps {
    id?: number,
    onFinish?: () => void | null,
    buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
const RecommendForm = ({ id, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: RecommendedCommonReqFormProps) => {
    const dispatch = useDispatch();
    const [isModalVisible, setIsModalVisible] = useState<any>(null)

    useEffect(() => {
        if (isModalVisible !== null) {
            dispatch(setModalVisible(isModalVisible))
        }
    }, [isModalVisible])

    return <>
        {
            buttonType === BUTTON_TYPE.TEXT ?
                <Button
                    ghost={screenMode === SCREEN_MODE.CREATE}
                    type='primary'
                    className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
                    onClick={() => setIsModalVisible(true)}
                    icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
                >
                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'recommended-common-requirement.btn-create' : 'common.action.update' })}
                </Button> :
                buttonType === BUTTON_TYPE.ICON ?
                    <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
                    <></>
        }
        {isModalVisible === true ? <RecommendCommonComponentModal id={id} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
    </>
}
export default RecommendForm
