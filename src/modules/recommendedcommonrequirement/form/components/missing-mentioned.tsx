import { commonPrefixType } from '../../../../helper/share'
import { PlusCircleOutlined } from '@ant-design/icons'
import { Card, Table } from 'antd'
import React from 'react'
import intl from '../../../../config/locale.config'
const RecommendCommonComponentMissingMentioned = (props) => {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      render: (text, item) => {
        const prefix = commonPrefixType(item.type, false)
        return prefix + ' - ' + item.name || item.code
      }
    },
    {
      title: '',
      dataIndex: 'name',
      render: (text, item) => <a href='#' onClick={(e) => onAdd(e, item)}><PlusCircleOutlined /></a>
    },
  ]

  const onAdd = (e, item) => {
    e.preventDefault()
    if (props.onAdd) props.onAdd(item)
  }

  return (
    <Card className='rq-form-block rq-form-block-p0' title={intl.formatMessage({ id: 'recommend_common_component.card.missing_mentioned' })}>
      <Table
        locale={{
          emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
          filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
        }}
        className="lav-table"
        bordered
        dataSource={props.dataSource}
        columns={columns}
        rowKey='rowId'
        pagination={props.dataSource?.length <= 5 ? false : {
          pageSize: 10,
          total: props.dataSource?.length,
          size: 'small',
          showLessItems: true,
          position: ['bottomRight'],
          // showTotal: (total, range) =>
          //   `${range[0]}-${range[1]} ${intl.formatMessage({
          //     id: 'common.table.pagination.of',
          //   })} ${total} ${intl.formatMessage({
          //     id: 'common.table.pagination.items',
          //   })}`
        }}
        loading={false}
      />
    </Card>
  )
}

export default RecommendCommonComponentMissingMentioned
