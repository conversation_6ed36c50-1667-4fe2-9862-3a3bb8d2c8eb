import React from 'react'
import intl from '../../../../config/locale.config'
import { Button, Card, Table, Tooltip } from 'antd'
import CustomSvgIcons from '../../../../helper/component/custom-icons'

const RecommendComponentWorkflow = (props) => {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      render: (text, item) => {
        return <div onClick={() => props.onSelect(item)}
          className={props.selectedArtefact?.artefactType === item.type && props.selectedArtefact?.id === item.id ? 'selected-preview' : ''}
          style={{ maxWidth: 200, color: '#2979FF', cursor: 'pointer' }}><Tooltip title={text}>{text}</Tooltip></div>
      }
    },
    {
      title: '',
      dataIndex: 'name',
      render: (text, item, index) => {
        return <Button icon={<CustomSvgIcons name="DeleteCustomIcon" />} type="link" onClick={() => handleRemove(index)}></Button>
      }
    },
  ]

  const handleRemove = (index) => {
    let currentData = Object.assign([], props.dataSource);
    if (currentData.length <= 1) {
      currentData = []
    } else {
      currentData.splice(index, 1);
    }
    props.onChange(currentData);
  }

  return (
    <Card className='rq-form-block rq-form-block-p0' title={intl.formatMessage({ id: 'recommend_common_component.card.objects' })}>
      <Table
        locale={{
          emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
          filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
        }}
        className="lav-table"
        bordered
        dataSource={props.dataSource}
        columns={columns}
        rowKey='id'
        pagination={props.dataSource?.length <= 5 ? false : {
          pageSize: 5,
          total: props.dataSource?.length,
          size: 'small',
          showLessItems: true,
          position: ['bottomRight'],
          // showTotal: (total, range) =>
          //   `${range[0]}-${range[1]} ${intl.formatMessage({
          //     id: 'common.table.pagination.of',
          //   })} ${total} ${intl.formatMessage({
          //     id: 'common.table.pagination.items',
          //   })}`
        }}
      />
    </Card>
  )
}

export default RecommendComponentWorkflow
