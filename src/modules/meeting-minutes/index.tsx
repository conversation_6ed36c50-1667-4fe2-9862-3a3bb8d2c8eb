import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, DATE_FORMAT, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import MeetingMinuteFormPage from './form/form'

const MeetingMinute = () => {
  
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  
  useEffect(() => {
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'meeting.header.title' });
  }, [screenMode]);

  const columns = [
    {
      title: intl.formatMessage({ id: 'meeting.column.meeting-code' }),
      dataIndex: 'code',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      sorter: true,
      sortOrder: 'descend',
      width: '85px',
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MEETING_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'meeting.column.subject' }),
      dataIndex: 'subject',
      ...getColumnSearchProps('subject', SEARCH_TYPE.TEXT),
      sorter: true
    },
    {
      title: intl.formatMessage({ id: 'meeting.column.meeting-date' }),
      dataIndex: 'meetingDate',
      ...getColumnSearchProps('meetingDate', SEARCH_TYPE.DATE),
      sorter: true,
      width: '130px',
      render: (text) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
    },
    {
      title: intl.formatMessage({ id: 'meeting.column.status' }),
      dataIndex: 'status',
      ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      sorter: true,
      width: '80px',
      render: (record) => renderStatusBadge(record),
    }
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'meeting.button.create-meeting' })}
      </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer || hasRole(APP_ROLES.PM)) && record?.status === STATUS.SUBMITTED)) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return ((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && record.status !== STATUS.DELETE) ?
      children : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        screenMode === SCREEN_MODE.VIEW ?
          <LavTable
            title="meeting.header.title"
            artefact_type="common.artefact.meeting-minustes"
            apiUrl={API_URLS.MEETING_MINUTES}
            columns={columns}
            artefactType={REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE}
            deleteComponent={DeleteComponent}
            updateComponent={UpdateComponent}
            createComponent={CreateComponent}
          /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <MeetingMinuteFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <MeetingMinuteFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} meetingID={id} /> : <></>
      }

    </Space>
  )
}

export default MeetingMinute
