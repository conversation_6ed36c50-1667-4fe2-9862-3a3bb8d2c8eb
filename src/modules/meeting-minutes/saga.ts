import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListMeetingMinutesFailed, getListMeetingMinutesRequest, getListMeetingMinutesSuccess, getListReferenceDocumentsFailed, getListReferenceDocumentsRequest, getListReferenceDocumentsSuccess, getListRequest,
  getListSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.MEETING_MINUTES}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.MEETING_MINUTES + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.MEETING_MINUTES + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.meeting-minustes')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.meeting-minustes')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.MEETING_MINUTES, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.meeting-minustes')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.meeting-minustes', err)
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.MEETING_MINUTES + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.meeting-minustes')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.meeting-minustes')
    }
  }
}

function* handleGetListReferenceDocuments(action: Action) {
  if (getListReferenceDocumentsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_DOCUMENT)
      yield put(getListReferenceDocumentsSuccess(res.data))
    } catch (err) {
      yield put(getListReferenceDocumentsFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListMeetingMinutes(action: Action) {
  if (getListMeetingMinutesRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_MEETING)
      yield put(getListMeetingMinutesSuccess(res.data))
    } catch (err) {
      yield put(getListMeetingMinutesFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getListReferenceDocumentsRequest.type, handleGetListReferenceDocuments)
  yield takeLatest(getListMeetingMinutesRequest.type, handleGetListMeetingMinutes)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
}
export default function* MeetingMinuteSaga() {
  yield all([fork(watchFetchRequest)])
}
