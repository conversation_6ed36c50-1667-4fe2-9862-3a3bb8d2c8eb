
export interface MeetingMinuteState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: MeetingMinuteDetail | null
  selectedData?: MeetingMinuteDetail | null
  referenceDocuments?: any | null
  meetingMinutes?: any | null
  isModalShow?:boolean
}

export interface MeetingMinuteDetail {
  id?: number | null,
  code: string,
  subject: string,
  status: number,
  venue: string,
  participants: string,
  absent: string,
  agenda: string,
  meetingDate: string,
  storage: string
  jira: string
  confluence: string
  discussionItems: DiscussionItem[],
  userRequirements: UserRequirement[],
  projectId?: number | null
}
export interface DiscussionItem {
  type: number | null,
  order: number | null,
  subject: "",
  details: "",
  id: number | null
}
export interface UserRequirement {
  code: string,
  name: string,
  description: string,
  type: number | null,
  scope: number | null,
  source: number | null,
  meetingMinuteIds: number[],
  referenceDocumentIds: number[],
  sourceOther: string,
  sender: string,
  sendDate: string,
  reqElicitation: number | null,
  documentation: number | null,
  development: number | null,
  confluence: string,
  storage: string,
  jira: string,
  status: number,
  id: number | null
}
export const defaultState: MeetingMinuteState = {
  detail: {
    id: null,
    code: '',
    subject: '',
    status: 0,
    venue: '',
    participants: '',
    absent: '',
    agenda: '',
    meetingDate: '',
    storage: '',
    jira: '',
    confluence: '',
    discussionItems: [],
    userRequirements: [],
    projectId: null
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  referenceDocuments: [],
  meetingMinutes: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/MEETING_MINUTE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/MEETING_MINUTE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/MEETING_MINUTE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/MEETING_MINUTE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/MEETING_MINUTE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/MEETING_MINUTE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/MEETING_MINUTE/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/MEETING_MINUTE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/MEETING_MINUTE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/MEETING_MINUTE/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/MEETING_MINUTE/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/MEETING_MINUTE/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/MEETING_MINUTE/GET_LIST_FAILED',

  GET_LIST_MEETING_MINUTES_REQUEST = '@@MODULES/MEETING_MINUTE/GET_LIST_MEETING_MINUTE_REQUEST',
  GET_LIST_MEETING_MINUTES_SUCCESS = '@@MODULES/MEETING_MINUTE/GET_LIST_MEETING_MINUTE_SUCCESS',
  GET_LIST_MEETING_MINUTES_FAILED = '@@MODULES/MEETING_MINUTE/GET_LIST_MEETING_MINUTE_FAILED',

  GET_LIST_REFERENCE_DOCUMENTS_REQUEST = '@@MODULES/MEETING_MINUTE/GET_LIST_REFERENCE_DOCUMENTS_REQUEST',
  GET_LIST_REFERENCE_DOCUMENTS_SUCCESS = '@@MODULES/MEETING_MINUTE/GET_LIST_REFERENCE_DOCUMENTS_SUCCESS',
  GET_LIST_REFERENCE_DOCUMENTS_FAILED = '@@MODULES/MEETING_MINUTE/GET_LIST_REFERENCE_DOCUMENTS_FAILED',

  DELETE_REQUEST = '@@MODULES/MEETING_MINUTE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/MEETING_MINUTE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/MEETING_MINUTE/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/MEETING_MINUTE/SET_MODAL_VISIBLE',
}
