import AppState from '@/store/types'
import {
    <PERSON><PERSON><PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import moment from 'moment'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, DATE_FORMAT, DISCUSSION_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCOPE_TYPE_LIST, SCREEN_MODE, STATUS } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import RqTable from '../../../helper/component/edit-table-fe'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, getProjectName, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../_shared/comment/action'
import TriggerComment from '../../_shared/comment/trigger-comment'
import { CommentState } from '../../_shared/comment/type'
import { deleteRequest } from '../action'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    meetingID: number,
    // onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}
const RightControl = ({ data, meetingID, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);
    const columns = [
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.order' })}
                </Text>
            ),
            dataIndex: 'order',
            width: '5%',
            key: 'order',
            render: (text, record, index) => <Text>{index + 1}</Text>,
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'user-requirement.column.user-requirement-code',
                    })}
                </Text>
            ),
            dataIndex: 'code',
            key: 'code',
            width: '10%',
            render: (code: string, record: any) => {
                const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USER_REQUIREMENT_DETAIL}${record.id}`
                return <Link to={href}>{code}</Link>
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'discussion.label.subject' })}
                </Text>
            ),
            dataIndex: 'name',
            key: 'name',
            width: '30%',
            render: (text) => {
                return <Text>{text}</Text>
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'user-requirement.column.type',
                    })}
                </Text>
            ),
            dataIndex: 'type',
            key: 'type',
            width: '10%',
            render: (type: number) => {
                const typeIndex = SCOPE_TYPE_LIST.findIndex((item) => item.id == type)

                if (typeIndex !== -1) {
                    return <Text>{SCOPE_TYPE_LIST[typeIndex].name} </Text>
                } else {
                    return <Text></Text>
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'user-requirement.label.user-requirement-details',
                    })}
                </Text>
            ),
            dataIndex: 'description',
            key: 'description',
            width: '45%',
            render: (text, record) => {
                return (
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: text }}
                    ></div>
                )
            },
        },
    ]

    const columnsDiscuess = [
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.order' })}
                </Text>
            ),
            dataIndex: 'order',
            width: '5%',
            key: 'order',
            render: (text, record, index) => <Text>{index + 1}</Text>,
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'discussion.label.subject' })}
                </Text>
            ),
            dataIndex: 'subject',
            key: 'subject',
            width: '10%',
            render: (text) => {
                return <Text>{text}</Text>
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'discussion.label.type',
                    })}{' '}
                </Text>
            ),
            dataIndex: 'type',
            key: 'type',
            width: '20%',
            render: (text, record) => {
                const objIndex = DISCUSSION_TYPE.findIndex(
                    (item: any) => item.id === text
                )
                return <Text>{DISCUSSION_TYPE[objIndex]?.name}</Text>
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'discussion.label.details' })}{' '}
                </Text>
            ),
            dataIndex: 'details',
            key: 'details',
            width: '65%',
            render: (text, record) => {
                return (
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: text }}
                    ></div>
                )
            },
        },
    ]

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;
    useEffect(() => {        
        if(data)
            document.title = data?.code + "-" + data?.subject; 
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'meeting-date', title: intl.formatMessage({ id: 'create-meeting.label.meeting-date' }), },
            { field: 'venue', title: intl.formatMessage({ id: 'create-meeting.label.venue' }), },
            { field: 'subject', title: intl.formatMessage({ id: 'create-meeting.label.subject' }), },
            { field: 'participants', title: intl.formatMessage({ id: 'create-meeting.label.participants' }), },
            { field: 'absent', title: intl.formatMessage({ id: 'create-meeting.label.absent' }), },
            { field: 'agenda', title: intl.formatMessage({ id: 'create-meeting.label.agenda' }), },
            { field: 'user-requirement', title: intl.formatMessage({ id: 'create-meeting.label.user-requirement' }), },
            { field: 'other-discussion-item', title: intl.formatMessage({ id: 'create-meeting.label.other-discussion-item' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: data?.projectId, itemId: data?.id, fields }));

        const payload = {
            projectId: data?.projectId,
            itemId: data?.id,
            artefact: ARTEFACT_COMMENT.MEETING_MINUTES,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT

    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>{data?.code}-{data?.subject}</Title>
                </div>

                <Space size="small">
                    <DeleteButton
                        type={BUTTON_TYPE.TEXT}
                        content={`${intl.formatMessage(
                            { id: 'CFD_7' },
                            {
                                artefact_type: `${intl.formatMessage({
                                    id: 'common.artefact.meeting-minustes',
                                })}`,
                            }
                        )}`}
                        okCB={() => dispatch(deleteRequest(meetingID))}
                    ></DeleteButton>

                    {data?.status !== STATUS.CANCELLED && (
                        <Button
                        type='primary'
                        className='lav-btn-create'
                        onClick={() => {
                            setScreenMode(SCREEN_MODE.EDIT)
                        }} >{intl.formatMessage({ id: 'common.action.update' })}</Button>
                    )}
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">                        
                        <Space size="large">                        
                        <span >
                            <TriggerComment field='version'>                               
                                <a onClick={() => {
                                    setScreenMode(SCREEN_MODE.HISTORY)
                                }}>
                                    {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                </a>
                            </TriggerComment>
                        </span>
                        {renderStatusBadge(data?.status)}
                            {/* <Button
                                type='primary'
                                className='lav-btn-create'
                                onClick={() => {
                                    setScreenMode(SCREEN_MODE.HISTORY)
                                }} >View History</Button> */}
                        </Space>
                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'create-meeting.card.meeting-infomation',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={4}>
                                    <TriggerComment field="meeting-date">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.meeting-date',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                {/* lavdate */}
                                <Col span={20}>{data?.meetingDate ? moment(data?.meetingDate).format(DATE_FORMAT) : ''}</Col>

                                <Col span={4}>
                                    <TriggerComment field="venue">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.venue',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>{data?.venue}</Col>

                                <Col span={4}>
                                    <TriggerComment field="subject">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.subject',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>{data?.subject}</Col>

                                <Col span={4}>
                                    <TriggerComment field="participants">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.participants',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20} className="description">
                                    {data?.participants}
                                </Col>

                                <Col span={4}>
                                    <TriggerComment field="absent">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.absent',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20} className="description">
                                    {data?.absent}
                                </Col>

                                <Col span={4}>
                                    <TriggerComment field="agenda">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.agenda',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.agenda }}
                                    ></div>
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field="user-requirement">
                                        <Text type="secondary">
                                            {' '}
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.user-requirement',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <RqTable
                                        dataSource={data?.userRequirements}
                                        columns={columns}
                                        isLoading={false}
                                    />
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field="other-discussion-item">
                                        <Text type="secondary">
                                            {' '}
                                            {intl.formatMessage({
                                                id: 'create-meeting.label.other-discussion-item',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <RqTable
                                        dataSource={data?.discussionItems}
                                        columns={columnsDiscuess}
                                        isLoading={false}
                                    />
                                </Col>
                            </Row>
                        </Card>

                        <Space direction='vertical' size="middle">
                            <LavReferences data={data} />
                            <LavRelatedLinks data={data} />
                        </Space>

                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
