import AppState from '@/store/types'
import {
    But<PERSON>,
    Card, Checkbox, Col, DatePicker, Form, Input, Modal, Row, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { ARTEFACT_COMMENT, DATE_FORMAT, MESSAGE_TYPES, SCREEN_MODE, STATUS } from '../../../constants'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import TextAreaBullet from '../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { concatMentionReferences, currentUserName, getReferencesFromEditor, renderStatusBadge } from '../../../helper/share'
import { getListDocumentRequest } from '../../../modules/user-requirement/action'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { MeetingMinuteState } from '../type'
import DiscussionTable from './discussion-table'
import UserRequirementTable from './user-requirement-table'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'


const { Title } = Typography
const { confirm } = Modal

interface MeetingMinuteFormModalProps {
    meetingID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}

const MeetingMinuteFormPage = ({ meetingID, screenMode, onFinish, onDismiss }: MeetingMinuteFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm()
    const tableRef: any = useRef()
    const tableUserRequirementRef: any = useRef()
    const getCkeditor: any = createRef()
    const state = useSelector<AppState | null>((s) => s?.MeetingMinute) as MeetingMinuteState
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const modalConfirmConfig = useModalConfirmationConfig()
    const [reLoad, setReLoad] = useState(false)
    const [isBack, setIsBack] = useState(false)
    const [agenda, setAgenda] = useState('')

    useBeforeUnload();
    // Destroy
    useEffect(() => {
        setReLoad(false);
        setIsBack(false);
        dispatch(getListDocumentRequest(null))
        form.setFieldsValue({
            assignee: currentUserName()
        })
        return () => {
            dispatch(resetState(null));
            resetForm();
            form.resetFields(['createMore']);
        }
    }, [])


    useEffect(() => {
        if (meetingID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(meetingID))
        }
        console.log("Screen Mode : ", screenMode);
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'meeting.page_create_title' : 'meeting.page_update_title' })
    }, [screenMode, meetingID])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }

    useEffect(() => {
        if (meetingID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);
            form.setFieldsValue({
                ...state.detail,
                // lavdate
                meetingDate: state.detail.meetingDate ? moment(new Date(state.detail.meetingDate)) : null,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
            })
            setAgenda(state.detail?.agenda)
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            if (isCreateMore) {
                resetForm();
                form.setFieldsValue({
                    assignee: currentUserName(),
                    dueDate: moment(new Date()),
                })
            } else {
                if (onFinish) {
                    onFinish();
                }
                onDismiss();
            }
            setIsDraft(null);
            setIsCreateMore(false);
        }
    }, [state.createSuccess, state.updateSuccess])

    const onSubmit = debounce(async (values: any, st?: string) => {
        let discussionItems = tableRef.current.getTableState();
        if (discussionItems && Array.isArray(discussionItems)) {
            discussionItems = discussionItems.map((e) => {
                return {
                    type: e.type,
                    order: e.order,
                    subject: e.subject,
                    details: e.details,
                    id: e.id
                }
            })
        }

        let userRequirements = tableUserRequirementRef.current.getTableState();
        if (userRequirements && Array.isArray(userRequirements)) {
            userRequirements = userRequirements.map((e) => {
                return {
                    code: e.code,
                    name: e.name,
                    version: e.version,
                    description: e.description,
                    type: e.type,
                    scope: e.scope,
                    source: e.source,
                    meetingMinuteIds: [],
                    referenceDocumentIds: [],
                    sourceOther: '',
                    sender: e.sender,
                    sendDate: e.sendDate,
                    reqElicitation: e.reqElicitation,
                    documentation: e.documentation,
                    development: e.development,
                    confluence: e.confluence,
                    storage: e.storage,
                    jira: e.jira,
                    status: e.status,
                    id: e.id || null,
                }
            })
        }

        // Collect all references
        let mentionReferences = getReferencesFromEditor(getCkeditor.current?.props?.data);
        try {
            discussionItems?.forEach((e) => {
                mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.details));
            })
        } catch (err) {
            console.log(err);
        }
        try {
            userRequirements?.forEach((e) => {
                mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.description));
            })
        } catch (err) {
            console.log(err);
        }
        const requestData: any = {
            id: meetingID || null,
            ...values,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
            status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS.DRAFT : state.detail?.status) : STATUS.SUBMITTED,
            discussionItems: discussionItems,
            userRequirements: userRequirements,
            meetingDate: values.meetingDate?.toDate(),
            agenda: getCkeditor?.current?.props?.data,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
        }
        setIsCreateMore(values.createMore);
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.meeting-minustes' }) }
                ),
                onOk() {
                    requestData.messageAction = MESSAGE_TYPES.SUBMIT;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
    }, 500)

    const onFinishFailed = (errorInfo: any) => {

    }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        setIsDraft(null);
        setIsBack(false);
        setReLoad(false);
        setAgenda('');
        form.resetFields([
            'version',
            'code',
            'meetingDate',
            'venue',
            'subject',
            'participants',
            'absent',
            'agenda',
            'userTable',
            'discussTable',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
            'req',
            'documentation',
            'reviewer',
            'dueDate',
            'completeDate'
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })

    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'meeting-date', title: intl.formatMessage({ id: 'create-meeting.label.meeting-date' }), },
            { field: 'venue', title: intl.formatMessage({ id: 'create-meeting.label.venue' }), },
            { field: 'subject', title: intl.formatMessage({ id: 'create-meeting.label.subject' }), },
            { field: 'participants', title: intl.formatMessage({ id: 'create-meeting.label.participants' }), },
            { field: 'absent', title: intl.formatMessage({ id: 'create-meeting.label.absent' }), },
            { field: 'agenda', title: intl.formatMessage({ id: 'create-meeting.label.agenda' }), },
            { field: 'user-requirement', title: intl.formatMessage({ id: 'create-meeting.label.user-requirement' }), },
            { field: 'other-discussion-item', title: intl.formatMessage({ id: 'create-meeting.label.other-discussion-item' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.MEETING_MINUTES,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    //#endregion COMMENT INIT

    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name="MeetingForm"
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb={false}
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'meeting.page_create_title' : 'meeting.page_update_title' })}
                >
                    <Space size="small">
                        {screenMode == SCREEN_MODE.CREATE ?
                            (<Form.Item
                                style={{ marginBottom: '0px' }}
                                valuePropName="checked"
                                name="createMore"
                                wrapperCol={{ span: 24 }}
                            >
                                <Checkbox disabled={state.isLoading}>
                                    {intl.formatMessage({
                                        id: 'createobject.checkbox.create-another',
                                    })}
                                </Checkbox>
                            </Form.Item>) : <></>
                        }
                        <Button onClick={debounce(confirmCancel, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>
                        {screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.DRAFT || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.REJECT_CUSTOMER ?
                            <Button type="primary" ghost htmlType="submit" onClick={() => setIsDraft(false)}>
                                {intl.formatMessage({ id: 'common.action.submit' })}
                            </Button> : <></>
                        }
                        <Form.Item style={{ marginBottom: '0px' }}>
                            <Button
                                onClick={() => setIsDraft(true)}
                                className="success-btn"
                                htmlType="submit"
                            >
                                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.update' })}
                            </Button>
                        </Form.Item> : <></>
                    </Space>
                </LavPageHeader>
            </div>
            <Row align="middle">
                {/* <Col span={2}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={14} controlSpan={10} label={
                                <TriggerComment screenMode={screenMode} field='actor'>
                                    {intl.formatMessage({ id: 'createobject.place-holder.version' })}
                                </TriggerComment>}>
                                <Form.Item
                                    name="version"
                                    rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col> */}
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderStatusBadge(state.detail?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>
            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'create-meeting.card.meeting-infomation' })}>
                    {
                        screenMode === SCREEN_MODE.EDIT ?
                            <FormGroup inline labelSpan={3} controlSpan={3} label={intl.formatMessage({ id: 'common.label.code' })}>
                                <Form.Item rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                    <Input width={'100%'} value={state.detail?.code} disabled maxLength={255} />
                                </Form.Item>
                            </FormGroup> : <></>
                    }

                    <FormGroup inline labelSpan={3} controlSpan={3} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="meeting-date">
                            {intl.formatMessage({ id: 'create-meeting.label.meeting-date' })}
                        </TriggerComment>
                    }>
                        <Form.Item validateTrigger="onBlur" name="meetingDate">
                            <DatePicker style={{ width: '100%' }} format={[DATE_FORMAT]} />
                        </Form.Item>
                    </FormGroup>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="venue">
                            {intl.formatMessage({ id: 'create-meeting.label.venue' })}
                        </TriggerComment>}>
                        <Form.Item
                            validateTrigger="onBlur"
                            rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                            name="venue"
                        >
                            <Input maxLength={255} />
                        </Form.Item>
                    </FormGroup>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" required label={
                        <TriggerComment screenMode={screenMode} field="subject">
                            {intl.formatMessage({ id: 'create-meeting.label.subject' })}
                        </TriggerComment>}>
                        <Form.Item
                            validateTrigger="onBlur"
                            rules={[
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                { required: true, message: intl.formatMessage({ id: 'IEM_1' }) }
                            ]}
                            name="subject"
                        >
                            <Input maxLength={255} />
                        </Form.Item>
                    </FormGroup>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="participants">
                            {intl.formatMessage({ id: 'create-meeting.label.participants' })}
                        </TriggerComment>}>
                        <TextAreaBullet
                            reload={reLoad}
                            reloadAfterBack={true}
                            label=""
                            name="participants"
                            labelAlign="left"
                            rules={[
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        ></TextAreaBullet>
                    </FormGroup>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="absent">
                            {intl.formatMessage({ id: 'create-meeting.label.absent' })}
                        </TriggerComment>}>
                        <TextAreaBullet
                            reload={reLoad}
                            reloadAfterBack={true}
                            label=""
                            name="absent"
                            labelAlign="left"
                            rules={[
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        ></TextAreaBullet>
                    </FormGroup>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="agenda">
                            {intl.formatMessage({ id: 'create-meeting.label.agenda' })}
                        </TriggerComment>}>
                        <Form.Item name="agenda">
                            <CkeditorMention
                                ref={getCkeditor}
                                data={agenda}
                            />
                        </Form.Item>
                    </FormGroup>

                    <FormGroup className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="user-requirement">
                            {intl.formatMessage({ id: 'create-meeting.label.user-requirement' })}
                        </TriggerComment>}>
                        <Form.Item name="userTable">
                            <UserRequirementTable
                                data={state.detail?.userRequirements}
                                ref={tableUserRequirementRef}
                                reloadTable={reLoad}
                                reloadTableAfterBack={isBack} />
                        </Form.Item>
                    </FormGroup>

                    <FormGroup className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="other-discussion-item">
                            {intl.formatMessage({ id: 'create-meeting.label.other-discussion-item' })}
                        </TriggerComment>}>
                        <Form.Item name="discussTable">
                            <DiscussionTable
                                data={state.detail?.discussionItems}
                                form={form}
                                ref={tableRef}
                                reloadTable={reLoad}
                                reloadTableAfterBack={isBack} />
                        </Form.Item>
                    </FormGroup>
                </Card>

                <LavRelatedLinksForm form={form} screenMode={screenMode} />
            </Space>
        </Form>
    </Spin>
}


export default MeetingMinuteFormPage
