import {
  Button, Modal, Table,
  Typography
} from 'antd'
import React, { useEffect, useImperativeHandle, useState } from 'react'
import ReactDragListView from 'react-drag-listview'
import intl from '../../../../config/locale.config'
import { DISCUSSION_TYPE, SCREEN_MODE } from '../../../../constants'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import DiscussionForm from './form'

const { confirm } = Modal
const { Text } = Typography
interface DiscussionTableProps {
  form: any,
  reloadTable: boolean,
  reloadTableAfterBack: boolean,
  data: any
}
const DiscussionTable = React.forwardRef(({ form, reloadTable, reloadTableAfterBack, data }: DiscussionTableProps, ref: any) => {
  const [dataS, setDataSource] = useState([])
  const modalConfirmConfig = useModalConfirmationConfig()

  useEffect(() => {
    setDataSource([])
  }, [reloadTable, reloadTableAfterBack])
  const [deleteList, setDeleteList] = useState<any>([])

  useEffect(() => {
    if (data) {
      setDataSource(data)
    }
  }, [data])

  useImperativeHandle(
    ref,
    () => ({
      getTableState: () => {
        return dataS
      },
      getDeleteList: () => {
        return deleteList
      },
    }),
    [dataS, deleteList]
  )

  const editProperty = (data: any, index) => {
    const dataSource: any = [...dataS]
    dataSource[index] = data;
    setDataSource(dataSource)
  }

  const deleteRow = (rowIndex) => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_1' })}`,
      onOk() {
        let currentData: any = Object.assign([], dataS)
        if (currentData.length == 1) {
          currentData = []
        } else {
          currentData.splice(rowIndex, 1)
        }
        setDataSource(currentData)
      },
      onCancel() { },
    })
  }

  const columns = [
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'createscreen.column.order' })}
        </Text>
      ),
      dataIndex: 'order',
      width: '3%',
      key: 'order',
      render: (text, record, index) => <Text>{index + 1}</Text>,
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'discussion.label.subject' })}
        </Text>
      ),
      dataIndex: 'subject',
      key: 'subject',
      width: '9.5%',
      render: (text) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'discussion.label.type',
          })}
        </Text>
      ),
      dataIndex: 'type',
      key: 'type',
      width: '20%',
      render: (text, record) => {
        const objIndex = DISCUSSION_TYPE.findIndex(
          (item: any) => item.id === text
        )
        return <Text>{DISCUSSION_TYPE[objIndex]?.name}</Text>
      },
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'discussion.label.details' })}
        </Text>
      ),
      dataIndex: 'details',
      key: 'details',
      width: '62.5%',
      render: (text, record) => {
        return (
          <div
            className="tableDangerous"
            dangerouslySetInnerHTML={{ __html: text }}
          ></div>
        )
      },
    },

    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'createscreen.column.action' })}
        </Text>
      ),
      key: 'action',
      className: 'rq-action',
      width: '5%',
      render: (text, record, index) => (
        <div className='rq-sub-table-action'>
          <DiscussionForm
            screenMode={SCREEN_MODE.EDIT}
            addData={addComponent}
            currentData={record}
            tableData={dataS}
            editData={(e) => editProperty(e, index)} />
          <Button type="text" icon={<CustomSvgIcons name="DeleteCustomIcon" />} onClick={() => deleteRow(index)} />
        </div>
      ),
    },
  ]

  const addComponent = (data) => {
    const cloneData: any = [...dataS]
    cloneData.push(data)
    setDataSource(cloneData)
  }

  const dragProps = {
    onDragEnd(fromIndex, toIndex) {
      const data = [...dataS]
      const item = data.splice(fromIndex, 1)[0]
      data.splice(toIndex, 0, item)
      setDataSource(data)
    },
    handleSelector: 'tr',
    ignoreSelector: 'tr.ant-table-expanded-row',
    nodeSelector: 'tr.ant-table-row',
    enableScroll: true,
    scrollSpeed: 4,
  }

  return (
    <>
      <ReactDragListView {...dragProps}>
        <Table
          bordered={true}
          columns={columns}
          pagination={false}
          dataSource={dataS}
          rowKey={(record) => `create${record.id}`}
        />
      </ReactDragListView>
      <DiscussionForm
        screenMode={SCREEN_MODE.CREATE}
        addData={addComponent}
        tableData={dataS} />
    </>
  )
})

export default DiscussionTable
