import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button, Checkbox, Col, Form, Input, Row, Select, Typography
} from 'antd'
import { createRef, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import intl from '../../../../../config/locale.config'
import { DISCUSSION_TYPE, ROW_STATUS, SCREEN_MODE } from '../../../../../constants'
import CkeditorMention from '../../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import CustomModal from '../../../../../helper/component/custom-modal'
import { MeetingMinuteState } from '../../../type'
const { Option } = Select
const { Text } = Typography

interface DiscussionFormProps {
  screenMode: number
  currentData?: any
  tableData: any
  addData: any
  editData?: any
}

const DiscussionForm = ({ screenMode, currentData, tableData, addData, editData }: DiscussionFormProps) => {
  const [visible, setVisible] = useState(false)
  const [createOther, setCreateOther] = useState(false)
  const [form] = Form.useForm()
  const [modalAction, setModalAction] = useState<any[]>([])
  const getCkeditorData: any = createRef()
  const state = useSelector<AppState | null>(
    (s) => s?.MeetingMinute
  ) as MeetingMinuteState

  useEffect(() => {
    if (visible && screenMode == SCREEN_MODE.EDIT) {
      form.setFieldsValue({
        ...currentData,
      })
      setModalAction([
        <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
          {intl.formatMessage({ id: `common.action.cancel` })}
        </Button>,
        <Button className="success-btn" key="5" onClick={form.submit}>
          {intl.formatMessage({ id: 'common.action.update' })}
        </Button>,
      ])
    } else if (visible && screenMode == SCREEN_MODE.CREATE) {
      setModalAction([
        <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
          {intl.formatMessage({ id: `common.action.cancel` })}
        </Button>,
        <Checkbox key="3" checked={createOther} onChange={changeCreateOther}>
          {intl.formatMessage({ id: 'createobject.checkbox.create-another' })}
        </Checkbox>,
        <Button className="success-btn" key="5" onClick={form.submit}>
          {intl.formatMessage({ id: 'common.action.create' })}
        </Button>,
      ])
    }
  }, [visible, createOther])

  const showModal = () => {
    setVisible(true)
  }

  const handleCancel = () => {
    form.resetFields()
    setVisible(false)
  }

  const onFinish = (values: any) => {
    if (screenMode === SCREEN_MODE.CREATE) {
      const requestData = {
        id: null,
        ...values,
        details: getCkeditorData.current?.props?.data,
        actionStatus: ROW_STATUS.CREATE,
      }
      addData(requestData)
      if (createOther === false) {
        setVisible(false)
        // setCreateOther(false)
        form.resetFields()
      } else {
        // setCreateOther(false)
        form.resetFields()
      }
    } else if (screenMode === SCREEN_MODE.EDIT) {
      const requestData = {
        id: currentData.id,
        ...values,
        details: getCkeditorData.current?.props?.data,
        actionStatus:
          currentData.actionStatus !== ROW_STATUS.CREATE
            ? ROW_STATUS.UPDATE
            : currentData.actionStatus,
      }
      editData(requestData)
      setVisible(false)
      form.resetFields()
    }
  }

  const onFinishFailed = (errorInfo: any) => {
  }

  const changeCreateOther = (e) => {
    setCreateOther(e.target.checked)
  }

  return (
    <>
      {screenMode === SCREEN_MODE.CREATE && (
        <Button icon={<PlusOutlined />} type="link" onClick={showModal}>{intl.formatMessage({ id: 'discussion.label.add-discussion-item' })}</Button>
      )}
      {screenMode === SCREEN_MODE.EDIT && (
        <Button type="text" icon={<CustomSvgIcons name="EditCustomIcon" />} onClick={showModal} />
      )}

      <Form
        form={form}
        id={Date.now().toString()}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        scrollToFirstError={{ block: 'center' }}
      >
        <CustomModal
          isLoading={state.isLoading}
          closable={true}
          title={
            screenMode === SCREEN_MODE.CREATE
              ? intl.formatMessage({
                id: 'discussion.label.add-discussion-item',
              })
              : intl.formatMessage({
                id: 'discussion.label.update-discussion-item',
              })
          }
          visible={visible}
          footer={[modalAction]}
          onCancel={handleCancel}
        >
          <Row>
            <Col span={8}>
              {intl.formatMessage({
                id: 'discussion.label.type',
              })}
              <Text type="danger">
                {intl.formatMessage({
                  id: `common.mandatory.*`,
                })}
              </Text>
            </Col>
            <Col span={16}>
              <Form.Item
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ]}
                name="type"
              >
                <Select
                  filterOption={(input, option: any) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                  showSearch
                // onChange={changeType}
                >
                  {DISCUSSION_TYPE.map((item: any) => (
                    <Option key={item.id} value={item.id}>
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              {intl.formatMessage({ id: 'discussion.label.subject' })}
              <Text type="danger">
                {intl.formatMessage({
                  id: `common.mandatory.*`,
                })}
              </Text>
            </Col>
            <Col span={16}>
              <Form.Item
                validateTrigger="onBlur"
                name={`subject`}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      } else if (value) {
                        if (screenMode === SCREEN_MODE.EDIT) {
                          const dupplicate = tableData.findIndex(
                            (item: any) =>
                              item.subject
                                .toLowerCase()
                                .replace(/\s+/g, ' ')
                                .trim() ===
                              value.toLowerCase().replace(/\s+/g, ' ').trim()
                          )
                          if (
                            currentData &&
                            value.toLowerCase().replace(/\s+/g, ' ').trim() !==
                            currentData.subject
                              .toLowerCase()
                              .replace(/\s+/g, ' ')
                              .trim() &&
                            dupplicate !== -1
                          ) {
                            throw new Error(
                              `${intl.formatMessage(
                                { id: 'EMSG_7' },
                                {
                                  Artefact: `${intl.formatMessage({
                                    id: 'common.artefact.discussion-item',
                                  })}`,
                                }
                              )}`
                            )
                          }
                        } else if (screenMode === SCREEN_MODE.CREATE) {
                          const dupplicate = tableData.findIndex(
                            (item: any) =>
                              item.subject
                                .toLowerCase()
                                .replace(/\s+/g, ' ')
                                .trim() ===
                              value.toLowerCase().replace(/\s+/g, ' ').trim()
                          )
                          if (dupplicate !== -1) {
                            throw new Error(
                              `${intl.formatMessage(
                                { id: 'EMSG_7' },
                                {
                                  Artefact: `${intl.formatMessage({
                                    id: 'common.artefact.discussion-item',
                                  })}`,
                                }
                              )}`
                            )
                          }
                        }
                      }
                    },
                  },
                ]}
              >
                <Input maxLength={255} />
              </Form.Item>
            </Col>

            <Col span={24}>
              {intl.formatMessage({ id: 'discussion.label.details' })}
            </Col>
            <Col span={24}>
              <Form.Item name="details">
                <CkeditorMention
                  ref={getCkeditorData}
                  data={currentData?.details || ''}
                />
              </Form.Item>
            </Col>
          </Row>
        </CustomModal>
      </Form>
    </>
  )
}

export default DiscussionForm
