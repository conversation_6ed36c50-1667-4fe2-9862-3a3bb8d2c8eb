import { PlusOutlined } from '@ant-design/icons'
import {
  <PERSON><PERSON>, Card, Checkbox, Col, DatePicker, Form, Input, InputNumber, Modal, Row, Select, Space
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import intl from '../../../../../config/locale.config'
import { ALL_SCOPE_LIST, DATE_FORMAT, MEETING_SCOPE_TYPE_LIST, SCREEN_MODE, STATUS } from '../../../../../constants'
import CkeditorMention from '../../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import CustomModal from '../../../../../helper/component/custom-modal'
import FormGroup from '../../../../../helper/component/form-group'
import LavRelatedLinksForm from '../../../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../../helper/hooks/useWindowDimensions'
import { currentUserName, renderStatusBadge } from '../../../../../helper/share'

const { confirm } = Modal
const { Option } = Select

interface UserRequirementFormProps {
  screenMode: number
  currentData?: any
  onChange: any
}

const UserRequirementForm = ({ screenMode, currentData, onChange }: UserRequirementFormProps) => {
  const [form] = Form.useForm()
  const getCkeditorData: any = createRef()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [type, setType] = useState<any>(-1)

  useEffect(() => {
    if (isModalVisible) {
      resetForm();
      form.resetFields(['createMore']);
      form.setFieldsValue({
        assignee: currentUserName()
      })
    }
  }, [isModalVisible])


  const getList = (data: any) => {
    if (data?.source === 1) {
      const listMeetings = data?.meetingMinutes?.map((e) => e?.name)
      return listMeetings
    } else if (data?.source === 2) {
      const listReferences = data?.referenceDocumnets?.map((e) => e.name)
      return listReferences
    } else if (data?.source === 3) {
      const sourceOther = data?.sourceValueOther
      return sourceOther
    }
  }


  useEffect(() => {
    if (isModalVisible && currentData) {
      form.setFieldsValue({
        ...currentData,
        sourceValue: getList(currentData),
        // lavdate
        sendDate: currentData?.sendDate ? moment(new Date(currentData.sendDate)) : null,
      })
      setType(currentData?.type)
    }
  }, [isModalVisible, currentData])


  const onFinish = debounce((values: any, st?: string) => {
    const requestData: any = {
      id: currentData?.id || null,
      code: values.code,
      name: values.name,
      order: values.order,
      status: STATUS.DRAFT,
      type: values.type,
      source: values.source,
      sourceValueOther: values.sourceValueOther,
      scope: values.scope ? values.scope : null,
      sender: values.sender,
      sendDate: values.sendDate ? values.sendDate.toDate() : null,
      description: getCkeditorData?.current?.props?.data,
      storage: JSON.stringify({
        textToDisplay: values?.storageLinkText || '',
        address: values?.storageWebLink || '',
      }),
      jira: JSON.stringify({
        textToDisplay: values?.jiraLinkText || '',
        address: values?.jiraWebLink || '',
      }),
      confluence: JSON.stringify({
        textToDisplay: values?.confluenceLinkText || '',
        address: values?.confluenceWebLink || '',
      }),
      reqElicitation: values.reqElicitation,
      documentation: values.documentation,
      development: values.development,
      actionStatus: values.actionStatus,
      meetingMinuteIds: [],
      referenceDocumentIds: [],
      sourceOther: ''
    }
    setIsCreateMore(values.createMore);
    onChange(requestData)
    if (values.createMore) {
      resetForm();
    } else {
      setIsModalVisible(false);
      setIsCreateMore(false);
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        setIsModalVisible(false)
      },
      onCancel() { },
    })
  }

  const showModal = () => {
    setIsModalVisible(true)
  }

  const changeType = (value) => {
    setType(value)
    form.setFieldsValue({
      'scope': ''
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);

    form.resetFields([
      'code',
      `name`,
      'type',
      'source',
      `sourceValue`,
      'sourceValueOther',
      'scope',
      `sender`,
      `sendDate`,
      `description`,
      'storageLinkText',
      'storageWebLink',
      'jiraLinkText',
      'jiraWebLink',
      'confluenceLinkText',
      'confluenceWebLink',
      `reqElicitation`,
      `documentation`,
      `development`
    ])
    if (screenMode === SCREEN_MODE.CREATE) {
      form.setFieldsValue({
        'source': 1,
        'sendDate': moment(new Date(), DATE_FORMAT),
        'assignee': currentUserName()
      })
    }
  }

  return (
    <>
      {screenMode === SCREEN_MODE.CREATE && (
        <Button icon={<PlusOutlined />} type="link" onClick={showModal}>{intl.formatMessage({ id: 'user-requirement.label.add-item' })}</Button>
      )}
      {screenMode === SCREEN_MODE.EDIT && (
        <Button type="text" icon={<CustomSvgIcons name="EditCustomIcon" />} onClick={showModal} />
      )}

      <Form
        form={form}
        id={Date.now().toString()}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        scrollToFirstError={{ block: 'center' }}
      >
        <CustomModal
          closable={false}
          size="medium"
          visible={isModalVisible}
          footer={null}
        >
          <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
          >
            <div className='rq-modal-header'>
              <Row>
                <Col span={10}>
                  <Space size="large">
                    <Form.Item
                      name="name"
                      rules={[
                        {
                          required: true,
                          message: intl.formatMessage({ id: 'IEM_1' }),
                        },
                        { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                        {
                          validator: async (rule, value) => {
                            if (value && value.trim().length === 0) {
                              throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                            }
                          },
                        },
                      ]}
                    >
                      <Input
                        size="large"
                        className="modal-create-name-input-field"
                        placeholder={`${intl.formatMessage({
                          id: `user-requirement.place-holder`,
                        })}${intl.formatMessage({
                          id: `common.mandatory.*`,
                        })}`}
                        maxLength={255}
                      />
                    </Form.Item>
                    {(screenMode === SCREEN_MODE.EDIT && currentData?.id) ? renderStatusBadge(currentData?.status) : <></>}
                  </Space>
                </Col>

                <Col span={14}>
                  <Row justify="end">
                    <Space size="small">
                      {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                        style={{ marginBottom: '0px' }}
                        valuePropName="checked"
                        name="createMore"
                        wrapperCol={{ span: 24 }}
                      >
                        <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                      </Form.Item> : <></>}
                      <Button onClick={debounce(confirmCancel, 500)}>
                        {intl.formatMessage({ id: 'common.action.close' })}
                      </Button>

                      <Button className="success-btn" htmlType="submit">
                        {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.add' : 'common.action.save' })}
                      </Button>
                    </Space>
                  </Row>
                </Col>
              </Row>
            </div>

            <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 280}>
              <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'user-requirement.label.user-requirement-infomation' })}>
                  {
                    (screenMode === SCREEN_MODE.EDIT && currentData?.code) ? <Row gutter={[40, 16]}>
                      <Col span={10}>
                        <FormGroup label={intl.formatMessage({ id: 'user-requirement.column.user-requirement-code' })}>
                          <Form.Item name='code' rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                            <Input maxLength={255} value={currentData?.code} disabled />
                          </Form.Item>
                        </FormGroup>
                      </Col>
                    </Row> : <></>
                  }
                  <Row gutter={[40, 16]}>
                    <Col span={10}>
                      <FormGroup className="rq-fg-comment" required label={intl.formatMessage({ id: 'user-requirement.column.type' })}>
                        <Form.Item name="type">
                          <Select onChange={changeType}>
                            {MEETING_SCOPE_TYPE_LIST.map((item: any) => (
                              <Option key={item.id} value={item.id}>{item.name}</Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </FormGroup>
                    </Col>
                    <Col span={10}>
                      <FormGroup className="rq-fg-comment" label={intl.formatMessage({ id: 'user-requirement.column.scope' })}>
                        <Form.Item validateTrigger="onBlur" name="scope">
                          <Select>
                            {
                              ALL_SCOPE_LIST.filter((x) => x.type.includes(type)).map((s) => {
                                return <Option key={s.id} value={s.id}>{s.name}</Option>
                              })
                            }
                          </Select>
                        </Form.Item>
                      </FormGroup>
                    </Col>
                  </Row>

                  <Row gutter={[40, 16]}>
                    <Col span={10}>
                      <FormGroup className="rq-fg-comment" label={intl.formatMessage({ id: 'user-requirement.column.sender' })}>
                        <Form.Item
                          name="sender"
                          rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                        >
                          <Input maxLength={255} />
                        </Form.Item>
                      </FormGroup>
                    </Col>
                    <Col span={10}>
                      <FormGroup className="rq-fg-comment" label={intl.formatMessage({ id: 'user-requirement.column.send-date' })}>
                        <Form.Item validateTrigger="onBlur" name="sendDate">
                          <DatePicker format={DATE_FORMAT} />
                        </Form.Item>
                      </FormGroup>
                    </Col>
                  </Row>
                  <FormGroup className="rq-fg-comment" label={intl.formatMessage({ id: 'user-requirement.label.user-requirement-details' })}>
                    <Form.Item name="description">
                      <CkeditorMention
                        ref={getCkeditorData}
                        data={screenMode == SCREEN_MODE.CREATE ? '' : currentData?.description}
                      />
                    </Form.Item>
                  </FormGroup>
                </Card>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.effort' })}>
                  <FormGroup className="rq-fg-comment" inline label={intl.formatMessage({ id: 'createscreen.label.req' })}>
                    <Form.Item name="reqElicitation">
                      <InputNumber min={0} maxLength={2} />
                    </Form.Item>
                  </FormGroup>
                  <FormGroup className="rq-fg-comment" inline label={intl.formatMessage({ id: 'createscreen.label.documentation' })}>
                    <Form.Item name="documentation">
                      <InputNumber min={0} maxLength={2} />
                    </Form.Item>
                  </FormGroup>
                  <FormGroup className="rq-fg-comment" inline label={intl.formatMessage({ id: 'createscreen.label.development' })}>
                    <Form.Item name="development">
                      <InputNumber min={0} maxLength={2} />
                    </Form.Item>
                  </FormGroup>
                </Card>

                <LavRelatedLinksForm form={form} screenMode={screenMode} />
              </Space>
            </Scrollbars>
          </Form>
        </CustomModal>
      </Form>
    </>
  )
}

export default UserRequirementForm
