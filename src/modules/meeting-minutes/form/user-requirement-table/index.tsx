import {
  Button, Modal, <PERSON>,
  Typography
} from 'antd'
import React, { useEffect, useImperativeHandle, useState } from 'react'
import ReactDragListView from 'react-drag-listview'
import intl from '../../../../config/locale.config'
import { ROW_STATUS, SCOPE_TYPE_LIST, SCREEN_MODE } from '../../../../constants'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import UserRequirementForm from './form'
const { confirm } = Modal
const { Text } = Typography
interface UserRequirementTableProps {
  reloadTable: boolean
  reloadTableAfterBack: boolean
  data: any
}
const UserRequirementTable = React.forwardRef(({ data, reloadTable, reloadTableAfterBack }: UserRequirementTableProps, ref: any) => {
  const [dataS, setDataSource] = useState([])
  const [deleteList, setDeleteList] = useState<any>([])
  const modalConfirmConfig = useModalConfirmationConfig()

  useEffect(() => {
    setDataSource([])
  }, [reloadTable, reloadTableAfterBack])

  useEffect(() => {
    if (data) {
      setDataSource(data)
    }
  }, [data])

  useImperativeHandle(
    ref,
    () => ({
      getTableState: () => {
        return dataS
      },
      getDeleteList: () => {
        return deleteList
      },
    }),
    [dataS, deleteList]
  )

  const deleteRow = (record: any) => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_1' })}`,
      onOk() {
        const dataSource = [...dataS]
        const newData = dataSource.filter((item: any) => item.id !== record.id)
        if (record.status !== ROW_STATUS.CREATE) {
          deleteList.push(record.id)
        }
        setDataSource(newData)
      },
      onCancel() { },
    })
  }

  const columns = [
    {
      title: intl.formatMessage({ id: 'createscreen.column.order' }),
      dataIndex: 'order',
      width: '3%',
      key: 'order',
      render: (text, record, index) => <Text>{index + 1}</Text>,
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.user-requirement-code' }),
      dataIndex: 'code',
      key: 'code',
      width: '9.5%',
      render: (text) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'discussion.label.subject' }),
      dataIndex: 'name',
      key: 'name',
      width: '10%',
      render: (text) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.type' }),
      dataIndex: 'type',
      key: 'type',
      width: '20%',
      render: (type: number, record: any) => {
        const typeIndex = SCOPE_TYPE_LIST.findIndex((item) => item.id == type)
        if (typeIndex !== -1) {
          return <Text>{SCOPE_TYPE_LIST[typeIndex].name} </Text>
        } else {
          return <Text></Text>
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.label.user-requirement-details' }),
      dataIndex: 'description',
      key: 'description',
      width: '52.5%',
      render: (text, record) => {
        return (
          <div
            className="tableDangerous"
            dangerouslySetInnerHTML={{ __html: text }}
          ></div>
        )
      },
    },

    {
      title: intl.formatMessage({ id: 'createscreen.column.action' }),
      key: 'action',
      className: 'rq-action',
      width: '5%',
      render: (text, record, index) => (
        <div className='rq-sub-table-action'>
          <UserRequirementForm
            currentData={record}
            onChange={(e) => handleChange(e, index)}
            screenMode={SCREEN_MODE.EDIT}
          />
          <Button type="text" icon={<CustomSvgIcons name="DeleteCustomIcon" />} onClick={() => deleteRow(record)} />
        </div>
      ),
    },
  ]

  const dragProps = {
    onDragEnd(fromIndex, toIndex) {
      const data = [...dataS]
      const item = data.splice(fromIndex, 1)[0]
      data.splice(toIndex, 0, item)
      setDataSource(data)
    },
    handleSelector: 'tr',
    ignoreSelector: 'tr.ant-table-expanded-row',
    nodeSelector: 'tr.ant-table-row',
    enableScroll: true,
    scrollSpeed: 4,
  }

  const handleChange = (e, index) => {
    let newData: any = [...dataS]
    if (index !== undefined) {
      newData[index] = e
    } else {
      newData.push(e)
    }
    setDataSource(newData)
  }

  return (
    <>
      <ReactDragListView {...dragProps}>
        <Table
          bordered={true}
          columns={columns}
          pagination={false}
          dataSource={dataS}
          rowKey={(record) => `create${record.id}`}
        />
      </ReactDragListView>
      <UserRequirementForm
        screenMode={SCREEN_MODE.CREATE}
        onChange={handleChange}
      // tableData={dataS}
      ></UserRequirementForm>
    </>
  )
})

export default UserRequirementTable
