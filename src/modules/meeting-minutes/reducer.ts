import { createReducer } from '@reduxjs/toolkit';
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListMeetingMinutesFailed, getListMeetingMinutesRequest, getListMeetingMinutesSuccess, getListReferenceDocumentsFailed, getListReferenceDocumentsRequest, getListReferenceDocumentsSuccess, getListRequest,
  getListSuccess, resetState, setModalVisible, updateFailed, updateRequest,
  updateSuccess
} from './action';
import { defaultState, MeetingMinuteState } from './type';

const initState: MeetingMinuteState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getListReferenceDocumentsRequest, (state, action?) => {
        state.referenceDocuments = null;
      })
      .addCase(getListReferenceDocumentsSuccess, (state, action) => {
        state.referenceDocuments = action.payload
      })
      .addCase(getListReferenceDocumentsFailed, (state, action) => {
        state.referenceDocuments = []
      })

      .addCase(getListMeetingMinutesRequest, (state, action?) => {
        state.meetingMinutes = null;
      })
      .addCase(getListMeetingMinutesSuccess, (state, action) => {
        state.meetingMinutes = action.payload
      })
      .addCase(getListMeetingMinutesFailed, (state, action) => {
        state.meetingMinutes = []
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })

      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })


      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if(!action.payload){
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })
  )
})

export default reducer
export { initState as MeetingMinuteState };

