import { createAction } from '@reduxjs/toolkit';
import {ActionEnum} from './type';

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<any>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<any>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getListReferenceDocumentsRequest = createAction<any>(ActionEnum.GET_LIST_REFERENCE_DOCUMENTS_REQUEST);
export const getListReferenceDocumentsSuccess = createAction<any>(ActionEnum.GET_LIST_REFERENCE_DOCUMENTS_SUCCESS);
export const getListReferenceDocumentsFailed = createAction<any>(ActionEnum.GET_LIST_REFERENCE_DOCUMENTS_FAILED);

export const getListMeetingMinutesRequest = createAction<any>(ActionEnum.GET_LIST_MEETING_MINUTES_REQUEST);
export const getListMeetingMinutesSuccess = createAction<any>(ActionEnum.GET_LIST_MEETING_MINUTES_SUCCESS);
export const getListMeetingMinutesFailed = createAction<any>(ActionEnum.GET_LIST_MEETING_MINUTES_FAILED);

export const getDetailRequest = createAction<any>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<any>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const createRequest = createAction<any>(ActionEnum.CREATE_REQUEST);
export const createSuccess = createAction<any>(ActionEnum.CREATE_SUCCESS);
export const createFailed = createAction<any>(ActionEnum.CREATE_FAILED);

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST);
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS);
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED);

export const deleteRequest = createAction<any>(ActionEnum.DELETE_REQUEST);
export const deleteSuccess = createAction<any>(ActionEnum.DELETE_SUCCESS);
export const deleteFailed = createAction<any>(ActionEnum.DELETE_FAILED);

export const setModalVisible = createAction<any>(ActionEnum.SET_MODAL_VISIBLE)