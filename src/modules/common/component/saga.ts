import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../../constants'
import { apiCall } from '../../../helper/api/aloApi'
import { ShowAppMessage } from '../../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListRefsFailed, getListRefsRequest, getListRefsSuccess, getListRequest,
  getListSuccess, getMissingFailed, getMissingRequest, getMissingSuccess, getObjectsFailed, getObjectsRequest, getObjectsSuccess, getScreensFailed, getScreensRequest, getScreensSuccess, getUsecasesFailed, getUsecasesRequest, getUsecasesSuccess, getWarningMessageFailed, getWarningMessageRequest, getWarningMessageSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.COMMON_COMPONENT}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_COMPONENT + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_COMPONENT + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.common-component')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-component')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.COMMON_COMPONENT, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.common-component')
      yield put(createSuccess(null));
    } catch (err: any) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-component', 'common_component.column.name-short')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.COMMON_COMPONENT + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.common-component')
      yield put(updateSuccess(null));
    } catch (err: any) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-component', 'common_component.column.name-short')
    }
  }
}

function* handleGetListRefs(action: Action) {
  if (getListRefsRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_REFERENCES_ARTEFACT + '?search=' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getListRefsSuccess(res.data));
    } catch (err) {
      yield put(getListRefsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListMissing(action: Action) {
  if (getMissingRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'POST', API_URLS.COMMON_REFERENCES_ARTEFACT_MISSING, action.payload)
      yield put(getMissingSuccess(res.data));
    } catch (err) {
      yield put(getMissingFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListWarningMessage(action: Action) {
  if (getWarningMessageRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'POST', API_URLS.COMMON_REFERENCES_ARTEFACT_MISSING, action.payload)
      yield put(getWarningMessageSuccess(res.data));
    } catch (err) {
      yield put(getWarningMessageFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListObjects(action: Action) {
  if (getObjectsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.COMMON_REFERENCE_OBJECTS, action.payload)
      yield put(getObjectsSuccess(res.data));
    } catch (err) {
      yield put(getObjectsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListUsecases(action: Action) {
  if (getUsecasesRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.COMMON_REFERENCE_USECASE, action.payload)
      yield put(getUsecasesSuccess(res.data));
    } catch (err) {
      yield put(getUsecasesFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListScreens(action: Action) {
  if (getScreensRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.COMMON_REFERENCE_SCREENS, action.payload)
      yield put(getScreensSuccess(res.data));
    } catch (err) {
      yield put(getScreensFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
  yield takeLatest(getListRefsRequest.type, handleGetListRefs)
  yield takeLatest(getMissingRequest.type, handleGetListMissing)
  yield takeLatest(getWarningMessageRequest.type, handleGetListWarningMessage)
  yield takeLatest(getObjectsRequest.type, handleGetListObjects)
  yield takeLatest(getUsecasesRequest.type, handleGetListUsecases)
  yield takeLatest(getScreensRequest.type, handleGetListScreens)
}
export default function* CommonComponentSaga() {
  yield all([fork(watchFetchRequest)])
}
