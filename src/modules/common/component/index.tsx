import { Space } from 'antd'
import React, { FC, useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS_COMMON_FILTER } from '../../../constants'
import LavTable from '../../../helper/component/lav-table'
import { getColumnDropdownFilterProps, getColumnSearchProps, renderCommonStatusBadge } from '../../../helper/share'
import AppCommonService from '../../../services/app.service'
import CommonComponentForm from './form'

const CommonComponent: FC = () => {
    const [columns, setColumns] = useState<any>(null)
    useEffect(() => {
        AppCommonService.getData3List(
            API_URLS.COMMON_REFERENCE_OBJECTS,
            API_URLS.COMMON_REFERENCE_USECASE,
            API_URLS.COMMON_REFERENCE_SCREENS
        ).then((res) => {
            const lstObjects = res[0].map(e => { return { value: e.id, text: e.name } })
            const lstUsecases = res[1].map(e => { return { value: e.id, text: e.name } })
            const lstScreens = res[2].map(e => { return { value: e.id, text: e.name } })
            setColumns(configColumns(lstObjects, lstUsecases, lstScreens));
        }).catch(err => {
            setColumns(configColumns([], [], []))
        })
    }, [])

    const configColumns = (objs, usecases, screens) => [
        {
            title: intl.formatMessage({ id: 'common.action.code' }),
            dataIndex: 'code',
            width: '85px',
            editable: true,
            sorter: true,
            sortOrder: 'descend',
            ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
            render: (text: string, record: any) => {
                const href = `${APP_ROUTES.COMMON_COMPONENT_DETAIL}` + record.id
                return <Link to={href}>{text}</Link>
            },
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.component_name' }),
            dataIndex: 'name',
            ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
            sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.description' }),
            dataIndex: 'description',
            width:'20%',
            ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
            render: (text, item: any) => {
                return <div style={{ maxWidth: '400px', overflow: 'auto' }} dangerouslySetInnerHTML={{ __html: text }} />
            }
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.object' }),
            dataIndex: 'objects',
            width:'15%',
            ...getColumnDropdownFilterProps(objs, 'ObjectId', true),
            render: (lst: any, record: any) => {
                if (lst && lst.length > 0) {
                    return <>
                        {
                            lst.map((e: any, index) => {
                                return e ? <Link key={e.id} to={`${APP_ROUTES.COMMON_OBJECT_DETAIL}${e.id}`}>{index !== 0 ? `,` : ``} {e.name}</Link> : <></>
                            })
                        }
                    </>
                } else {
                    return <></>
                }

            }
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.use_case' }),
            dataIndex: 'useCases',
            width:'15%',
            ...getColumnDropdownFilterProps(usecases, 'UseCaseId', true),
            render: (lst: any, record: any) => {
                if (lst && lst.length > 0) {
                    return <>
                        {
                            lst?.map((e: any, index) => {
                                return e ? <Link key={e.id} to={`${APP_ROUTES.COMMON_USECASE_DETAIL}${e.id}`}>{index !== 0 ? `,` : ``} {e.name}</Link> : <></>
                            })
                        }
                    </>
                } else {
                    return <></>
                }
            }
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.screen' }),
            dataIndex: 'screens',
            width:'15%',
            ...getColumnDropdownFilterProps(screens, 'ScreenId', true),
            render: (lst: any, record: any) => {
                if (lst && lst.length > 0) {
                    return <>
                        {
                            lst?.map((e: any, index) => {
                                return e ? <Link key={e.id} to={`${APP_ROUTES.COMMON_SCREEN_DETAIL}${e.id}`}>{index !== 0 ? `,` : ``} {e.name}</Link> : <></>
                            })
                        }
                    </>
                } else {
                    return <></>
                }
            }
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.status' }),
            width: '80px',
            dataIndex: 'status',
            ...getColumnDropdownFilterProps(STATUS_COMMON_FILTER, 'status'),
            sorter: true,
            render: (record) => renderCommonStatusBadge(record),
        },
    ]

    const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
        return <CommonComponentForm onFinish={() => handleDataChange()} />
    }

    const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
        return <CommonComponentForm onFinish={() => handleDataChange()} screenMode={SCREEN_MODE.EDIT} componentID={record.id} buttonType={BUTTON_TYPE.ICON} />
    }

    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return children;
    }

    return (
        <Space direction="vertical" size="middle" className="full-width p-20px">
            {
                columns ? <LavTable
                    showBreadcumb={false}
                    title="common_component.page_title"
                    artefact_type="common.artefact.common-component"
                    apiUrl={API_URLS.COMMON_COMPONENT}
                    isCommon={true}
                    artefactType={COM_ARTEFACT_TYPE_ID.COMPONENT}
                    columns={columns}
                    updateComponent={UpdateComponent}
                    createComponent={CreateComponent}
                    deleteComponent={DeleteComponent}
                /> : <></>
            }
        </Space>
    )
}

export default CommonComponent
