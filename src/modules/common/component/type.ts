export interface CommonComponentState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: CommonComponentDetail | null,
  selectedData?: CommonComponentDetail | null,

  listObjects?: any[] | null,
  isLoadingObjects?: boolean,

  listFunctions?: any[] | null,
  isLoadingFunctions?: boolean,

  listScreens?: any[] | null,
  isLoadingScreens?: boolean,

  listRefs?: any[],
  isLoadingRefs?: boolean,

  isModalShow?: boolean

  warningMessage?: any,
  isLoadingWarningMessage?: boolean,

  missing?: any,
  isLoadingMissing?: boolean,
}
export interface CommonComponentDetail {
  id?: number | null,
  name: string,
  status: number,
  code: string,
  scope: number,
  description: string,
  warningMessage: string,
  auditTrails: any[]
  objects: any[],
  screens: any[],
  useCases: any[],
  others: any[],
  commonBusinessRules?: any[],
  emailTemplates?: any[],
  messages?: any[],
  workflows?: any[]
  nonFunctionRequirements?: any[]
}

export const defaultState: CommonComponentState = {
  detail: {
    id: null,
    name: '',
    status: 0,
    code: '',
    scope: 1,
    description: '',
    warningMessage: '',
    objects: [],
    screens: [],
    useCases: [],
    others: [],
    commonBusinessRules: [],
    emailTemplates: [],
    messages: [],
    nonFunctionRequirements: [],
    auditTrails: [],
    workflows: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  listObjects: null,
  isLoadingObjects: false,
  listFunctions: null,
  isLoadingFunctions: false,
  listScreens: null,
  isLoadingScreens: false,
  listRefs: [],
  isLoadingRefs: false,
  warningMessage: null,
  isLoadingWarningMessage: false,
  missing: null,
  isLoadingMissing: false
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_COMPONENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_COMPONENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_COMPONENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_COMPONENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_COMPONENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_COMPONENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_COMPONENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_COMPONENT/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/COMMON_COMPONENT/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_COMPONENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_COMPONENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_COMPONENT/DELETE_FAILED',

  GET_LIST_REFS_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_LIST_REFS_REQUEST',
  GET_LIST_REFS_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_LIST_REFS_SUCCESS',
  GET_LIST_REFS_FAILED = '@@MODULES/COMMON_COMPONENT/GET_LIST_REFS_FAILED',

  GET_WARNING_MESSAGE_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_WARNING_MESSAGE_REQUEST',
  GET_WARNING_MESSAGE_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_WARNING_MESSAGE_SUCCESS',
  GET_WARNING_MESSAGE_FAILED = '@@MODULES/COMMON_COMPONENT/GET_WARNING_MESSAGE_FAILED',

  GET_MISSTING_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_MISSTING_REQUEST',
  GET_MISSTING_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_MISSTING_SUCCESS',
  GET_MISSTING_FAILED = '@@MODULES/COMMON_COMPONENT/GET_MISSTING_FAILED',

  GET_OBJECTS_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_OBJECTS_REQUEST',
  GET_OBJECTS_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_OBJECTS_SUCCESS',
  GET_OBJECTS_FAILED = '@@MODULES/COMMON_COMPONENT/GET_OBJECTS_FAILED',

  GET_USECASES_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_USECASES_REQUEST',
  GET_USECASES_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_USECASES_SUCCESS',
  GET_USECASES_FAILED = '@@MODULES/COMMON_COMPONENT/GET_USECASES_FAILED',

  GET_SCREENS_REQUEST = '@@MODULES/COMMON_COMPONENT/GET_SCREENS_REQUEST',
  GET_SCREENS_SUCCESS = '@@MODULES/COMMON_COMPONENT/GET_SCREENS_SUCCESS',
  GET_SCREENS_FAILED = '@@MODULES/COMMON_COMPONENT/GET_SCREENS_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/COMMON_COMPONENT/SET_MODAL_VISIBLE',
}
