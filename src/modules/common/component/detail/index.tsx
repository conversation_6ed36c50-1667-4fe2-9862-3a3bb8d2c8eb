import { extractProjectCode } from '../../../../helper/share'
import { Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { API_URLS, APP_ROUTES, COM_ARTEFACT_TYPE_ID, PROJECT_PREFIX, SCREEN_MODE } from '../../../../constants'
import AppState from '../../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import CommonComponentForm from '../form'
import { CommonComponentState } from '../type'
import LavLeftControl from './../../../_shared/left-menu'
import RightControl from './content'
import HistoryScreen from '../../../../modules/history'
import AppCommonService from '../../../../services/app.service'
import CommonComponentVersionDetails from './history/details'

const CommonComponentDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.CommonComponent) as CommonComponentState;
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.componentID) {
      dispatch(getDetailRequest(props.match.params.componentID))
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.COMMON_COMPONENT + '/' + props.match.params.componentID +  '/' + selectedRowVersion).then((e) => {
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);    
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])


  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(APP_ROUTES.COMMON_COMPONENT)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true)
    dispatch(getDetailRequest(props.match.params.componentID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if(isCreate) {
      setIsCreate(false)
      history.push(`${APP_ROUTES.COMMON_COMPONENT_DETAIL}` + items[0].id)
    }
  }
  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
        <Col span={5}>
          <LavLeftControl
            activeId={props.match.params.componentID}
            apiUrl={API_URLS.COMMON_REFERENCE_COMPONENTS}
            route={APP_ROUTES.COMMON_COMPONENT_DETAIL}
            title='common_component.page_title'
            artefactType={COM_ARTEFACT_TYPE_ID.COMPONENT}
            reload={reload}
            reloadSuccess={() => setReload(false)}
            isCommon
            handleCreate={handleCreate}
          >
            <CommonComponentForm onFinish={() => {setReload(true); setIsCreate(true)}} />
          </LavLeftControl>
        </Col>
        </>
        : <></>
      }
      {screenMode === SCREEN_MODE.VIEW ?
        <>          
          <Col span={19}>
            <RightControl onChange={handleReloadData} setScreenMode={setScreenMode}  isLoading={state?.isLoading} data={state?.selectedData} componentID={props.match.params.componentID} isModalShow={state?.isModalShow} />
          </Col>
        </> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.common-component"
                            apiURL = {API_URLS.COMMON_HISTORY} isCommon = {true}
                            artefactType = {COM_ARTEFACT_TYPE_ID.COMPONENT}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.name!}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
              <CommonComponentVersionDetails componentID={props.match.params.componentID} setSelectedRowVersion = {setSelectedRowVersion} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }      
    </Row>
  )
}

export default CommonComponentDetail
