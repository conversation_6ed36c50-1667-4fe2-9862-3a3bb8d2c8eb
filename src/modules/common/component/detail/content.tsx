import AppState from '@/store/types'
import {
    <PERSON>read<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Table, Typography
} from 'antd'
import { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import DeleteButton from '../../../../helper/component/commonButton/DeleteButton'
import LavButtons from '../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { renderCommonStatusBadge } from '../../../../helper/share'
import { listCategoryDetail } from '../../../../modules/common/non-functional-requirement/type'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { deleteRequest } from '../action'
import CommonComponentForm from '../form'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    componentID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any,
}
const RightControl = ({ data, componentID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const [dataSource, setDataSource] = useState<any[]>([])

    useEffect(() => {
        let rs: any[] = [];
        if (data?.objects) {
            data?.objects.forEach((e) => {
                rs.push({ ...e, link: APP_ROUTES.COMMON_OBJECT_DETAIL + e.id, field: 'objects' })
            })
        }
        if (data?.useCases) {
            data?.useCases.forEach((e) => {
                rs.push({ ...e, link: APP_ROUTES.COMMON_USECASE_DETAIL + e.id, field: 'function' })
            })
        }
        if (data?.screens) {
            data?.screens.forEach((e) => {
                rs.push({ ...e, link: APP_ROUTES.COMMON_SCREEN_DETAIL + e.id, field: 'screens' })
            })
        }
        if (data?.commonBusinessRules) {
            data?.commonBusinessRules.forEach((e) => {
                rs.push({ ...e, link: APP_ROUTES.COMMON_CBR_DETAIL + e.id, field: 'others' })
            })
        }
        if (data?.emailTemplates) {
            data?.emailTemplates.forEach((e) => {
                rs.push({ ...e, link: APP_ROUTES.COMMON_EMAIL_DETAIL + e.id, field: 'others' })
            })
        }
        if (data?.messages) {
            data?.messages.forEach((e) => {
                rs.push({ ...e, name: e.name, link: APP_ROUTES.COMMON_MESSAGE_DETAIL + e.id, field: 'others' })
            })
        }
        if (data?.nonFunctionRequirements) {
            data?.nonFunctionRequirements.forEach((e) => {
                const category = listCategoryDetail.find(
                    (category: any) => category.id === e?.category
                )
                rs.push({ ...e, name: category?.name, link: APP_ROUTES.COMMON_NONFUNCTIONAL_DETAIL + e.id, field: 'others' })
            })
        }
        if (data?.workflows) {
            data?.workflows.forEach((e) => {
                rs.push({ ...e, name: e.name, link: APP_ROUTES.COMMON_WORKFLOW_DETAIL + e.id, field: 'others' })
            })
        }
        setDataSource(rs);
    }, [data])

    const columns = [
        {
            title: intl.formatMessage({ id: 'common.action.code' }),
            dataIndex: 'code',
            width: 150,
            key: 'code',
            render: (text, item: any) => {
                return item.status !== STATUS_COMMON.RECOMMEND ? <TriggerComment field={item.field}>
                    <Link to={item.link}>{text}</Link>
                </TriggerComment> : text
            }
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.component_name' }),
            dataIndex: 'name',
            key: 'name',
            render: (text, item) => {
                const category = listCategoryDetail.find(
                    (category: any) => category.id === item?.category
                )
                return text ? text : item.objective ? item.objective : item.content ? item.content : category?.name ? category.name : ''
            }
        },
        {
            title: intl.formatMessage({ id: 'common_component.column.description' }),
            dataIndex: 'description',
            key: 'description',
            render: text => {
                return <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
            }
        },
    ];

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'scope', title: intl.formatMessage({ id: 'state.scope' }), },
            { field: 'description', title: intl.formatMessage({ id: 'common_component.column.description' }), },
            { field: 'warning_message', title: intl.formatMessage({ id: 'common_component.column.warning_message' }), },
            { field: 'objects', title: intl.formatMessage({ id: 'common_component.card.objects' }), },
            { field: 'function', title: intl.formatMessage({ id: 'common_component.card.function' }), },
            { field: 'screens', title: intl.formatMessage({ id: 'common_component.card.screens' }), },
            { field: 'others', title: intl.formatMessage({ id: 'common_component.card.others' }), },
            { field: 'missing_mentioned', title: intl.formatMessage({ id: 'common_component.card.missing_mentioned' }), },
            { field: 'missing_referenced', title: intl.formatMessage({ id: 'common_component.card.missing_referenced' }), },

        ];
        dispatch(initComment({ projectId: null, itemId: data?.id, fields }));

        const payload = {
            projectId: null,
            itemId: data?.id,
            artefact: ARTEFACT_COMMENT.COMMON_COMPONENT,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT

    return (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                        <Breadcrumb.Item>
                            <Link to={APP_ROUTES.COMMON_COMPONENT}>{intl.formatMessage({ id: 'common_component.page_title' })}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.name}
                    </Title>
                </div>

                <Space size="small">
                    <LavButtons
                        isCommon={true}
                        isHasReject={true}
                        isHasApprove={true}
                        isCommonComponent={true}
                        artefactType={COM_ARTEFACT_TYPE_ID.COMPONENT}
                        id={componentID}
                        url={`${API_URLS.COMMON_COMPONENT}/${componentID}`}
                        artefact_type="common.artefact.common-component"
                        status={data?.status}
                        changePage={() => onChange()}>
                        {/* Delete record */}
                        {data?.status != STATUS_COMMON.DELETED && <DeleteButton
                            type={BUTTON_TYPE.TEXT}
                            content={intl.formatMessage(
                                { id: 'CFD_7' },
                                { artefact_type: intl.formatMessage({ id: 'common.artefact.common-component' }) }
                            )}
                            okCB={() => dispatch(deleteRequest(componentID))}
                            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} />}

                        {/* Update record */}
                        {(data?.status !== STATUS_COMMON.DELETED) && (
                            <CommonComponentForm
                                screenMode={SCREEN_MODE.EDIT}
                                buttonType={BUTTON_TYPE.TEXT}
                                onFinish={() => onChange()}
                                componentID={parseInt(componentID)} />
                        )}
                    </LavButtons>
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="small">
                        <Row>
                            <Col span={4}>
                                <TriggerComment field='version'>                               
                                    <Text type="secondary">
                                        {intl.formatMessage({ id: `common.label.version` })}
                                    </Text>
                                </TriggerComment>                                
                            </Col>
                            <Col span={20}>                                       
                                <a onClick={() => {
                                    setScreenMode(SCREEN_MODE.HISTORY)
                                }}>
                                    {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                </a>
                            </Col>
                            <Col span={4}>
                                <Text type="secondary">
                                    {intl.formatMessage({ id: 'common_component.column.status', })}
                                </Text>
                            </Col>
                            <Col span={20}>{renderCommonStatusBadge(data?.status)}</Col>

                            <Col span={4}>
                                <TriggerComment field='scope'>
                                    <Text type="secondary">
                                        {intl.formatMessage({ id: 'state.scope', })}
                                    </Text>
                                </TriggerComment>
                            </Col>
                            <Col span={20}>
                                {intl.formatMessage({
                                    id: data?.scope == 0 ? 'common_component.scope.all_projects' :
                                        'common_component.scope.current_customer'
                                })}
                            </Col>
                            <Col span={4}>
                                <TriggerComment field='description'>
                                    <Text type="secondary">
                                        {intl.formatMessage({ id: 'common_component.column.description', })}
                                    </Text>
                                </TriggerComment>
                            </Col>
                            <Col span={20}>
                                <div dangerouslySetInnerHTML={{ __html: data?.description }} />
                            </Col>

                            <Col span={20}>
                                {data?.warningMessage}
                            </Col>

                        </Row>
                        <Card
                            title={<Title level={5}>{intl.formatMessage({ id: 'common_component.column.properties' })}</Title>}
                            bordered={true}>
                            <Table dataSource={dataSource} columns={columns} />
                        </Card>

                        <LavReferences data={data} isCommon />
                        <LavCommonAuditTrail data={data?.auditTrails} />
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    )
}

export default RightControl
