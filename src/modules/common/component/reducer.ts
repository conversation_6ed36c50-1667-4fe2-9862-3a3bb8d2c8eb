import { createReducer } from '@reduxjs/toolkit'
import {
  getDetailRequest,
  getDetailSuccess,
  getDetailFailed,
  createRequest,
  createSuccess,
  createFailed,
  updateRequest,
  updateSuccess,
  updateFailed,
  getListRequest,
  getListSuccess,
  getListFailed,
  deleteRequest,
  deleteSuccess,
  deleteFailed,
  resetState,
  getListRefsRequest,
  getListRefsSuccess,
  getListRefsFailed,
  setModalVisible,
  getMissingRequest,
  getMissingSuccess,
  getMissingFailed,
  getWarningMessageRequest,
  getWarningMessageSuccess,
  getWarningMessageFailed,
  getObjectsRequest,
  getObjectsSuccess,
  getObjectsFailed,
  getUsecasesRequest,
  getUsecasesSuccess,
  getUsecasesFailed,
  getScreensRequest,
  getScreensSuccess,
  getScreensFailed,
} from './action';
import { CommonComponentState, defaultState } from './type';

const initState: CommonComponentState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })


      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })


      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })

      .addCase(getListRefsRequest, (state, action?) => {
        state.isLoadingRefs = true;
      })
      .addCase(getListRefsSuccess, (state, action) => {
        state.isLoadingRefs = false
        state.listRefs = action.payload
      })
      .addCase(getListRefsFailed, (state, action) => {
        state.isLoadingRefs = false
        state.listRefs = []
      })
      .addCase(getMissingRequest, (state, action?) => {
        state.isLoadingMissing = true;
      })
      .addCase(getMissingSuccess, (state, action) => {
        state.isLoadingMissing = false
        state.missing = action.payload
      })
      .addCase(getMissingFailed, (state, action) => {
        state.isLoadingMissing = false
        state.missing = null
      })
      
      .addCase(getWarningMessageRequest, (state, action?) => {
        state.isLoadingWarningMessage = true;
      })
      .addCase(getWarningMessageSuccess, (state, action) => {
        state.isLoadingWarningMessage = false
        state.warningMessage = action.payload
      })
      .addCase(getWarningMessageFailed, (state, action) => {
        state.isLoadingWarningMessage = false
        state.warningMessage = null
      })

      .addCase(getObjectsRequest, (state, action) => {
        state.isLoadingObjects = true
      })
      .addCase(getObjectsSuccess, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = action.payload
      })
      .addCase(getObjectsFailed, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = []
      })

      .addCase(getUsecasesRequest, (state, action) => {
        state.isLoadingFunctions = true
      })
      .addCase(getUsecasesSuccess, (state, action) => {
        state.isLoadingFunctions = false
        state.listFunctions = action.payload
      })
      .addCase(getUsecasesFailed, (state, action) => {
        state.isLoadingFunctions = false
        state.listFunctions = []
      })

      .addCase(getScreensRequest, (state, action) => {
        state.isLoadingScreens = true
      })
      .addCase(getScreensSuccess, (state, action) => {
        state.isLoadingScreens = false
        state.listScreens = action.payload
      })
      .addCase(getScreensFailed, (state, action) => {
        state.isLoadingScreens = false
        state.listScreens = []
      })

      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if(!action.payload){
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })
  )
})

export default reducer
export { initState as CommonComponentState }
