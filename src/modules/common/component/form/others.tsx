import { SCREEN_MODE } from '@/constants';
import { <PERSON>ton, Card, Table, Tooltip } from 'antd';
import intl from '../../../../config/locale.config';
import CustomSvgIcons from '../../../../helper/component/custom-icons';
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment';

interface CommonComponentOthers {
  dataSource?: any,
  onChange?: any,
  screenMode?: SCREEN_MODE,
  onSelect?: any
  selectedArtefact?: any
}

const CommonComponentOthers = (props: CommonComponentOthers) => {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      render: (text, item) => {
        return <div onClick={() => props.onSelect(item)}
          className={props.selectedArtefact?.artefactType === item.type && props.selectedArtefact?.id === item.id ? 'selected-preview' : ''}
          style={{ maxWidth: 200, color: '#2979FF', cursor: 'pointer' }}><Tooltip title={item.name || item.code}>{item.name || item.code}</Tooltip></div>
      }
    },
    {
      title: '',
      dataIndex: 'name',
      render: (text, item, index) => {
        return <Button icon={<CustomSvgIcons name="DeleteCustomIcon" />} type="link" onClick={() => handleRemove(index)}></Button>
      }
    },
  ]

  const handleRemove = (index) => {
    let currentData = Object.assign([], props.dataSource);
    if (currentData.length <= 1) {
      currentData = []
    } else {
      currentData.splice(index, 1);
    }
    props.onChange(currentData);
  }
  return (
    <Card className='rq-form-block rq-form-block-p0' title={<TriggerComment screenMode={props.screenMode} field="others">
      {intl.formatMessage({ id: 'common_component.card.others' })}
    </TriggerComment>}>
      <Table
        locale={{
          emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
          filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
        }}
        className="lav-table"
        bordered
        dataSource={props.dataSource}
        columns={columns}
        rowKey='id'
        pagination={props.dataSource?.length <= 5 ? false : {
          pageSize: 5,
          total: props.dataSource?.length,
          size: 'small',
          showLessItems: true,
          position: ['bottomRight'],
          // showTotal: (total, range) =>
          //   `${range[0]}-${range[1]} ${intl.formatMessage({
          //     id: 'common.table.pagination.of',
          //   })} ${total} ${intl.formatMessage({
          //     id: 'common.table.pagination.items',
          //   })}`
        }}
        loading={false}
      />
    </Card>
  )
}

export default CommonComponentOthers
