import { Card } from 'antd'
import intl from '../../../../config/locale.config'
import ObjectPropertiesTable from '../../../objects/detail/object-properties'

const CommonComponentUPD = () => {
  return (
    <Card style={{ width: '100%', height: 282 }} className='rq-form-block' title={intl.formatMessage({ id: 'common_component.card.user_profile_detail' })}>
      {/* <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Debitis rerum placeat quam facilis, fuga unde ad soluta est labore deleniti nobis veniam enim molestiae, animi fugiat quasi. Nisi, maxime facilis?</p> */}
      <ObjectPropertiesTable dataSource={[]} />
    </Card>
  )
}

export default CommonComponentUPD
