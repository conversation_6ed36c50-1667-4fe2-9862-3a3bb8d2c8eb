import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  AutoComplete, Button, Card, Checkbox, Col, Form, Input, Modal, notification, Row, Select, Space, Spin
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_COMMON_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, COMPONENT_SCOPE, COM_ARTEFACT_TYPE_ID, LIST_NFR_CATEGORY, MESSAGE_TYPES, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { commonPrefixType, getReferencesFromEditor, hasCommonRole, renderCommonStatusBadge } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import AppCommonService from '../../../../services/app.service'
import CommonCBRDetailInfo from '../../business-rule/detail/content-preview'
import CommonEmailDetailInfo from '../../email-templates/detail/content-preview'
import CommonMessDetailInfo from '../../messages/detail/content-preview'
import CommonNonFunctonalDetailInfo from '../../non-functional-requirement/detail/content-preview'
import CommonObjectDetailInfo from '../../object/detail/content-preview'
import CommonScreenDetailInfo from '../../screen/detail/content-preview'
import CommonUseCaseDetailInfo from '../../usecase/detail/content-preview'
import CommonWorkflowDetailInfo from '../../workflow/detail/content-preview'
import { createRequest, getDetailRequest, getListRefsRequest, getMissingRequest, getWarningMessageRequest, resetState, setModalVisible, updateRequest } from '../action'
import { CommonComponentState } from '../type'
import CommonComponentFunction from './function'
import CommonComponentMissingMentioned from './missing-mentioned'
import CommonComponentMissingReferenced from './missing-referenced'
import CommonComponentObjects from './objects'
import CommonComponentOthers from './others'
import CommonComponentScreens from './screens'
const reqNoti = notification

const { confirm } = Modal
const { Option } = Select

interface CommonComponentFormProps {
  componentID?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface CommonComponentFormModalProps {
  componentID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}
const CommonComponentFormModal = ({ componentID, screenMode, onFinish, onDismiss }: CommonComponentFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorData: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.CommonComponent) as CommonComponentState
  const [isDraft, setIsDraft] = useState(false);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [inputSearch, setInputSearch] = useState('');

  const [objects, setObjects] = useState<any>([]);
  const [functions, setFunctions] = useState<any>([]);
  const [screens, setScreens] = useState<any>([]);
  const [others, setOthers] = useState<any>([]);
  const [options, setOptions] = useState<any>([]);
  const [selectedInput, setSelectedInput] = useState<any>(null);
  const [missing, setMissing] = useState<any>(null);
  const [warningMessage, setWarningMessage] = useState<any>(null);
  const [selectedArtefact, setSelectedArtefact] = useState<any>(null);
  const categoryName = (catId) => {
    const cat = LIST_NFR_CATEGORY.find(
      (category: any) => category.id === catId
    )
    return cat?.name || ''
  }

  // Destroy
  useEffect(() => {
    form.setFieldsValue({
      'scope': 0,
    })
    return () => {
      dispatch(resetState(null));
      setInputSearch('');
      setOptions([]);
      setObjects([]);
      setFunctions([]);
      setScreens([]);
      setOthers([]);
      resetForm();
      setMissing(null);
      setWarningMessage(null);
    }
  }, [])

  useEffect(() => {
    if (componentID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(componentID))
    }
  }, [screenMode, componentID])

  useEffect(() => {
    if (componentID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        code: state.detail.code,
        name: state.detail.name,
        scope: state.detail.scope,
        description: state.detail.description,
      })
      const lstObjects: any[] = state.detail.objects?.map((e) => {
        return { ...e, value: e.id, label: e.name, type: COM_ARTEFACT_TYPE_ID.OBJECT }
      });
      const lstScreens: any[] = state.detail.screens?.map((e) => {
        return { ...e, value: e.id, label: e.name, type: COM_ARTEFACT_TYPE_ID.SCREEN }
      });
      const lstUsecases: any[] = state.detail.useCases?.map((e) => {
        return { ...e, value: e.id, label: e.name, type: COM_ARTEFACT_TYPE_ID.USECASE }
      });

      const lstCBR: any[] = state.detail.commonBusinessRules?.map((e) => {
        return { ...e, value: e.id, label: 'CBR - ' + e.name, name: 'CBR - ' + e.name, type: COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE }
      }) || [];
      const lstEmails: any[] = state.detail.emailTemplates?.map((e) => {
        return { ...e, value: e.id, label: 'ET - ' + e.name, name: 'ET - ' + e.name, type: COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE }
      }) || [];
      const lstMessages: any[] = state.detail.messages?.map((e) => {
        return { ...e, value: e.id, label: 'MSG - ' + (e.name || e.code), name: 'MSG - ' + (e.name || e.code), type: COM_ARTEFACT_TYPE_ID.MESSAGE }
      }) || [];
      const lstNFR: any[] = state.detail.nonFunctionRequirements?.map((e) => {
        return { ...e, value: e.id, label: 'NFR - ' + (e.name || e.code), name: 'NFR - ' + (e.name || e.code), type: COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT }
      }) || [];

      const lstWF: any[] = state.detail.workflows?.map((e) => {
        return { ...e, value: e.id, label: 'WF - ' + (e.name || e.code), name: 'NFR - ' + (e.name || e.code), type: COM_ARTEFACT_TYPE_ID.WORKFLOW }
      }) || [];

      const lstOthers = lstCBR.concat(lstEmails).concat(lstMessages).concat(lstNFR).concat(lstWF)
      setObjects(lstObjects.sort((a, b) => a.name.localeCompare(b.name)))
      setScreens(lstScreens.sort((a, b) => a.name.localeCompare(b.name)))
      setFunctions(lstUsecases.sort((a, b) => a.name.localeCompare(b.name)))
      setOthers(lstOthers.sort((a, b) => a.name.localeCompare(b.name)))
      const postData = selectedData(null, null, lstObjects, lstScreens, lstUsecases, lstOthers);
      dispatch(getWarningMessageRequest(postData));
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm(true);
      } else {
        resetForm();
        onDismiss();
      }
      setIsDraft(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSearch = (searchText: string) => {
    setInputSearch(searchText);
    dispatch(getListRefsRequest(searchText))
  };

  useEffect(() => {
    let allOptions = state.listRefs?.map((e) => {
      return {
        ...e,
        value: e.type + '_' + e.id,
        label: e.code + (e.name ? ' - ' + e.name : '')
      }
    }
    ) || [];
    let optList: any[] = [];
    let selectedItems: any[] = [];
    if (objects && Array.isArray(objects)) {
      selectedItems = selectedItems.concat(objects)
    }
    if (functions && Array.isArray(functions)) {
      selectedItems = selectedItems.concat(functions)
    }
    if (screens && Array.isArray(screens)) {
      selectedItems = selectedItems.concat(screens)
    }
    if (others && Array.isArray(others)) {
      selectedItems = selectedItems.concat(others)
    }
    allOptions.forEach(opt => {
      const existsIndex = selectedItems.findIndex((e) => e.id === opt.id && e.type === opt.type);
      if (existsIndex === -1) {
        opt.rowId = opt.type + '_' + opt.id;
        optList.push(opt)
      }
    });
    setOptions(optList)

    if (inputSearch === '') {
      setOptions([])
    }
  }, [state.listRefs, objects, functions, screens, others, inputSearch])


  const onSelect = (onSelect: string, option: any) => {
    setInputSearch(option.label);
    setSelectedInput(option);
  };

  const onSubmit = debounce(async (values: any, st?: string) => {
    const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data, true)
    let inputObjects = objects?.map((e) => {
      return {
        id: e.id,
        objectPropertyIds: e.objectPropertyIds
      }
    })
    let inputScreens = screens?.map((e) => {
      return {
        id: e.id,
        screenComponentIds: e.screenComponentIds
      }
    })

    // lstWorkflows
    const othersData = extractOtherList(others);
    const requestData: any = {
      id: componentID || null,
      code: state.detail?.code,
      name: values.name,
      scope: values.scope,
      status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) :
        hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED
      ,
      description: getCkeditorData.current?.props?.data,
      objects: inputObjects,
      screens: inputScreens,
      useCaseIds: functions?.map((e) => e.id),
      commonBusinessRuleIds: othersData.lstCBR?.map((e) => e.id),
      nonFunctionalRequirementIds: othersData.lstNFR?.map((e) => e.id),
      messageIds: othersData.lstMessages?.map((e) => e.id),
      workflowIds: othersData.lstWorkflows?.map((e) => e.id),
      emailTemplateIds: othersData.lstEmails?.map((e) => e.id),
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      if ((!objects || objects.length <= 0) && (!screens || screens.length <= 0) && (!functions || functions.length <= 0) && (!others || others.length <= 0)) {
        reqNoti['error']({
          description: intl.formatMessage({ id: 'EMSG_5' }),
          message: intl.formatMessage({ id: 'common.message.error' }),
          placement: 'bottomRight',
        })
        return;
      }
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.common-component' }) }
        ),
        onOk() {
          requestData.messageAction = hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? MESSAGE_TYPES.APPROVE : MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss()
      },
      onCancel() { },
    })
  }

  const resetForm = (createMore = false) => {
    setIsDraft(false);
    setOptions([]);
    setObjects([]);
    setFunctions([]);
    setOthers([]);
    setScreens([]);
    setMissing(null);
    setWarningMessage(null);
    setSelectedArtefact(null);
    form.resetFields([
      'code',
      'name',
      'description',
    ])
    form.setFieldsValue({
      'scope': 0,
    })
    if (!createMore) {
      form.resetFields(['createMore'])
      setIsCreateMore(false);
    }
  }

  const addData = (item, fromMissing = false) => {
    setIsLoading(true);
    let lstObjects: any[] = Object.assign([], objects);
    let lstScreens: any[] = Object.assign([], screens);
    let lstUsecases: any[] = Object.assign([], functions);
    let lstOthers: any[] = Object.assign([], others);

    switch (item.type) {
      case COM_ARTEFACT_TYPE_ID.OBJECT:
        if (lstObjects.findIndex(e => e.rowId === item.rowId) === -1) {
          lstObjects.push(item);
          lstObjects.sort((a, b) => a.name.localeCompare(b.name))
          setObjects(lstObjects)
        }
        break;
      case COM_ARTEFACT_TYPE_ID.SCREEN:
        if (lstScreens.findIndex(e => e.rowId === item.rowId) === -1) {
          lstScreens.push(item);
          lstScreens.sort((a, b) => a.name.localeCompare(b.name))
          setScreens(lstScreens)
        }
        break;
      case COM_ARTEFACT_TYPE_ID.USECASE:
        if (lstUsecases.findIndex(e => e.rowId === item.rowId) === -1) {
          lstUsecases.push(item);
          lstUsecases.sort((a, b) => a.name.localeCompare(b.name))
          setFunctions(lstUsecases)
        }
        break;
      default:
        if (lstOthers.findIndex(e => e.rowId === item.rowId) === -1) {
          const prefix = commonPrefixType(item.type, true)
          lstOthers.push({
            ...item,
            name: prefix + ' - ' + (item.name ? item.name : item.code)
          });
          lstOthers.sort((a, b) => a.name.localeCompare(b.name))
          setOthers(lstOthers)
        }
        break;
    }
    const postData = selectedData(null, null, lstObjects, lstScreens, lstUsecases, lstOthers);
    dispatch(getWarningMessageRequest(postData));
    if (fromMissing) {
      const postData = selectedData(selectedArtefact.type, selectedArtefact.id, lstObjects, lstScreens, lstUsecases, lstOthers);
      dispatch(getMissingRequest(postData));
    } else {
      setInputSearch('');
    }
  }

  const handleAdd = () => {
    if (!options || options.length <= 0) {
      return
    }
    addData(selectedInput);
  }

  const extractOtherList = (lstOthers) => {
    let lstCBR: any[] = [];
    let lstNFR: any[] = [];
    let lstMessages: any[] = [];
    let lstEmails: any[] = [];
    let lstWorkflows: any[] = []
    lstOthers?.forEach(element => {
      switch (element.type) {
        case COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE: {
          lstCBR.push(element)
          break
        }
        case COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT: {
          lstNFR.push(element)
          break
        }
        case COM_ARTEFACT_TYPE_ID.MESSAGE: {
          lstMessages.push(element)
          break
        }
        case COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE: {
          lstEmails.push(element)
          break
        }
        case COM_ARTEFACT_TYPE_ID.WORKFLOW: {
          lstWorkflows.push(element)
          break
        }
      }
    });
    return { lstCBR, lstNFR, lstMessages, lstEmails, lstWorkflows }
  }

  const selectedData = (artefactType, artefactId, lstObjects, lstScreens, lstUsecases, lstOthers) => {
    const othersData = extractOtherList(lstOthers);
    return {
      "artefactType": artefactType,
      "artefactId": artefactId,
      "objectIds": lstObjects.map(e => e.id),
      "screenIds": lstScreens.map(e => e.id),
      "useCaseIds": lstUsecases.map(e => e.id),
      "workflowIds": othersData.lstWorkflows.map(e => e.id),
      "commonBusinessRuleIds": othersData.lstCBR.map(e => e.id),
      "nonFunctionalRequirementIds": othersData.lstNFR.map(e => e.id),
      "messageIds": othersData.lstMessages.map(e => e.id),
      "emailTemplateIds": othersData.lstEmails.map(e => e.id),
    }
  }

  const handleViewDetail = (e) => {
    if (e.type === selectedArtefact?.type && e.id === selectedArtefact?.id) {
      setSelectedArtefact(null);
      setMissingData(state.warningMessage);
      return
    }
    const postData = selectedData(e.type, e.id, objects, screens, functions, others);
    dispatch(getMissingRequest(postData));
    // Load detail
    let url = '';
    switch (e.type) {
      case COM_ARTEFACT_TYPE_ID.OBJECT: {
        url = screenMode === SCREEN_MODE.CREATE ? API_URLS.COMMON_OBJECT : `${API_URLS.COMMON_COMPONENT}/${componentID}/Objects`
        break;
      }
      case COM_ARTEFACT_TYPE_ID.SCREEN: {
        url = screenMode === SCREEN_MODE.CREATE ? API_URLS.COMMON_SCREEN : `${API_URLS.COMMON_COMPONENT}/${componentID}/Screens`
        break;
      }
      case COM_ARTEFACT_TYPE_ID.USECASE: {
        url = API_URLS.COMMON_USECASE
        break;
      }
      case COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT: {
        url = API_URLS.COMMON_NONFUNCTIONAL_REQUIREMENT
        break;
      }
      case COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE: {
        url = API_URLS.COMMON_BUSINESS_RULE
        break;
      }
      case COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE: {
        url = API_URLS.COMMON_EMAIL
        break;
      }
      case COM_ARTEFACT_TYPE_ID.MESSAGE: {
        url = API_URLS.COMMON_MESSAGE
        break;
      }
      case COM_ARTEFACT_TYPE_ID.WORKFLOW: {
        url = API_URLS.COMMON_WORKFLOW
        break;
      }
    }
    AppCommonService.viewDetailCommonArtefact(url, e.id).then(res => {
      let artefact: any = {
        ...e,
        ...res.data,
        artefactType: e.type
      }
      if (e.type === COM_ARTEFACT_TYPE_ID.OBJECT && !e.objectPropertyIds) {
        artefact.objectPropertyIds = res.data.objectProperties?.filter(e => screenMode === SCREEN_MODE.CREATE ? true : e.selected)?.map((e) => e.id)
      }
      if (e.type === COM_ARTEFACT_TYPE_ID.SCREEN && !e.screenComponentIds) {
        artefact.screenComponentIds = res.data.screenComponents?.filter(e => screenMode === SCREEN_MODE.CREATE ? true : e.selected).map((e) => e.id)
      }
      setSelectedArtefact(artefact);
    }).catch(err => {
      setSelectedArtefact(null);
    }).finally(() => {
    })
  }

  const handleAddMissing = (e) => {
    addData(e, true);
  }

  useEffect(() => {
    setMissingData(state.missing);
  }, [state.missing])

  const setMissingData = (data) => {
    let lstMissingMentions: any[] = [];
    let lstMissingReferences: any[] = [];
    data?.missingMentions?.forEach((element: any) => {
      lstMissingMentions.push({
        ...element,
        rowId: element.type + '_' + element.id
      })
    });
    data?.missingReferences?.forEach((element: any) => {
      lstMissingReferences.push({
        ...element,
        rowId: element.type + '_' + element.id
      })
    });
    setMissing({
      missingMentions: lstMissingMentions,
      missingReferences: lstMissingReferences
    });
  }

  useEffect(() => {
    setWarningMessage(state.warningMessage);
    setIsLoading(false);
    if (!selectedArtefact) {
      setMissingData(state.warningMessage);
    }
  }, [state.warningMessage])

  const updateDataSource = (lstObjects, lstScreens, lstUsecases, lstOthers, updateType) => {
    if (
      (updateType === 1 && lstObjects.findIndex((e) => e.id === selectedArtefact?.id) === -1) ||
      (updateType === 2 && lstScreens.findIndex((e) => e.id === selectedArtefact?.id) === -1) ||
      (updateType === 3 && lstUsecases.findIndex((e) => e.id === selectedArtefact?.id) === -1) ||
      (updateType === 4 && lstOthers.findIndex((e) => e.id === selectedArtefact?.id) === -1)
    ) {
      setMissing(null);
      setSelectedArtefact(null)
    }
    setWarningMessage(null)
    const postData = selectedData(null, null, lstObjects, lstScreens, lstUsecases, lstOthers);
    dispatch(getWarningMessageRequest(postData));
  }

  const handleUpdateScreenComponents = (e) => {
    let lstScreens = Object.assign([], screens);
    lstScreens.forEach(o => {
      if (o.id === selectedArtefact.id) {
        o.screenComponentIds = e;
      }
    });
    setScreens(lstScreens.sort((a, b) => a.name.localeCompare(b.name)));
  }

  const handleUpdateObjProperties = (e) => {
    let lstObjects = Object.assign([], objects);
    lstObjects.forEach(o => {
      if (o.id === selectedArtefact.id) {
        o.objectPropertyIds = e;
      }
    });
    setObjects(lstObjects.sort((a, b) => a.name.localeCompare(b.name)));
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'scope', title: intl.formatMessage({ id: 'state.scope' }), },
      { field: 'description', title: intl.formatMessage({ id: 'common_component.column.description' }), },
      { field: 'warning_message', title: intl.formatMessage({ id: 'common_component.column.warning_message' }), },
      { field: 'objects', title: intl.formatMessage({ id: 'common_component.card.objects' }), },
      { field: 'function', title: intl.formatMessage({ id: 'common_component.card.function' }), },
      { field: 'screens', title: intl.formatMessage({ id: 'common_component.card.screens' }), },
      { field: 'others', title: intl.formatMessage({ id: 'common_component.card.others' }), },
      { field: 'missing_mentioned', title: intl.formatMessage({ id: 'common_component.card.missing_mentioned' }), },
      { field: 'missing_referenced', title: intl.formatMessage({ id: 'common_component.card.missing_referenced' }), },
    ];
    dispatch(initComment({ projectId: null, itemId: state.detail.id, fields }));

    const payload = {
      projectId: null,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_COMPONENT,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    size="extra"
    visible={true}
    footer={null}
    className="common-component-modal"
  >
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={16}>
            <Space size="large">
              <Form.Item
                name="name"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <Input
                  size="large"
                  className="modal-create-name-input-field"
                  placeholder={`${intl.formatMessage({ id: `common_component.column.name` })}${intl.formatMessage({ id: `common.mandatory.*` })}`}
                  maxLength={255}
                />
              </Form.Item>
              {screenMode === SCREEN_MODE.EDIT ? renderCommonStatusBadge(state.detail?.status) : <></>}
            </Space>
          </Col>

          <Col span={8}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                {screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
                  <Button type="primary" ghost htmlType="submit">
                    {intl.formatMessage({ id: 'common.action.submit' })}
                  </Button> : <></>
                }

                <Button onClick={() => setIsDraft(true)} className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                </Button>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 180}>
        <Spin spinning={isLoading}>
          <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
            <Card className='rq-form-block' title={intl.formatMessage({ id: 'common_component.card.general_information' })}>
              <Row>
                <Col span={4}>
                  <FormGroup label={
                    <TriggerComment screenMode={screenMode} field="scope">
                      {intl.formatMessage({ id: 'state.scope' })}
                    </TriggerComment>}>
                    <Form.Item name="scope">
                      <Select allowClear>
                        {COMPONENT_SCOPE.map((item: any) =>
                          <Option key={item.id} value={item.id}>
                            {item.name}
                          </Option>
                        )}
                      </Select>
                    </Form.Item>
                  </FormGroup>
                </Col>
              </Row>

              <FormGroup label={
                <TriggerComment screenMode={screenMode} field="description">
                  {intl.formatMessage({ id: 'common_component.column.description' })}
                </TriggerComment>}>
                <Form.Item name="description" labelAlign="left" wrapperCol={{ span: 24 }}>
                  <CkeditorMention isCommon ref={getCkeditorData} data={screenMode === SCREEN_MODE.EDIT ? state.detail?.description || '' : ''} />
                </Form.Item>
              </FormGroup>

              <FormGroup label={
                <TriggerComment screenMode={screenMode} field="warning_message">
                  {intl.formatMessage({ id: 'common_component.column.warning_message' })}
                </TriggerComment>}>
                <div style={{ background: '#f7f7f7', minHeight: 80, border: '1px solid #d9d9d9', padding: 16 }}>
                  {
                    warningMessage ? <>
                      {
                        (warningMessage.missingMentions && warningMessage.missingMentions.length > 0) ? <>
                          <div>Missing Mentioned Items:</div>
                          <ul>
                            {warningMessage.missingMentions?.map((mm, idx) => {
                              return <li key={idx}>{mm.code}{mm.name ? ': ' + mm.name : ''}</li>
                            })
                            }
                          </ul>
                        </> : <></>
                      }
                      {
                        (warningMessage.missingReferences && warningMessage.missingReferences.length > 0) ? <>
                          <div>Missing Referenced Items:</div>
                          <ul>
                            {warningMessage.missingReferences?.map((mr, idx) => {
                              return <li key={idx}>{mr.code}{mr.name ? ': ' + mr.name : ''}</li>
                            })
                            }
                          </ul>
                        </> : <></>
                      }
                    </> : <></>
                  }
                </div>
              </FormGroup>
            </Card>

            <Card className='rq-form-block' title={intl.formatMessage({ id: 'common_component.card.component_detail' })}>
              <Space direction='vertical'>
                <Space>
                  <AutoComplete
                    options={options}
                    style={{ width: 400 }}
                    onSelect={onSelect}
                    onSearch={onSearch}
                    value={inputSearch}
                    notFoundContent={inputSearch ? 'No data' : ''}
                    size='middle'
                  />
                  <Button onClick={handleAdd} type='link' size='middle' icon={<PlusOutlined />}>{intl.formatMessage({ id: 'common_component.action.add' })}</Button>
                </Space>

                <Row gutter={10}>
                  <Col span={18}>
                    <Row gutter={10} style={{ marginBottom: 8 }}>
                      <Col span={6}>
                        <CommonComponentObjects selectedArtefact={selectedArtefact} onSelect={handleViewDetail} screenMode={screenMode} dataSource={objects} onChange={(e) => { setObjects(e.sort((a, b) => a.name.localeCompare(b.name))); updateDataSource(e, screens, functions, others, 1) }} />
                      </Col>
                      <Col span={6}>
                        <CommonComponentFunction selectedArtefact={selectedArtefact} onSelect={handleViewDetail} screenMode={screenMode} dataSource={functions} onChange={(e) => { setFunctions(e.sort((a, b) => a.name.localeCompare(b.name))); updateDataSource(objects, screens, e, others, 2) }} />
                      </Col>
                      <Col span={6}>
                        <CommonComponentScreens selectedArtefact={selectedArtefact} onSelect={handleViewDetail} screenMode={screenMode} dataSource={screens} onChange={(e) => { setScreens(e.sort((a, b) => a.name.localeCompare(b.name))); updateDataSource(objects, e, functions, others, 3) }} />
                      </Col>
                      <Col span={6}>
                        <CommonComponentOthers selectedArtefact={selectedArtefact} onSelect={handleViewDetail} screenMode={screenMode} dataSource={others} onChange={(e) => { setOthers(e.sort((a, b) => a.name.localeCompare(b.name))); updateDataSource(objects, screens, functions, e, 4) }} />
                      </Col>
                    </Row>
                    {
                      selectedArtefact ? <Card className='rq-form-block' title={
                        selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT ? selectedArtefact.code + ' - ' + categoryName(selectedArtefact.category) :
                          selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE ? selectedArtefact.code + ' - ' + (selectedArtefact.subject ? selectedArtefact.subject : intl.formatMessage({ id: 'common_component.card.email-template' })) :
                            selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.MESSAGE ? selectedArtefact.code + ' - ' + (selectedArtefact.content ? selectedArtefact.content : intl.formatMessage({ id: 'common_component.card.message' })) :
                              selectedArtefact.code + ' - ' + selectedArtefact.name
                      }>
                        {/* Workflow */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.WORKFLOW ? <CommonWorkflowDetailInfo data={selectedArtefact} /> : <></>}
                        {/* Object */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.OBJECT ? <CommonObjectDetailInfo onChange={handleUpdateObjProperties} data={selectedArtefact} /> : <></>}
                        {/* Usercase */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.USECASE ? <CommonUseCaseDetailInfo data={selectedArtefact} /> : <></>}
                        {/* Screen */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.SCREEN ? <CommonScreenDetailInfo onChange={handleUpdateScreenComponents} data={selectedArtefact} /> : <></>}
                        {/* Email */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE ? <CommonEmailDetailInfo data={selectedArtefact} /> : <></>}
                        {/* Message */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.MESSAGE ? <CommonMessDetailInfo data={selectedArtefact} /> : <></>}
                        {/* Non functional Business */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT ? <CommonNonFunctonalDetailInfo data={selectedArtefact} /> : <></>}
                        {/* Common Business Rule */}
                        {selectedArtefact.artefactType === COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE ? <CommonCBRDetailInfo data={selectedArtefact} /> : <></>}
                      </Card> : <></>
                    }
                  </Col>
                  <Col span={6}>
                    <div style={{ marginBottom: 8 }}>
                      <CommonComponentMissingMentioned onAdd={handleAddMissing} dataSource={missing?.missingMentions || []} screenMode={screenMode} />
                    </div>
                    <CommonComponentMissingReferenced onAdd={handleAddMissing} dataSource={missing?.missingReferences || []} screenMode={screenMode} />
                  </Col>
                </Row>
              </Space>
            </Card>
          </Space>
        </Spin>
      </Scrollbars>
    </Form>
  </CustomModal>
}

const CommonComponentForm = ({ componentID, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: CommonComponentFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return <>
    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button
          ghost={screenMode === SCREEN_MODE.CREATE}
          type='primary'
          className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
          onClick={() => setIsModalVisible(true)}
          icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
        >
          {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common_component.action.create' : 'common.action.update' })}
        </Button> :
        buttonType === BUTTON_TYPE.ICON ?
          <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
          <></>
    }
    {isModalVisible === true ? <CommonComponentFormModal componentID={componentID} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}

export default CommonComponentForm
