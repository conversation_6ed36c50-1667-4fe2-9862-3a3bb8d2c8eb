import LavPageHeader from '../../../../helper/component/lav-breadcumb'
import AppState from '@/store/types'
import { Button, Card, Checkbox, Col, Form, Input, Modal, Row, Space, Spin, Typography } from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, SCREEN_MODE } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { getReferencesFromEditor } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { CommonBusinessRuleState } from '../type'

const { Text } = Typography
const { confirm } = Modal

// interface BusinessRuleFormProps {
//   id?: number
//   buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT
//   screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
//   onFinish?: () => void | null
//   onDismiss: () => void | null
// }
interface BusinessRuleFormModalProps {
  id?: number
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const BusinessRuleFormPage = ({ id, onFinish, screenMode, onDismiss }: BusinessRuleFormModalProps) => {
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>(
    (s) => s?.CommonBusinessRule
  ) as CommonBusinessRuleState
  const dispatch = useDispatch()
  const [isCreateMore, setIsCreateMore] = useState(false);
  const getCkeditorData: any = createRef()
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()

  // Destroy
  useEffect(() => {
    return () => {
      resetForm()
      dispatch(resetState(null));
    }
  }, [])

  const resetForm = () => {
    form.resetFields([
      'businessruleName',
      'version',
      'img',
      'businessruleExplanation',
      'userRequirement',
    ])
  }

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
  }, [screenMode, id])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.details?.id) {
      form.setFieldsValue({
        code: state.details?.code,
        businessruleName: state.details?.name,
        businessruleExplanation: state.details?.description
      })
    }
  }, [state.details])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm();
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss()
      }
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce(async (values: any, st?: string) => {
    const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data, true)
    const requestData: any = {
      id: id || null,
      name: values.businessruleName,
      description: getCkeditorData.current?.props?.data,
      version: values.version,
      userRequirement: values.userRequirement,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    }
    setIsCreateMore(values.createMore);
    requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));

  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss()
      },
      onCancel() { },
    })
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.details?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.label.version' }), },
      { field: 'description', title: intl.formatMessage({ id: 'cbr.column.description' }), },
      { field: 'user-requirement', title: intl.formatMessage({ id: 'cbr.user-requirement' }), }
    ];
    dispatch(initComment({ projectId: null, itemId: state.details.id, fields }));

    const payload = {
      projectId: null,
      itemId: state.details.id,
      artefact: ARTEFACT_COMMENT.COMMON_COMMON_BUSINESS_RULE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.details])

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name="crearebusinessrule"
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>


        {/* <Row> */}
        {/* <Col span={10}>
            <Space size='large'>
              <Form.Item
                name="businessruleName"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <Input
                  size="large"
                  className="modal-create-name-input-field"
                  placeholder={`${intl.formatMessage({
                    id: `cbr.place-holder.cbr`,
                  })}${intl.formatMessage({
                    id: `common.mandatory.*`,
                  })}`}
                  maxLength={255}
                />
              </Form.Item>

            </Space>
          </Col> */}
        <LavPageHeader
          showBreadcumb={false}
          title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createcbr.header.title' : 'updatecbr.header.title' })}
        >

          <Space size="small">
            {screenMode == SCREEN_MODE.CREATE ?
              (<Form.Item
                style={{ marginBottom: '0px' }}
                valuePropName="checked"
                name="createMore"
                wrapperCol={{ span: 24 }}
              >
                <Checkbox disabled={state.isLoading}>
                  {intl.formatMessage({
                    id: 'createobject.checkbox.create-another',
                  })}
                </Checkbox>
              </Form.Item>) : <></>
            }

            <Button onClick={debounce(confirmCancel, 500)}>
              {intl.formatMessage({ id: 'common.action.close' })}
            </Button>

            <Form.Item style={{ marginBottom: '0px' }}>
              <Button
                className="success-btn"
                htmlType="submit"
              >
                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save' : 'common.action.update' })}
              </Button>
            </Form.Item>
          </Space>
        </LavPageHeader>
      </div>

      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Card className='rq-form-block' title={intl.formatMessage({ id: 'cbr.column.cbr-info' })}>
          {
            screenMode === SCREEN_MODE.EDIT ?
              <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                  <Input maxLength={255} disabled />
                </Form.Item>
              </FormGroup> : <></>
          }

          <FormGroup inline required label={intl.formatMessage({
            id: 'common.label.name',
          })}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item
              name="businessruleName"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
            >
              <Input
                maxLength={255}
              />
            </Form.Item>
          </FormGroup>

          <FormGroup inline className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="description">
              {intl.formatMessage({ id: 'cbr.column.description' })}
            </TriggerComment>}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item name="businessruleExplanation">
              <CkeditorMention isCommon ref={getCkeditorData} data={screenMode == SCREEN_MODE.EDIT ? state.details?.description : ''} />
            </Form.Item>
          </FormGroup>
        </Card>
      </Space>

    </Form>
  </Spin>
}

export default BusinessRuleFormPage
