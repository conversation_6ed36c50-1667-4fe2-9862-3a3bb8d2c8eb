.icon_size {
  cursor: pointer;
  font-size: 20px;
}

.status {
  width: 70px;
  border: 2px solid black;
  border-radius: 5px;
  text-align: center;
  color: black;
}

.draft,
.stable,
.reviewed {
  background-color: yellow;
}

.signed {
  background-color: rgb(76, 165, 76);
}

.cancelled {
  background-color: rgb(236, 122, 122);
}

.ant-pagination-total-text {
  flex-grow: 1 !important;
}

.sorter-no-tooltip .ant-table-column-sorters:before {
  content: none !important;
}

fieldset {
  border: 1px solid black;
  /* border-top-color: black;  */
  box-sizing: border-box;
  grid-area: 1 / 1; /* first row, first column */
  padding: 20px;
  width: inherit;
}

legend {
  /* font: 15pt/0 'Averia Serif Libre';  */
  width: auto !important;
  padding: 0 4px;
}
