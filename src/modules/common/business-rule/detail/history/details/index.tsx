import AppState from '@/store/types'
import {
    Breadcrumb, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS_COMMON } from '../../../../../../constants'
import LavButtons from '../../../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../../../helper/hooks/useWindowDimensions'
import { initComment, initCommentScreen } from '../../../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../../../modules/_shared/comment/type'
import HistoryNavigation from '../../../../../../modules/history/navigation'
import debounce from 'lodash.debounce'

const { Title, Text } = Typography
interface CommonBusinessRuleVersionDetailsProps {
    data: any | [],
    businessRuleID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any,
    setSelectedRowVersion: (version: string) => void, 
    onDismiss: () => void | null,
}
const CommonBusinessRuleVersionDetails = ({ data, businessRuleID, onChange, isLoading, isModalShow, setScreenMode, setSelectedRowVersion, onDismiss }: CommonBusinessRuleVersionDetailsProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.label.version' }), },
            { field: 'description', title: intl.formatMessage({ id: 'cbr.column.description' }), },
            { field: 'user-requirement', title: intl.formatMessage({ id: 'cbr.user-requirement' }), },
        ];
        dispatch(initComment({ projectId: null, itemId: data.id, fields }));
        const payload = {
            projectId: null,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.COMMON_COMMON_BUSINESS_RULE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    return (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                        <Breadcrumb.Item>
                            <Link to={APP_ROUTES.COMMON_CBR}>{intl.formatMessage({ id: 'commonbusinessrule.page_title' })}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code} - {data?.name}
                    </Title>
                </div>
                <Space size="small">
                    <LavButtons
                        isCommon={true}
                        url={`${API_URLS.COMMON_BUSINESS_RULE}/${data?.id}`}
                        artefact_type="common.artefact.business-rule"
                        status={data?.status}
                        changePage={() => onChange()}>                                                                 
                        <Button onClick={debounce(onDismiss, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>
                    </LavButtons>

                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />          
            { data?.nextPrevious.latestVersion === data?.version ? <></>:
                    <HistoryNavigation isCommon={true} data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.business-rule"} artefactType={COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE} />
                }
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical">
                        <Space size="large">
                            <span>
                                <TriggerComment field="version">
                                    <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
                                </TriggerComment>
                            </span>
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'cbr.column.cbr-info',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={24}>
                                    <TriggerComment field="description">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'cbr.column.description',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{
                                            __html: data?.description,
                                        }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>
                        <LavReferences data={data} isCommon />
                        <Col span={24}>
                            <LavCommonAuditTrail data={data?.auditTrails} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    )
}

export default CommonBusinessRuleVersionDetails
