import { Reference } from '../../../constants'

export interface Details {
  id: number | null
  name: string
  status: number
  code: string
  diagram: string
  description: string
  userRequirement: Reference
  projectId?: number
  
}


export interface CommonBusinessRuleState {
  isLoading: boolean
  sourceUserRequirement: Reference[]
  details: Details | null
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  isLoadingList?: boolean
  listData?: any
  selectedData?: Details | null,

  listUserRequirements?: any[],
  isLoadingUserRequirements?: boolean,
  isModalShow?: boolean
}

export const defaultState: CommonBusinessRuleState = {
  sourceUserRequirement: [],
  isLoading: false,
  details: {
    id: null,
    name: '',
    status: -1,
    code: '',
    diagram: '',
    description: '',
    userRequirement: {
      id: -1,
      name: '',
    },
  },
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  listUserRequirements: [],
  isLoadingUserRequirements: false
}
export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_BUSINESSRULE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_BUSINESSRULE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_BUSINESSRULE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_BUSINESSRULE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_BUSINESSRULE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_BUSINESSRULE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_BUSINESSRULE/UPDATE_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_BUSINESSRULE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_BUSINESSRULE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_BUSINESSRULE/DELETE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_BUSINESSRULE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_BUSINESSRULE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_BUSINESSRULE/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/COMMON_BUSINESSRULE/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/COMMON_BUSINESSRULE/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/COMMON_BUSINESSRULE/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/COMMON_BUSINESSRULE/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/COMMON_BUSINESSRULE/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/COMMON_BUSINESSRULE/GET_LIST_FAILED',

  GET_LIST_UR_REQUEST = '@@MODULES/COMMON_BUSINESSRULE/GET_LIST_UR_REQUEST',
  GET_LIST_UR_SUCCESS = '@@MODULES/COMMON_BUSINESSRULE/GET_LIST_UR_SUCCESS',
  GET_LIST_UR_FAILED = '@@MODULES/COMMON_BUSINESSRULE/GET_LIST__URFAILED',

  SET_MODAL_VISIBLE = '@@MODULES/COMMON_BUSINESSRULE/SET_MODAL_VISIBLE',
}
