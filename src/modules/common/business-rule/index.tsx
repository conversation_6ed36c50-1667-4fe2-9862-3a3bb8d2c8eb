import CustomSvgIcons from '../../../helper/component/custom-icons'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import { useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_COMMON } from '../../../constants'
import LavTable from '../../../helper/component/lav-table'
import { getColumnSearchProps, hasRole } from '../../../helper/share'
import BusinessRuleForm from './form'
import BusinessRuleFormPage from './form/form'

const CommonCBR = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  const columns = [
    {
      title: intl.formatMessage({ id: 'common.action.code' }),
      dataIndex: 'code',
      width: '5%',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${APP_ROUTES.COMMON_CBR_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'cbr.column.cbr' }),
      dataIndex: 'name',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'cbr.button.create-cbr' })}
      </Button> 
    )
    // <BusinessRuleForm onFinish={() => handleDataChange()} screenMode={SCREEN_MODE.CREATE} buttonType={BUTTON_TYPE.TEXT} />
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return  (record.status !== STATUS_COMMON.DELETED) ?
    <Button ghost={screenMode === SCREEN_MODE.EDIT}
      style={{ border: 'none' }}
      icon={<CustomSvgIcons name="EditCustomIcon" />}
      onClick={() => {
        setScreenMode(SCREEN_MODE.EDIT)
        setId(record.id)
      }} /> : <></>


    // (record.status !== STATUS_COMMON.DELETED) ?
    //   <BusinessRuleForm onFinish={() => handleDataChange()} buttonType={BUTTON_TYPE.ICON} id={record.id} screenMode={SCREEN_MODE.EDIT} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (
      record.status !== STATUS.DELETE
    ) ?
      children : <></>


    // (
    //   record.status !== STATUS_COMMON.DELETED
    // ) ?
    //   children : <></>
  }
  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {columns && screenMode === SCREEN_MODE.VIEW ? <LavTable
        title="cbr.header.title"
        artefact_type="common.artefact.business-rule"
        showBreadcumb={false}
        apiUrl={API_URLS.COMMON_BUSINESS_RULE}
        columns={columns}
        isCommon={true}
        artefactType={COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE}
        deleteComponent={DeleteComponent}
        updateComponent={UpdateComponent}
        createComponent={CreateComponent}
      /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <BusinessRuleFormPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenMode={SCREEN_MODE.CREATE} buttonType={BUTTON_TYPE.TEXT} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <BusinessRuleFormPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} id={id} screenMode={SCREEN_MODE.EDIT} /> : <></>
      }
    </Space>
  )
}

export default CommonCBR
