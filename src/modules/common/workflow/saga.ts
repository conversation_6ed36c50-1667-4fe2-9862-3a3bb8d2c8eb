import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES, STATUS_COMMON } from '../../../constants'
import { apiCall } from '../../../helper/api/aloApi'
import { ShowAppMessage } from '../../../helper/share'
import {
  createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, getDetailFailed, getDetailRequest, getDetailSuccess, getListFailed, getListFunction,
  getListFunctionSuccess, getListRequest, getListSuccess, getSourceObject, getSourceObjectFailure, getSourceObjectSuccess, updateFailed, updateRequest, updateSuccess
} from './action'


function* addNewWorkFlowFlow(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.COMMON_WORKFLOW
      const res = yield call(apiCall, 'POST', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.workflow')
      yield put(createSuccess(res.data))
    } catch (err) {
      yield put(createFailed(null))
      ShowAppMessage(err, null, 'common.artefact.workflow')
    }
  }
}

function* deleteWorkFlowFlow(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const params = action.payload
      const url = API_URLS.COMMON_WORKFLOW + `/${params}`
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.workflow')
      yield put(deleteSuccess(null))
    } catch (err) {
      yield put(deleteFailed(null))
      ShowAppMessage(err, null, 'common.artefact.workflow')
    }
  }
}

function* getWorkFlowDetailFlow(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const params = action.payload
      const url = API_URLS.COMMON_WORKFLOW + `/${params}`
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data))
      if (res.data?.object?.id) {
        yield put(getListFunction(res.data.object.id))
      }
    } catch (err) {
      yield put(getDetailFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* updateWorkFlowDetailFlow(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.COMMON_WORKFLOW + `/${request.id}`
      const res = yield call(apiCall, 'PUT', url, request as any)
      yield put(updateSuccess(res.data))
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.workflow')
    } catch (err) {
      yield put(updateFailed(null))
      ShowAppMessage(err, null, 'common.artefact.workflow', err)
    }
  }
}


function* getListFunctionFlow(action: Action) {
  if (getListFunction.match(action)) {
    try {
      const params = action.payload
      const url = API_URLS.COMMON_REFERENCE_OBJECTS + `/${params}/usecases`
      const res = yield call(apiCall, 'GET', url)
      const filterReference = res.data?.filter((e) => e.status !== STATUS_COMMON.DELETED && e.status !== STATUS_COMMON.REMOVED)
      yield put(getListFunctionSuccess(filterReference))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* getListObjectFlow(action: Action) {
  if (getSourceObject.match(action)) {
    try {
      const url = API_URLS.COMMON_REFERENCE_OBJECTS
      const res = yield call(apiCall, 'GET', url)
      yield put(getSourceObjectSuccess(res.data))
    } catch (err) {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
      yield put(getSourceObjectFailure(null))

    }
  }
}

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.WORKFLOWS}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(createRequest.type, addNewWorkFlowFlow)
  yield takeLatest(deleteRequest.type, deleteWorkFlowFlow)
  yield takeLatest(getDetailRequest.type, getWorkFlowDetailFlow)
  yield takeLatest(updateRequest.type, updateWorkFlowDetailFlow)
  yield takeLatest(getListFunction.type, getListFunctionFlow)
  yield takeLatest(getSourceObject.type, getListObjectFlow)
  yield takeLatest(getListRequest.type, handleGetList)

}

export default function* CommonWorkFlowSaga() {
  yield all([fork(watchFetchRequest)])
}
