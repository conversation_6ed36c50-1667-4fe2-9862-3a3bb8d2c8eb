import { <PERSON>, <PERSON>, Row, Space, Typography } from "antd"
import intl from "../../../../config/locale.config"
import LavAttachmentPreview from "../../../../helper/component/lav-attachment-preview"
import LavReferences from "../../../../helper/component/lav-references"
import { renderCommonStatusBadge } from "../../../../helper/share"

const { Title, Text } = Typography

const CommonWorkflowDetailInfo = ({ data }) => {
    return <Space direction="vertical" size="middle">
        <Space size="large">
            {renderCommonStatusBadge(data?.status)}
        </Space>

        <Card
            title={
                <Title level={5}>
                    {`${intl.formatMessage({
                        id: 'view-workflow.legend.workflow-info',
                    })}`}
                </Title>
            }
            bordered={true}
        >
            <Row gutter={[16, 4]}>
                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-workflow.label.workflow-diagram',
                        })}:
                    </Text>
                </Col>
                <Col span={24}>
                    <LavAttachmentPreview attachment={data?.diagram} isCommon />
                </Col>

                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-workflow.label.workflow-explanation',
                        })}:
                    </Text>
                </Col>
                <Col span={24}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{
                            __html: data?.description,
                        }}
                    ></div>
                </Col>
            </Row>
        </Card>

        <LavReferences data={data} isCommon={true} />
    </Space>
}
export default CommonWorkflowDetailInfo