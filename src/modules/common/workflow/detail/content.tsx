import AppState from '@/store/types'
import { <PERSON><PERSON>, Card, Col, Divider, Row, Space, Spin, Typography } from 'antd'
import React, { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, STATUS_COMMON } from '../../../../constants'
import DeleteButton from '../../../../helper/component/commonButton/DeleteButton'
import LavAttachmentPreview from '../../../../helper/component/lav-attachment-preview'
import LavButtons from '../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, getProjectName, hasRole, renderCommonStatusBadge } from '../../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../_shared/comment/action'
import TriggerComment from '../../../_shared/comment/trigger-comment'
import { CommentState } from '../../../_shared/comment/type'
import { deleteRequest } from '../action'

const { Title, Text } = Typography
interface Props {
    data: any | [],
    workFlowID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}
const RightControl = ({ data, workFlowID, onChange, isLoading, isModalShow, setScreenMode }: Props) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;
    useEffect(() => {
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.WORKFLOW);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }
        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'diagram', title: intl.formatMessage({ id: 'create-workflow.label.workflow-diagram' }), },
            { field: 'workflow-explanation', title: intl.formatMessage({ id: 'create-workflow.label.workflow-explanation' }), },
            { field: 'actor', title: intl.formatMessage({ id: 'view-screen-list.label.actor' }), },
            { field: 'screen', title: intl.formatMessage({ id: 'view-screen-list.label.screen' }), },
            { field: 'state-transition', title: intl.formatMessage({ id: 'view-screen-list.label.state-transition' }), },
            { field: 'object', title: intl.formatMessage({ id: 'view-screen-list.label.object' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'implementation', title: intl.formatMessage({ id: 'view-screen-list.label.implementation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.WORKFLOW,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return  data?.status !== STATUS_COMMON.DELETED ?
            <DeleteButton
                type={BUTTON_TYPE.TEXT}
                content={`${intl.formatMessage(
                    { id: 'CFD_7' },
                    {
                        artefact_type: `${intl.formatMessage({
                            id: 'common.artefact.workflow',
                        })}`,
                    }
                )}`}
                okCB={() => dispatch(deleteRequest(parseInt(workFlowID)))}
                confirmButton={`${intl.formatMessage({
                    id: 'common.action.delete',
                })}`}
            ></DeleteButton> : <></>
    }
    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Title level={3} className='rq-page-title'>{data?.code}-{data?.name}</Title>
                </div>
                <Space size="small">
                    <LavButtons
                        url={`${API_URLS.COMMON_WORKFLOW}/${data?.id}`}
                        artefact_type="common.artefact.workflow"
                        status={data?.status}
                        isHasReject={true}
                        isHasRemove={true}
                        isHasApprove={true}
                        deleteButton={DeleteComponent}
                        isCommon={true}
                        artefactType={COM_ARTEFACT_TYPE_ID.WORKFLOW}
                        id={workFlowID}
                        changePage={() => onChange()}>

                        {data?.status !== STATUS_COMMON.DELETED && (
                            <Button
                                type='primary'
                                className='lav-btn-create'
                                onClick={() => {
                                    setScreenMode(SCREEN_MODE.EDIT)
                                }} >{intl.formatMessage({ id: 'common.action.update' })}</Button>
                        )}
                    </LavButtons>

                </Space>
            </Row>

            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Space size="large">
                            <span>          
                                <TriggerComment field='version'>                               
                                    <a onClick={() => {
                                        setScreenMode(SCREEN_MODE.HISTORY)
                                    }}>
                                        {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                    </a>
                                </TriggerComment>
                            </span>
                            {renderCommonStatusBadge(data?.status)}
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'view-workflow.legend.workflow-info',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={24}>
                                    <TriggerComment field="diagram">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-workflow.label.workflow-diagram',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <LavAttachmentPreview attachment={data?.diagram} isCommon={true} />
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field="workflow-explanation">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-workflow.label.workflow-explanation',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{
                                            __html: data?.description,
                                        }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>

                        <LavReferences data={data} isCommon={true} />
                        <LavCommonAuditTrail data={data?.auditTrails} />
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
