import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, COM_ARTEFACT_TYPE_ID, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../../constants'
import { extractProjectCode, hasRole } from '../../../../helper/share'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import WorkFlowFormPage from '../form/form'
import { CommonWorkFlowState } from '../type'
import LavLeftControl from './../../../_shared/left-menu'
import RightControl from './content'
import HistoryScreen from '../../../../modules/history'
import AppCommonService from '../../../../services/app.service'
import CommonWorkFlowVersionDetails from './history/details'

const WorkFlowDetailPage = (props) => {
  const dispatch = useDispatch()
  const history = useHistory()
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const state = useSelector<AppState | null>(
    (s) => s?.CommonWorkFlow
  ) as CommonWorkFlowState
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.workflowID) {
      dispatch(getDetailRequest(props.match.params.workflowID))
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.COMMON_WORKFLOW + '/' + props.match.params.workflowID +  '/' + selectedRowVersion).then((e) => {
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);    
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${APP_ROUTES.COMMON_WORKFLOW}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true)
    dispatch(getDetailRequest(props.match.params.workflowID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${APP_ROUTES.COMMON_WORKFLOW_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col span={5}>
            <LavLeftControl
              activeId={props.match.params.workflowID}
              apiUrl={API_URLS.COMMON_REFERENCE_WORKFLOW}
              route={APP_ROUTES.COMMON_WORKFLOW_DETAIL}
              artefactType={COM_ARTEFACT_TYPE_ID.WORKFLOW}
              title='view-workflow.header.workflow-list'
              reload={reload}
              isCommon
              reloadSuccess={() => setReload(false)}
              handleCreate={handleCreate}
            >
              {
                (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <Button ghost={true}
                  type='primary'
                  className='lav-btn-create'
                  icon={<PlusOutlined />}
                  onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'create-workflow.button.create-workflow' })}
                </Button> : <></>
              }
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>            
            <Col span={19}>
              <RightControl setScreenMode={setScreenMode} onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} workFlowID={props.match.params.workflowID} />
            </Col>
          </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <WorkFlowFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <WorkFlowFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
            }} workflowID={props.match.params.workflowID} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.workflow"
                            apiURL = {API_URLS.COMMON_HISTORY} isCommon = {true}
                            artefactType = {COM_ARTEFACT_TYPE_ID.WORKFLOW}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + state?.selectedData?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
              <CommonWorkFlowVersionDetails workFlowID={props.match.params.workFlowID} setSelectedRowVersion = {setSelectedRowVersion} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
    </Row>
  )
}

export default WorkFlowDetailPage
