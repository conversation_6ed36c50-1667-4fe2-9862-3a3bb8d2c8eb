import { createAction } from '@reduxjs/toolkit'
import { ActionEnum } from './type'

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<any>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<any>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<any>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<any>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const viewDetailRequest = createAction<any>(ActionEnum.VIEW_DETAIL_REQUEST);
export const viewDetailSuccess = createAction<any>(ActionEnum.VIEW_DETAIL_SUCCESS);
export const viewDetailFailed = createAction<any>(ActionEnum.VIEW_DETAIL_FAILED);

export const createRequest = createAction<any>(ActionEnum.CREATE_REQUEST);
export const createSuccess = createAction<any>(ActionEnum.CREATE_SUCCESS);
export const createFailed = createAction<any>(ActionEnum.CREATE_FAILED);

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST);
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS);
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED);

export const deleteRequest = createAction<any>(ActionEnum.DELETE_REQUEST);
export const deleteSuccess = createAction<any>(ActionEnum.DELETE_SUCCESS);
export const deleteFailed = createAction<any>(ActionEnum.DELETE_FAILED);

export const getSourceObject = createAction<void>(ActionEnum.GET_SOURCE_OBJECT)
export const getSourceObjectSuccess = createAction<any>(ActionEnum.GET_SOURCE_OBJECT_SUCCESS)
export const getSourceObjectFailure = createAction<any>(ActionEnum.GET_SOURCE_OBJECT_FAILURE)

export const getListFunction = createAction<any>(ActionEnum.GET_LIST_FUNCTION)
export const getListFunctionSuccess = createAction<any>(ActionEnum.GET_LIST_FUNCTION_SUCCESS)
export const getListFunctionFailure = createAction<any>(ActionEnum.GET_LIST_FUNCTION_FAILURE)
