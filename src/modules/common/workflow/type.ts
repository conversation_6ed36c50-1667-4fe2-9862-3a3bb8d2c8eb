export interface WorkFlowInfo {
  id: number | null
  name: string
  status: number
  code: string
  diagram: string
  description: string
  object: any | null
  useCases: any[]
  diagramId: any
  projectId?: number,
  impacts: string,
  // versionHistories?: VersionType[]
}


export interface CommonWorkFlowState {
  isLoading: boolean
  details: WorkFlowInfo | null

  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  listObjects?: any[],
  isLoadingObjects?: boolean,
  selectedData?: WorkFlowInfo | null,
  listFunctions?: any[],
  isLoadingFunctions?: boolean,
}

export const defaultState: CommonWorkFlowState = {
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  listData: [],
  listFunctions: [],
  listObjects: [],
  isLoadingFunctions: false,
  isLoadingList: false,
  isLoadingObjects: false,
  details: {
    id: null,
    name: '',
    status: -1,
    code: '',
    diagram: '',
    diagramId: '',
    description: '',
    object: null,
    useCases: [],
    impacts: "",
  },
}
export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_WORKFLOW/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_WORKFLOW/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_WORKFLOW/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_WORKFLOW/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_WORKFLOW/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_WORKFLOW/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_WORKFLOW/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_WORKFLOW/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_WORKFLOW/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_WORKFLOW/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/COMMON_WORKFLOW/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/COMMON_WORKFLOW/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/COMMON_WORKFLOW/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/COMMON_WORKFLOW/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/COMMON_WORKFLOW/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/COMMON_WORKFLOW/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_WORKFLOW/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_WORKFLOW/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_WORKFLOW/DELETE_FAILED',

  GET_SOURCE_OBJECT = '@@MODULES/COMMON_WORKFLOW/GET_SOURCE_OBJECT',
  GET_SOURCE_OBJECT_SUCCESS = '@@MODULES/COMMON_WORKFLOW/GET_SOURCE_OBJECT_SUCCESS',
  GET_SOURCE_OBJECT_FAILURE = '@@MODULES/COMMON_WORKFLOW/GET_SOURCE_OBJECT_FAILURE',


  GET_LIST_FUNCTION = '@@MODULES/COMMON_WORKFLOW/GET_LIST_FUNCTION',
  GET_LIST_FUNCTION_SUCCESS = '@@MODULES/COMMON_WORKFLOW/GET_LIST_FUNCTION_SUCCESS',
  GET_LIST_FUNCTION_FAILURE = '@@MODULES/COMMON_WORKFLOW/GET_LIST_FUNCTION_FAILURE',

  RESET = '@@MODULES/COMMON_WORKFLOW/RESET',
}
