import AppState from '@/store/types'
import {
    Button,
    Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { APP_COMMON_ROLES, APP_ROLES, ARTEFACT_COMMENT, COM_ARTEFACT_TYPE_ID, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, STATUS_COMMON } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import FormGroup from '../../../../helper/component/form-group'
import LavAttachmentUpload from '../../../../helper/component/lav-attachment-upload'
import LavPageHeader from '../../../../helper/component/lav-breadcumb'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, getReferencesFromEditor, hasCommonRole, hasRole, renderCommonStatusBadge, ShowMessgeAdditionalSubmit } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import AppCommonService from '../../../../services/app.service'
import { createRequest, getDetailRequest, getListFunction, getSourceObject, resetState, updateRequest } from '../action'
import { CommonWorkFlowState } from '../type'

const { confirm } = Modal
const { Option } = Select

interface WorkFlowFormModalProps {
    workflowID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}
const WorkFlowFormPage = ({ workflowID, screenMode, onFinish, onDismiss }: WorkFlowFormModalProps) => {
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.CommonWorkFlow) as CommonWorkFlowState
    const dispatch = useDispatch()
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const getCkeditorData: any = createRef()
    const modalConfirmConfig = useModalConfirmationConfig()
    const [attachment, setAttachment] = useState(null) as any
    const attachmentRef = useRef<any>()

    // Destroy
    useEffect(() => {
        dispatch(getSourceObject())
        return () => {
            dispatch(resetState(null));
            resetForm()
            form.resetFields(['createMore']);
            setAttachment(null)
        }
    }, [])

    useEffect(() => {
        if (workflowID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(workflowID))
        }
    }, [screenMode, workflowID])


    const resetForm = () => {
        form.resetFields([
            'workflowName',
            'version',
            'img',
            'workflowExplanation',
            'object',
            'useCases',
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })
    }

    useEffect(() => {
        if (workflowID && screenMode === SCREEN_MODE.EDIT && state.details?.id) {
            form.setFieldsValue({
                code: state.details?.code,
                workflowName: state.details?.name,
                object: state.details.object?.id,
                useCases: state.details?.useCases?.map((e) => {
                    return e?.id;
                }),
            })
            setAttachment(state.details?.diagram)
        }
    }, [state.details])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            const version = form.getFieldValue('version')
            const changeDescription = form.getFieldValue('changeDescription')

            if (version && version !== '') {

                const payload = {
                    version: version.substring(version.length - 1) === "." ? `${version}0` : version,
                    description: changeDescription,
                    artefactCode: state.details?.code,
                }
                AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.WORKFLOW, state.details?.id).then((e) => {
                    if (isCreateMore) {
                        resetForm();
                        setAttachment(null)
                    } else {
                        if (onFinish) {
                            onFinish();
                        }
                        onDismiss();
                    }
                    setIsDraft(null);
                    setIsCreateMore(false);
                })
            } else {
                if (isCreateMore) {
                    resetForm();
                    setAttachment(null)
                } else {
                    if (onFinish) {
                        onFinish();
                    }
                    onDismiss();
                }
                setIsDraft(null);
                setIsCreateMore(false);
            }
        }
    }, [state.createSuccess, state.updateSuccess])


    const onSubmit = debounce(async(values: any, st?: string) => {
        const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data, true)
        const requestData: any = {
            id: workflowID,
            name: values.workflowName,
            diagram: attachment?.id,
            description: getCkeditorData.current?.props?.data,
            objectId: values.object,
            status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.details?.status) : hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED,
            useCaseIds: values.useCases,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
        }
        setIsCreateMore(values.createMore);
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            if (!attachment?.id) {
                attachmentRef.current.scrollIntoView('img')
                ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.workflow')
                return
            }
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.workflow' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {
                },
            })
        }
        // } else {
        //   validateWorkFlowExplanation(getCkeditorData.current?.props?.data)
        // }
    }, 500)

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const changeObject = (e) => {
        form.setFieldsValue({
            [`useCases`]: undefined,
        })
        if (e) {
            dispatch(getListFunction(e))
        }
        //dispatch(getListFunction(state.listObjects ? state.listObjects[sourceObjectIndex].id : null))
    }

    const validateWorkFlowExplanation = (e) => {
        const err: any[] = []
        if (e && e.length > 0) {
            err.push({
                name: 'workflowExplanation',
                errors: null,
            })
        } else {
            err.push({
                name: 'workflowExplanation',
                errors: [` ${intl.formatMessage({ id: `IEM_1` })}`],
            })
        }
        form.setFields(err)
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }

    useEffect(() => {
        if (!state.details?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'diagram', title: intl.formatMessage({ id: 'create-workflow.label.workflow-diagram' }), },
            { field: 'workflow-explanation', title: intl.formatMessage({ id: 'create-workflow.label.workflow-explanation' }), },
            { field: 'object', title: intl.formatMessage({ id: 'create-workflow.label.object' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'create-workflow.label.use-case' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'createscreen.label.req' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'createscreen.label.documentation' }), },
            { field: 'implementation', title: intl.formatMessage({ id: 'function.implementation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.details.projectId, itemId: state.details.id, fields }));

        const payload = {
            projectId: state.details.projectId,
            itemId: state.details.id,
            artefact: ARTEFACT_COMMENT.WORKFLOW,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.details])

    const tagRender = (props) => {
        const { label, name, value, closable, onClose } = props;


        return (
            <Tag
                // color={value}
                // onMouseDown={onPreventMouseDown}
                closable={closable}
                onClose={onClose}
                style={{
                    marginRight: 3,
                    border: 'none',
                }}
                title={label}
            >
                {label.length > 20 ? label.substring(0, 20) + '...' : label}
            </Tag>
        );
    };

    //#endregion COMMENT INIT

    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name="creareworkflow"
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb={false}
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'create-workflow.title-create' : 'create-workflow.title-update' })}
                >
                    <Space size="small">
                        {screenMode == SCREEN_MODE.CREATE ?
                            (<Form.Item
                                style={{ marginBottom: '0px' }}
                                valuePropName="checked"
                                name="createMore"
                                wrapperCol={{ span: 24 }}
                            >
                                <Checkbox disabled={state.isLoading}>
                                    {intl.formatMessage({
                                        id: 'createobject.checkbox.create-another',
                                    })}
                                </Checkbox>
                            </Form.Item>) : <></>
                        }
                        <Button onClick={debounce(confirmCancel, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>
                        {screenMode == SCREEN_MODE.CREATE || state.details?.status == STATUS.DRAFT || state.details?.status == STATUS.REJECT || state.details?.status == STATUS.REJECT_CUSTOMER || (state.details?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA))) ?
                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button type="primary" ghost htmlType="submit" onClick={() => {
                                    setIsDraft(false)
                                    setIsSubmitForm(true)
                                }}>
                                    {intl.formatMessage({ id: 'common.action.submit' })}
                                </Button>
                            </Form.Item> : <></>}

                        <Form.Item style={{ marginBottom: '0px' }}>
                            <Button
                                onClick={() => {
                                    setIsDraft(true)
                                    setIsSubmitForm(true)
                                }}
                                className="success-btn"
                                htmlType="submit"
                            >
                                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.update' })}
                            </Button>
                        </Form.Item>
                    </Space>
                </LavPageHeader>
            </div>

            <Row align="middle">
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderCommonStatusBadge(state.details?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>

            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'create-workflow.card.workflow-information' })}>
                    {screenMode == SCREEN_MODE.EDIT &&
                        <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                            <Form.Item rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                <Input
                                    value={state.details?.code}
                                    disabled
                                ></Input>
                            </Form.Item>
                        </FormGroup>
                    }

                    <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                        <Form.Item
                            name="workflowName"
                            rules={[
                                {
                                    required: true,
                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                },
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        >
                            <Input
                                placeholder={`${intl.formatMessage({
                                    id: `create-workflow.place-holder.wf`,
                                })}${intl.formatMessage({
                                    id: `common.mandatory.*`,
                                })}`}
                                maxLength={255}
                            />
                        </Form.Item>
                    </FormGroup>

                    <div ref={attachmentRef}>
                        <FormGroup label={
                            <TriggerComment screenMode={screenMode} field="diagram">
                                {intl.formatMessage({ id: 'create-workflow.label.workflow-diagram' })}
                            </TriggerComment>}>
                            <Form.Item name="img">
                                <LavAttachmentUpload artefactType={COM_ARTEFACT_TYPE_ID.WORKFLOW} attachment={attachment} isCommon name="file" supportPDF onChange={setAttachment} />
                            </Form.Item>
                        </FormGroup>
                    </div>

                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="workflow-explanation">
                            {intl.formatMessage({ id: 'create-workflow.label.workflow-explanation' })}
                        </TriggerComment>}>
                        <Form.Item name="workflowExplanation">
                            <CkeditorMention
                                isCommon
                                ref={getCkeditorData}
                                data={screenMode == SCREEN_MODE.EDIT ? state.details?.description : ''}
                            />
                        </Form.Item>
                    </FormGroup>
                </Card>
                {/* label={intl.formatMessage({ id: 'create-workflow.label.object' })}> */}
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.reference' })}>
                    <FormGroup inline labelSpan={3} controlSpan={21} label={
                        <TriggerComment screenMode={screenMode} field="object">
                            {intl.formatMessage({ id: 'create-workflow.label.object' })}
                        </TriggerComment>}>
                        <Form.Item name="object">
                            <Select
                                filterOption={(input, option: any) =>
                                    option.children
                                        .toLowerCase()
                                        .indexOf(input.toLowerCase()) >= 0
                                }
                                onChange={changeObject}
                                showSearch
                                allowClear
                                optionLabelProp="label"
                                tagRender={tagRender}
                            >
                                {state.listObjects?.map(
                                    (item: any, index: any) =>
                                        item.status !== STATUS.DELETE &&
                                        item.status !== STATUS.CANCELLED && (
                                            <Option key={index} value={item.id} label={item.name}>
                                                {item.name}
                                            </Option>
                                        )
                                )}
                            </Select>
                        </Form.Item>
                    </FormGroup>
                    {/* label={intl.formatMessage({ id: 'create-workflow.label.use-case' })} */}
                    <FormGroup inline labelSpan={3} controlSpan={21} label={
                        <TriggerComment screenMode={screenMode} field="use-case">
                            {intl.formatMessage({ id: 'create-workflow.label.use-case' })}
                        </TriggerComment>}>
                        <Form.Item name="useCases">
                            <Select
                                optionLabelProp="label"
                                mode="multiple"
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                filterOption={(input, option: any) => {
                                    return option?.name?.toLowerCase()?.includes(input?.toLowerCase())
                                }}
                                tagRender={tagRender}
                            >
                                {state.listFunctions?.map(
                                    (item: any, index: any) =>
                                        <Option key={index} value={item.id} label={item.name}>
                                            {item.name}
                                        </Option>)}
                            </Select>
                        </Form.Item>
                    </FormGroup>
                </Card>
            </Space>
        </Form>
    </Spin>
}
export default WorkFlowFormPage
