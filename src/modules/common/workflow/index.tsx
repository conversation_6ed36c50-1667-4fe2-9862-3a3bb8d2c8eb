import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import { useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS_COMMON_FILTER } from '../../../constants'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import LavTable from '../../../helper/component/lav-table'
import { getColumnDropdownFilterProps, getColumnSearchProps, renderCommonStatusBadge } from '../../../helper/share'
import WorkFlowFormPage from './form/form'

const Worflow = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  const columns = [
    {
      title: intl.formatMessage({ id: 'view-workflow.column.workflow-code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record) => {
        const href = `${APP_ROUTES.COMMON_WORKFLOW_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'view-workflow.column.workflow' }),
      dataIndex: 'name',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },

    {
      title: intl.formatMessage({ id: 'view-workflow.column.status' }),
      dataIndex: 'status',
      width: '80px',
      ...getColumnDropdownFilterProps(STATUS_COMMON_FILTER, 'Statuses'),
      sorter: true,
      render: (record) => renderCommonStatusBadge(record),
    },
  ]


  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return <Button ghost={true}
      type='primary'
      className='lav-btn-create'
      icon={<PlusOutlined />}
      onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'create-workflow.button.create-workflow' })}
    </Button>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} />
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return children 
  }
  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        screenMode === SCREEN_MODE.VIEW ?
          <LavTable
            showBreadcumb={false}
            title="view-workflow.header.workflow-list"
            artefact_type="common.artefact.common-workflow"
            apiUrl={API_URLS.COMMON_WORKFLOW}
            columns={columns}
            isCommon={true}
            artefactType={COM_ARTEFACT_TYPE_ID.WORKFLOW}
            deleteComponent={DeleteComponent}
            updateComponent={UpdateComponent}
            createComponent={CreateComponent}
          /> : <></>
      }

      {
        screenMode === SCREEN_MODE.CREATE ? <WorkFlowFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <WorkFlowFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} workflowID={id} /> : <></>
      }
    </Space>
  )
}

export default Worflow
