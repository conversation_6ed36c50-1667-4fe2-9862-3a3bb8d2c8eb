import React, { FC, useEffect } from 'react'
import { Space } from 'antd'
import intl from '../../../config/locale.config'
import { API_URLS, BUTTON_TYPE, COMMITTEE_PERMISSION, COM_ARTEFACT_TYPE_ID, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import LavTable from '../../../helper/component/lav-table'
import { renderCommitteeStatusBadge } from '../../../helper/share'
import CommonCommitteeForm from './form'

const CommonCommittee: FC = () => { 
    useEffect(() => {        
        document.title = intl.formatMessage({ id: 'committee.title' });
    }, []);

    const columns = [
        {
            title: intl.formatMessage({ id: 'committee.title.column.account' }),
            dataIndex: 'account',
            sortOrder: 'ascend'
            // sorter: true,
            // ...getColumnSearchProps('account', SEARCH_TYPE.TEXT)
        },
        {
            title: intl.formatMessage({ id: 'committee.column.permission' }),
            dataIndex: 'role',
            // sorter: true,
            render: text => {
                return COMMITTEE_PERMISSION.find((e) => e.value == text)?.label
            }
        },
        {
            title: intl.formatMessage({ id: 'committee.column.title' }),
            dataIndex: 'additionalInfo',
            // sorter: true,
        },
        {
            title: intl.formatMessage({ id: 'committee.column.status' }),
            dataIndex: 'status',
            // ...getColumnSearchProps('activeStatus', SEARCH_TYPE.CHECK_BOX),
            // sorter: true,
            render: text => {
                return renderCommitteeStatusBadge(text)
            }
        },
    ]
    const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
        return <CommonCommitteeForm screenMode={SCREEN_MODE.CREATE} onFinish={() => handleDataChange()} />
    }

    const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
        return <CommonCommitteeForm onFinish={() => handleDataChange()} screenMode={SCREEN_MODE.EDIT} committeeID={record.id} buttonType={BUTTON_TYPE.ICON} />
    }

    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return children;
    }

    return (
        <>
            <Space direction="vertical" size="middle" className="full-width p-20px">
                <LavTable
                    showBreadcumb={false}
                    title="committee.title"
                    artefact_type="common.artefact.common-committee"
                    apiUrl={API_URLS.COMMON_COMMITTEE}
                    columns={columns}
                    artefactType={COM_ARTEFACT_TYPE_ID.COMMITTEE}
                    isCommon={true}
                    updateComponent={UpdateComponent}
                    createComponent={CreateComponent}
                    deleteComponent={DeleteComponent}
                    committee={true}
                />
            </Space>
        </>
    )
}

export default CommonCommittee
