export interface CommonCommitteeState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  detail?: CommonCommitteeDetail | null,
  isModalShow?:boolean
}
export interface CommonCommitteeDetail {
  id?: number | null,
  account: string,
  email: string,
  fullName: string,
  additionalInfo: string,
  status: number,
  role: number | null
}

export const defaultState = {
  detail: {
    id: null,
    account: '',
    email: 'TBU',
    fullName: 'TBU',
    additionalInfo: '',
    status: 1,
    role: 1
  },
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_COMMITEE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_COMMITEE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_COMMITEE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_COMMITEE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_COMMITEE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_COMMITEE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_COMMITEE/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_COMMITEE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_COMMITEE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_COMMITEE/GET_DETAIL_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_COMMITEE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_COMMITEE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_COMMITEE/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/COMMON_COMMITEE/SET_MODAL_VISIBLE',
}
