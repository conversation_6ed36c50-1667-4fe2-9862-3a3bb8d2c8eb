import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { BUTTON_TYPE, COMMITTEE_PERMISSION, COMMITTEE_STATUS, MESSAGE_TYPES, SCREEN_MODE } from '../../../../constants'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import { renderCommitteeStatusBadge } from '../../../../helper/share'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { CommonCommitteeState } from '../type'

const { confirm } = Modal
const { Title } = Typography
const { Option } = Select
const { TextArea } = Input

interface CommonCommitteeFormProps {
  committeeID?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface CommonCommitteeFormModalProps {
  committeeID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonCommitteeFormModal = ({ committeeID, screenMode, onFinish, onDismiss }: CommonCommitteeFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.CommonCommittee) as CommonCommitteeState
  const [isCreateMore, setIsCreateMore] = useState(false);
  const modalConfirmConfig = useModalConfirmationConfig()

  // Destroy
  useEffect(() => {
    return () => {
      resetForm();
      dispatch(resetState(null));
    }
  }, [])

  useEffect(() => {
    if (committeeID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(committeeID))
    }
  }, [screenMode, committeeID])

  useEffect(() => {
    if (committeeID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        ...state.detail
      })
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm();
      } else {
        onDismiss();
      }
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])


  const onSubmit = debounce(async (values: any, st?: string) => {
    const requestData: any = {
      ...values,
      id: committeeID || null,
      status: values.status,
    }
    setIsCreateMore(values.createMore);
    if (!committeeID) {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: 'CFD_6_1' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.common-committee' }) }
        ),
        onOk() {
          requestData.messageAction = SCREEN_MODE.CREATE;
          dispatch(createRequest(requestData));
        },
        onCancel() {

        },
      })
    } else {
      requestData.messageAction = MESSAGE_TYPES.UPDATE;
      dispatch(updateRequest(requestData));
    }

  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss()
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    form.resetFields([
      'account',
      'additionalInfo',
      'createMore'
    ])
    form.setFieldsValue({
      'role': 1,
      'status': 1
    })
  }

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    size="smalls"
    visible={true}
    footer={null}
  >
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={screenMode === SCREEN_MODE.EDIT ? 16 : 14}>
            <Space size="large">
              <Title level={4}>{intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'committee.btn.create' : 'committee.btn.edit' })}</Title>
              {screenMode === SCREEN_MODE.EDIT ? renderCommitteeStatusBadge(state.detail?.status) : <></>}
            </Space>
          </Col>

          <Col span={screenMode === SCREEN_MODE.EDIT ? 8 : 10}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                <Button className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.create' : 'common.action.update' })}
                </Button>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Space direction="vertical" size="middle" style={{ padding: '20px 10px 2px 0' }}>
        <Row gutter={24}>
          <Col span={12}>
            <FormGroup inline labelSpan={8} controlSpan={16} required label={intl.formatMessage({ id: 'committee.title.column.account' })}>
              <Form.Item name="account"
                rules={[
                  { required: true, message: intl.formatMessage({ id: 'IEM_1' }) },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) { throw new Error(intl.formatMessage({ id: 'IEM_1' })) }
                    }
                  }
                ]}
              >
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>

            <FormGroup inline labelSpan={8} controlSpan={16} required label={intl.formatMessage({ id: 'committee.column.status' })}>
              <Form.Item name="status" rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                <Select>
                  {
                    COMMITTEE_STATUS.map((e, idx) => {
                      return <Option key={idx} value={e.value}>{e.label}</Option>
                    })
                  }
                </Select>
              </Form.Item>
            </FormGroup>
          </Col>
          <Col span={12}>

            <FormGroup inline labelSpan={8} controlSpan={16} required label={intl.formatMessage({ id: 'committee.column.permission' })}>
              <Form.Item name="role" rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                <Select>
                  {
                    COMMITTEE_PERMISSION.map((e, idx) => {
                      return <Option key={idx} value={e.value}>{e.label}</Option>
                    })
                  }
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup inline labelSpan={8} controlSpan={16} label={intl.formatMessage({ id: 'committee.column.additionalInfo' })}>
              <Form.Item name="additionalInfo" >
                <TextArea rows={4} />
              </Form.Item>
            </FormGroup>
          </Col>
        </Row>
      </Space>
    </Form>
  </CustomModal>
}
const CommonCommitteeForm = ({ committeeID, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: CommonCommitteeFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])


  return <>
    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button
          ghost={screenMode === SCREEN_MODE.CREATE}
          type='primary'
          className={screenMode === SCREEN_MODE.CREATE ? '' : 'success-btn'}
          onClick={() => setIsModalVisible(true)}
          icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
        >
          {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'committee.btn.create' : 'committee.btn.update' })}
        </Button> :
        buttonType === BUTTON_TYPE.ICON ?
          <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
          <></>
    }
    {isModalVisible === true ? <CommonCommitteeFormModal committeeID={committeeID} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}
export default CommonCommitteeForm
