import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../../constants'
import { apiCall } from '../../../helper/api/aloApi'
import { ShowAppMessage } from '../../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_COMMITTEE + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_COMMITTEE + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.common-committee')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-committee')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.COMMON_COMMITTEE, request as any)

      // If account is current User
      try {
        const currentUserStored = localStorage.getItem('currentUserProjects') || '';
        let currentUserInfo = JSON.parse(currentUserStored);
        if (request.account === currentUserInfo.useName) {
          currentUserInfo.role = request.role;
          localStorage.setItem('currentUserProjects', JSON.stringify(currentUserInfo))
        }
      } catch (err) {

      }

      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.common-committee')
      yield put(createSuccess(null));
    } catch (err: any) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-committee', 'committee.title.column.account')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.COMMON_COMMITTEE + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      // If account is current User
      try {
        const currentUserStored = localStorage.getItem('currentUserProjects') || '';
        let currentUserInfo = JSON.parse(currentUserStored);
        if (request.account === currentUserInfo.useName) {
          currentUserInfo.role = request.role;
          localStorage.setItem('currentUserProjects', JSON.stringify(currentUserInfo))
        }
      } catch (err) {

      }
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.common-committee')
      yield put(updateSuccess(null));
    } catch (err: any) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-committee', 'committee.title.column.account')
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
}
export default function* CommonCommitteeSaga() {
  yield all([fork(watchFetchRequest)])
}
