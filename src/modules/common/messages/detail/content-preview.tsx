import { <PERSON>, <PERSON>, Row, Space, Typography } from "antd"
import intl from "../../../../config/locale.config"
import { MessageCategory } from "../type"

const { Title, Text } = Typography

const CommonMessDetailInfo = ({ data }) => {
    return <Space direction="vertical" size="middle">
        {/* <Space size="large">
            <span>
                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
            </span> */}
            {/* {renderCommonStatusBadge(data?.status)} */}
        {/* </Space> */}

        <Card
            title={
                <Title level={5}>
                    {`${intl.formatMessage({
                        id: 'common-mess.card.message-infomation',
                    })}`}
                </Title>
            }
            bordered={true}
        >
            <Row gutter={[16, 4]}>
                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'common-mess.label.category',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>
                    {
                        MessageCategory.filter(
                            (item: any) => item.id === data?.category
                        )[0]?.name
                    }
                </Col>

                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'common-mess.label.message',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>{data?.content}</Col>
            </Row>
        </Card>
    </Space>
}
export default CommonMessDetailInfo