import AppState from '@/store/types'
import { Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography } from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import DeleteButton from '../../../../helper/component/commonButton/DeleteButton'
import LavButtons from '../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { deleteRequest } from '../action'
import MessageForm from '../form'
import { MessageCategory } from '../type'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    messID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any
}
const RightControl = ({ data, messID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'category', title: intl.formatMessage({ id: 'create-mess.label.category' }), },
            { field: 'message', title: intl.formatMessage({ id: 'create-mess.label.message' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.COMMON_MESSAGE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return (data?.status !== STATUS_COMMON.DELETED) ? <DeleteButton
            type={BUTTON_TYPE.TEXT}
            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.message' }) })}
            okCB={() => dispatch(deleteRequest(messID))}
            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
    }
    return (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                        <Breadcrumb.Item>
                            <Link to={APP_ROUTES.COMMON_MESSAGE}>{intl.formatMessage({ id: 'commonmessage.page_title' })}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {intl.formatMessage({ id: `create-mess.label.message` })} - {data?.code}
                    </Title>
                </div>

                <Space size="small">
                    <LavButtons
                        isCommon={true}
                        url={`${API_URLS.COMMON_MESSAGE}/${messID}`}
                        artefact_type="common.artefact.message"
                        status={data?.status}
                        isHasRemove={true}
                        deleteButton={DeleteComponent}
                        changePage={() => onChange()}
                    >
                        {/* Update record */}
                        {(data?.status !== STATUS_COMMON.DELETED) ?
                            <Button
                                type='primary'
                                className='lav-btn-create'
                                onClick={() => {
                                    setScreenMode()
                                }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                        }
                    </LavButtons>
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        {/* <Space size="large">
                            <span>
                                <TriggerComment field="version">
                                    <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
                                </TriggerComment>
                            </span> */}
                        {/* {renderCommonStatusBadge(data?.status)} */}
                        {/* </Space> */}

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'common-mess.card.message-infomation',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={3}>
                                    <TriggerComment field="category">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'common-mess.label.category',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    {
                                        MessageCategory.filter(
                                            (item: any) => item.id === data?.category
                                        )[0]?.name
                                    }
                                </Col>

                                <Col span={3}>
                                    <TriggerComment field="message">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'common-mess.label.message',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>{data?.content}</Col>
                            </Row>
                        </Card>

                        <LavReferences data={data} isCommon />
                        <Col span={24}>
                            <LavCommonAuditTrail data={data?.auditTrails} />
                        </Col>

                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    )
}

export default RightControl
