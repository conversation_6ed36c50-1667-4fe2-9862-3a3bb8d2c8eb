import { extractProjectCode } from '../../../../helper/share'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { API_URLS, APP_ROUTES, COM_ARTEFACT_TYPE_ID, PROJECT_PREFIX, SCREEN_MODE } from '../../../../constants'
import AppState from '../../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import CommonMessageForm from '../form'
import { CommonMessagesState } from '../type'
import LavLeftControl from './../../../_shared/left-menu'
import RightControl from './content'
import { PlusOutlined } from '@ant-design/icons'
import CommonMessageFormModalPage from '../form/form'
import intl from '../../../../config/locale.config'

const CommonMessageDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const state = useSelector<AppState | null>((s) => s?.CommonMessage) as CommonMessagesState;

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.messID) {
      dispatch(getDetailRequest(props.match.params.messID))
    }
  }, [props])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${APP_ROUTES.COMMON_MESSAGE}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true)
    dispatch(getDetailRequest(props.match.params.messID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)
  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${APP_ROUTES.COMMON_MESSAGE_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>
            <Col span={5}>
              <LavLeftControl
                activeId={props.match.params.messID}
                apiUrl={API_URLS.COMMON_REFERENCE_MESSAGES}
                route={APP_ROUTES.COMMON_MESSAGE_DETAIL}
                title='common-mess.header.title'
                artefactType={COM_ARTEFACT_TYPE_ID.MESSAGE}
                reload={reload}
                reloadSuccess={() => setReload(false)}
                isCommon
                handleCreate={handleCreate}
                hideStatus={true}
              >
                {
                  <Button ghost={true}
                    type='primary'
                    className='lav-btn-create'
                    icon={<PlusOutlined />}
                    onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common-mess.label.create-mess' })}
                  </Button>
                }
              </LavLeftControl>
            </Col>
            <Col span={19}>
              <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} messID={props.match.params.messID} setScreenMode={() => setScreenMode(SCREEN_MODE.EDIT)} isModalShow={state?.isModalShow} />
            </Col>
          </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <CommonMessageFormModalPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <CommonMessageFormModalPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
            }} messID={props.match.params.messID} />
          </Col> : <></>
      }
    </Row>
  )
}

export default CommonMessageDetail
