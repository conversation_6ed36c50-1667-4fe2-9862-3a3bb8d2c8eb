export interface CommonMessagesState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: MessageDetail | null,
  selectedData?: MessageDetail | null,
  isModalShow?:boolean
}
export interface MessageDetail {
  id?: number | null,
  code: string,
  status: number,
  category: number | null,
  content: string,
  projectId?: number
}

export const defaultState : CommonMessagesState = {
  detail: {
    id: null,
    code: '',
    status: 0,
    category: null,
    content: '',
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
}

export const MessageCategory = [
  { id: 1, name: 'Error Message' },
  { id: 2, name: 'Inline Error Message' },
  { id: 3, name: 'Confirmation Dialog' },
  { id: 4, name: 'Success Dialog' }
]

export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_MESSAGE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_MESSAGE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_MESSAGE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_MESSAGE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_MESSAGE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_MESSAGE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_MESSAGE/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_MESSAGE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_MESSAGE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_MESSAGE/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/COMMON_MESSAGE/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/COMMON_MESSAGE/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/COMMON_MESSAGE/GET_LIST_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/COMMON_MESSAGE/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/COMMON_MESSAGE/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/COMMON_MESSAGE/GET_LIST_OBJECTS_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_MESSAGE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_MESSAGE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_MESSAGE/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/COMMON_MESSAGE/SET_MODAL_VISIBLE',
}
