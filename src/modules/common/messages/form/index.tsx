import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import React, { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, SCREEN_MODE, STATUS } from '../../../../constants'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { renderStatusBadge } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import { CommentState } from '../../../../modules/_shared/comment/type'
import TriggerComment from '../../../_shared/comment/trigger-comment'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { CommonMessagesState, MessageCategory } from '../type'

const { Text, Title } = Typography
const { confirm } = Modal
const { Option } = Select

interface CommonMessageFormProps {
  messID?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface CommonMessageFormModalProps {
  messID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonMessageFormModal = ({ messID, screenMode, onFinish, onDismiss }: CommonMessageFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.CommonMessage) as CommonMessagesState
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()

  // Destroy
  useEffect(() => {
    return () => {
      dispatch(resetState(null));
      resetForm();
    }
  }, [])

  useEffect(() => {
    if (messID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(messID))
    }
  }, [screenMode, messID])

  useEffect(() => {
    if (messID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        ...state.detail
      })
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm();
        form.setFieldsValue({
          createMore: isCreateMore
        })
      } else {
        onDismiss();
      }
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce(async (values: any, st?: string) => {
    const requestData: any = {
      ...values,
      id: messID || null,
    }
    setIsCreateMore(values.createMore);
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    // } else {
    //   confirm({
    //     ...modalConfirmConfig,
    //     content: intl.formatMessage(
    //       { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
    //       { Artefact: intl.formatMessage({ id: 'common.artefact.message' }) }
    //     ),
    //     onOk() {
    //       requestData.messageAction = MESSAGE_TYPES.SUBMIT;
    //       dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    //     },
    //     onCancel() {

    //     },
    //   })
    // }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss()
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    form.resetFields([
      'version',
      'code',
      'category',
      'content',
      'storage',
      'jira',
      'confluence',
      'reqElicitation',
      'documentation',
      'reviewer',
      'dueDate',
      'completeDate'
    ])
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'category', title: intl.formatMessage({ id: 'common-mess.label.category' }), },
      { field: 'message', title: intl.formatMessage({ id: 'common-mess.label.message' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_MESSAGE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    size="medium"
    visible={true}
    footer={null}
  >
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={10}>
            <Space size="large">
              <Title level={4}>{intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'common-mess.label.update-mess' : 'common-mess.label.create-mess' })}</Title>
              {/* {screenMode === SCREEN_MODE.EDIT ? renderStatusBadge(state.detail?.status) : <></>} */}
            </Space>
          </Col>

          <Col span={14}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                <Button className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save' : 'common.action.update' })}
                </Button>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 280}>
        <Row align="middle">
          <Col span={2}>
            <TriggerComment screenMode={screenMode} field="version">
              <Text>{intl.formatMessage({ id: 'createobject.place-holder.version' })}</Text>
            </TriggerComment>
          </Col>

          <Col span={2}>
            <Form.Item
              className="mb-0"
              name="version"
              rules={[
                // { required: true, message: intl.formatMessage({ id: 'IEM_1' }) },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
              ]}
            >
              <Input
                placeholder={intl.formatMessage({ id: `createobject.place-holder.version` })}
                maxLength={255}
              />
            </Form.Item>
          </Col>
        </Row>

        <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
          <Card className='rq-form-block' title={intl.formatMessage({ id: 'common-mess.card.message-infomation' })}>
            {
              screenMode === SCREEN_MODE.EDIT ? <FormGroup label={intl.formatMessage({ id: 'common-mess.label.code' })}>
                <Form.Item rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                  <Input maxLength={255} value={state.detail?.code} disabled></Input>
                </Form.Item>
              </FormGroup> : <></>
            }

            <FormGroup className="rq-fg-comment" required label={
              <TriggerComment screenMode={screenMode} field="category">
                {intl.formatMessage({ id: 'common-mess.label.category' })}
              </TriggerComment>
            }>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ]}
                name="category"
              >
                <Select
                  showSearch
                  filterOption={(input, option: any) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {MessageCategory.map((item: any) => (
                    <Option key={item.id} value={item.id}>
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup className="rq-fg-comment" required label={
              <TriggerComment screenMode={screenMode} field="message">
                {intl.formatMessage({ id: 'common-mess.label.message' })}
              </TriggerComment>}>
              <Form.Item
                name="content"
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }
                ]}
              >
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>
          </Card>
        </Space>
      </Scrollbars>
    </Form>
  </CustomModal>
}
const CommonMessageForm = ({ messID, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: CommonMessageFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return <>
    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button
          ghost={screenMode === SCREEN_MODE.CREATE}
          type='primary'
          className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
          onClick={() => 
            {
              setIsModalVisible(true)
            }
          }
          icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
        >
          {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common-mess.label.create-mess' : 'common.action.update' })}
        </Button> :
        buttonType === BUTTON_TYPE.ICON ?
          <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
          <></>
    }
    {isModalVisible === true ? <CommonMessageFormModal messID={messID} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}

export default CommonMessageForm
