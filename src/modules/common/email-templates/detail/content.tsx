import AppState from '@/store/types'
import {
    Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Modal, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import DeleteButton from '../../../../helper/component/commonButton/DeleteButton'
import LavButtons from '../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../helper/component/lav-references'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, getProjectName } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { deleteRequest } from '../action'
import EmailTemplateForm from '../form'

const { Title, Text } = Typography
const { confirm } = Modal

interface RightControlProps {
    data: any | [],
    emailID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any
}
const RightControl = ({ data, emailID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const modalConfirmConfig = useModalConfirmationConfig()

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            // { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'objective', title: intl.formatMessage({ id: 'create-email.label.objective' }), },
            { field: 'send-to', title: intl.formatMessage({ id: 'create-email.label.send-to' }), },
            { field: 'cc', title: intl.formatMessage({ id: 'create-email.label.cc' }), },
            { field: 'subject', title: intl.formatMessage({ id: 'create-email.label.subject' }), },
            { field: 'body', title: intl.formatMessage({ id: 'create-email.label.body' }), },
            { field: 'remarks', title: intl.formatMessage({ id: 'create-email.label.remarks' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.COMMON_EMAIL_TEMPLATE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT

    return (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                        <Breadcrumb.Item>
                            <Link to={APP_ROUTES.COMMON_EMAIL}>{intl.formatMessage({ id: 'commonemail.page_title' })}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {intl.formatMessage({ id: `create-email.title.create-email` })} - {data?.code}
                    </Title>
                </div>
                <Space size="small">
                    {/* Delete record */}
                    <LavButtons
                        isCommon={true}
                        url={`${API_URLS.COMMON_EMAIL}/${emailID}`}
                        artefact_type="common.artefact.email"
                        status={data?.status}
                        isHasRemove={true}
                        changePage={() => onChange()}
                    >

                        {(data?.status !== STATUS_COMMON.DELETED) ? <DeleteButton
                            type={BUTTON_TYPE.TEXT}
                            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.email' }) })}
                            okCB={() => dispatch(deleteRequest(emailID))}
                            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
                        }

                        {/* Update record */}
                        {(data?.status !== STATUS_COMMON.DELETED) ? 
                        <Button
                        type='primary'
                        className='lav-btn-create'
                        onClick={() => {
                            setScreenMode()
                        }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                        }
                    </LavButtons>
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        {/* <Space size="large">
                            <span>
                                <TriggerComment field="version">
                                    <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
                                </TriggerComment>
                            </span> */}
                            {/* {renderCommonStatusBadge(data?.status)} */}
                        {/* </Space> */}
                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'create-email.card.email-infomation',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={3}>
                                    <TriggerComment field="objective">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.objective',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <Text>{data?.objective}</Text>
                                </Col>

                                <Col span={3}>
                                    <TriggerComment field="send-to">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.send-to',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <Text>{data?.sendTo}</Text>
                                </Col>
                                <Col span={3}>
                                    <TriggerComment field="cc">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.cc',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <Text>{data?.cc}</Text>
                                </Col>
                                <Col span={3}>
                                    <TriggerComment field="subject">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.subject',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>{data?.subject}</Col>
                                <Col span={3}>
                                    <TriggerComment field="body">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.body',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.body }}
                                    ></div>
                                </Col>
                                <Col span={3}>
                                    <TriggerComment field="remarks">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.remarks',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.remark }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>
                        <LavReferences data={data} isCommon />
                        <Col span={24}>
                            <LavCommonAuditTrail data={data?.auditTrails} />
                        </Col>

                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    )
}

export default RightControl
