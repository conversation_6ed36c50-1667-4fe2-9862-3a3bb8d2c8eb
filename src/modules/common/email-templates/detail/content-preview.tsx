import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Typography } from "antd"
import intl from "../../../../config/locale.config"

const { Title, Text } = Typography

const CommonEmailDetailInfo = ({ data }) => {
    return <Space direction="vertical" size="middle">
        {/* <Space size="large">
            <span>
                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
            </span> */}
            {/* {renderCommonStatusBadge(data?.status)} */}
        {/* </Space> */}
        <Card
            title={
                <Title level={5}>
                    {`${intl.formatMessage({
                        id: 'create-email.card.email-infomation',
                    })}`}
                </Title>
            }
            bordered={true}
        >
            <Row gutter={[16, 4]}>
                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-email.label.objective',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>
                    <Text>{data?.objective}</Text>
                </Col>

                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-email.label.send-to',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>
                    <Text>{data?.sendTo}</Text>
                </Col>
                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-email.label.cc',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>
                    <Text>{data?.cc}</Text>
                </Col>
                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-email.label.subject',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>{data?.subject}</Col>
                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-email.label.body',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: data?.body }}
                    ></div>
                </Col>
                <Col span={3}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'create-email.label.remarks',
                        })}:
                    </Text>
                </Col>
                <Col span={21}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: data?.remark }}
                    ></div>
                </Col>
            </Row>
        </Card>
    </Space>
}
export default CommonEmailDetailInfo