export interface CommonEmailTemplateState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?:any | null,
  selectedData?: EmailTemplateDetail | null,
  isLoadingActors?: boolean,
  sendToAndCCList?: any | [],
  isModalShow?:boolean
}
export interface EmailTemplateDetail {
  id?: number | null,
  code: string,
  status: number,
  objective: string,
  sendTo: string,
  cc: string,
  subject: string,
  body: string,
  remarks: string,
}

export const defaultState : CommonEmailTemplateState = {
  detail: {
    id: null,
    code: '',
    version: '',
    status: 0,
    objective: '',
    sendTo: '',
    subject: '',
    body: '',
    remarks: '',
  },
  selectedData:null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  isLoadingActors: false,
  sendToAndCCList: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_EMAIL_TEMPLATE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_EMAIL_TEMPLATE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_EMAIL_TEMPLATE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_EMAIL_TEMPLATE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_EMAIL_TEMPLATE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_EMAIL_TEMPLATE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_EMAIL_TEMPLATE/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_LIST_FAILED',

  GET_LIST_ACTORS_REQUEST = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_LIST_ACTORS_REQUEST',
  GET_LIST_ACTORS_SUCCESS = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_LIST_ACTORS_SUCCESS',
  GET_LIST_ACTORS_FAILED = '@@MODULES/COMMON_EMAIL_TEMPLATE/GET_LIST_ACTORS_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_EMAIL_TEMPLATE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_EMAIL_TEMPLATE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_EMAIL_TEMPLATE/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/COMMON_EMAIL_TEMPLATE/SET_MODAL_VISIBLE',
}
