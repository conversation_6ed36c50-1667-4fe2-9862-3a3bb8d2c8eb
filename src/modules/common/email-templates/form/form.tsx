import { CommentState } from '@/modules/_shared/comment/type'
import AppState from '@/store/types'
import { isDraft } from '@reduxjs/toolkit'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, SCREEN_MODE } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, getReferencesFromEditor } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { CommonEmailTemplateState } from '../type'

const { Text, Title } = Typography
const { confirm } = Modal


interface EmailTemplateFormModalProps {
  emailID?: number
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonEmailTemplateFormModalPage = ({ emailID, screenMode, onFinish, onDismiss }: EmailTemplateFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorDataBody: any = createRef()
  const getCkeditorDataRemarks: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.CommonEmailTemplate) as CommonEmailTemplateState
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [bodyMessage, setBodyMessage] = useState('');
  const [remarksEmail, setRemarksEmail] = useState('');
  // Destroy
  useEffect(() => {
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])


  useEffect(() => {
    if (emailID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(emailID))
    }
  }, [screenMode, emailID])

  useEffect(() => {
    if (emailID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        ...state.detail,
      })
      setBodyMessage(state.detail.body)
      setRemarksEmail(state.detail.remark)
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm();
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss();
      }
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce(async (values: any, st?: string) => {
    let bodyData = ''
    let remarkData = ''
    if (getCkeditorDataBody.current?.props?.data) {
      bodyData = getCkeditorDataBody.current.props.data
    }
    if (getCkeditorDataRemarks.current?.props?.data) {
      remarkData = getCkeditorDataRemarks.current.props.data
    }
    let mentionReferences = getReferencesFromEditor(bodyData, true);
    mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(remarkData, true));

    const requestData: any = {
      code: values.code,
      version: values.version,
      objective: values.objective,
      sendTo: values.sendTo,
      cc: values.cc,
      subject: values.subject,
      body: bodyData,
      remark: remarkData,
      id: emailID || null,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    }
    setIsCreateMore(values.createMore);
    requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setBodyMessage('');
    setRemarksEmail('');
    form.resetFields([
      'version',
      'code',
      'objective',
      'sendTo',
      'cc',
      'subject',
      'body',
      'remarks',
    ])
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'objective', title: intl.formatMessage({ id: 'create-email.label.objective' }), },
      { field: 'send-to', title: intl.formatMessage({ id: 'create-email.label.send-to' }), },
      { field: 'cc', title: intl.formatMessage({ id: 'create-email.label.cc' }), },
      { field: 'subject', title: intl.formatMessage({ id: 'create-email.label.subject' }), },
      { field: 'body', title: intl.formatMessage({ id: 'create-email.label.body' }), },
      { field: 'remarks', title: intl.formatMessage({ id: 'create-email.label.remarks' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_EMAIL_TEMPLATE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={10}>
            <Space size="large">
              <Title level={4}>{intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'update-email.label.update-email' : 'create-email.title.create-email' })}</Title>
            </Space>
          </Col>

          <Col span={14}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                <Button className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save' : 'common.action.update' })}
                </Button>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Scrollbars autoHide autoHeight autoHeightMin={windowHeight}>

        <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
          <Card className='rq-form-block' title={intl.formatMessage({ id: 'create-email.card.email-infomation' })}>
            {
              screenMode === SCREEN_MODE.EDIT ?
                <FormGroup inline label={intl.formatMessage({ id: 'common.label.code' })} labelSpan={3} controlSpan={2}>
                  <Form.Item rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input maxLength={255} value={state.detail?.code} disabled></Input>
                  </Form.Item>
                </FormGroup>
                : <></>
            }

            <FormGroup inline className="rq-fg-comment" required label={
              <TriggerComment screenMode={screenMode} field="objective">
                {intl.formatMessage({ id: 'create-email.label.objective' })}
              </TriggerComment>}
              labelSpan={3}
              controlSpan={21}
            >
              <Form.Item
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
                name="objective"
              >
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>

            <FormGroup inline className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="send-to">
                {intl.formatMessage({ id: 'create-email.label.send-to' })}
              </TriggerComment>}
              labelSpan={3}
              controlSpan={21}
            >
              <div className='rq-form-note'>
                {intl.formatMessage({ id: 'email.label.sendToAndCc' })}
              </div>
              <Form.Item name="sendTo"
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}>
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>

            <FormGroup inline className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="cc">
                {intl.formatMessage({ id: 'create-email.label.cc' })}
              </TriggerComment>}
              labelSpan={3}
              controlSpan={21}
            >
              <div className='rq-form-note'>
                {intl.formatMessage({ id: 'email.label.sendToAndCc' })}
              </div>
              <Form.Item name="cc">
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>

            <FormGroup inline required className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="subject">
                {intl.formatMessage({ id: 'create-email.label.subject' })}
              </TriggerComment>}
              labelSpan={3}
              controlSpan={21}
            >
              <Form.Item name="subject"
                rules={[
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  }]}>
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>

            <FormGroup inline required className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="body">
                {intl.formatMessage({ id: 'create-email.label.body' })}
              </TriggerComment>}
              labelSpan={3}
              controlSpan={21}
            >
              <div className='rq-form-note'>
                {intl.formatMessage({ id: 'email.label.body' })}
              </div>
              <Form.Item name="body"
                rules={[{
                  validator: async (rule, value) => {
                    const body = getCkeditorDataBody?.current?.props?.data
                    if (body == '' || body == undefined) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  }
                }]}
              >
                <CkeditorMention isCommon ref={getCkeditorDataBody} data={bodyMessage} />
              </Form.Item>
            </FormGroup>

            <FormGroup inline className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="remarks">
                {intl.formatMessage({ id: 'create-email.label.remarks' })}
              </TriggerComment>}
              labelSpan={3}
              controlSpan={21}
            >
              <Form.Item name="remarks">
                <CkeditorMention isCommon ref={getCkeditorDataRemarks} data={remarksEmail} />
              </Form.Item>
            </FormGroup>
          </Card>
        </Space>
      </Scrollbars>
    </Form>
  </Spin>
}

export default CommonEmailTemplateFormModalPage
