import CustomSvgIcons from '../../../helper/component/custom-icons'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS_COMMON, STATUS_FILTER } from '../../../constants'
import LavTable from '../../../helper/component/lav-table'
import { getColumnDropdownFilterProps, getColumnSearchProps, renderCommonStatusBadge } from '../../../helper/share'
import EmailTemplateForm from './form'
import CommonEmailTemplateFormModalPage from './form/form'
const { Text } = Typography

const CommonEmailTemplate = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  const columns = [
    {
      title: intl.formatMessage({ id: 'email.column.email-code' }),
      dataIndex: 'code',
      width: '5%',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${APP_ROUTES.COMMON_EMAIL_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'email.column.email-objective' }),
      dataIndex: 'objective',
      ...getColumnSearchProps('objective', SEARCH_TYPE.TEXT),
      render: (text) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'email.column.email-subject' }),
      dataIndex: 'subject',
      sorter: true,
      ...getColumnSearchProps('subject', SEARCH_TYPE.TEXT),
      render: (text) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'email.column.send-to' }),
      dataIndex: 'sendTo',
      ...getColumnSearchProps('sendTo', SEARCH_TYPE.TEXT),
    },
   
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'email.button.create-email' })}
      </Button>
    )
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (record.status !== STATUS_COMMON.DELETED) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (record.status !== STATUS_COMMON.DELETED) ? children : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ?
        <LavTable
          showBreadcumb={false}
          title="email.header.title"
          artefact_type="common.artefact.email"
          apiUrl={API_URLS.COMMON_EMAIL}
          artefactType={COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE}
          columns={columns}
          isCommon={true}
          updateComponent={UpdateComponent}
          createComponent={CreateComponent}
          deleteComponent={DeleteComponent}
        /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <CommonEmailTemplateFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenMode={SCREEN_MODE.CREATE} buttonType={BUTTON_TYPE.TEXT} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <CommonEmailTemplateFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} emailID={id} screenMode={SCREEN_MODE.EDIT} /> : <></>
      }
    </Space>
  )
}

export default CommonEmailTemplate
