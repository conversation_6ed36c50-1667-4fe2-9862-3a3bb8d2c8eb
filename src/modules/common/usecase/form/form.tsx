import {
  CheckOutlined,
  ExclamationCircleOutlined,
  PlusOutlined
} from '@ant-design/icons'
import {
  Button, Card, Checkbox, Col, Form,
  Input, Modal, Row, Select,
  Space,
  Spin,
  Table,
  Tag,
  Tooltip,
  Typography
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useRef, useState } from 'react'
import ReactDragListView from 'react-drag-listview'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { APP_COMMON_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, MESSAGE_TYPES, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomAutoCompleteBR from '../../../../helper/component/customBusinessRule'
import FormGroup from '../../../../helper/component/form-group'
import LavAttachmentUpload from '../../../../helper/component/lav-attachment-upload'
import LavPageHeader from '../../../../helper/component/lav-breadcumb'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, getReferencesFromEditor, hasCommonRole, renderCommonStatusBadge, ShowMessgeAdditionalSubmit } from '../../../../helper/share'
import { DEFAULT_DATA_BR } from '../../../../modules/usecase/type'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import AppState from '../../../../store/types'
import { createRequest, getDetailRequest, getListObjectRequest, resetState, setModalVisible, updateRequest } from '../action'
import { CommonFunctionState } from '../type'
import { initComment, initCommentScreen } from './../../../_shared/comment/action'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select


interface CommonCommitteeFormModalProps {
  id?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonFunctionFormModalPage = ({ id, screenMode, onFinish, onDismiss }: CommonCommitteeFormModalProps) => {
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const state = useSelector<AppState | null>(
    (s) => s?.CommonFunction
  ) as CommonFunctionState
  const getCkeditorData: any = createRef()
  const getCkeditorDataDes: any = createRef()
  const [isDraft, setIsDraft] = useState(false);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [attachment, setAttachment] = useState(null) as any
  const attachmentRef = useRef<any>()
  const preConditionRef: any = createRef();
  const postConditionRef: any = createRef()
  const businessRuleRef = useRef<any>()
  const actorRef = useRef<any>()
  const triggerRef = useRef<any>()
  const conditionRef = useRef<any>()
  const objsRef = useRef<any>()
  const [businessRules, setBusinessRules] = useState<any>([])


  const dragProps = {
    onDragEnd(fromIndex, toIndex) {
      const data = [...businessRules]
      const item = data.splice(fromIndex, 1)[0]
      data.splice(toIndex, 0, item)
      setBusinessRules(data)
    },
    handleSelector: 'tr',
    ignoreSelector: 'tr.ant-table-expanded-row',
    nodeSelector: 'tr.ant-table-row',
    enableScroll: true,
    scrollSpeed: 4,
  }

  // Destroy
  useEffect(() => {
    dispatch(getListObjectRequest(null))
    return () => {
      dispatch(resetState(null));
      dispatch(setModalVisible(false))
      setBusinessRules([])
      setAttachment(null)
      form.resetFields()
    }
  }, [])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
  }, [screenMode, id])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      const cloneBusinessRules = [...state.detail?.businessRules]
      const newBusinessRules = cloneBusinessRules.map((item, index) => {
        return { ...item, keyId: index, order: index }
      })

      setBusinessRules(newBusinessRules)
      form.setFieldsValue({
        name: state.detail?.name,
        description: state.detail?.description,
        postCondition: state.detail?.postCondition,
        trigger: state.detail?.trigger,
        actor: state.detail?.actor,

        objects: state.detail?.objects.map(
          (object: any) => object?.name
        ),
      })
      setAttachment(state.detail?.activeFlowPath)
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        form.resetFields();
        form.setFieldsValue({
          createMore: isCreateMore
        })
        setBusinessRules([])
        setAttachment(null)
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss();
      }
      setIsDraft(false);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])





  const onSubmit = debounce(async (values: any, st?: string) => {
    const lstObject = values.objects?.map((objs) => {
      const object: any = state.listObjects.find(
        (item: any) => item.name === objs
      )
      return object?.id
    })

    const triggerData = Object.assign(getCkeditorData.current?.props?.data)

    // Collect all references
    let mentionReferences = getReferencesFromEditor(triggerData, true);
    mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(getCkeditorDataDes.current?.props?.data, true))
    try {
      businessRules.filter((e) => e.status !== -1)?.forEach((e) => {
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.content, true));
      })
    } catch (err) {
      console.log(err);
    }

    const checkBr = businessRules.filter(e => e.step == "" || e.name == "" || e.content == "")

    if (checkBr.length) {
      ShowMessgeAdditionalSubmit('EMSG_16');
      return;
    }

    let requestData: any = {
      "id": id,
      "name": values.name,
      "trigger": getCkeditorData.current?.props?.data,
      "status": isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) :
        hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED
      ,
      "preCondition": preConditionRef.current?.props.data,
      "businessRules": businessRules.filter((e) => e.status !== -1).map((e, index) => {
        return {
          ...e,
          step: parseFloat(e.step)
        }
      }),
      "postCondition": postConditionRef.current?.props.data,
      "version": values.version,
      "description": getCkeditorDataDes?.current?.props?.data,
      "type": 0,
      "activeFlowPath": attachment?.id,
      "actor": values.actor,
      "objectIds": lstObject ? lstObject : [],
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    };

    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      const checkBr = businessRules.filter(e => e.step == "" || e.name == "" || e.content == "")

      if (checkBr.length) {
        ShowMessgeAdditionalSubmit('EMSG_16');
        return;
      }


      // if (!attachment?.id) {
      //   attachmentRef.current.scrollIntoView('file')
      //   ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.use-case');
      //   return;
      // }
      // if (!preConditions || preConditions.length === 0) {
      //   conditionRef.current.scrollIntoView('preConditions')
      //   ShowMessgeAdditionalSubmit('EMSG_31')
      //   return;
      // }
      if (!businessRules || businessRules.length === 0) {
        businessRuleRef.current.scrollIntoView('businessRules')
        ShowMessgeAdditionalSubmit('EMSG_16');
        return;
      }
      if (!requestData.actor || requestData.actor?.length === 0) {
        actorRef.current.scrollIntoView('actor')
        ShowMessgeAdditionalSubmit('EMSG_17');
        return;
      }
      if (Object.keys(triggerData).length === 0) {
        triggerRef.current.scrollIntoView('trigger')
        ShowMessgeAdditionalSubmit('EMSG_18');
        return;
      }
      if (!requestData.objectIds || requestData.objectIds?.length === 0) {
        objsRef.current.scrollIntoView('objects')
        ShowMessgeAdditionalSubmit('EMSG_19');
        return;
      }

      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_2' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.common-usecase' }) }
        ),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() { },
      })
    }
  }, 500)


  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }


  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'actor', title: intl.formatMessage({ id: 'view-use-case-details.label.actor' }), },
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'description', title: intl.formatMessage({ id: 'function.form.description', }), },
      { field: 'trigger', title: intl.formatMessage({ id: 'function.form.trigger' }), },
      { field: 'pre-condition', title: intl.formatMessage({ id: 'function.form.pre-condition' }), },
      { field: 'post-condition', title: intl.formatMessage({ id: 'function.form.post-condition' }), },
      { field: 'activity-flow', title: intl.formatMessage({ id: 'function.form.activity-flow' }), },
      { field: 'business-rule', title: intl.formatMessage({ id: 'view-use-case-details.label.business-rule' }), },
      { field: 'object', title: intl.formatMessage({ id: 'function.form.objects' }), },
    ];

    state?.detail?.businessRules?.forEach((e) => {
      fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
    })


    dispatch(initComment({ projectId: null, itemId: state.detail.id, fields }));

    const payload = {
      projectId: null,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_USE_CASE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])


  const tagRender = (props) => {
    const { label, name, value, closable, onClose } = props;


    return (
      <Tag
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 3,
          border: 'none',
        }}
        title={label}
      >
        {label.length > 20 ? label.substring(0, 20) + '...' : label}
      </Tag>
    );
  };




  const editRowPre = (order, data: any = {}) => {
    if ((data.step == "" || data.name == "" || data.content == "") && (data.editting)) {
      ShowMessgeAdditionalSubmit('EMSG_16');
      return;
    }

    const list = businessRules.map((record, index) => {
      let nxtRecord = record;
      if (index === order) {
        nxtRecord = {
          ...nxtRecord,
          editting: !nxtRecord.editting,
        };
      }
      return nxtRecord;
    })
    setBusinessRules(list)
  }






  const updateRecordBr = (order, partialRecord) => {
    const list = businessRules.map(record => {
      let nxtRecord = record;
      if (record.order === order) {
        nxtRecord = { ...nxtRecord, ...partialRecord };
      }
      return nxtRecord;
    })
    setBusinessRules(list)
  };


  const handleAddBr = () => {
    setBusinessRules(
      [...businessRules, { order: businessRules.length, ...DEFAULT_DATA_BR }]
    );
  };

  const handleAntdCompChangeBr = (order, prop) => ({ target }) => {
    debugger
    const regex = /^\d*\.?\d*$/.test(target.value)
    if (!regex) {
      ShowMessgeAdditionalSubmit('EMSG_41');
    } else {
      updateRecordBr(order, { [prop]: target.value });
    }
  };

  const handleChangeNameBr = (order, name, type) => {
    const list = businessRules.map(record => {
      let nxtRecord = record;
      if (record.order === order) {
        nxtRecord = {
          ...nxtRecord,
          name: name,
          type: name
        };
      }
      return nxtRecord;
    })
    setBusinessRules(list)
  }

  const handleChangeDesctiptionBr = (order, description) => {
    const list = businessRules.map(record => {
      let nxtRecord = record;
      if (record.order === order) {
        nxtRecord = {
          ...nxtRecord,
          content: description,
        };
      }
      return nxtRecord;
    })
    setBusinessRules(list)
  }



  const deleteRowBr = (index) => {
    const list = [...businessRules]
    list.splice(index, 1);
    setBusinessRules(list)
  }

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      onFinish={onSubmit}
      labelCol={{
        span: 2,
        offset: 0,
      }}
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <LavPageHeader
          showBreadcumb={false}
          title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'common_create_usecase.page_title' : 'common_update_usecase.page_title' })}
        >
          <Space size="small">

            {screenMode === SCREEN_MODE.CREATE ? <Form.Item
              style={{ marginBottom: '0px' }}
              valuePropName="checked"
              name="createMore"
              wrapperCol={{ span: 24 }}
            >
              <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
            </Form.Item> : <></>}
            <Button onClick={confirmCancel}>
              {intl.formatMessage({ id: 'common.action.close' })}
            </Button>

            {screenMode === SCREEN_MODE.CREATE || state.detail?.status == STATUS_COMMON.DRAFT || state.detail?.status == STATUS_COMMON.REJECTED ?
              <Form.Item style={{ marginBottom: '0px' }}>
                <Button htmlType="submit" type="primary" ghost onClick={() => setIsDraft(false)}>
                  {intl.formatMessage({ id: 'common.action.submit' })}
                </Button>
              </Form.Item> : <></>
            }

            <Form.Item style={{ marginBottom: '0px' }}>
              <Button
                className="success-btn"
                htmlType="submit"
                onClick={() => setIsDraft(true)}
              >
                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
              </Button>
            </Form.Item>
          </Space>
        </LavPageHeader>
      </div>


      <Row align="middle">
        {screenMode === SCREEN_MODE.EDIT ?
          <Col span={5}>
            <div className='status-container'>
              <div>
                {intl.formatMessage({ id: 'common.field.status' })}
              </div>
              <div>
                {renderCommonStatusBadge(state.detail?.status)}
              </div>
            </div>
          </Col> : <></>
        }
      </Row>

      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'function.usecase-information' })}>
          {screenMode == SCREEN_MODE.EDIT &&
            <FormGroup inline label={intl.formatMessage({ id: 'common.label.code' })} labelSpan={3} controlSpan={2}>
              <Form.Item>
                <Input
                  value={state.detail?.code}
                  disabled
                ></Input>
              </Form.Item>
            </FormGroup>
          }

          <FormGroup
            inline
            required
            label={intl.formatMessage({ id: 'common.label.name' })}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item
              name="name"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
            >
              <Input
                maxLength={255}
                placeholder={`${intl.formatMessage({
                  id: `function.place-holder.use-case-name`,
                })}${intl.formatMessage({
                  id: `common.mandatory.*`,
                })}`}
              />
            </Form.Item>
          </FormGroup>

          <div ref={actorRef}>
            <FormGroup inline label={intl.formatMessage({ id: 'function.form.actor' })}
              labelSpan={3}
              controlSpan={21}>
              <Form.Item
                name="actor"
                labelAlign="left"
                wrapperCol={{ span: 24 }}
                rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
              >
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>
          </div>

          <FormGroup inline className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field='description'>
              {intl.formatMessage({ id: 'function.form.description' })}
            </TriggerComment>}
            labelSpan={3}
            controlSpan={21}>
            <Form.Item
              name="description"
              labelAlign="left"
              rules={[{
                validator: async (rule, value) => {
                  const description = getCkeditorDataDes?.current?.props?.data
                  if ((description == '' || description == undefined) && (!isDraft || state.detail?.status === STATUS_COMMON.SUBMITTED)) {
                    throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                  }
                }
              }]}
              wrapperCol={{ span: 24 }}
            >
              <CkeditorMention
                isCommon
                ref={getCkeditorDataDes}
                data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
              />
            </Form.Item>
          </FormGroup>

          <div ref={triggerRef}>
            <FormGroup inline label={
              <TriggerComment screenMode={screenMode} field='trigger'>
                {intl.formatMessage({ id: 'function.form.trigger' })}
              </TriggerComment>
            }
              labelSpan={3}
              controlSpan={21}
            >
              <Form.Item
                name="trigger"
                labelAlign="left"
                wrapperCol={{ span: 24 }}
              >
                <CkeditorMention isCommon ref={getCkeditorData} data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.trigger} />
              </Form.Item>
            </FormGroup>
          </div>

          <div ref={conditionRef} style={{ marginBottom: '10px' }}>
            <FormGroup inline className="rq-fg-comment" labelSpan={3} controlSpan={21} label={
              <div className='tooltips-container'>
                <TriggerComment screenMode={screenMode} field='pre-condition'>
                  {intl.formatMessage({ id: 'function.form.pre-condition' })}
                </TriggerComment>
              </div>
            }
            >
              <Form.Item
                name="preCondition"
                labelAlign="left"
                rules={[{
                  validator: async (rule, value) => {
                    const preCondition = preConditionRef?.current?.props?.data
                    if ((preCondition == '' || preCondition == undefined) && (!isDraft || state.detail?.status === STATUS_COMMON.SUBMITTED)) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  }
                }]}
                wrapperCol={{ span: 24 }}>

                <CkeditorMention isCommon ref={preConditionRef} data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.preCondition} />
              </Form.Item>
            </FormGroup>
          </div>

          <FormGroup inline className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field='post-condition'>
              {intl.formatMessage({ id: 'function.form.post-condition' })}
            </TriggerComment>}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item
              name="postCondition"
              labelAlign="left"
              wrapperCol={{ span: 24 }}
              rules={[
                {
                  validator: async (rule, value) => {
                    if ((!isDraft || state.detail?.status === STATUS_COMMON.SUBMITTED) && postConditionRef.current?.props.data == '') {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}

            >
              <CkeditorMention isCommon ref={postConditionRef} data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.postCondition} />
            </Form.Item>
          </FormGroup>

          <div ref={attachmentRef}>
            <FormGroup label={
              <TriggerComment screenMode={screenMode} field='activity-flow'>
                {intl.formatMessage({ id: 'function.form.activity-flow' })}
              </TriggerComment>}>
              <Form.Item>
                <LavAttachmentUpload
                  artefactType={COM_ARTEFACT_TYPE_ID.USECASE}
                  supportPDF
                  name="file"
                  attachment={attachment}
                  onChange={setAttachment}
                  isCommon
                />
              </Form.Item>
            </FormGroup>
          </div>

          <div ref={businessRuleRef}>
            <FormGroup inline label={
              <div className='tooltips-container'>
                <TriggerComment screenMode={screenMode} field='business-rule'>
                  {intl.formatMessage({ id: 'function.form.br-rule' })}
                </TriggerComment>
                <Tooltip title={intl.formatMessage({ id: 'common.usercase.tooltip-br' })} className='tooltips-icon'>
                  <ExclamationCircleOutlined />
                </Tooltip>
              </div>
            }>


              <Space direction="vertical" size="small">
                <Row justify='end'>
                  <Button type="primary" onClick={handleAddBr} icon={<PlusOutlined />} >
                    {
                      intl.formatMessage({
                        id: 'function.form.new-business-rule',
                      })
                    }
                  </Button>
                </Row>
              </Space>
            </FormGroup>

            <div style={{ marginTop: 10 }}>
              <ReactDragListView {...dragProps}>
                <Table
                  pagination={false}
                  bordered
                  columns={
                    [
                      {
                        title: "Step",
                        dataIndex: "step",
                        width: '5%',
                        align: 'center',
                        render: (step,
                          {
                            order,
                            editting = false,
                            id
                          }) => {
                          if (!editting) {
                            return <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(id)}>
                              {step}
                            </TriggerComment>;
                          } return (
                            <Input
                              style={{ textAlign: 'center' }}
                              maxLength={4}
                              defaultValue={step} onChange={
                                handleAntdCompChangeBr(order,
                                  "step")
                              } />);
                        }
                      },
                      {
                        title: "BR Code",
                        dataIndex: "code",
                        align: 'center',
                        width: '7.5%',
                      },
                      {
                        title: "Description",
                        dataIndex: "content",
                        width: '84.5%',
                        render: (description,
                          {
                            order,
                            editting = false,
                            name
                          }) => {
                          if (!editting) {
                            const content = `<strong><u>${name}</u></strong>${description}`
                            return <div
                              className="tableDangerous"
                              dangerouslySetInnerHTML={{ __html: content }}
                            ></div>
                          }
                          return (<Space direction="vertical" size="small" >
                            <CustomAutoCompleteBR
                              order={order}
                              defaultValue={name}
                              handleChangeNameBr={handleChangeNameBr}
                            />
                            <CkeditorMention isCommon ref={businessRuleRef} data={description} saveDataPre={(e) => { handleChangeDesctiptionBr(order, e) }} />
                          </Space>);
                        }
                      },
                      {
                        title: "Action",
                        dataIndex: "action",
                        align: 'center',
                        width: '3%',
                        render: (data, record, index) => {
                          return (<div style={{ display: 'flex' }}>
                            {
                              record.editting ? <Button icon={<CheckOutlined name="EditCustomIcon" />} type="link" onClick={() => editRowPre(index, record)}></Button>
                                : <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={() => editRowPre(index, record)}></Button>
                            }
                            <Button type="text" icon={<CustomSvgIcons name="DeleteCustomIcon" />} onClick={() => deleteRowBr(index)} />
                          </div>)
                        }
                      }
                    ]
                  } dataSource={businessRules} />
              </ReactDragListView>
            </div>
          </div>
        </Card>

        <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'function.reference' })}>
          <div ref={objsRef}>
            <FormGroup label={
              <TriggerComment screenMode={screenMode} field='object'>
                {intl.formatMessage({ id: 'function.form.objects' })}
              </TriggerComment>
            }>
              <Form.Item name="objects" wrapperCol={{ span: 24 }}>
                <Select
                  optionLabelProp="label"
                  mode="multiple"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option: any) => {
                    return option?.children?.toLowerCase()?.includes(input?.toLowerCase())
                  }}
                  tagRender={tagRender}
                >
                  {state.listObjects.length > 0 &&
                    state.listObjects?.filter(e => e.status !== STATUS_COMMON.REMOVED)?.map(
                      (item: any, index: number) => <Option key={index} value={item.name} label={item.name}>{item.name}</Option>
                    )}
                </Select>
              </Form.Item>
            </FormGroup>
          </div>
        </Card>
      </Space>
    </Form >
  </Spin>
}


export default CommonFunctionFormModalPage
