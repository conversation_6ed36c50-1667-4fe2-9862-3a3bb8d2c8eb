import {
  PlusOutlined
} from '@ant-design/icons'
import {
  Button, Card, Checkbox, Col, Form,
  Input, Modal, Row, Select,
  Space,
  Tag,
  Typography
} from 'antd'
import debounce from 'lodash.debounce'
import React, { createRef, useEffect, useRef, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { APP_COMMON_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, MESSAGE_TYPES, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import BusinessRule from '../../../../helper/component/business-rule'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import LavAttachmentUpload from '../../../../helper/component/lav-attachment-upload'
import PreCondition from '../../../../helper/component/pre-condition'
import TextAreaBullet from '../../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, getReferencesFromEditor, hasCommonRole, renderCommonStatusBadge, ShowMessgeAdditionalSubmit } from '../../../../helper/share'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import AppState from '../../../../store/types'
import { createRequest, getDetailRequest, getListObjectRequest, resetState, setModalVisible, updateRequest } from '../action'
import { CommonFunctionState } from '../type'
import { initComment, initCommentScreen } from './../../../_shared/comment/action'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select
interface CommonFunctionFormProps {
  id?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface CommonCommitteeFormModalProps {
  id?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonFunctionFormModal = ({ id, screenMode, onFinish, onDismiss }: CommonCommitteeFormModalProps) => {
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const state = useSelector<AppState | null>(
    (s) => s?.CommonFunction
  ) as CommonFunctionState
  const getCkeditorData: any = createRef()
  const [isDraft, setIsDraft] = useState(false);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [attachment, setAttachment] = useState(null) as any
  const attachmentRef = useRef<any>()
  const businessRuleRef = useRef<any>()
  const actorRef = useRef<any>()
  const triggerRef = useRef<any>()
  const conditionRef = useRef<any>()
  const objsRef = useRef<any>()
  const [businessRules, setBusinessRules] = useState<any>([])
  const [preConditions, setPreConditions] = useState<any>([])
  const [keyNumber, setKeyNumber] = useState(0)
  const [updateKey, setUpdateKey] = useState(0)

  // Destroy
  useEffect(() => {
    dispatch(getListObjectRequest(null))
    return () => {
      dispatch(resetState(null));
      setPreConditions([])
      setBusinessRules([])
      setKeyNumber(0)
      setUpdateKey(0)
      setAttachment(null)
      form.resetFields()
    }
  }, [])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
  }, [screenMode, id])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      const cloneBusinessRules = [...state.detail?.businessRules]
      const newBusinessRules = cloneBusinessRules.map((item, index) => {
        return { ...item, keyId: index }
      })
      setKeyNumber(cloneBusinessRules.length)
     

      setBusinessRules(newBusinessRules)
      form.setFieldsValue({
        name: state.detail?.name,
        description: state.detail?.description,
        postCondition: state.detail?.postCondition,
        trigger: state.detail?.trigger,
        actor: state.detail?.actor,

        objects: state.detail?.objects.map(
          (object: any) => object?.name
        ),
      })
      setAttachment(state.detail?.activeFlowPath)
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        form.resetFields();
        form.setFieldsValue({
          createMore: isCreateMore
        })
        setPreConditions([])
        setBusinessRules([])
        setKeyNumber(0)
        setUpdateKey(0)
        setAttachment(null)
      } else {
        onDismiss();
      }
      setIsDraft(false);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const addItem = () => {
    const nextId = keyNumber + 1
    let newData = Object.assign([], businessRules)
    newData.push({
      keyId: nextId
    })
    setKeyNumber(nextId)
    setBusinessRules(newData)
  }

  const removeItem = (index) => {
    let newData = Object.assign([], businessRules)
    if (newData.length == 1) {
      newData = []
    } else {
      newData.splice(index, 1)
    }
    setBusinessRules(newData)
  }

  const addForUpdate = () => {
    const nextId = updateKey + 1
    let newData = Object.assign([], preConditions)
    newData.push({
      keyId: nextId,
      type: intl.formatMessage({ id: 'function.form.user-permission', })
    })
    setUpdateKey(nextId)
    setPreConditions(newData)
  }

  const removeForUpdate = (index) => {
    let newData = Object.assign([], preConditions)
    if (newData.length == 1) {
      newData = []
    } else {
      newData.splice(index, 1)
    }
    setPreConditions(newData)
  }

  const onSubmit = debounce((values: any, st?: string) => {
    const lstObject = values.objects?.map((objs) => {
      const object: any = state.listObjects.find(
        (item: any) => item.name === objs
      )
      return object?.id
    })

    const triggerData = Object.assign(getCkeditorData.current?.props?.data)

    // Collect all references
    let mentionReferences = getReferencesFromEditor(triggerData, true);
    try {
      preConditions.filter((e) => e.status !== -1)?.forEach((e) => {
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.description, true));
      })
    } catch (err) {
      console.log(err);
    }

    try {
      businessRules.filter((e) => e.status !== -1)?.forEach((e) => {
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.content, true));
      })
    } catch (err) {
      console.log(err);
    }
    let requestData: any = {
      "id": id,
      "name": values.name,
      "trigger": getCkeditorData.current?.props?.data,
      "status": isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) :
        hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED
      ,
      "preConditions": preConditions.filter((e) => e.status !== -1),
      "businessRules": businessRules.filter((e) => e.status !== -1),
      "postCondition": values.postCondition,
      "version": values.version,
      "description": values.description,
      "type": 0,
      "activeFlowPath": attachment?.id,
      "actor": values.actor,
      "objectIds": lstObject ? lstObject : [],
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    };

    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      if (!attachment?.id) {
        attachmentRef.current.scrollIntoView('file')
        ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.use-case');
        return;
      }
      if (!preConditions || preConditions.length === 0) {
        conditionRef.current.scrollIntoView('preConditions')
        ShowMessgeAdditionalSubmit('EMSG_31')
        return;
      }
      if (!businessRules || businessRules.length === 0) {
        businessRuleRef.current.scrollIntoView('businessRules')
        ShowMessgeAdditionalSubmit('EMSG_16');
        return;
      }
      if (!requestData.actor || requestData.actor?.length === 0) {
        actorRef.current.scrollIntoView('actor')
        ShowMessgeAdditionalSubmit('EMSG_17');
        return;
      }
      if (Object.keys(triggerData).length === 0) {
        triggerRef.current.scrollIntoView('trigger')
        ShowMessgeAdditionalSubmit('EMSG_18');
        return;
      }
      if (!requestData.objectIds || requestData.objectIds?.length === 0) {
        objsRef.current.scrollIntoView('objects')
        ShowMessgeAdditionalSubmit('EMSG_19');
        return;
      }

      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_2' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.common-usecase' }) }
        ),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() { },
      })
    }
  }, 500)


  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }


  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'description', title: intl.formatMessage({ id: 'function.form.description', }), },
      { field: 'trigger', title: intl.formatMessage({ id: 'function.form.trigger' }), },
      { field: 'pre-condition', title: intl.formatMessage({ id: 'function.form.pre-condition' }), },
      { field: 'post-condition', title: intl.formatMessage({ id: 'function.form.post-condition' }), },
      { field: 'activity-flow', title: intl.formatMessage({ id: 'function.form.activity-flow' }), },
      { field: 'business-rule', title: intl.formatMessage({ id: 'view-use-case-details.label.business-rule' }), },
      { field: 'object', title: intl.formatMessage({ id: 'function.form.objects' }), },
    ];
    dispatch(initComment({ projectId: null, itemId: state.detail.id, fields }));

    const payload = {
      projectId: null,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_USE_CASE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])


  const tagRender = (props) => {
    const { label, name, value, closable, onClose } = props;


    return (
      <Tag
        // color={value}
        // onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 3,
          border: 'none',
        }}
        title={label}
      >
        {label.length > 20 ? label.substring(0, 20) + '...' : label}
      </Tag>
    );
  };

  //#endregion COMMENT INIT

  return <CustomModal
    isLoading={state.isLoading}
    size="medium"
    closable={false}
    visible={true}
    footer={null}
  >
    <Form
      // initialValues={pre_condition:state.detail?.preConditions}
      form={form}
      onFinish={onSubmit}
      labelCol={{
        span: 2,
        offset: 0,
      }}
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={10}>
            <Space size="large">
              <Form.Item
                name="name"
                wrapperCol={{ span: 20 }}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <Input
                  size="large"
                  placeholder={`${intl.formatMessage({
                    id: `function.place-holder.use-case-name`,
                  })}${intl.formatMessage({
                    id: `common.mandatory.*`,
                  })}`}
                  maxLength={255}
                  className="modal-create-name-input-field"
                />
              </Form.Item>
              {screenMode === SCREEN_MODE.EDIT ? renderCommonStatusBadge(state.detail?.status) : <></>}
            </Space>
          </Col>

          <Col span={14}>
            <Row justify="end">
              <Space size="small">

                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={confirmCancel}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                {screenMode === SCREEN_MODE.CREATE || state.detail?.status == STATUS_COMMON.DRAFT || state.detail?.status == STATUS_COMMON.REJECTED ?
                  <Form.Item style={{ marginBottom: '0px' }}>
                    <Button htmlType="submit" type="primary" ghost onClick={() => setIsDraft(false)}>
                      {intl.formatMessage({ id: 'common.action.submit' })}
                    </Button>
                  </Form.Item> : <></>
                }
                {
                  screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
                    <Form.Item style={{ marginBottom: '0px' }}>
                      <Button
                        className="success-btn"
                        htmlType="submit"
                        onClick={() => setIsDraft(true)}
                      >
                        {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                      </Button>
                    </Form.Item> : <></>
                }
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 280}>
        <Row align="middle">
          <Col span={2}>
            <TriggerComment screenMode={screenMode} field="version">
              <Text>
                {intl.formatMessage({ id: 'createobject.place-holder.version' })}
              </Text>
            </TriggerComment>
          </Col>
          <Col span={2}>
            <Form.Item
              className="mb-0"
              name="version"
              rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
            >
              <Input maxLength={255} />
            </Form.Item>
          </Col>
        </Row>
        <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
          <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'function.usecase-information' })}>
            {screenMode == SCREEN_MODE.EDIT &&
              <FormGroup label={intl.formatMessage({ id: 'function.place-usecase-code' })}>
                <Form.Item>
                  <Input
                    value={state.detail?.code}
                    disabled
                  ></Input>
                </Form.Item>
              </FormGroup>
            }

            <div ref={actorRef}>
              <FormGroup label={intl.formatMessage({ id: 'function.form.actor' })}>
                <Form.Item
                  name="actor"
                  labelAlign="left"
                  wrapperCol={{ span: 24 }}
                  rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                >
                  <Input maxLength={255} />
                </Form.Item>
              </FormGroup>
            </div>

            <FormGroup className="rq-fg-comment" required label={
              <TriggerComment screenMode={screenMode} field='description'>
                {intl.formatMessage({ id: 'function.form.description' })}
              </TriggerComment>}>
              <TextAreaBullet
                reload={state.isLoading}
                reloadAfterBack={state.isLoading}
                label=""
                name="description"
                labelAlign="left"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              ></TextAreaBullet>
            </FormGroup>

            <div ref={triggerRef}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field='trigger'>
                  {intl.formatMessage({ id: 'function.form.trigger' })}
                </TriggerComment>}>
                <Form.Item
                  name="trigger"
                  labelAlign="left"
                  wrapperCol={{ span: 24 }}
                >
                  <CkeditorMention isCommon ref={getCkeditorData} data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.trigger} />
                </Form.Item>
              </FormGroup>
            </div>

            <div ref={conditionRef}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field='pre-condition'>
                  {intl.formatMessage({ id: 'function.form.pre-condition' })}
                </TriggerComment>}>
                <Form.Item name="preConditions" >
                  {
                    preConditions ?
                      preConditions.map((preCondition, index) => (
                        <PreCondition
                          form={form}
                          data={preCondition}
                          setUpdateItems={setPreConditions}
                          index={index}
                          remove={removeForUpdate}
                          updateItems={preConditions}
                          isCommon
                        />
                      )) : <></>}
                  <Form.Item wrapperCol={{ offset: 0, span: 5 }}>
                    <Button
                      type="dashed"
                      onClick={() => addForUpdate()}
                      icon={<PlusOutlined />}
                    >
                      {intl.formatMessage({
                        id: 'function.form.new-pre-condition',
                      })}
                    </Button>
                  </Form.Item>
                </Form.Item>
              </FormGroup>
            </div>

            <FormGroup className="rq-fg-comment" required label={
              <TriggerComment screenMode={screenMode} field='post-condition'>
                {intl.formatMessage({ id: 'function.form.post-condition' })}
              </TriggerComment>}>
              <Form.Item
                name="postCondition"
                labelAlign="left"
                wrapperCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}

              >
                <Input maxLength={255} />
              </Form.Item>
            </FormGroup>

            <div ref={attachmentRef}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field='activity-flow'>
                  {intl.formatMessage({ id: 'function.form.activity-flow' })}
                </TriggerComment>}>
                <Form.Item>
                  <LavAttachmentUpload
                    artefactType={COM_ARTEFACT_TYPE_ID.USECASE}
                    supportPDF
                    name="file"
                    attachment={attachment}
                    onChange={setAttachment}
                    isCommon
                  />
                </Form.Item>
              </FormGroup>
            </div>

            <div ref={businessRuleRef}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field='business-rule'>
                  {intl.formatMessage({ id: 'function.form.br-rule' })}
                </TriggerComment>}>
                <Form.Item name="businessRules">
                  {businessRules.length > 0 ? businessRules.map((businessRule, index) => (
                    <BusinessRule
                      form={form}
                      data={businessRule}
                      removeItem={removeItem}
                      setAllItems={setBusinessRules}
                      index={index}
                      allItems={businessRules}
                      isCommon
                    />
                  )) : <></>}
                </Form.Item>
              </FormGroup>
            </div>

            <Button
              type="dashed"
              onClick={() => addItem()}
              icon={<PlusOutlined />}
            >
              {intl.formatMessage({
                id: 'function.form.new-business-rule',
              })}
            </Button>

          </Card>

          <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'function.reference' })}>
            <div ref={objsRef}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field='object'>
                  {intl.formatMessage({ id: 'function.form.objects' })}
                </TriggerComment>
              }>
                <Form.Item name="objects" wrapperCol={{ span: 24 }}>
                  <Select
                    optionLabelProp="label"
                    mode="multiple"
                    showSearch
                    optionFilterProp="children"
                    filterOption={(input, option: any) => {
                      return option?.children?.toLowerCase()?.includes(input?.toLowerCase())
                    }}
                    tagRender={tagRender}
                  >
                    {state.listObjects.length > 0 &&
                      state.listObjects?.filter(e => e.status !== STATUS_COMMON.REMOVED)?.map(
                        (item: any, index: number) => <Option key={index} value={item.name} label={item.name}>{item.name}</Option>
                      )}
                  </Select>
                </Form.Item>
              </FormGroup>
            </div>
          </Card>
        </Space>
      </Scrollbars>
    </Form>
  </CustomModal>
}
const CommonFunctionForm = ({ id, onFinish, buttonType, screenMode }: CommonFunctionFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return <>
    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button
          ghost={screenMode === SCREEN_MODE.CREATE}
          type='primary'
          className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
          onClick={() => setIsModalVisible(true)}
          icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
        >
          {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'function.button.create-uc' : 'common.action.update' })}
        </Button> :
        buttonType === BUTTON_TYPE.ICON ?
          <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
          <></>
    }
    {isModalVisible === true ? <CommonFunctionFormModal id={id} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}

export default CommonFunctionForm
