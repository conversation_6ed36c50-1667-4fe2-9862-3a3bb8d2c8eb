import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Space
} from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS_COMMON_FILTER } from '../../../constants'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import LavTable from '../../../helper/component/lav-table'
import { getColumnDropdownFilterProps, getColumnSearchProps, renderCommonStatusBadge } from '../../../helper/share'
import AppCommonService from '../../../services/app.service'
import CommonFunctionFormModalPage from './form/form'

const ViewCommonUseCase = () => {
  const [columns, setColumns] = useState<any>(null)
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  useEffect(() => {
    AppCommonService.getData2List(API_URLS.COMMON_REFERENCE_SCREENS, API_URLS.COMMON_REFERENCE_OBJECTS).then((res) => {
      const lstScreens = res[0].map(e => { return { value: e.id, text: e.name } })
      const lstObjects = res[1].map(e => { return { value: e.id, text: e.name } })
      setColumns(configColumns(lstObjects, lstScreens));
    }).catch(err => {
      setColumns(configColumns([], []))
    })
  }, [])

  const configColumns = (objs, scrs) => [
    {
      title: intl.formatMessage({ id: 'common.action.code' }),
      dataIndex: 'code',
      width: '5%',
      editable: true,
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (code: any, record: any) => {
        return (
          <Link to={`${APP_ROUTES.COMMON_USECASE_DETAIL}${record.id}`}>{code} </Link>
        )
      },
      defaultSortOrder: 'descend'
    },
    {
      title: intl.formatMessage({ id: 'function.column.function-name' }),
      dataIndex: 'name',
      width: '20%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: intl.formatMessage({ id: 'function.column.description' }),
      width: '30%',
      dataIndex: 'description',
      render: (text) => (
        <div
            className="tableDangerous"
            dangerouslySetInnerHTML={{ __html: text }}
        ></div>
    ),
    },
    {
      title: intl.formatMessage({ id: 'function.column.object-name' }),
      width: '15%',
      dataIndex: 'objects',
      ...getColumnDropdownFilterProps(objs, 'ObjectId', true),
      render: (listObject: any, record: any) => {
        return (
          <>
            {listObject?.map((e: any, index) => (
              <Link key={e.id} to={`${APP_ROUTES.COMMON_OBJECT_DETAIL}${e.id}`}>
                {index !== 0 ? `,` : ``} {e.name}
              </Link>
            ))}
          </>
        )

        // <p>{listObject?.map((e: any) => e.name).join(',')}</p>
      },
    },
    {
      title: intl.formatMessage({ id: 'function.column.screen' }),
      width: '15%',
      dataIndex: 'screens',
      ...getColumnDropdownFilterProps(scrs, 'ScreenId', true),
      render: (listScreen: any, record: any) => {
        return (
          <>
            {listScreen?.map((e: any, index) => (
              <Link key={e.id} to={`${APP_ROUTES.COMMON_SCREEN_DETAIL}${e.id}`}>
                {index !== 0 ? `,` : ``} {e.name}
              </Link>
            ))}
          </>
        )
      },
    },
    {
      title: intl.formatMessage({ id: 'function.column.status' }),
      dataIndex: 'status',
      width: '80px',
      ...getColumnDropdownFilterProps(STATUS_COMMON_FILTER, 'status'),
      // defaultFilteredValue: [0, 1, 2, 3],
      sorter: true,
      render: (record) => renderCommonStatusBadge(record),
    },
  ]
  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common_usecase.action.create' })}
      </Button>
    )

  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} />
    )

  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return children
  }

  console.log(screenMode)
  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        columns && screenMode === SCREEN_MODE.VIEW ? <LavTable
          showBreadcumb={false}
          title="function.header.title"
          artefact_type="common.artefact.common-usecase"
          apiUrl={API_URLS.COMMON_USECASE}
          columns={columns}
          isCommon={true}
          artefactType={COM_ARTEFACT_TYPE_ID.USECASE}
          deleteComponent={DeleteComponent}
          updateComponent={UpdateComponent}
          createComponent={CreateComponent}
        /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <CommonFunctionFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenMode={SCREEN_MODE.CREATE} buttonType={BUTTON_TYPE.TEXT} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <CommonFunctionFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} id={id} screenMode={SCREEN_MODE.EDIT}/> : <></>
      }
    </Space>
  )
}

export default ViewCommonUseCase
