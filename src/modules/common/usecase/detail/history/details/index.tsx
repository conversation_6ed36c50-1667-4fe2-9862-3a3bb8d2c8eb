import AppState from '@/store/types'
import {
    Breadcrumb, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, STATUS_COMMON } from '../../../../../../constants'
import LavAttachmentPreview from '../../../../../../helper/component/lav-attachment-preview'
import LavButtons from '../../../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../../../helper/hooks/useWindowDimensions'
import { renderCommonStatusBadge } from '../../../../../../helper/share'
import TriggerComment from '../../../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../../../modules/_shared/comment/type'
import { initComment, initCommentScreen } from '../../../../../_shared/comment/action'
import TableComponent from '../.././table'
import debounce from 'lodash.debounce'
import HistoryNavigation from '../../../../../../modules/history/navigation'

const { Text, Title } = Typography
interface CommonUseCaseVersionDetailsProps {
    data: any | [],
    id: number,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any,
    setSelectedRowVersion: (version: string) => void, 
    onDismiss: () => void | null,
}
const CommonUseCaseVersionDetails = ({ data, id, onChange, isLoading, isModalShow, setScreenMode, setSelectedRowVersion, onDismiss }: CommonUseCaseVersionDetailsProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'actor', title: intl.formatMessage({ id: 'view-use-case-details.label.actor' }), },
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'description', title: intl.formatMessage({ id: 'view-use-case-details.label.description', }), },
            { field: 'trigger', title: intl.formatMessage({ id: 'view-use-case-details.label.trigger' }), },
            { field: 'pre-condition', title: intl.formatMessage({ id: 'view-use-case-details.label.pre-condition' }), },
            { field: 'post-condition', title: intl.formatMessage({ id: 'view-use-case-details.label.post-condition' }), },
            { field: 'activity-flow', title: intl.formatMessage({ id: 'view-use-case-details.label.activity-flow' }), },
            { field: 'business-rule', title: intl.formatMessage({ id: 'view-use-case-details.label.business-rule' }), },
            { field: 'object', title: intl.formatMessage({ id: 'view-use-case-details.label.object' }), },
            { field: 'screen', title: intl.formatMessage({ id: 'view-use-case-details.label.screen' }), },
        ];

        data?.businessRules?.forEach((e) => {
            fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
        })

        data?.preConditions?.forEach((e) => {
            fields.push({ field: e.id ? e.id.toString() : '', title: e?.type })
        })

        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.COMMON_USE_CASE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    return (
        <>
            <Space
                direction="vertical"
                size="middle"
                className="record-detail-right-control-container p-1rem"
            >
                <Row align="middle" justify="space-between">
                    <div>
                        <Breadcrumb className='rq-breadcrumb' separator=">">
                            <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                            <Breadcrumb.Item>
                                <Link to={APP_ROUTES.COMMON_USECASE}>{intl.formatMessage({ id: 'commonusecase.page_title' })}</Link>
                            </Breadcrumb.Item>
                        </Breadcrumb>
                        <Title level={3} className='rq-page-title'>
                            {data?.code} - {data?.name}
                        </Title>
                    </div>
                    <Space size="small">
                        <LavButtons
                            isCommon={true}
                            url={`${API_URLS.COMMON_USECASE}/${id}`}
                            artefact_type="common.artefact.common-usecase"
                            status={data?.status}
                            artefactType={COM_ARTEFACT_TYPE_ID.USECASE}
                            id={id}
                            changePage={() => onChange()}>                                                              
                            <Button onClick={debounce(onDismiss, 500)}>
                                {intl.formatMessage({ id: 'common.action.close' })}
                            </Button>
                        </LavButtons>
                    </Space>
                </Row>

                <Divider className="mt-0 mb-0" />              
                { data?.nextPrevious.latestVersion === data?.version ? <></>:
                    <HistoryNavigation isCommon={true} data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.common-usecase"} artefactType={COM_ARTEFACT_TYPE_ID.USECASE} />
                }
                <Spin spinning={isLoading}>
                    <Scrollbars
                        autoHide
                    >
                        <Space direction="vertical">
                            <Space size="large">
                                {/* <span>
                                    <TriggerComment field="version">
                                        <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
                                    </TriggerComment>
                                </span> */}
                                {renderCommonStatusBadge(data?.status)}
                            </Space>

                            <Card
                                title={
                                    <Title level={5}>
                                        {`${intl.formatMessage({
                                            id: 'function.usecase-information',
                                        })}`}
                                    </Title>
                                }
                                bordered={true}
                            >
                                <Row gutter={[16, 4]}>

                                    <Col span={6}>
                                        <TriggerComment field='actor'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.actor',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={18} className="description">
                                        {data?.actor}
                                    </Col>
                                    <Col span={6}>
                                        <TriggerComment field="description">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.description',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={18} className="description">
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.description,
                                            }}
                                        ></div>
                                    </Col>

                                    <Col span={6}>
                                        <TriggerComment field="trigger">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.trigger',
                                                })}:
                                            </Text>
                                        </TriggerComment>

                                    </Col>
                                    <Col span={18}>
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.trigger,
                                            }}
                                        ></div>
                                    </Col>

                                    <Col span={6}>
                                        <TriggerComment field="pre-condition">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.pre-condition',
                                                })}
                                            </Text>
                                        </TriggerComment>

                                    </Col>
                                    <Col span={18}>
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.preCondition,
                                            }}
                                        ></div>
                                        {/* {data?.preConditions?.map((item: any) => (
                                            <TriggerComment field={JSON.stringify(item?.id)}>
                                                <div
                                                    key={item.id}
                                                    className="tableDangerous"
                                                    dangerouslySetInnerHTML={{ __html: item.description }}
                                                ></div>
                                            </TriggerComment>
                                        ))} */}
                                    </Col>
                                    <Col span={6}>
                                        <TriggerComment field="post-condition">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.post-condition',
                                                })}
                                            </Text>
                                        </TriggerComment>

                                    </Col>
                                    <Col span={18}>
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{ __html: data?.postCondition }}
                                        ></div>
                                    </Col>

                                    <Col span={24}>
                                        <TriggerComment field="activity-flow">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.activity-flow',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={24}>
                                        <LavAttachmentPreview attachment={data?.activeFlowPath} isCommon />
                                    </Col>
                                    <Col span={24}>
                                        <TriggerComment field="business-rule">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.business-rule',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={24}>
                                        <TableComponent businessRule={data?.businessRules}></TableComponent>
                                    </Col>
                                </Row>
                            </Card>

                            <LavReferences data={data} isCommon />
                            <LavCommonAuditTrail data={data?.auditTrails} />
                        </Space>
                    </Scrollbars>
                </Spin>
            </Space>
        </>
    )
}

export default CommonUseCaseVersionDetails
