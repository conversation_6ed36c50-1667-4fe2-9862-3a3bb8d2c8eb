import { Col, Row } from 'antd'
import React from 'react'
import { useHistory } from 'react-router'
import intl from '../../../../../config/locale.config'
import { STATUS } from '../../../../../constants'
import './style.css'

const ObjectInfo = (props) => {
  const { objectInfo } = props
  const history = useHistory()
  const edit = () => {
    history.push(`/updateobject/${objectInfo.id}`)
  }
  return (
    <>
      <Row>
        <Col span={20}>
          <Row>
            <Col span={2}> Lui </Col>

            <Col span={10} className="bigTitle objectName">
              {' '}
              {`${intl.formatMessage({
                id: 'objectSpecification.header.title',
              })} ${objectInfo.objectName}`}
            </Col>
            {objectInfo.status === STATUS.DRAFT && (
              <Col span={2} className="border">
                {' '}
                {intl.formatMessage({ id: 'common.enum.draft' })}{' '}
              </Col>
            )}
            {objectInfo.status === STATUS.CANCELLED && (
              <Col span={2} className="border">
                {' '}
                {intl.formatMessage({ id: 'common.enum.canceled' })}{' '}
              </Col>
            )}
          </Row>
        </Col>
        <Col span={4} onClick={edit}>
          <Col span={10} className="border edit">
            {' '}
            {intl.formatMessage({ id: 'common.action.edit' })}{' '}
          </Col>
        </Col>
      </Row>
      <Row className="mt-2">
        <Col span={2}>
          {' '}
          {intl.formatMessage({
            id: 'objectSpecification.title.description',
          })}{' '}
        </Col>
        <Col span={22} className="bold">
          {' '}
          {`${objectInfo.description} `}{' '}
        </Col>
      </Row>
      <Row className="mt-2">
        <Col span={2}>
          {intl.formatMessage({
            id: 'objectSpecification.title.source-object',
          })}
        </Col>
        <Col span={22} className="bold">
          {' '}
          {`${objectInfo.sourceObject?.map((e) => e.name).join(',')} `}{' '}
        </Col>
      </Row>
      <Row className="mt-2">
        <Col span={2}>
          {' '}
          {intl.formatMessage({
            id: 'objectSpecification.title.target-object',
          })}{' '}
        </Col>
        <Col span={22} className="bold">
          {' '}
          {`${objectInfo.targetObject?.map((e) => e.name).join(',')} `}{' '}
        </Col>
      </Row>
      <Row className="mt-2">
        <Col span={2}>
          {' '}
          {intl.formatMessage({ id: 'objectSpecification.title.screen' })}{' '}
        </Col>
        <Col span={22} className="bold">
          {' '}
          {`${objectInfo.screen?.map((e) => e.name).join(',')} `}{' '}
        </Col>
      </Row>
      <Row className="mt-2">
        <Col span={2}>
          {' '}
          {intl.formatMessage({ id: 'view-use-case-details.label.ur' })}{' '}
        </Col>
        <Col span={22} className="bold">
          {' '}
          {`${objectInfo.userRequirements?.map((e) => e.name).join(',')} `}{' '}
        </Col>
      </Row>
    </>
  )
}

export default ObjectInfo
