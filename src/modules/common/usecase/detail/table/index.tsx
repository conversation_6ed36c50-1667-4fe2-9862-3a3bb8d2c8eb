import { SCREEN_MODE } from '../../../../../constants'
import { Table } from 'antd'
import React from 'react'
import intl from '../../../../../config/locale.config'
import TriggerComment from '../../../../../modules/_shared/comment/trigger-comment'
import './style.css'


const TableComponent = (props) => {
  const columns = [
    {
      title: `${intl.formatMessage({
        id: 'view-use-case-details.column.step',
      })}`,
      key: 'step',
      dataIndex: 'step',
      width: '12%',
      sorter: (
        item1: any,
        item2: any
      ) => {
        return item1.order - item2.order
      },
      render: (step: string, record: any) => {
        return <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(record?.id)}>
          <p>{`(${step})`}</p>
        </TriggerComment>
      },
    },
    {
      title: `${intl.formatMessage({
        id: 'view-use-case-details.column.brCode',
      })}`,
      key: 'code',
      dataIndex: 'code',
      width: '14%',
      sorter: (
        item1: any,
        item2: any
      ) => {
        if (item1.code < item2.code) {
          return -1
        } else if (item1.code > item2.code) {
          return 1
        }
        return 0
      },
    },
    {
      title: `${intl.formatMessage({
        id: 'view-use-case-details.column.description',
      })}`,
      key: 'content',
      dataIndex: 'content',
      width: '70%',
      sorter: (item1: any, item2: any) => {
        if (item1.content < item2.content) {
          return -1
        } else if (item1.content > item2.content) {
          return 1
        }
        return 0
      },
      render: (description: string) => {
        return (
          <div
            className="tableDangerous"
            dangerouslySetInnerHTML={{ __html: description }}
          ></div>
        )
      },
    },
  ]
  const dataSource = props.businessRule;

  const data: any = []
  dataSource?.map((element: any) => {
    let { step, name, content, code, id } = element
    content = `<strong><u>${name}</u></strong>${content ? content : ''}`
    const loadData = {
      id: id,
      code: code,
      step: step,
      content: content
    }
    data.push(loadData)
  })

  return (
    <>
      <Table
        bordered
        dataSource={data}
        columns={columns}
        rowKey="code"
        pagination={false}
      />
    </>
  )
}

export default React.memo(TableComponent)
