import { Card, Col, Row, Space, Typography } from "antd"
import LavAttachmentPreview from "../../../../helper/component/lav-attachment-preview"
import intl from "../../../../config/locale.config"
import { STATUS_COMMON } from "../../../../constants"
import LavReferences from "../../../../helper/component/lav-references"
import { renderCommonStatusBadge } from "../../../../helper/share"
import TableComponent from './table'

const { Title, Text } = Typography

const CommonUseCaseDetailInfo = ({ data }) => {
    return <Space direction="vertical">
        <Space size="large">
            {/* <span>
                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
            </span> */}
            {data?.status !== STATUS_COMMON.RECOMMEND ? renderCommonStatusBadge(data?.status) : <></>}
        </Space>

        <Card title={<Title level={5}>{intl.formatMessage({ id: 'function.usecase-information' })}</Title>} bordered={true}>
            <Row gutter={[16, 4]}>
                <Col span={6}>
                    <Text type="secondary">{intl.formatMessage({ id: 'view-use-case-details.label.description' })}:</Text>
                </Col>
                <Col span={18} className="description">
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: data?.description }}
                    ></div>
                </Col>

                <Col span={6}>
                    <Text type="secondary">{intl.formatMessage({ id: 'view-use-case-details.label.trigger' })}:</Text>
                </Col>
                <Col span={18}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{
                            __html: data.trigger,
                        }}
                    ></div>
                </Col>

                <Col span={6}>
                    <Text type="secondary"> {intl.formatMessage({ id: 'view-use-case-details.label.pre-condition' })}:</Text>
                </Col>
                <Col span={18}>
                    {data.preConditions?.map((item: any) => (
                        <div
                            key={item.id}
                            className="tableDangerous"
                            dangerouslySetInnerHTML={{ __html: item.description }}
                        ></div>
                    ))}
                </Col>
                <Col span={6}>
                    <Text type="secondary">{intl.formatMessage({ id: 'view-use-case-details.label.post-condition' })}:</Text>
                </Col>
                <Col span={18}>{data.postCondition}</Col>

                <Col span={24}>
                    <Text type="secondary">{intl.formatMessage({ id: 'view-use-case-details.label.activity-flow' })}:</Text>
                </Col>

                <LavAttachmentPreview attachment={data?.activeFlowPath} isCommon />
                <Col span={24}>
                    <TableComponent businessRule={data.businessRules}></TableComponent>
                </Col>
            </Row>
        </Card>

        <LavReferences data={data} isCommon />
    </Space>
}
export default CommonUseCaseDetailInfo