
export interface CommonFunctionState {
  isLoading: boolean
  detail: Detail | null
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  isLoadingList?: boolean
  listData?: any,
  selectedData?: Detail | null,
  isLoadingObject: boolean
  listObjects: any[]
  isModalShow?: boolean,
}

export interface Detail {
  id: number | null
  code: string
  description: string
  name: string
  trigger: string
  postCondition: string
  activeFlowPath: any
  actor: any[]
  objects: any[]
  status: number | null
  preCondition: string
  businessRules: any[]
}



export const defaultState: CommonFunctionState = {
  isLoading: false,
  detail: {
    id: null,
    code: '',
    description: '',
    name: '',
    trigger: '',
    postCondition: '',
    activeFlowPath: '',
    actor: [],
    objects: [],
    status: null,
    preCondition: '',
    businessRules: [],
  },
  isLoadingObject: false,
  listObjects: [],
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
}

export enum PreConditionType {
  userPermission = 0,
  security = 1,
}

export enum BRRule {
  screenDisplayingRules = 0,
  confirmationRules = 1,
  validatingRules = 2,
  creatingRules = 3,
  deletingRules = 4,
  updatingRules = 5,
  sendingEmailNotificationRules = 6,
  sendingMessageNotificationRules = 7,
  loggingInRules = 8,
  loggingOutRules = 9,
}
export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_FUNCTION/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_FUNCTION/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_FUNCTION/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_FUNCTION/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_FUNCTION/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_FUNCTION/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_FUNCTION/UPDATE_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_FUNCTION/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_FUNCTION/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_FUNCTION/DELETE_FAILED',

  SAVE_IMG_ID = '@@MODULES/COMMON_FUNCTION/SAVE_IMG_ID',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_FUNCTION/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_FUNCTION/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_FUNCTION/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/COMMON_FUNCTION/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/COMMON_FUNCTION/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/COMMON_FUNCTION/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/COMMON_FUNCTION/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/COMMON_FUNCTION/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/COMMON_FUNCTION/GET_LIST_FAILED',

  GET_LIST_ACTORS_REQUEST = '@@MODULES/COMMON_FUNCTION/GET_LIST_ACTORS_REQUEST',
  GET_LIST_ACTORS_SUCCESS = '@@MODULES/COMMON_FUNCTION/GET_LIST_ACTORS_SUCCESS',
  GET_LIST_ACTORS_FAILED = '@@MODULES/COMMON_FUNCTION/GET_LIST_ACTORS_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/COMMON_FUNCTION/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/COMMON_FUNCTION/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/COMMON_FUNCTION/GET_LIST_OBJECTS_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/COMMON_FUNCTION/SET_MODAL_VISIBLE',
  
}
