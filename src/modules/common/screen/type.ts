export interface CommonScreenState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: CommonScreenDetail | null,
  selectedData?: CommonScreenDetail | null,

  listObjects?: any[],
  isLoadingObjects?: boolean,

  listFunctions?: any[],
  isLoadingFunctions?: boolean,

  listObjectProperties: any[],
  isLoadingListObjectProperties: boolean

  listTargetUseCase: any[],
  isLoadingTargetUseCase: boolean,

  listTargetScreen: any[],
  isLoadingTargetScreen: boolean,
  isModalShow?:boolean,

  listSelectObjectProperties: any[]
}
export interface CommonScreenDetail {
  id?: number | null,
  name: string,
  code: string,
  access: string,
  mockUpScreen: any,
  requirement: string,
  description: string,
  dateCreated: string,
  createdBy: string,
  submittedBy: string,
  dateSubmitted: string,
  status: number,
  screenComponents: ScreenComponent[],
  objects: number[],
  useCases: any,
}

export interface ScreenComponent {
  id?: number | null,
  component: string,
  name: string
  componentType: number,
  componentTypeDetail: number,
  order: number,
  editable: true,
  mandatory: true,
  defaultValue: string,
  description: string,
  objectScreenComponent: number,
  sourceObjectProperties: number,
  screen: number,
  useCase: number,
  status: number,
  listObject: number[],
  listUseCase: number[],
}
export const defaultState: CommonScreenState = {
  detail: {
    id: null,
    name: '',
    code: '',
    access: '',
    mockUpScreen: 0,
    requirement: '',
    description: '',
    dateCreated: '',
    createdBy: '',
    submittedBy: '',
    dateSubmitted: '',
    status: 0,
    screenComponents: [],
    objects: [],
    useCases: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  listObjects: [],
  isLoadingObjects: false,
  listFunctions: [],
  isLoadingFunctions: false,
  listObjectProperties: [],
  isLoadingListObjectProperties: false,
  listTargetUseCase: [],
  isLoadingTargetUseCase: false,
  listTargetScreen: [],
  isLoadingTargetScreen: false,
  listSelectObjectProperties: []
}

export const DiscussionType = [
  {
    id: 1,
    name: 'Follow-up Action',
  },
  {
    id: 2,
    name: 'Discussion',
  },
  {
    id: 3,
    name: 'Other',
  },
]
export enum ActionEnum {
  RESET_STATE = '@@MODULES/COMMON_SCREEN/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/COMMON_SCREEN/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/COMMON_SCREEN/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/COMMON_SCREEN/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/COMMON_SCREEN/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/COMMON_SCREEN/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/COMMON_SCREEN/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/COMMON_SCREEN/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/COMMON_SCREEN/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/COMMON_SCREEN/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/COMMON_SCREEN/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/COMMON_SCREEN/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/COMMON_SCREEN/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/COMMON_SCREEN/DELETE_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/COMMON_SCREEN/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/COMMON_SCREEN/GET_LIST_OBJECTS_FAILED',

  GET_LIST_FUNCTIONS_REQUEST = '@@MODULES/COMMON_SCREEN/GET_LIST_FUNCTIONS_REQUEST',
  GET_LIST_FUNCTIONS_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_LIST_FUNCTIONS_SUCCESS',
  GET_LIST_FUNCTIONS_FAILED = '@@MODULES/COMMON_SCREEN/GET_LIST_FUNCTIONS_FAILED',

  GET_LIST_OBJECT_PROPERTIES_REQUEST = '@@MODULES/COMMON_SCREEN/GET_LIST_OBJECT_PROPERTIES_REQUEST',
  GET_LIST_OBJECT_PROPERTIES_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_LIST_OBJECT_PROPERTIES_SUCCESS',
  GET_LIST_OBJECT_PROPERTIES_FAILED = '@@MODULES/COMMON_SCREEN/GET_LIST_OBJECT_PROPERTIES_FAILED',

  GET_LIST_TARGET_USECASE = '@@MODULES/COMMON_SCREEN/GET_LIST_TARGET_USECASE',
  GET_LIST_TARGET_USECASE_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_LIST_TARGET_USECASE_SUCCESS',
  GET_LIST_TARGET_USECASE_FAILED = '@@MODULES/COMMON_SCREEN/GET_LIST_TARGET_USECASE_FAILED',

  GET_LIST_TARGET_SCREEN = '@@MODULES/COMMON_SCREEN/GET_LIST_TARGET_SCREEN',
  GET_LIST_TARGET_SCREEN_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_LIST_TARGET_SCREEN_SUCCESS',
  GET_LIST_TARGET_SCREEN_FAILED = '@@MODULES/COMMON_SCREEN/GET_LIST_TARGET_SCREEN_FAILED',

  GET_LIST_SELECT_PROPERTIES = '@@MODULES/COMMON_SCREEN/GET_LIST_SELECT_PROPERTIES',
  GET_LIST_SELECT_PROPERTIES_SUCCESS = '@@MODULES/COMMON_SCREEN/GET_LIST_SELECT_PROPERTIES_SUCCESS',
  GET_LIST_SELECT_PROPERTIES_FAILED = '@@MODULES/COMMON_SCREEN/GET_LIST_SELECT_PROPERTIES_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/COMMON_SCREEN/SET_MODAL_VISIBLE',
}
