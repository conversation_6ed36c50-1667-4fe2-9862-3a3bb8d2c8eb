import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../../constants'
import { apiCall } from '../../../helper/api/aloApi'
import { ShowAppMessage } from '../../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListFunctionsFailed, getListFunctionsRequest,
  getListFunctionsSuccess, getListObjectPropertiesFailed, getListObjectPropertiesRequest,
  getListObjectPropertiesSuccess, getListObjectsFailed, getListObjectsRequest,
  getListObjectsSuccess, getListRequest,
  getListSelectProperties,
  getListSelectPropertiesSuccess,
  getListSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.COMMON_SCREEN}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_SCREEN + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_SCREEN + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.common-screen')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-screen')
    }
  }
}

function* handleCreateScreen(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.COMMON_SCREEN, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.common-screen')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-screen', 'common_component.column.name-short')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.COMMON_SCREEN + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.common-screen')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.common-screen', 'common_component.column.name-short')
    }
  }
}

function* handleGetListObjects(action: Action) {
  if (getListObjectsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.COMMON_REFERENCE_REF_OBJECTS);
      yield put(getListObjectsSuccess(res.data));
    } catch (err) {
      yield put(getListObjectsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListFunctionsRequest(action: Action) {
  if (getListFunctionsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.COMMON_REFERENCE_USECASE);
      yield put(getListFunctionsSuccess(res.data));
    } catch (err) {
      yield put(getListFunctionsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}




function* handleGetListObjectPropertiesRequest(action: Action) {
  if (getListObjectPropertiesRequest.match(action)) {
    try {
      const url = API_URLS.COMMON_REFERENCE_OBJECTS + `/${action.payload}/Properties`
      const res = yield call(apiCall, 'GET', url)
      yield put(getListObjectPropertiesSuccess(res.data));
    } catch (err) {
      yield put(getListObjectPropertiesFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}


function* handleGetListSelectObjectProperties(action: Action) {
  if (getListSelectProperties.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', `${API_URLS.COMMON_OBJECT}/${action.payload}/properties`)
      yield put(getListSelectPropertiesSuccess(res.data))
    } catch {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreateScreen)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
  yield takeLatest(getListObjectsRequest.type, handleGetListObjects)
  yield takeLatest(getListFunctionsRequest.type, handleGetListFunctionsRequest)
  yield takeLatest(getListObjectPropertiesRequest.type, handleGetListObjectPropertiesRequest)
  yield takeLatest(getListSelectProperties.type, handleGetListSelectObjectProperties)
}
export default function* CommonScreenSaga() {
  yield all([fork(watchFetchRequest)])
}
function err(arg0: string, err: any) {
  throw new Error('Function not implemented.')
}
