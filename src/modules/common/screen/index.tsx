import { PlusOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_COMMON, STATUS_COMMON_FILTER } from '../../../constants'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import LavTable from '../../../helper/component/lav-table'
import { getColumnDropdownFilterProps, getColumnSearchProps, renderCommonStatusBadge } from '../../../helper/share'
import AppCommonService from '../../../services/app.service'
import CommonMockupScreenFormModalPage from './form/form'

const { Text } = Typography
const CommonScreen = () => {
  const [columns, setColumns] = useState<any>(null)
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  useEffect(() => {
    AppCommonService.getData(API_URLS.COMMON_REFERENCE_OBJECTS).then((e) => {
      setColumns(configColumns(e?.map(e => {
        return { text: e.name, value: e.id }
      })));
    }).catch(err => {
      setColumns([])
    })
  }, [])

  const configColumns = (objs) => [
    {
      title: intl.formatMessage({ id: 'common_screen.column.screen-code' }),
      dataIndex: 'code',
      width: '5%',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${APP_ROUTES.COMMON_SCREEN_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'common_screen.column.screen' }),
      dataIndex: 'name',
      width: '20%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
      render: (text: string) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'common_screen.column.description' }),
      dataIndex: 'description',
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      render: (text: string) => {
        return <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
      },
    },
    {
      title: intl.formatMessage({ id: 'common_screen.column.object' }),
      dataIndex: 'objects',
      width: '20%',
      ...getColumnDropdownFilterProps(objs, 'ObjectId', true),
      render: (listObject: any, record: any) => {
        return (
          <>
            {listObject?.map((e: any, index) => (
              <Link key={e.id} to={`${APP_ROUTES.COMMON_OBJECT_DETAIL}${e.id}`}>
                {index !== 0 ? `,` : ``} {e.name}
              </Link>
            ))}
          </>
        )
      },
    },
    {
      title: intl.formatMessage({ id: 'common_screen.column.status' }),
      dataIndex: 'status',
      width: '80px',
      sorter: true,
      ...getColumnDropdownFilterProps(STATUS_COMMON_FILTER, 'status'),
      // defaultFilteredValue: [0, 1, 2, 3],
      render: (record) => renderCommonStatusBadge(record),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common_screen.button.create-screen' })}
      </Button>
    )

  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (record.status !== STATUS_COMMON.DELETED) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>

  }
  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (
      record.status !== STATUS.DELETE
    ) ?
      children : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        columns && screenMode === SCREEN_MODE.VIEW ? <LavTable
          showBreadcumb={false}
          title="common_screen.header.title"
          artefact_type="common.artefact.common-screen"
          apiUrl={API_URLS.COMMON_SCREEN}
          artefactType={COM_ARTEFACT_TYPE_ID.SCREEN}
          columns={columns}
          isCommon={true}
          updateComponent={UpdateComponent}
          createComponent={CreateComponent}
          deleteComponent={DeleteComponent}
        /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <CommonMockupScreenFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenMode={SCREEN_MODE.CREATE} buttonType={BUTTON_TYPE.TEXT} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <CommonMockupScreenFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenID={id} screenMode={SCREEN_MODE.EDIT} /> : <></>
      }
    </Space>
  )
}

export default CommonScreen
