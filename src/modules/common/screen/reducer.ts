import { createReducer } from '@reduxjs/toolkit';
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListFunctionsFailed, getListFunctionsRequest,
  getListFunctionsSuccess, getListObjectPropertiesFailed, getListObjectPropertiesRequest,
  getListObjectPropertiesSuccess, getListObjectsFailed, getListObjectsRequest,
  getListObjectsSuccess, getListRequest,
  getListSelectProperties,
  getListSelectPropertiesFailure,
  getListSelectPropertiesSuccess,
  getListSuccess, resetState, setModalVisible, updateFailed, updateRequest,
  updateSuccess
} from './action';
import { CommonScreenState, defaultState } from './type';

const initState: CommonScreenState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })


      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })


      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })

      .addCase(getListObjectsRequest, (state, action?) => {
        state.isLoadingObjects = true;
      })
      .addCase(getListObjectsSuccess, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = action.payload
      })
      .addCase(getListObjectsFailed, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = []
      })

      .addCase(getListFunctionsRequest, (state, action?) => {
        state.isLoadingFunctions = true;
      })
      .addCase(getListFunctionsSuccess, (state, action) => {
        state.isLoadingFunctions = false
        state.listFunctions = action.payload
      })
      .addCase(getListFunctionsFailed, (state, action) => {
        state.isLoadingFunctions = false
        state.listFunctions = []
      })

      .addCase(getListObjectPropertiesRequest, (state, action?) => {
        state.isLoadingListObjectProperties = true;
      })
      .addCase(getListObjectPropertiesSuccess, (state, action) => {
        state.isLoadingListObjectProperties = false
        state.listObjectProperties = action.payload
      })
      .addCase(getListObjectPropertiesFailed, (state, action) => {
        state.isLoadingListObjectProperties = false
        state.listObjectProperties = []
      })

      
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if(!action.payload){
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })

      .addCase(getListSelectProperties, (state, action) => {
        state.isLoading = true
      })
      .addCase(getListSelectPropertiesSuccess, (state, action) => {
        state.isLoading = false
        state.listSelectObjectProperties = action.payload
      })
      .addCase(getListSelectPropertiesFailure, (state, action) => {
        state.isLoading = false
        state.listSelectObjectProperties = []
      })
  )
})

export default reducer
export { initState as CommonScreenState };

