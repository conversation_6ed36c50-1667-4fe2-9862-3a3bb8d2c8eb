import { CommentState } from '@/modules/_shared/comment/type'
import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Tag, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useRef, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { APP_COMMON_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, MESSAGE_TYPES, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import LavAttachmentUpload from '../../../../helper/component/lav-attachment-upload'
import TextAreaBullet from '../../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, getReferencesFromEditor, hasCommonRole, renderCommonStatusBadge, ShowMessgeAdditionalSubmit } from '../../../../helper/share'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { createRequest, getDetailRequest, getListFunctionsRequest, getListObjectsRequest, resetState, setModalVisible, updateRequest } from '../action'
import ScreenComponents from '../form/screen-components'
import { CommonScreenState } from '../type'
import { initComment, initCommentScreen } from './../../../_shared/comment/action'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select

interface CommonMockupScreenFormProps {
  screenID?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface CommonMockupScreenFormModalProps {
  screenID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonMockupScreenFormModal = ({ screenID, screenMode, onFinish, onDismiss }: CommonMockupScreenFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.CommonScreen) as CommonScreenState
  const [isDraft, setIsDraft] = useState(false);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const [access, setAccess] = useState('') as any
  const [choosedObj, setChoosedObj] = useState([]) as any
  const [attachment, setAttachment] = useState(null) as any
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const getCkeditorData: any = createRef()
  const tableRef: any = useRef()
  const attachmentRef = useRef<any>()
  const useCaseRef = useRef<any>()
  const tableUpdateRef = useRef<any>()

  useEffect(() => {
    dispatch(getListObjectsRequest(null));
    dispatch(getListFunctionsRequest(null))
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (state?.listObjects) {
      setChoosedObj(state.listObjects)
    }
  }, [state.listObjects])
  useEffect(() => {
    if (screenID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(screenID))
    }
  }, [screenMode, screenID])

  useEffect(() => {
    if (screenID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        ...state.detail,
        Objects: state.detail.objects?.map((e: any) => e.id),
        useCases: state.detail.useCases?.map((e: any) => e.id),
      })
      setAccess(state.detail?.access)
      setAttachment(state.detail?.mockUpScreen)
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm();
        form.setFieldsValue({
          createMore: isCreateMore
        })
      } else {
        onDismiss();
      }
      setIsDraft(false);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce((values: any, st?: string) => {
    let mentionReferences = getReferencesFromEditor(getCkeditorData?.current?.props?.data, true);
    tableRef.current.getTableState().forEach((e) => {
      if (e?.description) {
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.description, true));
      }
    })
    const requestData: any = {
      "id": screenID,
      "name": values.name,
      "code": values.code,
      "access": getCkeditorData?.current?.props?.data,
      "mockUpScreen": attachment?.id,
      "version": values.version,
      "requirement": '',
      "description": values.description,
      "dateCreated": null,
      "createdBy": null,
      "submittedBy": null,
      "dateSubmitted": null,
      "status": isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) : hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED,
      "screenComponents": tableRef.current.getTableState().map((e, index) => {
        return {
          "id": e.id,
          "name": e.name,
          "component": e.component,
          "componentType": e.componentType,
          "componentTypeDetail": e.componentType,
          "order": index,
          "editable": e.editable,
          "mandatory": e.mandatory,
          "defaultValue": e.defaultValue,
          "description": e.description,
          "targetScreenId": e.targetScreenId,
          "objectId": e.object?.id,
          "objectPropertyId": e.objectProperty?.id,
          "usecaseId": e.useCase?.id
        }
      }),
      "objectIds": values.Objects,
      "usecaseIds": values.useCases ? Array.isArray(values.useCases) ? values.useCases : [values.useCases] : [],
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      if (!attachment?.id) {
        attachmentRef.current.scrollIntoView('mockUpScreen')
        ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.common-screen')
        return
      }
      if (!requestData.usecaseIds || requestData.usecaseIds?.length === 0) {
        useCaseRef.current.scrollIntoView('useCases')
        ShowMessgeAdditionalSubmit('EMSG_14');
        return
      }
      if (!requestData.screenComponents || requestData.screenComponents?.length === 0) {
        tableUpdateRef.current.scrollIntoView('updatetablescreen')
        ShowMessgeAdditionalSubmit('EMSG_15')
        return
      }
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.screen' }) }
        ),
        onOk() {
          requestData.messageAction = hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? MESSAGE_TYPES.APPROVE : MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(false);
    setAccess('');
    setAttachment(null);
    form.resetFields([
      'name',
      'code',
      'objects',
      'description',
      'access',
      'mockUpScreen',
      'useCases',
      'version'
    ])
    form.resetFields();
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'object', title: intl.formatMessage({ id: 'common_screen.label.object' }), },
      { field: 'description', title: intl.formatMessage({ id: 'common-screen-details.screen-info.description', }), },
      { field: 'access', title: intl.formatMessage({ id: 'common-screen-details.screen-info.access' }), },
      { field: 'mockup-screen', title: intl.formatMessage({ id: 'common-screen-details.screen-info.mockup-screen' }), },
      { field: 'screen-description', title: intl.formatMessage({ id: 'common-screen-details.screen-info.screen-description' }), },
      { field: 'use-case', title: intl.formatMessage({ id: 'common_screen.label.use-case' }), },
    ];
    dispatch(initComment({ projectId: null, itemId: state.detail.id, fields }));

    const payload = {
      projectId: null,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_SCREEN,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  const tagRender = (props) => {
    const { label, name, value, closable, onClose } = props;


    return (
      <Tag
        // color={value}
        // onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 3,
          border: 'none',
        }}
        title={label}
      >
        {label.length > 20 ? label.substring(0, 20) + '...' : label}
      </Tag>
    );
  };

  //#endregion COMMENT INIT

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    size="medium"
    visible={true}
    footer={null}
  >
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={10}>
            <Space size="large">
              <Form.Item
                name="name"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <Input
                  size="large"
                  className="modal-create-name-input-field"
                  placeholder={`${intl.formatMessage({ id: `createscreen.input-placehold.screen-name` })}${intl.formatMessage({
                    id: `common.mandatory.*`,
                  })}`}
                  maxLength={255}
                />
              </Form.Item>
              {screenMode === SCREEN_MODE.EDIT ? renderCommonStatusBadge(state.detail?.status) : <></>}
            </Space>
          </Col>

          <Col span={14}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                {screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
                  <Button type="primary" ghost htmlType="submit" onClick={() => setIsDraft(false)}>
                    {intl.formatMessage({ id: 'common.action.submit' })}
                  </Button> : <></>
                }
                {
                  screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
                    <Button onClick={() => setIsDraft(true)} className="success-btn" htmlType="submit">
                      {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                    </Button> : <></>
                }
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 280}>
        <Row align="middle">
          <Col span={2}>
            <TriggerComment screenMode={screenMode} field="version">
              <Text>
                {intl.formatMessage({
                  id: 'createobject.place-holder.version',
                })}
              </Text>
            </TriggerComment>
          </Col>
          <Col span={2}>
            <Form.Item
              className="mb-0"
              name="version"
              rules={[
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
              ]}
            >
              <Input
                placeholder={`${intl.formatMessage({
                  id: `createobject.place-holder.version`,
                })}`}
                maxLength={255}
              />
            </Form.Item>
          </Col>
        </Row>
        <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
          <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.screen-information' })}>
            {
              screenMode === SCREEN_MODE.EDIT ? <Row>
                <Col span={6}>
                  <FormGroup label={intl.formatMessage({ id: 'createscreen.label-modal.screen-code' })}>
                    <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                      <Input disabled maxLength={255} />
                    </Form.Item>
                  </FormGroup>
                </Col>
              </Row> : <></>
            }

            <FormGroup label={
              <TriggerComment screenMode={screenMode} field='object'>
                {intl.formatMessage({ id: 'createscreen.label.object' })}
              </TriggerComment>}>
              <Form.Item name="Objects">
                <Select
                  filterOption={(input, option: any) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                  showSearch
                  mode="multiple"
                >
                  {state.listObjects?.filter(e => e.status !== STATUS_COMMON.REMOVED)?.map(
                    (item: any) => <Option key={item.id} value={item.id}>{item.name}</Option>
                  )}
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup label={
              <TriggerComment screenMode={screenMode} field='description'>
                {intl.formatMessage({ id: 'createscreen.label.description' })}
              </TriggerComment>}>

              <TextAreaBullet
                // reload={state.reLoad}
                // reloadAfterBack={state.isBack}
                label=""
                name="description"
                labelAlign="left"
                rules={[
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              ></TextAreaBullet>
            </FormGroup>

            <FormGroup label={
              <TriggerComment screenMode={screenMode} field='access'>
                {intl.formatMessage({ id: 'createscreen.label.access' })}
              </TriggerComment>}>
              <Form.Item name="access">
                <CkeditorMention
                  ref={getCkeditorData}
                  data={access}
                  isCommon
                />
              </Form.Item>
            </FormGroup>
            <div ref={attachmentRef}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field='mockup-screen'>
                  {intl.formatMessage({ id: 'createscreen.label.mockup' })}
                </TriggerComment>}>
                <Form.Item name="mockUpScreen">
                  <LavAttachmentUpload
                    artefactType={COM_ARTEFACT_TYPE_ID.SCREEN}
                    attachment={attachment}
                    onChange={setAttachment}
                    name="file"
                    supportPDF
                    isCommon
                  />
                </Form.Item>
              </FormGroup>
            </div>

            <div ref={tableUpdateRef}></div>
            <FormGroup label={
              <TriggerComment screenMode={screenMode} field='screen-description'>
                {intl.formatMessage({ id: 'createscreen.label.screen-description' })}
              </TriggerComment>}>
              <Form.Item name="updatetablescreen">
                <ScreenComponents
                  form={form}
                  ref={tableRef}
                  isCreate={screenMode != SCREEN_MODE.EDIT}
                  choosedObj={choosedObj}
                />
              </Form.Item>
            </FormGroup>
          </Card>

          <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.reference' })} >
            <div ref={useCaseRef}>
              <FormGroup label={
                <TriggerComment screenMode={screenMode} field='use-case'>
                  {intl.formatMessage({ id: 'createscreen.label.usecase' })}
                </TriggerComment>}>
                <Form.Item name="useCases" wrapperCol={{ span: 24 }}>
                  <Select
                    filterOption={(input, option: any) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                    showSearch
                    allowClear
                    optionLabelProp="label"
                    tagRender={tagRender}
                  >
                    {state.listFunctions?.map(
                      (item: any) =>
                        item.status !== STATUS_COMMON.DELETED && (
                          <Option key={item.id} value={item.id} label={item.name}>
                            {item.name}
                          </Option>
                        )
                    )}
                  </Select>
                </Form.Item>
              </FormGroup>
            </div>
          </Card>
        </Space>
      </Scrollbars>
    </Form>
  </CustomModal>
}
const CommonMockupScreenForm = ({ screenID, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: CommonMockupScreenFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return <>
    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button
          ghost={screenMode === SCREEN_MODE.CREATE}
          type='primary'
          className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
          onClick={() => setIsModalVisible(true)}
          icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
        >
          {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common_screen.button.create-screen' : 'common.action.update' })}
        </Button> :
        buttonType === BUTTON_TYPE.ICON ?
          <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
          <></>
    }
    {isModalVisible === true ? <CommonMockupScreenFormModal screenID={screenID} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}

export default CommonMockupScreenForm
