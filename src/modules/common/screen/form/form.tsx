import LavPageHeader from '../../../../helper/component/lav-breadcumb'
import { CommentState } from '@/modules/_shared/comment/type'
import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useRef, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { APP_COMMON_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, COMMENT_STATUS, COM_ARTEFACT_TYPE_ID, MESSAGE_TYPES, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import LavAttachmentUpload from '../../../../helper/component/lav-attachment-upload'
import TextAreaBullet from '../../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, getReferencesFromEditor, hasCommonRole, renderCommonStatusBadge, ShowMessgeAdditionalSubmit } from '../../../../helper/share'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { createFailed, createRequest, getDetailRequest, getListFunctionsRequest, getListObjectsRequest, resetState, setModalVisible, updateFailed, updateRequest } from '../action'
import ScreenComponents from '../form/screen-components'
import { CommonScreenState } from '../type'
import { initComment, initCommentScreen } from './../../../_shared/comment/action'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select


interface CommonMockupScreenFormModalProps {
  screenID?: number
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonMockupScreenFormModalPage = ({ screenID, screenMode, onFinish, onDismiss }: CommonMockupScreenFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.CommonScreen) as CommonScreenState
  const [isDraft, setIsDraft] = useState(false);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const [access, setAccess] = useState('') as any
  const [choosedObj, setChoosedObj] = useState([]) as any
  const [attachment, setAttachment] = useState(null) as any
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const getCkeditorData: any = createRef()
  const tableRef: any = useRef()
  const attachmentRef = useRef<any>()
  const getCkeditorDataDes: any = createRef()
  const useCaseRef = useRef<any>()
  const tableUpdateRef = useRef<any>()

  useEffect(() => {
    dispatch(getListObjectsRequest(null));
    dispatch(getListFunctionsRequest(null))
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (state?.listObjects) {
      setChoosedObj(state.listObjects)
    }
  }, [state.listObjects])
  useEffect(() => {
    if (screenID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(screenID))
    }
  }, [screenMode, screenID])

  useEffect(() => {
    if (screenID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        ...state.detail,
        Objects: state.detail.objects?.map((e: any) => e.id),
        useCases: state.detail.useCases?.map((e: any) => e.id),
      })
      setAccess(state.detail?.access)
      setAttachment(state.detail?.mockUpScreen)
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm();
        form.setFieldsValue({
          createMore: isCreateMore
        })
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss();
      }
      setIsDraft(false);
      setIsCreateMore(false);
      dispatch(createFailed(null))
      dispatch(updateFailed(null))
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce(async (values: any, st?: string) => {
    let mentionReferences = getReferencesFromEditor(getCkeditorData?.current?.props?.data, true);
    mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(getCkeditorDataDes.current?.props?.data, true))
    tableRef.current.getTableState().forEach((e) => {
      if (e?.description) {
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.description, true));
      }
    })

    const checkSc = tableRef.current.getTableState()?.filter(e => e.name.replace(/\s+/g, ' ')
      .trim() === "" || e?.componentType.replace(/\s+/g, ' ')
        .trim() === "")

    if (checkSc && checkSc.length > 0) {
      tableUpdateRef.current.scrollIntoView('updatetablescreen')
      ShowMessgeAdditionalSubmit('EMSG_34');
      return;
    }
    let valueArr = tableRef.current.getTableState().map((item) => { return item?.name });
    let isDuplicate = valueArr.some((item, idx) => {
      return valueArr.indexOf(item) != idx
    });
    if (isDuplicate) {
      tableUpdateRef.current.scrollIntoView('updatetablescreen')
      ShowMessgeAdditionalSubmit('EMSG_7', 'common.artefact.component');
      return;
    }
    const requestData: any = {
      "id": screenID,
      "name": values.name,
      "code": values.code,
      "access": getCkeditorData?.current?.props?.data,
      "mockUpScreen": attachment?.id,
      "version": values.version,
      "requirement": '',
      "description": getCkeditorDataDes?.current?.props?.data,
      "dateCreated": null,
      "createdBy": null,
      "submittedBy": null,
      "dateSubmitted": null,
      "status": isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) : hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED,
      "screenComponents": tableRef.current.getTableState().map((e, index) => {
        return {
          "id": e.id,
          "name": e.name,
          "component": e.component,
          "componentType": e.componentType,
          "componentTypeDetail": e.componentType,
          "order": index,
          "editable": e.editable,
          "mandatory": e.mandatory,
          "defaultValue": e.defaultValue,
          "description": e.description,
          "targetScreenId": e.targetScreenId,
          "objectId": e.object?.id,
          "objectPropertyId": e.objectProperty?.id,
          "usecaseId": e.useCase?.id
        }
      }),
      "objectIds": values.Objects,
      "usecaseIds": values.useCases ? Array.isArray(values.useCases) ? values.useCases : [values.useCases] : [],
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      if (!attachment?.id) {
        attachmentRef.current.scrollIntoView('mockUpScreen')
        ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.common-screen')
        return
      }
      if (!requestData.usecaseIds || requestData.usecaseIds?.length === 0) {
        useCaseRef.current.scrollIntoView('useCases')
        ShowMessgeAdditionalSubmit('EMSG_14');
        return
      }
      if (!requestData.screenComponents || requestData.screenComponents?.length === 0) {
        tableUpdateRef.current.scrollIntoView('updatetablescreen')
        ShowMessgeAdditionalSubmit('EMSG_15')
        return
      }
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.screen' }) }
        ),
        onOk() {
          requestData.messageAction = hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? MESSAGE_TYPES.APPROVE : MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(false);
    setAccess('');
    setAttachment(null);
    form.resetFields([
      'name',
      'code',
      'objects',
      'description',
      'access',
      'mockUpScreen',
      'useCases',
      'version'
    ])
    form.resetFields();
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'object', title: intl.formatMessage({ id: 'common_screen.label.object' }), },
      { field: 'description', title: intl.formatMessage({ id: 'common-screen-details.screen-info.description', }), },
      { field: 'access', title: intl.formatMessage({ id: 'common-screen-details.screen-info.access' }), },
      { field: 'mockup-screen', title: intl.formatMessage({ id: 'common-screen-details.screen-info.mockup-screen' }), },
      { field: 'screen-description', title: intl.formatMessage({ id: 'common-screen-details.screen-info.screen-description' }), },
      { field: 'use-case', title: intl.formatMessage({ id: 'common_screen.label.use-case' }), },
    ];
    state.detail?.screenComponents?.forEach((e) => {
      fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
    })
    dispatch(initComment({ projectId: null, itemId: state.detail.id, fields }));

    const payload = {
      projectId: null,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_SCREEN,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  const tagRender = (props) => {
    const { label, name, value, closable, onClose } = props;


    return (
      <Tag
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 3,
          border: 'none',
        }}
        title={label}
      >
        {label.length > 20 ? label.substring(0, 20) + '...' : label}
      </Tag>
    );
  };

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <LavPageHeader
          showBreadcumb={false}
          title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createscreen.label.title' : 'updatescreen.label.title' })}
        >
          <Space size="small">
            {screenMode === SCREEN_MODE.CREATE ? <Form.Item
              style={{ marginBottom: '0px' }}
              valuePropName="checked"
              name="createMore"
              wrapperCol={{ span: 24 }}
            >
              <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
            </Form.Item> : <></>}
            <Button onClick={debounce(confirmCancel, 500)}>
              {intl.formatMessage({ id: 'common.action.close' })}
            </Button>

            {screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
              <Button type="primary" ghost htmlType="submit" onClick={() => setIsDraft(false)}>
                {intl.formatMessage({ id: 'common.action.submit' })}
              </Button> : <></>
            }

            <Button onClick={() => setIsDraft(true)} className="success-btn" htmlType="submit">
              {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
            </Button>
          </Space>
        </LavPageHeader>
      </div>

      <Row align="middle">
        {screenMode === SCREEN_MODE.EDIT ?
          <Col span={5}>
            <div className='status-container'>
              <div>
                {intl.formatMessage({ id: 'common.field.status' })}
              </div>
              <div>
                {renderCommonStatusBadge(state.detail?.status)}
              </div>
            </div>
          </Col> : <></>
        }
      </Row>

      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.screen-information' })}>
          {
            screenMode === SCREEN_MODE.EDIT ?
              <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                  <Input disabled maxLength={255} />
                </Form.Item>
              </FormGroup> : <></>
          }
          <FormGroup
            inline
            required
            label={intl.formatMessage({ id: 'common.label.name' })}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item
              name="name"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
            >
              <Input
                maxLength={255}
              />
            </Form.Item>
          </FormGroup>

          <FormGroup inline label={
            <TriggerComment screenMode={screenMode} field='object'>
              {intl.formatMessage({ id: 'createscreen.label.object' })}
            </TriggerComment>}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item name="Objects">
              <Select
                filterOption={(input, option: any) =>
                  option.children
                    .toLowerCase()
                    .indexOf(input.toLowerCase()) >= 0
                }
                showSearch
                mode="multiple"
              >
                {state.listObjects?.filter(e => e.status !== STATUS_COMMON.REMOVED)?.map(
                  (item: any) => <Option key={item.id} value={item.id}>{item.name}</Option>
                )}
              </Select>
            </Form.Item>
          </FormGroup>

          <FormGroup inline label={
            <TriggerComment screenMode={screenMode} field='description'>
              {intl.formatMessage({ id: 'createscreen.label.description' })}
            </TriggerComment>}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item
              name="description"
              labelAlign="left"
              rules={[{
                validator: async (rule, value) => {
                  const description = getCkeditorDataDes?.current?.props?.data
                  if ((description == '' || description == undefined) && (!isDraft || state.detail?.status === STATUS_COMMON.SUBMITTED)) {
                    throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                  }
                }
              }]}
              wrapperCol={{ span: 24 }}
            >
              <CkeditorMention
                isCommon
                ref={getCkeditorDataDes}
                data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
              />
            </Form.Item>
          </FormGroup>

          <FormGroup inline label={
            <TriggerComment screenMode={screenMode} field='access'>
              {intl.formatMessage({ id: 'createscreen.label.access' })}
            </TriggerComment>}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item name="access">
              <CkeditorMention
                ref={getCkeditorData}
                data={access}
                isCommon
              />
            </Form.Item>
          </FormGroup>

          <div ref={attachmentRef}>
            <FormGroup label={
              <TriggerComment screenMode={screenMode} field='mockup-screen'>
                {intl.formatMessage({ id: 'createscreen.label.mockup' })}
              </TriggerComment>}>
              <Form.Item name="mockUpScreen">
                <LavAttachmentUpload
                  artefactType={COM_ARTEFACT_TYPE_ID.SCREEN}
                  attachment={attachment}
                  onChange={setAttachment}
                  name="file"
                  supportPDF
                  isCommon
                />
              </Form.Item>
            </FormGroup>
          </div>

          <div ref={tableUpdateRef}>
            <ScreenComponents
              form={form}
              ref={tableRef}
              isCreate={screenMode != SCREEN_MODE.EDIT}
              choosedObj={choosedObj}
            />
          </div>

        </Card>

        <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.reference' })} >
          <div ref={useCaseRef}>
            <FormGroup inline label={
              <TriggerComment screenMode={screenMode} field='use-case'>
                {intl.formatMessage({ id: 'createscreen.label.usecase' })}
              </TriggerComment>}
              labelSpan={3}
              controlSpan={21}
            >
              <Form.Item name="useCases" >
                <Select
                  filterOption={(input, option: any) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                  showSearch
                  allowClear
                  optionLabelProp="label"
                  tagRender={tagRender}
                >
                  {state.listFunctions?.map(
                    (item: any) =>
                      item.status !== STATUS_COMMON.DELETED && (
                        <Option key={item.id} value={item.id} label={item.name}>
                          {item.name}
                        </Option>
                      )
                  )}
                </Select>
              </Form.Item>
            </FormGroup>
          </div>
        </Card>
      </Space>
    </Form>
  </Spin>
}



export default CommonMockupScreenFormModalPage
