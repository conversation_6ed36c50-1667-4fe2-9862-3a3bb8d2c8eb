import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
    AutoComplete,
    Button, Checkbox, Col, Form, Input, Modal, Row, Select, Typography
} from 'antd'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../../config/locale.config'
import { COMP_TYPE, ROW_STATUS, SCREEN_MODE, STATUS, STATUS_COMMON } from '../../../../../constants'
import CkeditorMention from '../../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import { getListId, Regex } from '../../../../../helper/share/type'
import { getListObjectPropertiesRequest } from '../../action'
import { CommonScreenState } from '../../type'

const { Option } = Select
const { Text } = Typography
const CreateScreenComponent = (props) => {
    const create_state = useSelector<AppState | null>(
        (s) => s?.CommonScreen
    ) as CommonScreenState

    const dispatch = useDispatch()
    const [visible, setVisible] = useState(false)
    const [createOther, setCreateOther] = useState(false)
    const [form] = Form.useForm()
    const [listRefProperty, setListRefProperty] = useState<any[]>([])

    const getCkeditorData: any = createRef()
    const [ckData, setCkData] = useState()
    const [modalAction, setModalAction] = useState<any[]>([])
    const [selectCompType, setSelectCompType] = useState<string>('')

    useEffect(() => {
        setListRefProperty(create_state.listObjectProperties || [])
    }, [create_state.listObjectProperties, create_state.listTargetUseCase])

    useEffect(() => {
        // case edit
        if (visible && props.type === SCREEN_MODE.EDIT) {
            const editData = props.editData
            setCkData(editData.description)
            // case edit new screen component
            if (editData.status !== ROW_STATUS.CREATE) {
                if (editData.object) {
                    // case pick object
                    dispatch(getListObjectPropertiesRequest(editData.object.id));
                    setSelectCompType(editData.componentType)
                    form.setFieldsValue({
                        name: editData.name,
                        componentType: editData.componentType
                            ? editData.componentType
                            : undefined,
                        objectId: editData.object
                            ? editData.object.id
                                ? editData.object.id
                                : editData.object
                            : undefined,
                        objectPropertyId: editData.objectProperty
                            ? editData.objectProperty.id
                                ? editData.objectProperty.id
                                : editData.objectProperty
                            : undefined,
                        screen: editData.screen
                            ? editData.screen.id
                                ? editData.screen.id
                                : editData.screen
                            : undefined,
                        useCase: editData.useCase
                            ? editData.useCase.id
                                ? editData.useCase.id
                                : editData.useCase
                            : undefined,
                        editable: editData.editable,
                        mandatory: editData.mandatory,
                        defaultValue: editData.defaultValue,
                    })
                } else {
                    // case don't pick object
                    form.setFieldsValue({
                        name: editData.name,
                        componentType: editData.componentType
                            ? editData.componentType
                            : undefined,
                        editable: editData.editable,
                        mandatory: editData.mandatory,
                        defaultValue: editData.defaultValue,
                    })
                    setSelectCompType(editData.componentType)
                }
            } else {
                // case edit new screen component get from server
                if (editData.object?.id) {
                    dispatch(getListObjectPropertiesRequest(editData.object.id));
                }
                setSelectCompType(editData.componentType)
                form.setFieldsValue({
                    name: editData.name,
                    componentType: editData.componentType,
                    objectId: editData.object
                        ? editData.object.id
                            ? editData.object.id
                            : editData.object
                        : undefined,
                    objectPropertyId: editData.objectProperty
                        ? editData.objectProperty.id
                            ? editData.objectProperty.id
                            : editData.objectProperty
                        : undefined,
                    screen: editData.screen ? editData.screen : undefined,
                    useCase: editData.useCase ? editData.useCase : undefined,
                    editable: editData.editable,
                    mandatory: editData.mandatory,
                    defaultValue: editData.defaultValue,
                })
            }
            setModalAction([
                <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
                    {intl.formatMessage({ id: `common.action.cancel` })}
                </Button>,
                <Button key="5" onClick={form.submit} className="success-btn">
                    {intl.formatMessage({ id: 'common.action.update' })}
                </Button>,
            ])
        } else if (visible && props.type == SCREEN_MODE.CREATE) {
            // case add new screen des
            setListRefProperty(listRefProperty ? listRefProperty : [])
            // if (selectCompType !== -1) {
            //     form.setFieldsValue({
            //         componentType: selectCompType,
            //     })
            // }
            // form.resetFields()
            setModalAction([
                <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
                    {intl.formatMessage({ id: `common.action.cancel` })}
                </Button>,
                <Checkbox key="3" checked={createOther} onChange={changeCreateOther}>
                    {intl.formatMessage({ id: 'createobject.checkbox.create-another' })}
                </Checkbox>,
                <Button key="5" onClick={form.submit} className="success-btn">
                    {intl.formatMessage({ id: 'common.action.add-to-table' })}
                </Button>,
            ])
        }
    }, [visible, createOther, create_state.listObjects])

    useEffect(() => {
        if (visible && props.type == SCREEN_MODE.CREATE) {
            setSelectCompType('');
            form.resetFields()
        }
    }, [visible])

    const showModal = () => {
        setVisible(true)
    }

    const handleCancel = () => {
        form.resetFields()
        setVisible(false)
        setCreateOther(false)
    }


    const getSourceObject = (id) => {
        const obj = create_state.listObjects?.find((e) => e.id === id);
        return obj || null
    }

    const getSourceObjectProperty = (id) => {
        const obj = create_state.listObjectProperties?.find((e) => e.id === id);
        return obj || null
    }
    const onFinish = (values: any) => {
        let data = ''

        if (getCkeditorData?.current?.props?.data) {
            data = getCkeditorData.current?.props?.data
        }
        // 1 get list object
        const regex = Regex.GET_LIST_OBJECT_FROM_CK
        const regexGetId = Regex.GET_LIST_OBJECT_ID
        const foundObject = data.match(regex)
        const listObjectId = getListId(regexGetId, foundObject)
        // 2 get list usecase
        const regexUseCase = Regex.GET_LIST_USE_CASE_FROM_CK
        const regexGetIdUseCase = Regex.GET_LIST_USE_CASE_ID
        const foundUsecase = data.match(regexUseCase)
        const listUseCaseId = getListId(regexGetIdUseCase, foundUsecase)


        const sendDataToServer = {
            listObject: listObjectId,
            listUseCase: listUseCaseId,
        }

        if (props.type === SCREEN_MODE.CREATE) {
            if (values.componentType !== COMP_TYPE.TABLE) {
                const column = {
                    ...values,
                    editable: values.editable || false,
                    mandatory: values.mandatory || false,
                    description: getCkeditorData?.current?.props?.data,
                    id: null,
                    status: ROW_STATUS.CREATE,
                    object: getSourceObject(values.objectId),
                    objectProperty: getSourceObjectProperty(values.objectPropertyId),

                    ...sendDataToServer,
                }
                props.addComponent(column)
                form.resetFields()
            } else {
                const column = {
                    ...values,
                    description: getCkeditorData?.current?.props?.data,
                    id: null,
                    status: ROW_STATUS.CREATE,
                    object: getSourceObject(values.objectId),
                    objectProperty: getSourceObjectProperty(values.objectPropertyId),

                    ...sendDataToServer,
                }
                props.addComponent(column)
            }
            // setCreateOther(false)
            if (createOther === false) {
                setVisible(false)
            }
        } else if (props.type === SCREEN_MODE.EDIT) {
            const column = {
                ...values,
                description: getCkeditorData?.current?.props?.data,
                status:
                    props.editData.status !== ROW_STATUS.CREATE
                        ? ROW_STATUS.UPDATE
                        : props.editData.status,
                ...sendDataToServer,
                object: getSourceObject(values.objectId),
                objectProperty: getSourceObjectProperty(values.objectPropertyId),

            }
            props.editDataHandle(column, props.index)
            setVisible(false)
            form.resetFields()
        }

        if (createOther) {
            setSelectCompType('');
            form.resetFields()
            setListRefProperty([])
        }
    }

    const onFinishFailed = (errorInfo: any) => { }

    const changeSelectCompType = (e) => {
        setSelectCompType(e)
    }

    const changeObject = (e) => {
        form.setFieldsValue({
            [`objectPropertyId`]: undefined,
            [`screen`]: undefined,
            [`useCase`]: undefined,
        })

        setListRefProperty([])
        if (e) {
            dispatch(getListObjectPropertiesRequest(e));
        }
    }

    const changeCreateOther = (e) => {
        setCreateOther(e.target.checked)
    }

    return (
        <>
            {props.type === SCREEN_MODE.CREATE && (
                <Button icon={<PlusOutlined />} type="link" onClick={showModal}>
                    {intl.formatMessage({ id: 'createscreen.button.new-component' })}
                </Button>
            )}
            {props.type === SCREEN_MODE.EDIT && (
                <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={showModal}></Button>
            )}
            <Form
                form={form}
                id={Date.now().toString()}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
                scrollToFirstError={{ block: 'center' }}
            >
                {visible && (
                    <Modal
                        width={700}
                        onCancel={handleCancel}
                        title={intl.formatMessage({ id: props.type === SCREEN_MODE.CREATE ? 'common_screen.modal.title-add-screen-component' : 'common_screen.modal.title-update-screen-component' })}
                        visible={visible}
                        footer={modalAction}
                        maskClosable={false}
                    >
                        <Row gutter={[16, 4]}>
                            <Col span={5}>
                                <Text>
                                    {intl.formatMessage({ id: 'createscreen.label-modal.component' })}
                                </Text>
                                <Text type="danger">
                                    {intl.formatMessage({
                                        id: `common.mandatory.*`,
                                    })}
                                </Text>
                            </Col>
                            <Col span={7}>
                                <Form.Item
                                    name="name"
                                    validateTrigger="onBlur"
                                    rules={[
                                        {
                                            required: true,
                                            message: intl.formatMessage({ id: 'IEM_1' }),
                                        },
                                        { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                        {
                                            validator: async (rule, value) => {
                                                if (value && value.trim().length === 0) {
                                                    throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                                }
                                                if (value && value.length > 0) {
                                                    if (props.type === SCREEN_MODE.EDIT) {
                                                        const dupplicate = props.currentData?.findIndex(
                                                            (item: any) =>
                                                                item.name
                                                                    .toLowerCase()
                                                                    .replace(/\s+/g, ' ')
                                                                    .trim() ===
                                                                value.toLowerCase().replace(/\s+/g, ' ').trim()
                                                        )
                                                        if (
                                                            props.editData &&
                                                            value
                                                                .toLowerCase()
                                                                .replace(/\s+/g, ' ')
                                                                .trim() !==
                                                            props.editData.name
                                                                .toLowerCase()
                                                                .replace(/\s+/g, ' ')
                                                                .trim() &&
                                                            dupplicate !== -1
                                                        ) {
                                                            throw new Error(
                                                                intl.formatMessage({ id: 'EMSG_7' }, { Artefact: intl.formatMessage({ id: 'common.artefact.component' }) })
                                                            )
                                                        }
                                                    } else if (props.type === SCREEN_MODE.CREATE) {
                                                        const dupplicate = props.currentData?.findIndex(
                                                            (item: any) =>
                                                                item.name
                                                                    .toLowerCase()
                                                                    .replace(/\s+/g, ' ')
                                                                    .trim() ===
                                                                value.toLowerCase().replace(/\s+/g, ' ').trim()
                                                        )

                                                        if (dupplicate !== -1) {
                                                            throw new Error(
                                                                intl.formatMessage({ id: 'EMSG_7' }, { Artefact: intl.formatMessage({ id: 'common.artefact.component' }) })
                                                            )
                                                        }
                                                    }
                                                }
                                            },
                                        },
                                    ]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </Col>

                            <Col span={5}>
                                <Text>
                                    {intl.formatMessage({
                                        id: 'createscreen.label-modal.type',
                                    })}
                                </Text>
                            </Col>
                            <Col span={7}>
                                <Form.Item name="componentType">
                                    {/* <Select
                                        filterOption={(input, option: any) =>
                                            option.children
                                                .toLowerCase()
                                                .indexOf(input.toLowerCase()) >= 0
                                        }
                                        showSearch
                                        allowClear
                                        onChange={changeSelectCompType}
                                    >
                                        {props.compType?.map((item: any) => (
                                            <Option key={item.id} value={item.id}>
                                                {item.name}
                                            </Option>
                                        ))}
                                    </Select> */}

                                    <AutoComplete
                                        maxLength={255}
                                        options={props.compType}
                                        defaultValue={props.data?.name}
                                        onSelect={(value, option) => {
                                            setSelectCompType(value)
                                        }}
                                        onChange={(value) => {
                                            setSelectCompType(value)
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                            {/* <Col span={5}>
                                <Text>
                                    {intl.formatMessage({
                                        id: 'createscreen.label-modal.object',
                                    })}
                                </Text>
                            </Col>
                            <Col span={7}>
                                <Form.Item name="objectId">
                                    <Select
                                        filterOption={(input, option: any) =>
                                            option.children
                                                .toLowerCase()
                                                .indexOf(input.toLowerCase()) >= 0
                                        }
                                        showSearch
                                        allowClear
                                        onChange={changeObject}
                                    >
                                        {props.objects?.map(
                                            (item: any) =>
                                                item.status !== STATUS_COMMON.DELETED && (
                                                    <Option key={item.id} value={item.id}>
                                                        {item.name}
                                                    </Option>
                                                )
                                        )}
                                    </Select>
                                </Form.Item>
                            </Col> */}
                            {selectCompType !== 'Table' && (
                                <>
                                    <Col span={5}>
                                        <Text>
                                            {intl.formatMessage({
                                                id: 'createscreen.label-modal.object',
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={7}>
                                        <Form.Item name="objectId">
                                            <Select
                                                filterOption={(input, option: any) =>
                                                    option.children
                                                        .toLowerCase()
                                                        .indexOf(input.toLowerCase()) >= 0
                                                }
                                                showSearch
                                                allowClear
                                                onChange={changeObject}
                                            >
                                                {props.objects?.map(
                                                    (item: any) =>
                                                        item.status !== STATUS_COMMON.DELETED && (
                                                            <Option key={item.id} value={item.id}>
                                                                {item.name}
                                                            </Option>
                                                        )
                                                )}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    {' '}
                                    <Col span={5}>
                                        <Text>
                                            {intl.formatMessage({
                                                id: 'createscreen.label-modal.object-property',
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={7}>
                                        <Form.Item name="objectPropertyId" rules={[
                                            {
                                                validator: async (rule, value) => {
                                                    const sourceObj = form.getFieldValue('objectId');
                                                    if (sourceObj != undefined && !value) {
                                                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                                    }
                                                }
                                            }

                                        ]}>
                                            <Select
                                                filterOption={(input, option: any) =>
                                                    option.children
                                                        .toLowerCase()
                                                        .indexOf(input.toLowerCase()) >= 0
                                                }
                                                showSearch
                                                allowClear
                                            >
                                                {listRefProperty?.map(
                                                    (item: any) =>
                                                        item.status !== STATUS.DELETE &&
                                                        item.status !== STATUS.CANCELLED && (
                                                            <Option key={item.id} value={item.id}>
                                                                {item.name}
                                                            </Option>
                                                        )
                                                )}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={5}>
                                        <Text>
                                            {intl.formatMessage({
                                                id: 'createscreen.label-modal.editable',
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={1}>
                                        <Form.Item
                                            style={{ marginBottom: '0px' }}
                                            valuePropName="checked"
                                            name="editable"
                                        >
                                            <Checkbox key="1"></Checkbox>
                                        </Form.Item>
                                    </Col>
                                    <Col span={6}></Col>
                                    <Col span={5}>
                                        <Text>
                                            {intl.formatMessage({
                                                id: 'createscreen.label-modal.mandatory',
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={1}>
                                        <Form.Item
                                            style={{ marginBottom: '0px' }}
                                            valuePropName="checked"
                                            name="mandatory"
                                            wrapperCol={{ span: 24 }}
                                        >
                                            <Checkbox key="2"></Checkbox>
                                        </Form.Item>
                                    </Col>
                                    <Col span={6}></Col>
                                    <Col span={5}>
                                        <Text>
                                            {intl.formatMessage({
                                                id: 'createscreen.label-modal.default',
                                            })}
                                        </Text>
                                    </Col>
                                    <Col span={7}>
                                        <Form.Item name="defaultValue" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                            <Input maxLength={255} />
                                        </Form.Item>
                                    </Col>
                                </>
                            )}

                            <Col span={24}>
                                <Text>
                                    {intl.formatMessage({
                                        id: 'createscreen.label-modal.description',
                                    })}
                                </Text>
                            </Col>
                            <Col span={24}>
                                <Form.Item name="ckeditor">
                                    <CkeditorMention
                                        isCommon
                                        ref={getCkeditorData}
                                        data={props?.editData?.description || ''}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Modal>
                )}
            </Form>
        </>
    )
}

export default CreateScreenComponent
