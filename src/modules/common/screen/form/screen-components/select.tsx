import AppState from '@/store/types'
import { CheckOutlined, PlusOutlined } from '@ant-design/icons'
import {
    Button, Modal, Select, Table, Typography
} from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../../config/locale.config'
import { ROW_STATUS, SCREEN_MODE, STATUS } from '../../../../../constants'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import { getListSelectProperties } from '../../action'
import { CommonScreenState } from '../../type'

const { Option } = Select
const { Text } = Typography
const { confirm } = Modal
const SelectScreenComponent = (props) => {
    const state = useSelector<AppState | null>(
        (s) => s?.CommonScreen
      ) as CommonScreenState
    const dispatch = useDispatch()
    const [visible, setVisible] = useState(false)
    const [dataS, setDataSource] = useState<any>([])
    const [dataSelected, setDataSelected] = useState<any>([])
    const [warning, setWarning] = useState<boolean>(false)
    const [oldObjectLength, setOldObjectLength] = useState<number>(0);

    const showModal = () => {
        setDataSource([])
        setVisible(true)
    }


    const handleCancel = () => {
        setVisible(false)
        setDataSource([])
        setDataSelected([])
    }

    useEffect(() => {
        let listDropdown = state?.listSelectObjectProperties?.map((e) => {
            return {
                ...e,
                component: e.name,
            }
        })
        let arr: any = listDropdown?.filter((item) => {
            return props?.currentData.findIndex((e) => e.component === item.component && e?.object?.name === item?.object?.name) === -1;
        });
        // currentData
        setDataSource([
            ...arr,
            ...dataS
        ])
    }, [state.listSelectObjectProperties])

    const changeObject = (e) => {
       
        if(e.length == 0) {
            setDataSource([])
            setOldObjectLength(0)
            return
        }
        

        if(e.length > oldObjectLength) {
            const itemExisted = dataS.findIndex(data => data.object.id == e[e?.length - 1])
            if(itemExisted == -1) {
                setWarning(false)
                dispatch(getListSelectProperties(e[e?.length - 1]));
                setOldObjectLength(e.length)
                return
            }
            return
        }

        if(e.length < oldObjectLength) {
            const newDataS = dataS.filter(data => e.includes(data.object.id));
            setDataSource(newDataS);
            setOldObjectLength(e.length)
            return
        }
        
       
    }
    const columns = [
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.object-name' }),
            dataIndex: 'name',
            width: '13%',
            render: (text, record) => {
                return record?.object?.name
            }
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.object-property' }),
            dataIndex: 'name',
            width: '13%',
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.unique' }),
            dataIndex: 'unique',
            width: '5%',
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.mandatory' }),
            dataIndex: 'mandatory',
            width: '5%',
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.max-length' }),
            dataIndex: 'maxLength',
            width: '10%',
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.meaning' }),
            dataIndex: 'description',
            width: '27%',
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object' }),
            dataIndex: 'sourceObject',
            width: '15%',
            render: (text, item) => {
                return item.sourceObject?.name
            }
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object-property' }),
            dataIndex: 'sourceObjectProperty',
            width: '20%',
            render: (text, item) => {
                return item.sourceObjectProperty?.name
            }
        },
    ]

    const rowSelection = {
        onChange: (selectedRowKeys: React.Key[], selectedRows) => {
            if (selectedRowKeys?.length !== 0) {
                setWarning(false)
                let list = Object.assign([], selectedRows)
                list = list.map((e) => {
                    return {
                        ...e,
                        editable: false,
                        component: e.name,
                        componentType: 'Single line of text',
                        defaultValue: '',
                        status: ROW_STATUS.CREATE,
                        description: e.meaning,
                        objectScreenComponent: e?.sourceObject?.id,
                        sourceObjectProperties: e?.refObjectProperty?.id,
                    }
                })
                setDataSelected(list)
            } else {
                setDataSelected([])
                setWarning(true)
            }
        },
        getCheckboxProps: (record) => ({
        }),
    };

    const SelectObjProperties = () => {
        if (dataSelected?.length === 0) {
            setWarning(true)
        } else {
            let newData = dataSelected?.map((e) => {
                delete e?.id
                return {
                    ...e,
                    description: `<div>
                       <p>${e.description || e.component}</p> ${e.maxLength ? '<br /> -Max Length: ' + e.maxLength : ''} ${e.unique ? '<br /> -Unique: Yes' : ''} 
                        ${e?.refObjectProperty ? '<br/> -Source Object: ' + e?.sourceObject?.name : ''} ${e?.sourceObjectProperty ? '<br/> -Source Object Property: ' + e?.sourceObjectProperty?.name : ''}
                    </div>`
                }
            })
            setWarning(false)
            props.setNewScreenComponent(newData)
            setVisible(false)
            setDataSource([])
            setDataSelected([])
        }
    }
    return (
        <>
            {props.type === SCREEN_MODE.CREATE && (
                <Button icon={<PlusOutlined />} type="link" onClick={showModal}>
                    {intl.formatMessage({ id: 'createscreen.button.select-objproperties' })}
                </Button>
            )}
            {props.type === SCREEN_MODE.EDIT && (
                <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={showModal}></Button>
            )}
            {visible && (
                <Modal
                    width={1500}
                    onCancel={handleCancel}
                    title={intl.formatMessage({ id: 'view-screen-list.modal.title-select-object-properties' })}
                    visible={visible}
                    footer={[]}
                    maskClosable={false}

                >
                    <div>
                        <Select
                            style={{ width: '30%' }}
                            filterOption={(input, option: any) =>
                                option.children
                                    .toLowerCase()
                                    .indexOf(input.toLowerCase()) >= 0
                            }
                            showSearch
                            allowClear
                            mode="multiple"
                            onChange={changeObject}
                        >
                            {props?.objects?.map(
                                (item: any) =>
                                    item.status !== STATUS.DELETE &&
                                    item.status !== STATUS.CANCELLED && (
                                        <Option key={item.id} value={item.id}>
                                            {item.name}
                                        </Option>
                                    )
                            )}
                        </Select>
                    </div>

                    <div style={{ marginTop: '10px' }}>
                        <Table
                            locale={{ emptyText: 'NO DATA' }}
                            rowSelection={{
                                type: 'checkbox',
                                ...rowSelection,
                            }}
                            rowKey={(record) => record.name}
                            bordered
                            dataSource={dataS}
                            columns={columns}
                            pagination={false}
                        />
                    </div>

                    {warning ? <Typography.Text type='danger' style={{ margin: '10px 0' }}>You have to select at least one property</Typography.Text> : <></>}

                    <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
                        <Button className='success-btn' onClick={() => SelectObjProperties()}>{intl.formatMessage({ id: 'common.action.add-to-table' })}</Button>
                    </div>
                </Modal>
            )}

        </>
    )
}

export default SelectScreenComponent
