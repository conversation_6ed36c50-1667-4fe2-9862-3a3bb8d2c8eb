import { extractProjectCode } from '../../../../helper/share'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { API_URLS, APP_ROUTES, COM_ARTEFACT_TYPE_ID, PROJECT_PREFIX, SCREEN_MODE } from '../../../../constants'
import AppState from '../../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import { CommonScreenState } from '../type'
import LavLeftControl from './../../../_shared/left-menu'
import RightControl from './content'
import { PlusOutlined } from '@ant-design/icons'
import CommonMockupScreenFormModalPage from '../form/form'
import intl from '../../../../config/locale.config'
import HistoryScreen from '../../../../modules/history'
import AppCommonService from '../../../../services/app.service'
import CommonScreenVersionDetails from './history/details'

const CommonScreenDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const state = useSelector<AppState | null>((s) => s?.CommonScreen) as CommonScreenState;
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.screenID) {
      dispatch(getDetailRequest(props.match.params.screenID))
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.COMMON_SCREEN + '/' + props.match.params.screenID +  '/' + selectedRowVersion).then((e) => {
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);    
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(APP_ROUTES.COMMON_SCREEN)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.screenID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)
  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${APP_ROUTES.COMMON_SCREEN_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>      
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col span={5}>
            <LavLeftControl
              activeId={props.match.params.screenID}
              apiUrl={API_URLS.COMMON_REFERENCE_SCREENS}
              route={APP_ROUTES.COMMON_SCREEN_DETAIL}
              title='common_screen.header.title'
              artefactType={COM_ARTEFACT_TYPE_ID.SCREEN}
              reload={reload}
              reloadSuccess={() => setReload(false)}
              isCommon
              handleCreate={handleCreate}
            >
              <Button ghost={true}
                type='primary'
                className='lav-btn-create'
                icon={<PlusOutlined />}
                onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common_screen.button.create-screen' })}
              </Button>
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {screenMode === SCREEN_MODE.VIEW ?
        <>          
          <Col span={19}>
            <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} screenID={props.match.params.screenID} setScreenMode={setScreenMode} isModalShow={state?.isModalShow} />
          </Col>
        </> : <></>
      }
      {
          screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <CommonMockupScreenFormModalPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
         screenMode === SCREEN_MODE.EDIT ?
         <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
           <CommonMockupScreenFormModalPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
             handleReloadData()
             setScreenMode(SCREEN_MODE.VIEW)
           }} screenID={props.match.params.screenID} />
         </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.common-screen"
                            apiURL = {API_URLS.COMMON_HISTORY} isCommon = {true}
                            artefactType = {COM_ARTEFACT_TYPE_ID.SCREEN}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + state?.selectedData?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
              <CommonScreenVersionDetails screenID={props.match.params.screenID} setSelectedRowVersion = {setSelectedRowVersion} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
    </Row>
  )
}

export default CommonScreenDetail
