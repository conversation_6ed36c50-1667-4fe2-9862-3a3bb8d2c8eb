import AppState from '@/store/types'
import {
    <PERSON>read<PERSON>rumb, Button, Card, Checkbox, Col, Divider, Row, Space, Spin, Table, Typography
} from 'antd'
import React, { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useHistory } from 'react-router-dom'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, COMP_TYPE, COMP_TYPE_LIST, COM_ARTEFACT_TYPE_ID, PROJECT_PREFIX, ROW_STATUS, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import DeleteButton from '../../../../helper/component/commonButton/DeleteButton'
import LavAttachmentPreview from '../../../../helper/component/lav-attachment-preview'
import LavButtons from '../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { extractProjectCode, renderCommonStatusBadge } from '../../../../helper/share'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { deleteRequest } from '../action'
import MockupScreenForm from '../form'
import { initComment, initCommentScreen } from './../../../_shared/comment/action'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    screenID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any
}
const RightControl = ({ data, screenID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const history = useHistory();
    const [compType, setCompType] = useState(COMP_TYPE_LIST)
    const [dataS, setDataSource] = useState<any>([])
    const [id, setId] = useState(0)

    useEffect(() => {
        setDataSource([])
        if (data?.screenComponents && data?.screenComponents.length > 0) {
            let x: any[] = data.screenComponents
            let currentSC = Object.assign([], data?.screenComponents);
            currentSC.sort((a: any, b: any) => { return a.order - b.order })
            setDataSource(transformDataSource(currentSC))
            const maxID = Math.max.apply(
                Math,
                x.map((o: any) => {
                    return o.id
                })
            )

            if (maxID) {
                setId(maxID + 1)
            }
        }
    }, [data?.screenComponents])
    const tableType = COMP_TYPE_LIST[20].value;
    const transformDataSource = (data) => {
        let newData: any = [];
        let newIndex = 1;
        data.forEach((e: any, idx) => {
            newData.push({
                ...e,
                rowId: idx,
                rowNumber: newIndex
            })
            if (e.componentType !== tableType) {
                newIndex++;
            }
        })
        return newData
    }
    const renderDescription = (text, record) => {
        if (
            record.status &&
            (record.status === ROW_STATUS.CREATE ||
                record.status === ROW_STATUS.UPDATE)
        ) {
            let property = ''
            let object = ''

            if (record.objectScreenComponent) {
                let currentObject: any = null;
                const listObjects = data.listObjects || []
                const existsIndex = listObjects.findIndex((item: any) => record.objectScreenComponent === item.id);
                if (existsIndex != -1 && existsIndex < listObjects.length - 1) {
                    currentObject = listObjects![existsIndex]
                }

                if (currentObject) {
                    object = currentObject.name
                    if (record.sourceObjectProperties) {
                        const listProperty = data.listObjectProperties || []
                        const existsIndex = listProperty.findIndex((item: any) => record.sourceObjectProperties === item.id)
                        if (existsIndex != -1 && existsIndex < listProperty.length - 1) {
                            const currentProperty = listProperty[existsIndex]
                            if (currentProperty) {
                                property = currentProperty.name
                            }
                        }
                    }
                }
            }
            return <div>
                <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
                {record.objectScreenComponent && (
                    <div>
                        <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object' })}: ${object}`}</Text>
                    </div>
                )}
                {record.sourceObjectProperties && (
                    <div>
                        <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object-property' })}: ${property}`}</Text>
                    </div>
                )}
            </div>
        } else {
            return <>
                <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
                {record.objectScreenComponent && (
                    <div>
                        <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object' })}: ${record.objectScreenComponent.name}`}</Text>
                    </div>
                )}
                {record.sourceObjectProperties &&
                    record.sourceObjectProperties.length > 0 && (
                        <div>
                            <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object-property' })}: ${record.sourceObjectProperties[0].name}`}</Text>
                        </div>
                    )}
            </>
        }
    }

    const columns = [
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.order' })}
                </Text>
            ),
            dataIndex: 'order',
            width: '5%',
            render: (text: number, item, index) => {
                return {
                    children: item.componentType === tableType ? <></> : (item.rowNumber),
                    props: {
                        colSpan: 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.component' })}
                </Text>
            ),
            dataIndex: 'name',
            key: 'name',
            width: '10%',
            render: (text, item) => {
                return {
                    children: item.componentType === tableType ? <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(item?.id)}>
                        <div className="tableDangerous" dangerouslySetInnerHTML={{
                            __html: `<div>${item.name}</div>${item?.description}`
                        }}></div>
                    </TriggerComment> : <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(item?.id)}>
                        <Text>{text}</Text>
                    </TriggerComment>,
                    props: {
                        colSpan: item.componentType === tableType ? 6 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.component-type',
                    })}
                </Text>
            ),
            dataIndex: 'componentType',
            key: 'componentType',
            width: '10%',
            render: (text, item) => {
                return {
                    children: <Text>{text}</Text>,
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.editable' })}
                </Text>
            ),
            dataIndex: 'editable',
            key: 'editable',
            width: '10%',
            render: (editable: string, item) => {
                return {
                    children: editable ? (
                        <Checkbox checked={true} disabled />
                    ) : (
                        <Checkbox checked={false} disabled />
                    ),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.mandatory' })}
                </Text>
            ),
            dataIndex: 'mandatory',
            key: 'mandatory',
            width: '10%',
            render: (mandatory: string, item: any) => {
                return {
                    children: mandatory ? (
                        <Checkbox checked={true} disabled />
                    ) : (
                        <Checkbox checked={false} disabled />
                    ),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.default-value',
                    })}
                </Text>
            ),
            dataIndex: 'defaultValue',
            key: 'defaultValue',
            width: '10%',
            render: (value, item) => {
                return {
                    children: value ? value : '',
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.description',
                    })}
                </Text>
            ),
            dataIndex: 'description',
            key: 'description',
            width: '35%',
            render: (text, item) => {
                return {
                    children: renderDescription(text, item),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
    ]
    const changeMenu = (id: number) => {
        if (screenID.toString() !== id.toString()) {
            const href = `${APP_ROUTES.COMMON_SCREEN_DETAIL}` + id
            history.push(href)
        }
    }


    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'description', title: intl.formatMessage({ id: 'common-screen-details.screen-info.description', }), },
            { field: 'access', title: intl.formatMessage({ id: 'common-screen-details.screen-info.access' }), },
            { field: 'mockup-screen', title: intl.formatMessage({ id: 'common-screen-details.screen-info.mockup-screen' }), },
            { field: 'screen-description', title: intl.formatMessage({ id: 'common-screen-details.screen-info.screen-description' }), },
            { field: 'object', title: intl.formatMessage({ id: 'common_screen.label.object' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'common_screen.label.use-case' }), },
            { field: 'source-screen', title: intl.formatMessage({ id: 'common_screen.label.source-screen' }), },
            { field: 'target-screen', title: intl.formatMessage({ id: 'common_screen.label.target-screen' }), },
            { field: 'state-transition', title: intl.formatMessage({ id: 'common_screen.label.state-transition' }), },

        ];

        data?.screenComponents?.forEach((e) => {
            fields.push({ field: e.id ? e.id.toString() : '', title: e?.component })
        })
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.COMMON_SCREEN,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])
    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return data?.status != STATUS_COMMON.DELETED ? (<DeleteButton
            type={BUTTON_TYPE.TEXT}
            content={intl.formatMessage(
                { id: 'CFD_7' },
                { artefact_type: intl.formatMessage({ id: 'common.artefact.common-screen' }) }
            )}
            okCB={() => { dispatch(deleteRequest(screenID)) }}
            confirmButton={intl.formatMessage({ id: 'common.action.delete' })}></DeleteButton>) : <></>
    }
    return (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                        <Breadcrumb.Item>
                            <Link to={APP_ROUTES.COMMON_SCREEN}>{intl.formatMessage({ id: 'commonscreen.page_title' })}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code} - {data?.name}
                    </Title>
                </div>

                <Space size="small">
                    <LavButtons
                        isCommon={true}
                        url={`${API_URLS.COMMON_SCREEN}/${screenID}`}
                        artefact_type="common.artefact.common-screen"
                        status={data?.status}
                        isHasReject={true}
                        artefactType={COM_ARTEFACT_TYPE_ID.SCREEN}
                        id={screenID}
                        isHasRemove={true}
                        isHasApprove={true}
                        deleteButton={DeleteComponent}
                        changePage={() => onChange()}>
                        {/* Update record */}
                        {
                            <Button
                                type='primary'
                                className='lav-btn-create'
                                onClick={() => {
                                    setScreenMode(SCREEN_MODE.EDIT)
                                }} >{intl.formatMessage({ id: 'common.action.update' })}</Button>
                        }
                    </LavButtons>
                </Space>
            </Row >
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Space size="large">
                            <span>          
                                <TriggerComment field='version'>                               
                                    <a onClick={() => {
                                        setScreenMode(SCREEN_MODE.HISTORY)
                                    }}>
                                        {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                    </a>
                                </TriggerComment>
                            </span>
                            {renderCommonStatusBadge(data?.status)}
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'common-screen-details.legend.screen-info',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={4}>
                                    <TriggerComment field='description'>
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'common-screen-details.screen-info.description',
                                            })}
                                        </Text>
                                    </TriggerComment>

                                </Col>
                                <Col span={20}>
                                    <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: data?.description }}></div>
                                </Col>

                                <Col span={4}>
                                    <TriggerComment field='access'>
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'common-screen-details.screen-info.access',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{
                                            __html: data?.access,
                                        }}
                                    ></div>
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field='mockup-screen'>
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'common-screen-details.screen-info.mockup-screen',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>

                                <Col span={24}>
                                    <LavAttachmentPreview attachment={data?.mockUpScreen} isCommon />
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field='screen-description'>
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'common-screen-details.screen-info.screen-description',
                                            })}
                                        </Text>
                                    </TriggerComment>

                                </Col>
                                <Col span={24} style={{ width: '1px', overflowX: 'scroll' }}>
                                    <Table
                                        locale={{ emptyText: 'NO DATA' }}
                                        bordered
                                        dataSource={dataS}
                                        columns={columns}
                                        rowKey="id"
                                        pagination={false}
                                    />
                                </Col>
                            </Row>
                        </Card>

                        <LavReferences data={data} isCommon />

                        <LavCommonAuditTrail data={data?.auditTrails} />
                    </Space>
                </Scrollbars>
            </Spin>
        </Space >
    )
}

export default RightControl
