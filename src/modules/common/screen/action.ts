import { createAction } from '@reduxjs/toolkit';
import { ActionEnum } from './type';

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<any>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<any>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<any>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<any>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const createRequest = createAction<any>(ActionEnum.CREATE_REQUEST);
export const createSuccess = createAction<any>(ActionEnum.CREATE_SUCCESS);
export const createFailed = createAction<any>(ActionEnum.CREATE_FAILED);

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST);
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS);
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED);

export const deleteRequest = createAction<any>(ActionEnum.DELETE_REQUEST);
export const deleteSuccess = createAction<any>(ActionEnum.DELETE_SUCCESS);
export const deleteFailed = createAction<any>(ActionEnum.DELETE_FAILED);

export const getListObjectsRequest = createAction<any>(ActionEnum.GET_LIST_OBJECTS_REQUEST);
export const getListObjectsSuccess = createAction<any>(ActionEnum.GET_LIST_OBJECTS_SUCCESS);
export const getListObjectsFailed = createAction<any>(ActionEnum.GET_LIST_OBJECTS_FAILED);

export const getListFunctionsRequest = createAction<any>(ActionEnum.GET_LIST_FUNCTIONS_REQUEST);
export const getListFunctionsSuccess = createAction<any>(ActionEnum.GET_LIST_FUNCTIONS_SUCCESS);
export const getListFunctionsFailed = createAction<any>(ActionEnum.GET_LIST_FUNCTIONS_FAILED);

export const getListObjectPropertiesRequest = createAction<any>(ActionEnum.GET_LIST_OBJECT_PROPERTIES_REQUEST);
export const getListObjectPropertiesSuccess = createAction<any>(ActionEnum.GET_LIST_OBJECT_PROPERTIES_SUCCESS);
export const getListObjectPropertiesFailed = createAction<any>(ActionEnum.GET_LIST_OBJECT_PROPERTIES_FAILED);

export const getListSelectProperties = createAction<any>(ActionEnum.GET_LIST_SELECT_PROPERTIES)
export const getListSelectPropertiesSuccess = createAction<any>(ActionEnum.GET_LIST_SELECT_PROPERTIES_SUCCESS)
export const getListSelectPropertiesFailure = createAction<any>(ActionEnum.GET_LIST_SELECT_PROPERTIES_FAILED)

export const setModalVisible = createAction<any>(ActionEnum.SET_MODAL_VISIBLE)
