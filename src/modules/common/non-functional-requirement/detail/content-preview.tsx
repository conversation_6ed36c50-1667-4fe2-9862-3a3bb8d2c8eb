import { <PERSON>, <PERSON>, Row, <PERSON>, Typography } from "antd"
import intl from "../../../../config/locale.config"
import { STATUS_COMMON } from "../../../../constants"
import { renderCommonStatusBadge } from "../../../../helper/share"
import { listCategoryDetail, listSubCategoryDetail, listTypeDetail, listVariaDetail } from "../type"

const { Title, Text } = Typography

const CommonNonFunctonalDetailInfo = ({ data }) => {
    const category = listCategoryDetail.find(
        (category: any) => category.id === data?.category
    )
    const subcategory = listSubCategoryDetail.find(
        (subcategory: any) => subcategory.id === data?.subCategory
    )
    const type = listTypeDetail.find(
        (type: any) => type.id === data?.type
    )
    const varias = listVariaDetail.filter((varia: any) =>
        data?.variables?.includes(varia.id)
    )
    return <Space direction="vertical">
        <Space size="large">
            {/* <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''} */}
            {data?.status !== STATUS_COMMON.RECOMMEND ? renderCommonStatusBadge(data?.status) : <></>}
        </Space>

        <Card
            title={
                <Title level={5}>
                    {`${intl.formatMessage({
                        id: 'common.nfr.info',
                    })}`}
                </Title>
            }
            bordered={true}
        >
            <Row gutter={[16, 4]}>
                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'common.nfr.column.category',
                        })}:
                    </Text>
                </Col>
                <Col span={20}>
                    {category?.name}
                </Col>
                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'common.nfr.column.sub-category',
                        })}:
                    </Text>
                </Col>
                <Col span={20}>
                    {subcategory?.subName}
                </Col>

                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'common.nfr.column.type',
                        })}:
                    </Text>
                </Col>
                <Col span={20}>
                    {type?.typeName}
                </Col>
                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'common.nfr.column.varia',
                        })}:
                    </Text>
                </Col>
                <Col span={20}>
                    {/* <h3>{state.nonFunctionalInfo.Variable}</h3> */}
                    {varias?.map((item: any, index) => (
                        <p key={item.id}>
                            {item.variaName}
                        </p>
                    ))}
                </Col>
                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'nfr.column.remark',
                        })}:
                    </Text>
                </Col>
                <Col span={24}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{
                            __html: data?.description,
                        }}
                    ></div>
                </Col>
            </Row>
        </Card>
    </Space>
}
export default CommonNonFunctonalDetailInfo