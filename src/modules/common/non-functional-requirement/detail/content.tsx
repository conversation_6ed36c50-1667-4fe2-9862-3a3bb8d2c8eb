import AppState from '@/store/types'
import {
    Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import DeleteButton from '../../../../helper/component/commonButton/DeleteButton'
import LavButtons from '../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../helper/component/lav-common-audit-trail/index'
import LavReferences from '../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { renderCommonStatusBadge } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { deleteRequest } from '../action'
import CommonNonFunctionalRequirementForm from '../form'
import { listCategoryDetail, listSubCategoryDetail, listTypeDetail, listVariaDetail } from '../type'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    nonFunctionalID: number,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any
}
const RightControl = ({ data, nonFunctionalID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const category = listCategoryDetail.find(
        (category: any) => category.id === data?.category
    )
    const subcategory = listSubCategoryDetail.find(
        (subcategory: any) => subcategory.id === data?.subCategory
    )
    const type = listTypeDetail.find(
        (type: any) => type.id === data?.type
    )
    const varias = listVariaDetail.filter((varia: any) =>
        data?.variables?.includes(varia.id)
    )

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.nfr.version' }), },
            { field: 'category', title: intl.formatMessage({ id: 'common.nfr.column.category' }), },
            { field: 'sub-category', title: intl.formatMessage({ id: 'common.nfr.column.sub-category' }), },
            { field: 'type', title: intl.formatMessage({ id: 'common.nfr.column.type' }), },
            { field: 'varia', title: intl.formatMessage({ id: 'common.nfr.column.varia' }), },
            { field: 'remark', title: intl.formatMessage({ id: 'common.nfr.column.remark' }), },
        ];
        dispatch(initComment({ projectId: null, itemId: data.id, fields }));

        const payload = {
            projectId: null,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.COMMON_NON_FUNCTION_REQUIREMENT,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return data?.status !== STATUS_COMMON.DELETED ? (
            <DeleteButton
                type={BUTTON_TYPE.TEXT}
                content={intl.formatMessage(
                    { id: 'CFD_7' },
                    { artefact_type: intl.formatMessage({ id: 'common.artefact.non-functional' }) }
                )}
                okCB={() => dispatch(deleteRequest(nonFunctionalID))}
                confirmButton={intl.formatMessage({ id: 'common.action.delete' })}
            ></DeleteButton>) : <></>
    }
    return (
        <>
            <Space
                direction="vertical"
                size="middle"
                className="record-detail-right-control-container p-1rem"
            >
                <Row align="middle" justify="space-between">
                    <div>
                        <Breadcrumb className='rq-breadcrumb' separator=">">
                            <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                            <Breadcrumb.Item>
                                <Link to={APP_ROUTES.COMMON_NONFUNCTIONAL}>{intl.formatMessage({ id: 'common.nfr.detail' })}</Link>
                            </Breadcrumb.Item>
                        </Breadcrumb>
                        <Title level={3} className='rq-page-title'>
                            {`${data?.code} - ${category?.name}`}
                        </Title>
                    </div>
                    <Space size="small">
                        <LavButtons
                            isCommon={true}
                            url={`${API_URLS.COMMON_NONFUNCTIONAL_REQUIREMENT}/${nonFunctionalID}`}
                            artefact_type="common.artefact.non-functional"
                            status={data?.status}
                            isHasRemove={true}
                            isHasReject={true}
                            isHasApprove={true}
                            deleteButton={DeleteComponent}
                            artefactType={COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT}
                            id={nonFunctionalID}
                            changePage={() => onChange()}>

                            {/* Update record */}
                            {data?.status !== STATUS_COMMON.DELETED && (
                                <Button
                                    type='primary'
                                    className='lav-btn-create'
                                    onClick={() => {
                                        setScreenMode(SCREEN_MODE.EDIT)
                                    }} >{intl.formatMessage({ id: 'common.action.update' })}</Button>
                            )}
                        </LavButtons>
                    </Space>
                </Row>

                <Divider className="mt-0 mb-0" />

                <Spin spinning={isLoading}>
                    <Scrollbars
                        autoHide
                    >
                        <Space direction="vertical">
                            <Space size="large">
                                <span>          
                                    <TriggerComment field='version'>                               
                                        <a onClick={() => {
                                            setScreenMode(SCREEN_MODE.HISTORY)
                                        }}>
                                            {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                        </a>
                                    </TriggerComment>
                                </span>
                                {renderCommonStatusBadge(data?.status)}
                            </Space>

                            <Card
                                title={
                                    <Title level={5}>
                                        {`${intl.formatMessage({
                                            id: 'common.nfr.info',
                                        })}`}
                                    </Title>
                                }
                                bordered={true}
                            >
                                <Row gutter={[16, 4]}>
                                    <Col span={4}>
                                        <TriggerComment field="category">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'common.nfr.column.category',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20}>
                                        {category?.name}
                                    </Col>
                                    <Col span={4}>
                                        <TriggerComment field="sub-category">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'common.nfr.column.sub-category',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20}>
                                        {subcategory?.subName}
                                    </Col>

                                    <Col span={4}>
                                        <TriggerComment field="type">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'common.nfr.column.type',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20}>
                                        {type?.typeName}
                                    </Col>
                                    <Col span={4}>
                                        <TriggerComment field="varia">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'common.nfr.column.varia',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20}>
                                        {/* <h3>{state.nonFunctionalInfo.Variable}</h3> */}
                                        {varias?.map((item: any, index) => (
                                            <p key={item.id}>
                                                {item.variaName}
                                            </p>
                                        ))}
                                    </Col>
                                    <Col span={24}>
                                        <TriggerComment field="remark">
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'nfr.column.remark',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={24}>
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.description,
                                            }}
                                        ></div>
                                    </Col>
                                </Row>
                            </Card>
                            <LavReferences data={data} isCommon />
                            <Col span={24}>
                                <LavCommonAuditTrail data={data?.auditTrails} />
                            </Col>
                        </Space>
                    </Scrollbars>
                </Spin>
            </Space>
        </>
    )
}

export default RightControl
