import CustomSvgIcons from '../../../helper/component/custom-icons'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS_COMMON, STATUS_COMMON_FILTER } from '../../../constants'
import LavTable from '../../../helper/component/lav-table'
import { getColumnDropdownFilterProps, getColumnSearchProps, renderCommonStatusBadge } from '../../../helper/share'
import CommonNonFunctionalRequirementForm from './form'
import {
  listCategoryDetail,
  listTypeDetail,
  listVariaDetail,
  NFRCategoryEnum,
  NFRCategoryValueEnum,
  NFRTypeEnum,
  NFRTypeValueEnum
} from './type'
import CommonNonFunctionalRequirementFormModalPage from './form/form'
const { Text } = Typography

const CommonNonFunctionalRequirement = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  const columns = [
    {
      title: intl.formatMessage({ id: 'common.action.code' }),
      dataIndex: 'code',
      width: '5%',
      editable: true,
      sorter: true,
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${APP_ROUTES.COMMON_NONFUNCTIONAL_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'common.nfr.column.category' }),
      dataIndex: 'category',
      width: '200px',
      sorter: {
        compare: (a, b) => a.category - b.category,
        multiple: 1,
      },
      sortOrder: 'descend',
      ...getColumnDropdownFilterProps(
        listCategoryDetail.map((item) => {
          return { text: item.name, value: item.id }
        })
      ),
      render: (record) => {
        let content

        switch (record) {
          case NFRCategoryEnum.PERFORMANCEREQUIREMENT:
            content = (
              <Text>
                {
                  NFRCategoryValueEnum[NFRCategoryEnum.PERFORMANCEREQUIREMENT]
                    .text
                }
              </Text>
            )

            break
          case NFRCategoryEnum.SAFETYREQUIREMENTS:
            content = (
              <Text>
                {NFRCategoryValueEnum[NFRCategoryEnum.SAFETYREQUIREMENTS].text}
              </Text>
            )

            break
          case NFRCategoryEnum.SECURITYREQUIREMENTS:
            content = (
              <Text>
                {
                  NFRCategoryValueEnum[NFRCategoryEnum.SECURITYREQUIREMENTS]
                    .text
                }
              </Text>
            )

            break
          case NFRCategoryEnum.SOFTWAREQUALITYATTRIBUTES:
            content = (
              <Text>
                {
                  NFRCategoryValueEnum[
                    NFRCategoryEnum.SOFTWAREQUALITYATTRIBUTES
                  ].text
                }
              </Text>
            )

            break
        }
        return content
        // return <Badge text={content} key={record.status}></Badge>
      },
    },
    {
      title: intl.formatMessage({ id: 'common.nfr.column.type' }),
      dataIndex: 'type',
      width: '170px',
      sorter: {
        compare: (a, b) => a.type - b.type,
        multiple: 2,
      },
      sortOrder: 'descend',
      ...getColumnDropdownFilterProps(
        listTypeDetail.map((item) => {
          return { text: item.typeName, value: item.id }
        })
      ),
      render: (record) => {
        let content
        switch (record) {
          case NFRTypeEnum.RESPONSETIME:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.RESPONSETIME].text}</Text>
            )

            break
          case NFRTypeEnum.WORKLOAD:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.WORKLOAD].text}</Text>
            break
          case NFRTypeEnum.SCALABILITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.SCALABILITY].text}</Text>
            )

            break
          case NFRTypeEnum.PLATFORM:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.PLATFORM].text}</Text>
            break
          case NFRTypeEnum.AUTHENTICITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.AUTHENTICITY].text}</Text>
            )

            break
          case NFRTypeEnum.PRIVACY:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.PRIVACY].text}</Text>
            break
          case NFRTypeEnum.AUTHENTICATION:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.AUTHENTICATION].text}</Text>
            )

            break
          case NFRTypeEnum.ACCESSIBILITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.ACCESSIBILITY].text}</Text>
            )

            break
          case NFRTypeEnum.INTERNATIONALISATION:
            content = (
              <Text>
                {NFRTypeValueEnum[NFRTypeEnum.INTERNATIONALISATION].text}
              </Text>
            )

            break
          case NFRTypeEnum.EASEOFUSE:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.EASEOFUSE].text}</Text>
            )

            break
          case NFRTypeEnum.AVAILABILITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.AVAILABILITY].text}</Text>
            )

            break
          case NFRTypeEnum.BACKUP:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.BACKUP].text}</Text>
            break
          case NFRTypeEnum.ACCURACY:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.ACCURACY].text}</Text>
            break
          case NFRTypeEnum.COMPLETENESS:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.COMPLETENESS].text}</Text>
            )

            break
          case NFRTypeEnum.REGULATORYCOMPLIANCE:
            content = (
              <Text>
                {NFRTypeValueEnum[NFRTypeEnum.REGULATORYCOMPLIANCE].text}
              </Text>
            )

            break
          case NFRTypeEnum.PRICE:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.PRICE].text}</Text>
            break
          case NFRTypeEnum.TIMELINE:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.TIMELINE].text}</Text>
            break
        }
        return content
      },
    },
    {
      title: intl.formatMessage({ id: 'common.nfr.column.varia' }),
      dataIndex: 'variables',
      width: '350px',
      render: (record) => {
        let data = ''
        const varias = listVariaDetail.filter((varia: any) => record?.includes(varia.id))
        varias.forEach((e, index) => {
          if (index == 0) {
            data += e.variaName
          } else {
            data += ', ' + e.variaName
          }
        })
        return <>
          {varias.map((e) => <>- {e.variaName}<br /></>)}
        </>
      }
    },
    {
      title: intl.formatMessage({ id: 'common.nfr.column.remark' }),
      dataIndex: 'description',
      render: (text) => (
        <div
          className="tableDangerous"
          dangerouslySetInnerHTML={{ __html: text }}
        ></div>
      ),
    },
    {
      title: intl.formatMessage({ id: 'common.nfr.column.status' }),
      width: '5%',
      dataIndex: 'status',
      ...getColumnDropdownFilterProps(STATUS_COMMON_FILTER, 'status'),
      // defaultFilteredValue: [0, 1, 2, 3],
      sorter: true,
      render: (record) => renderCommonStatusBadge(record),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common.nfr.createnfr' })}
      </Button>
    )

  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (record.status !== STATUS_COMMON.DELETED) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (
      record.status !== STATUS_COMMON.DELETED ?
        children : <></>
    )
  }
  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        screenMode === SCREEN_MODE.VIEW ?
          <LavTable
            title="common.nfr.list"
            artefact_type="common.artefact.non-functional"
            apiUrl={API_URLS.COMMON_NONFUNCTIONAL_REQUIREMENT}
            columns={columns}
            artefactType={COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT}
            showBreadcumb={false}
            isCommon={true}
            deleteComponent={DeleteComponent}
            updateComponent={UpdateComponent}
            createComponent={CreateComponent}
          /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <CommonNonFunctionalRequirementFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenMode={SCREEN_MODE.CREATE} buttonType={BUTTON_TYPE.TEXT} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <CommonNonFunctionalRequirementFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} nonFunctionalID={id} screenMode={SCREEN_MODE.EDIT}/> : <></>
      }
    </Space>
  )
}

export default CommonNonFunctionalRequirement
