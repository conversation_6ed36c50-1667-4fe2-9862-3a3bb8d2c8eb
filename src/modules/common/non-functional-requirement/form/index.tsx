import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import React, { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { APP_COMMON_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, SCREEN_MODE, STATUS_COMMON } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import { getReferencesFromEditor, hasCommonRole, renderCommonStatusBadge } from '../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { CommonNonFunctionalRequirementState, nfrCategory } from '../type'

const { Text, Title } = Typography
const { confirm } = Modal
const { Option } = Select

interface CommonNonFunctionalRequirementFormProps {
  nonFunctionalID?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface CommonNonFunctionalRequirementFormModalProps {
  nonFunctionalID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const CommonNonFunctionalRequirementFormModal = ({ nonFunctionalID, screenMode, onFinish, onDismiss }: CommonNonFunctionalRequirementFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorData: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.CommonNonFunctionalRequirement) as CommonNonFunctionalRequirementState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [description, setDescription] = useState('');
  const [subCategoryList, setSubCategoryList] = useState<any>([])
  const [typeList, setTypeList] = useState<any[]>([])
  const [variaList, setVariaList] = useState<any[]>([])
  const [filled, setFilled] = useState<any>(false)
  const [categorySelected, setCategorySelected] = useState<number>(0)

  // Destroy
  useEffect(() => {
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])


  useEffect(() => {
    if (nonFunctionalID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(nonFunctionalID))
    }
  }, [screenMode, nonFunctionalID])

  useEffect(() => {
    setCategorySelected(state.detail?.category)
    if (nonFunctionalID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      let listSub: any = []
      let listType: any = []
      let listVariable: any = []
      if (
        state.detail?.category ||
        state.detail?.category === 0
      ) {
        nfrCategory?.map((item) => {
          if (item.id === state.detail?.category) {
            listSub = item?.listNFRSub
            if (
              item?.listNFRSub?.length > 0 &&
              (state.detail?.subCategory ||
                state.detail?.subCategory === 0)
            ) {
              item?.listNFRSub?.map((sub) => {
                if (sub.id === state.detail?.subCategory) {
                  listType = sub?.listNFRtype
                  if (
                    state.detail?.type ||
                    state.detail?.type === 0
                  ) {
                    sub?.listNFRtype?.map((type) => {
                      if (type.id === state.detail?.type) {
                        listVariable = type.listVarias
                      }
                    })
                  }
                }
              })
            } else {
              listType = item?.listNFRtype
              if (
                state.detail?.type ||
                state.detail?.type === 0
              ) {
                listType?.map((type) => {
                  if (type.id === state.detail?.type) {
                    listVariable = type.listVarias
                  }
                })
              }
            }
          }
        })
      } else {
      }
      setSubCategoryList(listSub)
      setTypeList(listType)
      setVariaList(listVariable)

      form.setFieldsValue({
        code: state.detail.code,
        category: state.detail.category,
        subcategory: state.detail.subCategory,
        type: state.detail.type,
        variables: state.detail?.variables,
      })
      setDescription(state.detail.description);
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish();
      }
      if (isCreateMore) {
        resetForm();
      } else {
        onDismiss();
      }
      setIsDraft(null);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])


  const changeCategory = (e) => {
    form.setFieldsValue({
      subcategory: '',
      type: '',
      variables: undefined,
    })
    setFilled(true)
    const nfrCategoryIndex = nfrCategory.findIndex((item: any) => e === item.id)
    if (nfrCategoryIndex !== -1) {
      setCategorySelected(nfrCategoryIndex)
      if (nfrCategory[nfrCategoryIndex]?.listNFRSub.length === 0) {
        const data: any = nfrCategory[nfrCategoryIndex]?.listNFRtype
        setTypeList(data)
        setSubCategoryList([])
      } else {
        setTypeList([])
        const dataSubCategory: any = nfrCategory[nfrCategoryIndex]?.listNFRSub
        setSubCategoryList(dataSubCategory)
      }
    }
  }

  const changeSubCategory = (e) => {
    form.setFieldsValue({
      type: undefined,
      variables: undefined,
    })
    const nfrSubCategoryIndex: any = nfrCategory[categorySelected]?.listNFRSub?.find((item: any) => e === item.id)
    setTypeList(nfrSubCategoryIndex?.listNFRtype)
  }

  const changeType = (e) => {
    form.setFieldsValue({
      variables: undefined,
    })
    let nfrTypeIndex: any = -1
    const values = form.getFieldsValue()
    const subCategoryID = values.subcategory

    if (subCategoryID !== null && subCategoryID > -1 && subCategoryID !== "") {
      const subCategory = nfrCategory[categorySelected]?.listNFRSub?.find((item: any) => subCategoryID === item.id)

      if (subCategory) {
        nfrTypeIndex = subCategory?.listNFRtype?.find(
          (item: any) => e === item.id
        )
      }
    } else {
      nfrTypeIndex = nfrCategory[categorySelected]?.listNFRtype?.find(
        (item: any) => e === item.id
      )
    }
    setVariaList(nfrTypeIndex?.listVarias)
  }

  const onSubmit = debounce(async (values: any, st?: string) => {
    const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data, true)
    const requestData: any = {
      id: nonFunctionalID || null,
      version: values.version,
      description: getCkeditorData.current?.props?.data,
      category: values.category,
      status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) :
        hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED
      ,
      subCategory: values.subcategory,
      type: values.type,
      variable: values.variables?.join(', '),
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.non-functional' }) }
        ),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss()
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(null);
    setDescription('')
    form.resetFields([
      'version',
      'code',
      'category',
      'subcategory',
      'type',
      'variables',
      'remarks',
    ])
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.nfr.version' }), },
      { field: 'category', title: intl.formatMessage({ id: 'common.nfr.column.category' }), },
      { field: 'sub-category', title: intl.formatMessage({ id: 'common.nfr.column.sub-category' }), },
      { field: 'type', title: intl.formatMessage({ id: 'common.nfr.column.type' }), },
      { field: 'varia', title: intl.formatMessage({ id: 'common.nfr.column.varia' }), },
      { field: 'remark', title: intl.formatMessage({ id: 'common.nfr.column.remark' }), },
    ];
    dispatch(initComment({ projectId: null, itemId: state.detail.id, fields }));

    const payload = {
      projectId: null,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_NON_FUNCTION_REQUIREMENT,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    size="medium"
    visible={true}
    footer={null}
  >
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={12}>
            <Space size="middle">
              <Title level={4}>
                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.nfr.create' : 'common.nfr.update' })}
              </Title>
              {screenMode === SCREEN_MODE.EDIT ? renderCommonStatusBadge(state.detail?.status) : <></>}
            </Space>
          </Col>

          <Col span={12}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={confirmCancel}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>
                {screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
                  <Form.Item style={{ marginBottom: '0px' }}>
                    <Button htmlType="submit" onClick={() => setIsDraft(false)}>
                      {intl.formatMessage({ id: 'common.action.submit' })}
                    </Button>
                  </Form.Item> : <></>
                }
                {
                  screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
                    <Form.Item style={{ marginBottom: '0px' }}>
                      <Button
                        onClick={() => setIsDraft(true)}
                        className="success-btn"
                        htmlType="submit"
                      >
                        {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.update' })}
                      </Button>
                    </Form.Item> : <></>
                }
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 280}>
        <Row align="middle">
          <Col span={2}>
            <TriggerComment screenMode={screenMode} field="version">
              <Text>
                {intl.formatMessage({
                  id: 'common.nfr.version',
                })}
              </Text>
            </TriggerComment>
          </Col>
          <Col span={2}>
            <Form.Item
              className="mb-0"
              name="version"
              rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
            >
              <Input maxLength={255} />
            </Form.Item>
          </Col>
        </Row>

        <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
          <Card className='rq-form-block' title={intl.formatMessage({ id: 'common.nfr.req' })}>
            <Row>
              <Col span={6}>
                {
                  screenMode === SCREEN_MODE.EDIT ? <FormGroup label={intl.formatMessage({ id: 'nfr.form.nfr-code' })}>
                    <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                      <Input maxLength={255} disabled />
                    </Form.Item>
                  </FormGroup> : <></>
                }
              </Col>
            </Row>

            <FormGroup required className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="category">
                {intl.formatMessage({ id: 'common.nfr.column.category' })}
              </TriggerComment>}>
              <Form.Item name="category"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                ]}>
                <Select onChange={changeCategory} >
                  {nfrCategory?.map((item: any) => (
                    <Option
                      key={item.id}
                      value={item.id}
                    // onChange={() => setIdCategory(item.id)}
                    >
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="sub-category">
                {intl.formatMessage({ id: 'common.nfr.column.sub-category' })}
              </TriggerComment>}>
              <Form.Item name="subcategory">
                <Select onChange={changeSubCategory}>
                  {subCategoryList?.map((item: any) => (
                    <Option key={item.id} value={item.id}>
                      {item.subName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </FormGroup>
            {filled && subCategoryList.length === 0 && <Text type="warning">{intl.formatMessage({ id: 'common.non.functional.message-warning' })}</Text>}

            <FormGroup className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="type">
                {intl.formatMessage({ id: 'common.nfr.column.type' })}
              </TriggerComment>}>
              <Form.Item name="type">
                <Select onChange={changeType}>
                  {typeList?.map((item: any) => (
                    <Option key={item.id} value={item.id}>
                      {item.typeName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="varia">
                {intl.formatMessage({ id: 'nfr.form.variables-criteria' })}
              </TriggerComment>}>
              <Form.Item name="variables">
                <Select mode="multiple">
                  {variaList?.map((item: any) => (
                    <Option key={item.id} value={item.id}>
                      {item.variaName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="remark">
                {intl.formatMessage({ id: 'nfr.form.remarks' })}
              </TriggerComment>}>
              <Form.Item
                name="remarks"
                labelAlign="left"
                wrapperCol={{ span: 24 }}
              >
                <CkeditorMention isCommon ref={getCkeditorData} data={description || ''} />
              </Form.Item>
            </FormGroup>
          </Card>
        </Space>
      </Scrollbars>
    </Form>
  </CustomModal>
}
const CommonNonFunctionalRequirementForm = ({ nonFunctionalID, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType }: CommonNonFunctionalRequirementFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return <>
    {
      buttonType === BUTTON_TYPE.TEXT ?
        <Button
          ghost={screenMode === SCREEN_MODE.CREATE}
          type='primary'
          className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
          onClick={() => setIsModalVisible(true)}
          icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
        >
          {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.nfr.createnfr' : 'common.action.update' })}
        </Button> :
        buttonType === BUTTON_TYPE.ICON ?
          <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
          <></>
    }
    {isModalVisible === true ? <CommonNonFunctionalRequirementFormModal nonFunctionalID={nonFunctionalID} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}

export default CommonNonFunctionalRequirementForm
