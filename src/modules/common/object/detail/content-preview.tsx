import { CheckOutlined } from "@ant-design/icons"
import { Card, Col, Row, Space, Table, Typography } from "antd"
import { useEffect, useRef, useState } from "react"
import intl from "../../../../config/locale.config"
import { STATUS_COMMON } from "../../../../constants"
import LavReferences from "../../../../helper/component/lav-references"
import { renderCommonStatusBadge, ShowWarningMessge } from "../../../../helper/share"

const { Title, Text } = Typography

interface CommonObjDetailInfoProps {
    data: any,
    onChange?: any,
    selectable?: boolean
}
const CommonObjectDetailInfo = ({ data, onChange, selectable = true }: CommonObjDetailInfoProps) => {
    const tableUpdateRef = useRef<any>()
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>(null)

    useEffect(() => {
        setSelectedRowKeys(null)
        return () => {
            setSelectedRowKeys(null)
        }
    }, [])

    useEffect(() => {
        if (data) {
            setSelectedRowKeys(data?.objectPropertyIds || [])
        }
    }, [data])

    const columns = [
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.object-property' }),
            dataIndex: 'name',
            width: '13%',
            sorter: (a, b) => a.name - b.name,
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.unique' }),
            dataIndex: 'unique',
            width: '10%',
            editable: true,
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.mandatory' }),
            dataIndex: 'mandatory',
            width: '5%',
            editable: true,
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.max-length' }),
            dataIndex: 'maxLength',
            width: '10%',
            editable: true,
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.meaning' }),
            dataIndex: 'description',
            width: '27%',
            editable: false,
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object' }),
            dataIndex: 'sourceObject',
            width: '15%',
            render: (text, item) => item.sourceObject?.name
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object-property' }),
            dataIndex: 'sourceObjectProperty',
            width: '20%',
            render: (text, item) => item.sourceObjectProperty?.name
        },
    ]

    return <Space direction="vertical" size="middle">
        <Space size="large">
            {/* <span>
                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
            </span> */}
            {data?.status !== STATUS_COMMON.RECOMMEND ? renderCommonStatusBadge(data?.status) : <></>}
        </Space>

        <Card title={<Title level={5}>{intl.formatMessage({ id: 'createobject.card-title.object-infomation' })}</Title>} bordered={true}>
            <Row gutter={[16, 4]}>
                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({ id: 'createobject.label.description' })}
                    </Text>
                </Col>
                <Col className="description" span={24}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: data?.description }}
                    ></div>
                </Col>
                <Col span={24} style={{ width: '100%', overflowX: 'scroll' }}>
                    {
                        selectable ? <>
                            {selectedRowKeys ? <Table
                                rowSelection={{
                                    selectedRowKeys: selectedRowKeys,
                                    onChange: (selectedRowKeys: any, selectedRows: any) => {
                                        if (selectedRowKeys?.length <= 0) {
                                            tableUpdateRef.current.scrollIntoView()
                                            ShowWarningMessge('common.message.require-select')
                                        } else {
                                            setSelectedRowKeys(selectedRowKeys);
                                            onChange(selectedRowKeys);
                                        }
                                    }
                                }}
                                bordered
                                dataSource={data.objectProperties}
                                columns={columns}
                                rowKey="id"
                                pagination={false}
                            /> : <></>}
                        </> : <Table
                            bordered
                            dataSource={data.objectProperties}
                            columns={columns}
                            rowKey="id"
                            pagination={false}
                        />
                    }
                    <div ref={tableUpdateRef}></div>
                </Col>
            </Row>
        </Card>

        <LavReferences data={data} isCommon />
    </Space>
}
export default CommonObjectDetailInfo