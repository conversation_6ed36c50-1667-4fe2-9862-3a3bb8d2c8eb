import { extractProjectCode } from '../../../../helper/share'
import AppState from '@/store/types'
import { Button, Col, Row } from 'antd'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { API_URLS, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, PROJECT_PREFIX, SCREEN_MODE } from '../../../../constants'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import CommonObjectForm from '../form'
import { CommonObjectState } from '../type'
import LavLeftControl from './../../../_shared/left-menu'
import RightControl from './content'
import { PlusOutlined } from '@ant-design/icons'
import intl from '../../../../config/locale.config'
import CommonObjectFormModalPage from '../form/form'
import HistoryScreen from '../../../../modules/history'
import AppCommonService from '../../../../services/app.service'
import CommonObjectVersionDetails from './history/details'

const ViewCommonObjectSpec = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const state = useSelector<AppState | null>((s) => s?.commonObject) as CommonObjectState
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.id) {
      dispatch(getDetailRequest(props.match.params.id))
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.COMMON_OBJECT + '/' + props.match.params.id +  '/' + selectedRowVersion).then((e) => {
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);    
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${APP_ROUTES.COMMON_OBJECT}`)
    }
  }, [state.deleteSuccess])


  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.id))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)
  const handleCreate = (items) => {
    if(isCreate) {
      setIsCreate(false)
      history.push(`${APP_ROUTES.COMMON_OBJECT_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col span={5}>
            <LavLeftControl
              activeId={props.match.params.id}
              apiUrl={API_URLS.COMMON_REFERENCE_OBJECTS}
              route={APP_ROUTES.COMMON_OBJECT_DETAIL}
              title='object.header.title'
              artefactType={COM_ARTEFACT_TYPE_ID.OBJECT}
              reload={reload}
              reloadSuccess={() => setReload(false)}
              isCommon
              handleCreate={handleCreate}
            >
              {
                <Button ghost={true}
                type='primary'
                className='lav-btn-create'
                icon={<PlusOutlined />}
                onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common-object.button.create-common-object' })}
              </Button>
              }
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {
        screenMode === SCREEN_MODE.VIEW ? 
        <>      
          <Col span={19}>
            <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} id={props.match.params.id} setScreenMode={setScreenMode} isModalShow={state?.isModalShow} />
          </Col>
        </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
        <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
          <CommonObjectFormModalPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
        </Col> : <></>
      }
      {
         screenMode === SCREEN_MODE.EDIT ?
         <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
           <CommonObjectFormModalPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
             handleReloadData()
             setScreenMode(SCREEN_MODE.VIEW)
           }} objectID={props.match.params.id} />
         </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.common-object"
                            apiURL = {API_URLS.COMMON_HISTORY} isCommon = {true}
                            artefactType = {COM_ARTEFACT_TYPE_ID.OBJECT}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + state?.selectedData?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
              <CommonObjectVersionDetails id={props.match.params.id} setSelectedRowVersion = {setSelectedRowVersion} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
    </Row>
  )
}

export default ViewCommonObjectSpec
