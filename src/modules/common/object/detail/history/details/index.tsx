import AppState from '@/store/types'
import {
    Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../../../config/locale.config'
import { API_URLS, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS_COMMON } from '../../../../../../constants'
import DeleteButton from '../../../../../../helper/component/commonButton/DeleteButton'
import LavButtons from '../../../../../../helper/component/lav-buttons'
import LavCommonAuditTrail from '../../../../../../helper/component/lav-common-audit-trail'
import LavReferences from '../../../../../../helper/component/lav-references'
import useWindowDimensions from '../../../../../../helper/hooks/useWindowDimensions'
import { renderCommonStatusBadge } from '../../../../../../helper/share'
import { initComment, initCommentScreen } from '../../../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../../../modules/_shared/comment/type'
import { deleteRequest } from '../../../action'
import { CommonObjectState } from '../../../type'
import TableComponent from '../../table'
import HistoryNavigation from '../../../../../../modules/history/navigation'
import debounce from 'lodash.debounce'

const { Title, Text } = Typography
interface CommonObjectVersionDetailsProps {
    data: any | [],
    id: number,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any,
    setSelectedRowVersion: (version: string) => void, 
    onDismiss: () => void | null,
}
const CommonObjectVersionDetails = ({ data, id, onChange, isLoading, isModalShow, setScreenMode, setSelectedRowVersion, onDismiss }: CommonObjectVersionDetailsProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    //#region COMMENT INIT
    const state = useSelector<AppState | null>((s) => s?.commonObject) as CommonObjectState
    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    const [dataSourceUpdate, setDataSourceUpdate] = useState([])

    useEffect(() => {
        setDataSourceUpdate(() => {
            const newData: any = []
            data?.objectProperties.map(item => {
                return newData[item.order] = item
            })
            return newData
        })
    }, [data])


    useEffect(() => {
        let fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'description', title: intl.formatMessage({ id: 'view-screen-details.screen-info.description' }), },
            { field: 'property', title: intl.formatMessage({ id: 'createobject.label.property' }), },
            { field: 'target-object', title: intl.formatMessage({ id: 'createobject.label.target-object' }), },
            { field: 'screen', title: intl.formatMessage({ id: 'createobject.label.screen' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'createobject.label.use-case' }), },
        ];
        if (state?.detail?.id !== null) {
            if (state?.detail?.objectProperties) {
                state?.detail?.objectProperties?.forEach((e) => {
                    fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
                })
            }

            dispatch(initComment({ projectId: null, itemId: state.detail?.id, fields }));

            const payload = {
                projectId: null,
                itemId: state.detail?.id,
                artefact: ARTEFACT_COMMENT.COMMON_OBJECT,
                fields: fields.map(o => o.field)
            };
            dispatch(initCommentScreen(payload));
        }
    }, [state.detail])

    //#endregion COMMENT INIT
    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return data?.status !== STATUS_COMMON.DELETED ? <DeleteButton
            type={BUTTON_TYPE.TEXT}
            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.common-object' }) })}
            okCB={() => dispatch(deleteRequest(id))}
            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
    }
    return (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>{intl.formatMessage({ id: 'common.breadcrumb.common' })}</Breadcrumb.Item>
                        <Breadcrumb.Item>
                            <Link to={APP_ROUTES.COMMON_OBJECT}>{intl.formatMessage({ id: 'commonemail.page_title' })}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code} - {data?.name}
                    </Title>
                </div>
                    <Space size="small">
                        <LavButtons
                            isCommon={true}
                            url={`${API_URLS.COMMON_OBJECT}/${id}`}
                            artefact_type="common.artefact.common-object"
                            status={data?.status}
                            artefactType={COM_ARTEFACT_TYPE_ID.OBJECT}
                            id={id}
                            changePage={() => onChange()}>                                                                 
                            <Button onClick={debounce(onDismiss, 500)}>
                                {intl.formatMessage({ id: 'common.action.close' })}
                            </Button>
                        </LavButtons>
    
                    </Space>
                </Row>
                <Divider className="mt-0 mb-0" />              
                { data?.nextPrevious.latestVersion === data?.version ? <></>:
                       <HistoryNavigation isCommon={true} data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.common-object"} artefactType={COM_ARTEFACT_TYPE_ID.OBJECT} />
                    }
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Space size="large">
                            {/* <span>
                                <TriggerComment field="version">
                                    <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
                                </TriggerComment>
                            </span> */}
                            {renderCommonStatusBadge(data?.status)}
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'createobject.card-title.object-infomation',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={24}>
                                    <TriggerComment field="description">
                                        <Text type="secondary">
                                            {intl.formatMessage({ id: 'createobject.label.description' })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col className="description" span={24}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.description }}
                                    ></div>
                                </Col>
                                <Col span={24}>
                                    <TriggerComment field="property">
                                        <Text type="secondary">
                                            {intl.formatMessage({ id: 'createobject.label.property' })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24} style={{ width: '100%', overflowX: 'scroll' }}>
                                    <TableComponent dataSource={dataSourceUpdate}></TableComponent>
                                </Col>
                            </Row>
                        </Card>

                        <LavReferences data={data} isCommon />
                        <LavCommonAuditTrail data={data?.auditTrails} />
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    )
}

export default CommonObjectVersionDetails