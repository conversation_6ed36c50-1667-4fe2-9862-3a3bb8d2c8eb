import { SCREEN_MODE } from '../../../../constants'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import AppState from '@/store/types'
import { CheckOutlined } from '@ant-design/icons'
import {
    Table, Typography
} from 'antd'
import React from 'react'
import { useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { CommonObjectState } from '../type'

const { Text } = Typography

const TableComponent = (props) => {
    const state = useSelector<AppState | null>(
        (s) => s?.commonObject
    ) as CommonObjectState

    const columns = [
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'objectSpecification.column.object-property',
                    })}
                </Text>
            ),
            dataIndex: 'name',
            width: '13%',
            sorter: (a, b) => a.name - b.name,
            render: (text, record) => {
                return <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(record?.id)}>
                    <Text>{text}</Text>
                </TriggerComment>
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'objectSpecification.column.unique',
                    })}
                </Text>
            ),
            dataIndex: 'unique',
            width: '10%',
            editable: true,
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'objectSpecification.column.mandatory',
                    })}
                </Text>
            ),
            dataIndex: 'mandatory',
            width: '5%',
            editable: true,
            render: (text: boolean) => {
                if (text === true) {
                    return <CheckOutlined />
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'objectSpecification.column.max-length',
                    })}
                </Text>
            ),
            dataIndex: 'maxLength',
            width: '10%',
            editable: true,
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'objectSpecification.column.meaning',
                    })}
                </Text>
            ),
            dataIndex: 'description',
            width: '27%',
            editable: false,
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object' }),
            dataIndex: 'sourceObject',
            width: '15%',
            render: (text, item) => {
                return item.sourceObject?.name
            }
        },
        {
            title: intl.formatMessage({ id: 'objectSpecification.column.source-object-property' }),
            dataIndex: 'sourceObjectProperty',
            width: '20%',
            render: (text, item) => {
                return item.sourceObjectProperty?.name
            }
        },
    ]

    return (
        <>

            <Table
                bordered
                dataSource={props.dataSource}
                columns={columns}
                rowKey="id"
                //    onChange ={handlePagechange}
                pagination={false}
            />

        </>
    )
}

export default React.memo(TableComponent)
