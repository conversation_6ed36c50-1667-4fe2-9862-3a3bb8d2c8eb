import CustomSvgIcons from '../../../helper/component/custom-icons'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, BUTTON_TYPE, COM_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS_COMMON_FILTER } from '../../../constants'
import LavTable from '../../../helper/component/lav-table'
import { getColumnDropdownFilterProps, getColumnSearchProps, renderCommonStatusBadge } from '../../../helper/share'
import CommonObjectForm from './form'
import CommonObjectFormModalPage from './form/form'


const CommonObject = () => {
    const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
    const [id, setId] = useState<number>(0)
    const columns = [
        {
            title: intl.formatMessage({ id: 'common-object.column.code' }),
            dataIndex: 'code',
            sorter: true,
            width: '5%',
            sortOrder: 'descend',
            ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
            render: (text: string, record: any) => {
                const href = `${APP_ROUTES.COMMON_OBJECT_DETAIL}` + record.id
                return <Link to={href}>{text}</Link>
            },
        },
        {
            title: intl.formatMessage({ id: 'common-object.column.object' }),
            dataIndex: 'name',
            width: '20%',
            ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
        },
        {
            title: intl.formatMessage({ id: 'common-object.column.description' }),
            dataIndex: 'description',
            width: '50%',
            ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
            render: (text) => {
                return <div
                className="tableDangerous"
                dangerouslySetInnerHTML={{ __html: text }}
              ></div>
            }
        },
        {
            title: intl.formatMessage({ id: 'common-object.column.properties' }),
            dataIndex: 'activeProperties',
        },
        {
            title: intl.formatMessage({ id: 'common-object.column.status' }),
            dataIndex: 'status',
            width: '80px',
            ...getColumnDropdownFilterProps(STATUS_COMMON_FILTER, 'status'),
            render: (record) => renderCommonStatusBadge(record),
        },

    ]

    const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
        return (
            <Button ghost={true}
                type='primary'
                className='lav-btn-create'
                icon={<PlusOutlined />}
                onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'common-object.button.create-common-object' })}
            </Button>
        )
    }

    const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
        return (
            <Button ghost={screenMode === SCREEN_MODE.EDIT}
                style={{ border: 'none' }}
                icon={<CustomSvgIcons name="EditCustomIcon" />}
                onClick={() => {
                    setScreenMode(SCREEN_MODE.EDIT)
                    setId(record.id)
                }} />
        )

    }

    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return children
    }

    return (
        <Space direction="vertical" size="middle" className="full-width p-20px">
            {columns && screenMode === SCREEN_MODE.VIEW ?

                <LavTable
                    showBreadcumb={false}
                    title="common-object.table.title"
                    artefact_type="common.artefact.object"
                    apiUrl={API_URLS.COMMON_OBJECT}
                    artefactType={COM_ARTEFACT_TYPE_ID.OBJECT}
                    isCommon={true}
                    columns={columns}
                    deleteComponent={DeleteComponent}
                    updateComponent={UpdateComponent}
                    createComponent={CreateComponent}
                /> : <></>
            }
            {
                screenMode === SCREEN_MODE.CREATE ? <CommonObjectFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenMode={SCREEN_MODE.CREATE} buttonType={BUTTON_TYPE.TEXT} /> : <></>
            }
            {
                screenMode === SCREEN_MODE.EDIT ? <CommonObjectFormModalPage onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} objectID={id} screenMode={SCREEN_MODE.EDIT} /> : <></>
            }


        </Space>
    )
}


export default CommonObject
