import { createReducer } from '@reduxjs/toolkit'
import { createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, getDetailFailed, getDetailRequest, getDetailSuccess, getListFailed, getListObjectsFailed, getListObjectsRequest, getListObjectsSuccess, getListPropertiesFailed, getListPropertiesRequest, getListPropertiesSuccess, getListRequest, getListSuccess, resetState, setModalVisible, updateFailed, updateRequest, updateSuccess } from './action'
import { CommonObjectState, defaultState } from './type'



const initState: CommonObjectState = defaultState
const reducer = createReducer(initState, (builder) => {
  return builder
    .addCase(resetState, (state, action?) => {
      Object.assign(state, {
        ...defaultState,
        selectedData: state.selectedData,
        listData: state.listData
      });
    })
    .addCase(createRequest, (state, action?) => {
      state.isLoading = true;
      state.createSuccess = false;
    })
    .addCase(createSuccess, (state, action) => {
      state.isLoading = false;
      state.createSuccess = true;
    })
    .addCase(createFailed, (state, action) => {
      state.isLoading = false;
      state.createSuccess = false;
    })
    .addCase(updateRequest, (state, action?) => {
      state.isLoading = true;
      state.updateSuccess = false;
    })
    .addCase(updateSuccess, (state, action) => {
      state.isLoading = false;
      state.updateSuccess = true;
    })
    .addCase(updateFailed, (state, action) => {
      state.isLoading = false;
      state.updateSuccess = false;
    })


    .addCase(deleteRequest, (state, action?) => {
      state.deleteSuccess = false;
    })
    .addCase(deleteSuccess, (state, action) => {
      state.deleteSuccess = true;
    })
    .addCase(deleteFailed, (state, action) => {
      state.deleteSuccess = false;
    })

    .addCase(getDetailRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getDetailSuccess, (state, action) => {
      state.isLoading = false
      state.detail = action.payload
      state.selectedData = action.payload
    })
    .addCase(getDetailFailed, (state, action) => {
      state.isLoading = false
      state.detail = null
      state.selectedData = null
    })

    .addCase(getListObjectsRequest, (state, action?) => {
      state.isLoadingSourceObjList = true;
    })
    .addCase(getListObjectsSuccess, (state, action) => {
      state.isLoadingSourceObjList = false
      state.objects = action.payload
    })
    .addCase(getListObjectsFailed, (state, action) => {
      state.isLoadingSourceObjList = false
      state.objects = []
    })

    .addCase(getListPropertiesRequest, (state, action?) => {
      state.isLoadingObjProperties = true;
    })
    .addCase(getListPropertiesSuccess, (state, action) => {
      state.isLoadingObjProperties = false
      state.objectProperties = action.payload
    })
    .addCase(getListPropertiesFailed, (state, action) => {
      state.isLoadingObjProperties = false
      state.objectProperties = []
    })

    .addCase(getListRequest, (state, action?) => {
      state.isLoadingList = true;
    })
    .addCase(getListSuccess, (state, action) => {
      state.isLoadingList = false
      state.listData = action.payload
    })
    .addCase(getListFailed, (state, action) => {
      state.isLoadingList = false
      state.listData = null
    })

    .addCase(setModalVisible, (state, action) => {
      state.isModalShow = action.payload
      if(!action.payload){
        state.createSuccess = false;
        state.updateSuccess = false;
      }
    })
})



export default reducer
export { initState as CommonObjectInitState }

