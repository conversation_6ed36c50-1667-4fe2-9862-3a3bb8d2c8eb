import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../../constants'
import { apiCall } from '../../../helper/api/aloApi'
import { ShowAppMessage } from '../../../helper/share'
import { createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, getDetailFailed, getDetailRequest, getDetailSuccess, getListFailed, getListObjectsFailed, getListObjectsRequest, getListObjectsSuccess, getListPropertiesFailed, getListPropertiesRequest, getListPropertiesSuccess, getListRequest, getListSuccess, updateFailed, updateRequest, updateSuccess } from './action'

function* handleCreateCommonObject(action: Action) {
    if (createRequest.match(action)) {
        try {
            const request = action.payload
            const createAnother = action.payload.createAnother || false
            const url = API_URLS.COMMON_OBJECT
            const res = yield call(apiCall, 'POST', url, request as any)
            //checkdone
            ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.object')
            yield put(createSuccess(res.data))
        } catch (err) {
            yield put(createFailed(null))
            ShowAppMessage(err, null, 'common.artefact.object', 'committee.title.column.name')
        }
    }
}

function* handleGetListObjects(action: Action) {
    if (getListObjectsRequest.match(action)) {
        try {
            const url = API_URLS.COMMON_REFERENCE_OBJECTS
            const res = yield call(apiCall, 'GET', url)
            yield put(getListObjectsSuccess(res.data))
        } catch {
            yield put(getListObjectsFailed(null))
            ShowAppMessage(MESSAGE_TYPE.ERROR)
        }
    }
}

function* handleGetListObjectProperties(action: Action) {
    if (getListPropertiesRequest.match(action)) {
        try {
            const url = API_URLS.COMMON_REFERENCE_OBJECTS + `/${action.payload}/Properties`
            const res = yield call(apiCall, 'GET', url)
            yield put(getListPropertiesSuccess(res.data))
        } catch {
            yield put(getListPropertiesFailed(null))
            ShowAppMessage(MESSAGE_TYPE.ERROR)
        }
    }
}

function* handleUpdateObject(action: Action) {
    if (updateRequest.match(action)) {
        try {
            const request = action.payload
            const url = API_URLS.COMMON_OBJECT + `/${request.id}`
            const res = yield call(apiCall, 'PUT', url, request as any)
            //checkdone
            ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.object');
            yield put(updateSuccess(res.data))
        } catch (error) {
            yield put(updateFailed(null))
            ShowAppMessage(error, null, 'common.artefact.object', 'committee.title.column.name')
        }
    }
}

function* handleDeleteObject(action: Action) {
    if (deleteRequest.match(action)) {
        try {
            const url = API_URLS.COMMON_OBJECT + `/${action.payload}`
            const res = yield call(apiCall, 'DELETE', url as any)
            //checkdone
            ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.object')
            yield put(deleteSuccess(res.data))
        } catch (error) {
            yield put(deleteFailed(null))
            ShowAppMessage(error, null, 'common.artefact.object')
        }
    }
}

function* handleGetDetail(action: Action) {
    if (getDetailRequest.match(action)) {
        try {
            const url = API_URLS.COMMON_OBJECT + '/' + action.payload
            const res = yield call(apiCall, 'GET', url)
            yield put(getDetailSuccess(res.data));
        } catch (err: any) {
            yield put(getDetailFailed(null));
            if (err.response.status !== 404) {
                ShowAppMessage(MESSAGE_TYPE.ERROR)
            }
        }
    }
}

function* handleGetList(action: Action) {
    if (getListRequest.match(action)) {
        try {
            const take = action.payload.take;
            const skip = (action.payload.skip - 1) * take;
            const url = `${API_URLS.COMMON_OBJECT}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
            const res = yield call(apiCall, 'GET', url);

            yield put(getListSuccess(res.data));
        } catch (err) {
            yield put(getListFailed(null));
            ShowAppMessage(MESSAGE_TYPE.ERROR)
        }
    }
}

function* watchFetchRequest() {
    yield takeLatest(createRequest.type, handleCreateCommonObject)
    yield takeLatest(getListObjectsRequest.type, handleGetListObjects)
    yield takeLatest(updateRequest.type, handleUpdateObject)
    yield takeLatest(getListRequest.type, handleGetList)
    yield takeLatest(deleteRequest.type, handleDeleteObject)
    yield takeLatest(getDetailRequest.type, handleGetDetail)
    yield takeLatest(getListPropertiesRequest.type, handleGetListObjectProperties)
}
export default function* commonObjectSaga() {
    yield all([fork(watchFetchRequest)])
}
