import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Card,
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Typography,
} from 'antd'
import debounce from 'lodash.debounce'
import React, { createRef, useEffect, useRef, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import {
  APP_COMMON_ROLES,
  ARTEFACT_COMMENT,
  BUTTON_TYPE,
  MESSAGE_TYPES,
  SCREEN_MODE,
  STATUS,
  STATUS_COMMON,
} from '../../../../constants'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomModal from '../../../../helper/component/custom-modal'
import FormGroup from '../../../../helper/component/form-group'
import TextAreaBullet from '../../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import {
  hasCommonRole,
  renderCommonStatusBadge,
  ShowMessgeAdditionalSubmit,
} from '../../../../helper/share'
import { createRequest, getDetailRequest, resetState, setModalVisible, updateRequest } from '../action'
import { CommonObjectState } from '../type'
import TableEdit from './object-properties/index'
import {
  initComment,
  initCommentScreen,
} from './../../../_shared/comment/action'
import { CommentState } from '../../../../modules/_shared/comment/type'
import TriggerComment from './../../../_shared/comment/trigger-comment';

const { Text } = Typography
const { confirm } = Modal

interface CommonObjectFormProps {
  objectID?: number
  onFinish?: () => void | null
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface CommonObjectFormModalProps {
  objectID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}
const CommonObjectFormModal = ({ objectID, screenMode, onFinish, onDismiss }: CommonObjectFormModalProps) => {
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const tableRef: any = useRef()
  const state = useSelector<AppState | null>(
    (s) => s?.commonObject
  ) as CommonObjectState
  const [objectProperties, setObjectProperties] = useState<any>([])
  const [isDraft, setIsDraft] = useState(false)
  const [isCreateMore, setIsCreateMore] = useState(false)
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const tableEditRef = createRef<any>()

  // Destroy
  useEffect(() => {
    return () => {
      dispatch(resetState(null));
      resetForm()
    }
  }, [])

  const resetForm = () => {
    setIsCreateMore(false)
    setIsDraft(false)
    form.resetFields()
    setObjectProperties([])
  }

  useEffect(() => {
    if (objectID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(objectID))
    }
  }, [screenMode, objectID])

  useEffect(() => {
    if (objectID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        id: state.detail.id,
        code: state.detail.code,
        name: state.detail.name,
        description: state.detail.description,
      })
      setObjectProperties(state.detail.objectProperties)
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (onFinish) {
        onFinish()
      }
      if (isCreateMore) {
        resetForm()
        form.setFieldsValue({
          createMore: isCreateMore
        })
      } else {
        onDismiss();
      }
      setIsDraft(false)
      setIsCreateMore(false)
    }
  }, [state.createSuccess, state.updateSuccess])
  const handleTableUpdated = (e) => {
    setObjectProperties(e)
  }
  const onSubmit = debounce(async(values: any, st?: string) => {
    const requestData: any = {
      ...values,
      id: objectID,
      status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) :
        hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED
      ,
      objectProperties: objectProperties?.map((e, index) => {
        return {
          name: e.name,
          description: e.description,
          mandatory: e.mandatory,
          maxLength: e.maxLength,
          unique: e.unique,
          sourceObjectId: e.sourceObject?.id,
          sourcePropertyId: e.sourceObjectProperty?.id,
          id: e.id ? e.id : null,
          order: index,
        }
      }),
      description: values.description,
    }
    if (requestData.status === STATUS.SUBMITTED) {
      if (objectProperties.length === 0) {
        tableEditRef.current.scrollIntoView('tableEdit')
        ShowMessgeAdditionalSubmit('EMSG_12', 'common.artefact.object')
        return
      }
    }
    setIsCreateMore(values.createMore)
    if (isDraft) {
      requestData.messageAction =
        screenMode === SCREEN_MODE.CREATE
          ? MESSAGE_TYPES.CREATE
          : MESSAGE_TYPES.UPDATE
      dispatch(
        screenMode === SCREEN_MODE.CREATE
          ? createRequest(requestData)
          : updateRequest(requestData)
      )
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.object' }) }
        ),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT
          dispatch(
            screenMode === SCREEN_MODE.CREATE
              ? createRequest(requestData)
              : updateRequest(requestData)
          )
        },
        onCancel() { },
      })
    }
  }, 500)

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }


  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return
    }

    const fields: { field; title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'description', title: intl.formatMessage({ id: 'view-screen-details.screen-info.description', }), },
      { field: 'property', title: intl.formatMessage({ id: 'createobject.label.property', }), },
    ]
    dispatch(initComment({ projectId: null, itemId: state.detail.id, fields }))

    const payload = {
      projectId: null,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.COMMON_OBJECT,
      fields: fields.map((o) => o.field),
    }
    dispatch(initCommentScreen(payload))
  }, [state.detail])

  //#endregion COMMENT INIT

  return <CustomModal
    isLoading={state.isLoading}
    closable={false}
    size="medium"
    visible={true}
    footer={null}
  >
    <Form
      form={form}
      name="basic"
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className="rq-modal-header">
        <Row>
          <Col span={10}>
            <Space size="large">
              <Form.Item
                name="name"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <Input
                  size="large"
                  className="modal-create-name-input-field"
                  placeholder={`${intl.formatMessage({
                    id: `createobject.place-holder.object-name`,
                  })}${intl.formatMessage({
                    id: `common.mandatory.*`,
                  })}`}
                  maxLength={255}
                />
              </Form.Item>
              {screenMode === SCREEN_MODE.EDIT ? (
                renderCommonStatusBadge(state.detail?.status)
              ) : (
                <></>
              )}
            </Space>
          </Col>
          <Col span={14}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? (
                  <Form.Item
                    style={{ marginBottom: '0px' }}
                    valuePropName="checked"
                    name="createMore"
                    wrapperCol={{ span: 24 }}
                  >
                    <Checkbox disabled={state.isLoading}>
                      {intl.formatMessage({
                        id: 'common.action.create-another',
                      })}
                    </Checkbox>
                  </Form.Item>
                ) : (
                  <></>
                )}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>
                {screenMode === SCREEN_MODE.CREATE ||
                  state.detail?.status === STATUS_COMMON.DRAFT ||
                  state.detail?.status === STATUS_COMMON.REJECTED ? (
                  <Form.Item style={{ marginBottom: '0px' }}>
                    <Button
                      type="primary"
                      ghost
                      htmlType="submit"
                      onClick={() => setIsDraft(false)}
                    >
                      {intl.formatMessage({ id: 'common.action.submit' })}
                    </Button>
                  </Form.Item>
                ) : (
                  <></>
                )}
                {
                  screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS_COMMON.DRAFT || state.detail?.status === STATUS_COMMON.REJECTED ?
                    <Form.Item style={{ marginBottom: '0px' }}>
                      <Button
                        className="success-btn"
                        onClick={() => setIsDraft(true)}
                        htmlType="submit"
                      >
                        {intl.formatMessage({
                          id:
                            screenMode === SCREEN_MODE.CREATE
                              ? 'common.action.save-as-draft'
                              : 'common.action.save',
                        })}
                      </Button>
                    </Form.Item> : <></>
                }
              </Space>
            </Row>
          </Col>
        </Row>
      </div>

      <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 280}>
        <Row align="middle">
          <Col span={2}>
            <TriggerComment screenMode={screenMode} field="version">
              <Text>
                {intl.formatMessage({
                  id: 'createobject.place-holder.version',
                })}
              </Text>
            </TriggerComment>
          </Col>
          <Col span={2}>
            <Form.Item
              className="mb-0"
              name="version"
              rules={[
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
              ]}
            >
              <Input
                placeholder={`${intl.formatMessage({
                  id: `createobject.place-holder.version`,
                })}`}
                maxLength={255}
              />
            </Form.Item>
          </Col>
        </Row>
        <Space
          direction="vertical"
          size="middle"
          style={{ padding: '0 10px 2px 0' }}
        >
          <Card
            className="rq-form-block"
            title={intl.formatMessage({
              id: 'createobject.card-title.object-infomation',
            })}
          >
            {screenMode === SCREEN_MODE.EDIT ? (
              <Row>
                <Col span={6}>
                  <FormGroup
                    label={intl.formatMessage({
                      id: 'createobject.label.object-code',
                    })}
                  >
                    <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                      <Input maxLength={255} disabled />
                    </Form.Item>
                  </FormGroup>
                </Col>
              </Row>
            ) : (
              <></>
            )}

            <FormGroup
              className="rq-fg-comment"
              required
              label={
                <TriggerComment screenMode={screenMode} field="description">
                  {intl.formatMessage({
                    id: 'createobject.label.description',
                  })}
                </TriggerComment>
              }
            >
              <TextAreaBullet
                reload={false}
                reloadAfterBack={false}
                label=""
                name="description"
                labelAlign="left"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              ></TextAreaBullet>
            </FormGroup>

            <TriggerComment field="property">
              <Text type="secondary">
                {intl.formatMessage({ id: 'createobject.label.property' })}
              </Text>
            </TriggerComment>

            <div ref={tableEditRef}>
              <TableEdit
                name="tableEdit"
                ref={tableRef}
                data={objectProperties}
                sourceObject={state.objects}
                form={form}
                onUpdate={handleTableUpdated}
              />
            </div>
          </Card>
        </Space>
      </Scrollbars>
    </Form>
  </CustomModal>
}
const CommonObjectForm = ({ objectID, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: CommonObjectFormProps) => {
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState<any>(null)

  useEffect(() => {
    if (isModalVisible !== null) {
      dispatch(setModalVisible(isModalVisible))
    }
  }, [isModalVisible])

  return <>
    {buttonType === BUTTON_TYPE.TEXT ? (
      <Button
        ghost={screenMode === SCREEN_MODE.CREATE}
        type="primary"
        className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
        onClick={() => setIsModalVisible(true)}
        icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
      >
        {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'object.button.create-object' : 'common.action.update' })}
      </Button>
    ) : buttonType === BUTTON_TYPE.ICON ? (
      <Button
        type="text"
        onClick={() => setIsModalVisible(true)}
        icon={screenMode === SCREEN_MODE.CREATE ? (<PlusOutlined />) : (<CustomSvgIcons name="EditCustomIcon" />)}
      />
    ) : (
      <></>
    )}
    {isModalVisible === true ? <CommonObjectFormModal objectID={objectID} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
  </>
}
export default CommonObjectForm
