import AppState from '@/store/types'
import {
  Button,
  Card,
  Checkbox, Col, Form,
  Input,
  Modal, Row, Space,
  Spin,
  Typography
} from 'antd'
import debounce from 'lodash.debounce'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import {
  APP_COMMON_ROLES,
  ARTEFACT_COMMENT,
  BUTTON_TYPE,
  MESSAGE_TYPES,
  SCREEN_MODE,
  STATUS,
  STATUS_COMMON
} from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import FormGroup from '../../../../helper/component/form-group'
import LavPageHeader from '../../../../helper/component/lav-breadcumb'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../helper/hooks/useWindowDimensions'
import {
  getReferencesFromEditor,
  hasCommonRole, renderCommonStatusBadge, renderStatusBadge, ShowMessgeAdditionalSubmit
} from '../../../../helper/share'
import { CommentState } from '../../../../modules/_shared/comment/type'
import { createFailed, createRequest, getDetailRequest, getListObjectsRequest, resetState, updateFailed, updateRequest } from '../action'
import { CommonObjectState } from '../type'
import {
  initComment,
  initCommentScreen
} from './../../../_shared/comment/action'
import TriggerComment from './../../../_shared/comment/trigger-comment'
import TableEdit from './object-properties/index'

const { Text } = Typography
const { confirm } = Modal


interface CommonObjectFormModalProps {
  objectID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT
  onFinish?: () => void | null
  onDismiss: () => void | null
}
const CommonObjectFormModalPage = ({ objectID, screenMode, onFinish, onDismiss }: CommonObjectFormModalProps) => {
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const tableRef: any = useRef()
  const state = useSelector<AppState | null>(
    (s) => s?.commonObject
  ) as CommonObjectState
  const [objectProperties, setObjectProperties] = useState<any>([])
  const [isDraft, setIsDraft] = useState(false)
  const [isCreateMore, setIsCreateMore] = useState(false)
  const [isSubmit, setIsSubmit] = useState<boolean>(false)
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const getCkeditorDataDes: any = createRef()
  const tableEditRef = createRef<any>()

  // Destroy
  useEffect(() => {
    dispatch(getListObjectsRequest(null))
    return () => {
      dispatch(resetState(null));
      resetForm()
    }
  }, [])

  const resetForm = () => {
    setIsCreateMore(false)
    setIsDraft(false)
    form.resetFields()
    setObjectProperties([])
  }

  useEffect(() => {
    if (objectID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(objectID))
    }
  }, [screenMode, objectID])

  useEffect(() => {
    if (objectID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      form.setFieldsValue({
        id: state.detail.id,
        code: state.detail.code,
        name: state.detail.name,
        description: state.detail.description,
      })
      setObjectProperties(state.detail.objectProperties)
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm()
        form.setFieldsValue({
          createMore: isCreateMore
        })
      } else {
        if (onFinish) {
          onFinish()
        }
        onDismiss();
      }
      setIsDraft(false)
      setIsCreateMore(false)
      dispatch(updateFailed(null))
      dispatch(createFailed(null))
    }
  }, [state.createSuccess, state.updateSuccess])
  const handleTableUpdated = (e) => {
    setObjectProperties(e)
  }
  const onSubmit = debounce(async (values: any, st?: string) => {
    let mentionReferences = getReferencesFromEditor(getCkeditorDataDes?.current?.props?.data, true);
    const requestData: any = {
      ...values,
      id: objectID,
      status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS_COMMON.DRAFT : state.detail?.status) :
        hasCommonRole(APP_COMMON_ROLES.REVIEWER) ? STATUS_COMMON.APPROVED : STATUS_COMMON.SUBMITTED
      ,
      objectProperties: objectProperties?.map((e, index) => {
        return {
          name: e.name,
          description: e.description,
          mandatory: e.mandatory,
          maxLength: e.maxLength,
          unique: e.unique,
          sourceObjectId: e.sourceObject,
          sourcePropertyId: e.refProperty,
          id: e.id ? e.id : null,
          order: index,
        }
      }),
      description: getCkeditorDataDes?.current?.props?.data,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null
    }
    const checkObj = objectProperties?.filter(e => e?.name === "" || e?.description === "")
    if (checkObj && checkObj.length > 0) {
      tableEditRef.current.scrollIntoView('tableEdit')
      ShowMessgeAdditionalSubmit('EMSG_36');
      return;
    }
    const checkProperty = objectProperties?.filter(e => e?.sourceObject && !e?.refProperty)
    if (checkProperty && checkProperty.length > 0) {
      tableEditRef.current.scrollIntoView('tableEdit')
      ShowMessgeAdditionalSubmit('EMSG_36A');
      return;
    }

    let valueArr = objectProperties?.map((item) => { return item?.name });
    let isDuplicate = valueArr.some((item, idx) => {
      return valueArr.indexOf(item) != idx
    });
    if (isDuplicate) {
      tableEditRef.current.scrollIntoView('tableEdit')
      ShowMessgeAdditionalSubmit('EMSG_7', 'common.artefact.property');
      return;
    }
    if (requestData.status === STATUS.SUBMITTED) {
      if (objectProperties.length === 0) {
        tableEditRef.current.scrollIntoView('tableEdit')
        ShowMessgeAdditionalSubmit('EMSG_12', 'common.artefact.object')
        return
      }
    }
    setIsCreateMore(values.createMore)
    if (isDraft) {
      requestData.messageAction =
        screenMode === SCREEN_MODE.CREATE
          ? MESSAGE_TYPES.CREATE
          : MESSAGE_TYPES.UPDATE
      dispatch(
        screenMode === SCREEN_MODE.CREATE
          ? createRequest(requestData)
          : updateRequest(requestData)
      )
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.object' }) }
        ),
        onOk() {
          requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
          dispatch(
            screenMode === SCREEN_MODE.CREATE
              ? createRequest(requestData)
              : updateRequest(requestData)
          )
        },
        onCancel() { },
      })
    }
  }, 500)

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }


  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState

  useEffect(() => {
    let fields: { field; title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'description', title: intl.formatMessage({ id: 'view-screen-details.screen-info.description', }), },
      { field: 'property', title: intl.formatMessage({ id: 'createobject.label.property', }), },
    ]

    if (state?.detail?.id !== null) {
      if (state?.detail?.objectProperties) {
        state?.detail?.objectProperties?.forEach((e) => {
          fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
        })
      }
      dispatch(initComment({ projectId: null, itemId: state.detail?.id, fields }))

      const payload = {
        projectId: null,
        itemId: state.detail?.id,
        artefact: ARTEFACT_COMMENT.COMMON_OBJECT,
        fields: fields.map((o) => o.field),
      }
      dispatch(initCommentScreen(payload))
    }

  }, [state.detail])

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name="basic"
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className="rq-modal-header">


        <LavPageHeader
          showBreadcumb={false}
          title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createobject.header.title' : 'updateobject.header.title' })}
        >
          <Space size="small">
            {screenMode === SCREEN_MODE.CREATE ? (
              <Form.Item
                style={{ marginBottom: '0px' }}
                valuePropName="checked"
                name="createMore"
                wrapperCol={{ span: 24 }}
              >
                <Checkbox disabled={state.isLoading}>
                  {intl.formatMessage({
                    id: 'common.action.create-another',
                  })}
                </Checkbox>
              </Form.Item>
            ) : (
              <></>
            )}
            <Button onClick={debounce(confirmCancel, 500)}>
              {intl.formatMessage({ id: 'common.action.close' })}
            </Button>
            {screenMode === SCREEN_MODE.CREATE ||
              state.detail?.status === STATUS_COMMON.DRAFT ||
              state.detail?.status === STATUS_COMMON.REJECTED ? (
              <Form.Item style={{ marginBottom: '0px' }}>
                <Button
                  type="primary"
                  ghost
                  htmlType="submit"
                  onClick={() => {
                    setIsSubmit(!isSubmit)
                    setIsDraft(false)
                  }}
                >
                  {intl.formatMessage({ id: 'common.action.submit' })}
                </Button>
              </Form.Item>
            ) : (
              <></>
            )}

            <Form.Item style={{ marginBottom: '0px' }}>
              <Button
                className="success-btn"
                onClick={() => {
                  setIsSubmit(!isSubmit)
                  setIsDraft(true)
                }}
                htmlType="submit"
              >
                {intl.formatMessage({
                  id:
                    screenMode === SCREEN_MODE.CREATE
                      ? 'common.action.save-as-draft'
                      : 'common.action.save',
                })}
              </Button>
            </Form.Item>
          </Space>
        </LavPageHeader>

      </div>
      <Row align="middle">
        {screenMode === SCREEN_MODE.EDIT ?
          <Col span={5}>
            <div className='status-container'>
              <div>
                {intl.formatMessage({ id: 'common.field.status' })}
              </div>
              <div>
                {renderCommonStatusBadge(state.detail?.status)}
              </div>
            </div>
          </Col> : <></>
        }
      </Row>
      <Space
        direction="vertical"
        size="middle"
        style={{ padding: '0 10px 2px 0' }}
      >

        <Card
          className="rq-form-block"
          title={intl.formatMessage({
            id: 'createobject.card-title.object-infomation',
          })}
        >
          {screenMode === SCREEN_MODE.EDIT ? (
            <FormGroup
              inline
              label={intl.formatMessage({
                id: 'common.label.code',
              })}
              labelSpan={3}
              controlSpan={2}
            >
              <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                <Input maxLength={255} disabled />
              </Form.Item>
            </FormGroup>

          ) : (
            <></>
          )}

          <FormGroup
            inline
            required
            label={intl.formatMessage({ id: 'common.label.name' })}
            labelSpan={3}
            controlSpan={21}
          >

            <Form.Item
              name="name"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
            >
              <Input
                maxLength={255}
              />
            </Form.Item>
          </FormGroup>

          <FormGroup
            className="rq-fg-comment"
            inline
            label={
              <TriggerComment screenMode={screenMode} field="description">
                {intl.formatMessage({
                  id: 'createobject.label.description',
                })}
              </TriggerComment>
            }
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item
              name="description"
              labelAlign="left"
              rules={[{
                validator: async (rule, value) => {
                  const description = getCkeditorDataDes?.current?.props?.data
                  if ((description == '' || description == undefined) && (!isDraft || state.detail?.status === STATUS_COMMON.SUBMITTED)) {
                    throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                  }
                }
              }]}
              wrapperCol={{ span: 24 }}
            >
              <CkeditorMention
                isCommon
                ref={getCkeditorDataDes}
                data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
              />
            </Form.Item>
          </FormGroup>

          <div ref={tableEditRef}>
            <TableEdit
              name="tableEdit"
              ref={tableRef}
              data={objectProperties}
              form={form}
              isSubmitForm={isSubmit}
              onUpdate={handleTableUpdated}
            />
          </div>
        </Card>
      </Space>
    </Form>
  </Spin>
}

export default CommonObjectFormModalPage
