import AppState from '@/store/types'
import {
  Button,
  Form,
  Checkbox,
  Input,
  Select,
  Row,
  Col,
  InputNumber,
  Typography,
} from 'antd'
import intl from '../../../../../config/locale.config'
import { useEffect, useState } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import CustomModal from '../../../../../helper/component/custom-modal'
import { useSelector, useDispatch } from 'react-redux'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import { STATUS, SCREEN_MODE } from '../../../../../constants'
import { CommonObjectState } from '../../type'
import { getListObjectsRequest, getListPropertiesRequest } from '../../action'
import { isFulfilled } from '@reduxjs/toolkit'
// getListPropertiesRequest
const { TextArea } = Input
const { Option } = Select
const { Text } = Typography

interface CreateObjectPropertyProps {
  objectID?: number,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE,
  currentData?: any,
  tableData: any,
  dataChanged: any
}

const CreateObjectProperty = ({ objectID, screenMode, currentData, tableData, dataChanged }: CreateObjectPropertyProps) => {
  const [visible, setVisible] = useState(false)
  const [createOther, setCreateOther] = useState(false)
  const [form] = Form.useForm()
  const [modalAction, setModalAction] = useState<any[]>([])
  const [objProp, setObjProp] = useState<any>([])
  const state = useSelector<AppState | null>(
    (s) => s?.commonObject
  ) as CommonObjectState
  const dispatch = useDispatch()

  useEffect(() => {
    if (visible === false) {
      setCreateOther(false);
      form.resetFields([
        'name',
        'description',
        'sourceObject',
        'refProperty',
        'unique',
        'mandatory',
        'maxLength'
      ])
    } else {
      dispatch(getListObjectsRequest(null));
    }
  }, [visible])
  useEffect(() => {
    setObjProp(state.objectProperties)
  }, [state.objectProperties])
  useEffect(() => {
    if (visible && screenMode == SCREEN_MODE.EDIT) {
      const sourceObjectIndex = state.objects?.findIndex(
        (item: any) =>
          currentData.sourceObject?.id === item.id &&
          item.status !== STATUS.CANCELLED &&
          item.status !== STATUS.DELETE
      )
      if (sourceObjectIndex != -1) {
        dispatch(getListPropertiesRequest(state.objects[sourceObjectIndex]?.id));
      }
      form.setFieldsValue({
        name: currentData.name,
        description: currentData.description,
        sourceObject: currentData.sourceObject?.id,
        refProperty: currentData.sourceObjectProperty?.id,
        unique: currentData.unique,
        mandatory: currentData.mandatory,
        maxLength: currentData.maxLength,
      })
      setModalAction([
        <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
          {intl.formatMessage({ id: `common.action.cancel` })}
        </Button>,
        <Button className="success-btn" key="5" onClick={form.submit}>
          {intl.formatMessage({ id: 'common.action.update' })}
        </Button>,
      ])
    } else if (visible && screenMode == SCREEN_MODE.CREATE) {
      setModalAction([
        <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
          {intl.formatMessage({ id: `common.action.cancel` })}
        </Button>,
        <Checkbox key="3" checked={createOther} onChange={(e) => setCreateOther(e.target.checked)}>
          {intl.formatMessage({ id: 'createobject.checkbox.create-another' })}
        </Checkbox>,
        <Button className="success-btn" key="5" onClick={form.submit}>
          {intl.formatMessage({ id: 'createobject.checkbox.add-to-table' })}
        </Button>,
      ])
    }
  }, [visible, createOther])

  const showModal = () => {
    setVisible(true)
  }

  const handleCancel = () => {
    form.resetFields()
    setVisible(false)
  }

  const onFinish = (values: any) => {
    if (!createOther) {
      setVisible(false);
    } else {
      form.resetFields([
        'name',
        'description',
        'sourceObject',
        'refProperty',
        'unique',
        'mandatory',
        'maxLength'
      ])
    }
    dataChanged({
      ...values,
      sourceObject: getSourceObject(values.sourceObject),
      sourceObjectProperty: getSourceObjectProperty(values.refProperty)
    });
  }

  const getSourceObject = (id) => {
    const obj = state.objects.find((e) => e.id === id);
    return obj || null
  }

  const getSourceObjectProperty = (id) => {
    const obj = state.objectProperties.find((e) => e.id === id);
    return obj || null
  }

  const onFinishFailed = (errorInfo: any) => {
  }

  const handleSourceObjectChanged = (e) => {
    form.resetFields([
      'refProperty'
    ])
    if (e) {
      dispatch(getListPropertiesRequest(e));
    } else {
      setObjProp([])
    }
    
  }

  return (
    <>
      {screenMode === SCREEN_MODE.EDIT && (
        <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={showModal}></Button>
      )}
      {screenMode === SCREEN_MODE.CREATE && (
        <Button icon={<PlusOutlined />} type="link" onClick={showModal}>{intl.formatMessage({ id: 'createobject.create-modal-title.add-property' })}</Button>
      )}

      <Form
        form={form}
        id={Date.now().toString()}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        scrollToFirstError={{ block: 'center' }}
      >
        <CustomModal
          isLoading={state.isLoading}
          closable={true}
          title={
            screenMode === SCREEN_MODE.CREATE
              ? intl.formatMessage({ id: 'createobject.create-modal-title.add-property' })
              : intl.formatMessage({ id: 'createobject.create-modal-title.update-property' })
          }
          visible={visible}
          footer={[modalAction]}
          onCancel={handleCancel}
          maskClosable={false}

        >
          <Row>
            <Col span={8}>
              <Text>{intl.formatMessage({ id: 'createobject.column.object-property' })}{' '}</Text>
              <Text type="danger">{intl.formatMessage({ id: `common.mandatory.*` })}</Text>
            </Col>
            <Col span={16}>
              <Form.Item
                name={`name`}
                labelAlign="left"
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                      if (value && value.length > 0) {
                        if (screenMode === SCREEN_MODE.EDIT) {
                          const dupplicate = tableData.findIndex(
                            (item: any) =>
                              item.name
                                .toLowerCase()
                                .replace(/\s+/g, ' ')
                                .trim() ===
                              value.toLowerCase().replace(/\s+/g, ' ').trim()
                          )
                          if (
                            currentData &&
                            value.toLowerCase().replace(/\s+/g, ' ').trim() !==
                            currentData.name
                              .toLowerCase()
                              .replace(/\s+/g, ' ')
                              .trim() &&
                            dupplicate !== -1
                          ) {
                            throw new Error(
                              `${intl.formatMessage(
                                { id: 'EMSG_7' },
                                {
                                  Artefact: `${intl.formatMessage({
                                    id: 'objectSpecification.column.object-property',
                                  })}`,
                                }
                              )}`
                            )
                          }
                        } else if (
                          screenMode === SCREEN_MODE.CREATE
                        ) {
                          const dupplicate = tableData.findIndex(
                            (item: any) =>
                              item.name
                                .toLowerCase()
                                .replace(/\s+/g, ' ')
                                .trim() ===
                              value.toLowerCase().replace(/\s+/g, ' ').trim()
                          )
                          if (dupplicate !== -1) {
                            throw new Error(
                              `${intl.formatMessage(
                                { id: 'EMSG_7' },
                                {
                                  Artefact: `${intl.formatMessage({
                                    id: 'objectSpecification.column.object-property',
                                  })}`,
                                }
                              )}`
                            )
                          }
                        }
                      }
                    },
                  },
                ]}
              >
                <Input maxLength={255}></Input>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Text>{intl.formatMessage({ id: 'createobject.column.meaning' })}{' '}</Text>
              <Text type="danger">{intl.formatMessage({ id: `common.mandatory.*` })}</Text>
            </Col>
            <Col span={16}>
              <Form.Item
                validateTrigger="onBlur"
                name={`description`}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <TextArea rows={3}></TextArea>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Text>{intl.formatMessage({ id: 'createobject.column.source-object' })}</Text>
            </Col>
            <Col span={16}>
              <Form.Item name={`sourceObject`} validateTrigger="onBlur">
                <Select
                  allowClear
                  filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                  showSearch
                  onChange={handleSourceObjectChanged}
                >
                  {state.objects && state.objects.map(
                    (item: any) =>
                      item.status !== STATUS.DELETE &&
                        item.status !== STATUS.CANCELLED &&
                        objectID ? objectID !== item.id : true &&
                      <Option key={item.id} value={item.id}>{item.name}</Option>
                  )}
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Text>{intl.formatMessage({ id: 'createobject.column.source-object-property' })}</Text>
            </Col>

            <Col span={16}>
              <Form.Item validateTrigger="onBlur" name={`refProperty`}>
                <Select
                  allowClear
                  filterOption={(input, option: any) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                  showSearch
                >
                  {objProp?.map((item: any) => (
                    <Option key={item.id} value={item.id}>
                      {item.name}
                    </Option>
                  ))
                  }
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={12}>
              <Row>
                <Col span={16}>
                  <Text>{intl.formatMessage({ id: 'createobject.column.unique' })}</Text>
                </Col>

                <Col span={8}>
                  <Form.Item
                    style={{ marginBottom: '0px' }}
                    valuePropName="checked"
                    name="unique"
                    wrapperCol={{ span: 24 }}
                  >
                    <Checkbox key="1"></Checkbox>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col span={12}>
              <Row>
                <Col span={16}>
                  <Text>{intl.formatMessage({ id: 'createobject.column.mandatory' })}</Text>
                </Col>
                <Col span={8}>
                  <Form.Item
                    style={{ marginBottom: '0px' }}
                    valuePropName="checked"
                    name="mandatory"
                    wrapperCol={{ span: 24 }}
                  >
                    <Checkbox key="2"></Checkbox>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
          </Row>
          <br />
          <Row>
            <Col span={8}>
              <Text>{intl.formatMessage({ id: 'createobject.column.max-length' })}</Text>
            </Col>
            <Col span={16}>
              <Form.Item validateTrigger="onBlur" name={`maxLength`}>
                <InputNumber min={0} max={99999} maxLength={5}></InputNumber>
              </Form.Item>
            </Col>
          </Row>
        </CustomModal>
      </Form>
    </>
  )
}

export default CreateObjectProperty
