

export interface ObjectInfo {
    id: number | null
    name: string
    status: number
    description: string

    code: string
    sourceObject: any[]
    targetObject: any[]

    actors: any[]
    screens: any[]
    state: any[]
    dataMigration: any[]
    workFlow: any[]
    useCase: any[]

    objectProperties?: ObjectProperty[] | null,
}

export interface CommonObjectState {
    isLoading: boolean
    detail: ObjectInfo | null
    selectedData?: ObjectInfo | null,
    createSuccess?: boolean,
    updateSuccess?: boolean,
    deleteSuccess?: boolean,
    listData?: any,
    isLoadingList?: boolean,
    isLoadingActors?: boolean,
    actorList?: any[]
    isLoadingSourceObjList?: boolean,
    objects: any[]
    isLoadingObjProperties?: boolean,
    objectProperties: ObjectProperty[]
    isModalShow?:boolean,
}

export interface ObjectProperty {
    id: number | null
    name: string
    meaning: string
    mandatory: boolean
    maxLength: number
    unique: boolean
    refProperty: number
    sourceObject: number
    createdBy: string
    status: number
    code: string
    version?: string
    description?: string
}

export const defaultState: CommonObjectState = {
    detail: {
        id: null,
        code: '',
        name: '',
        description: '',
        objectProperties: null,
        status: 0,
        actors: [],
        sourceObject: [],
        targetObject: [],
        screens: [],
        state: [],
        dataMigration: [],
        workFlow: [],
        useCase: [],
    },
    selectedData: null,
    isLoading: false,
    createSuccess: false,
    updateSuccess: false,
    deleteSuccess: false,
    isLoadingList: false,
    listData: [],
    isLoadingSourceObjList: false,
    objects: [],
    isLoadingObjProperties: false,
    objectProperties: [],
}
export enum ActionEnum {
    RESET_STATE = '@@MODULES/COMMON_OBJECT/RESET_STATE',

    CREATE_REQUEST = '@@MODULES/COMMON_OBJECT/CREATE_REQUEST',
    CREATE_SUCCESS = '@@MODULES/COMMON_OBJECT/CREATE_SUCCESS',
    CREATE_FAILED = '@@MODULES/COMMON_OBJECT/CREATE_FAILED',

    UPDATE_REQUEST = '@@MODULES/COMMON_OBJECT/UPDATE_REQUEST',
    UPDATE_SUCCESS = '@@MODULES/COMMON_OBJECT/UPDATE_SUCCESS',
    UPDATE_FAILED = '@@MODULES/COMMON_OBJECT/UPDATE_FAILED',

    GET_DETAIL_REQUEST = '@@MODULES/COMMON_OBJECT/GET_DETAIL_REQUEST',
    GET_DETAIL_SUCCESS = '@@MODULES/COMMON_OBJECT/GET_DETAIL_SUCCESS',
    GET_DETAIL_FAILED = '@@MODULES/COMMON_OBJECT/GET_DETAIL_FAILED',

    VIEW_DETAIL_REQUEST = '@@MODULES/COMMON_OBJECT/VIEW_DETAIL_REQUEST',
    VIEW_DETAIL_SUCCESS = '@@MODULES/COMMON_OBJECT/VIEW_DETAIL_SUCCESS',
    VIEW_DETAIL_FAILED = '@@MODULES/COMMON_OBJECT/VIEW_DETAIL_FAILED',

    GET_LIST_REQUEST = '@@MODULES/COMMON_OBJECT/GET_LIST_REQUEST',
    GET_LIST_SUCCESS = '@@MODULES/COMMON_OBJECT/GET_LIST_SUCCESS',
    GET_LIST_FAILED = '@@MODULES/COMMON_OBJECT/GET_LIST_FAILED',

    DELETE_REQUEST = '@@MODULES/COMMON_OBJECT/DELETE_REQUEST',
    DELETE_SUCCESS = '@@MODULES/COMMON_OBJECT/DELETE_SUCCESS',
    DELETE_FAILED = '@@MODULES/COMMON_OBJECT/DELETE_FAILED',

    GET_LIST_OBJECTS_REQUEST = '@@MODULES/COMMON_OBJECT/GET_LIST_OBJECTS_REQUEST',
    GET_LIST_OBJECTS_SUCCESS = '@@MODULES/COMMON_OBJECT/GET_LIST_OBJECTS_SUCCESS',
    GET_LIST_OBJECTS_FAILED = '@@MODULES/COMMON_OBJECT/GET_LIST_OBJECTS_FAILED',

    GET_LIST_FUNCTIONS_REQUEST = '@@MODULES/COMMON_OBJECT/GET_LIST_FUNCTIONS_REQUEST',
    GET_LIST_FUNCTIONS_SUCCESS = '@@MODULES/COMMON_OBJECT/GET_LIST_FUNCTIONS_SUCCESS',
    GET_LIST_FUNCTIONS_FAILED = '@@MODULES/COMMON_OBJECT/GET_LIST_FUNCTIONS_FAILED',

    GET_LIST_OBJECT_PROPERTIES_REQUEST = '@@MODULES/COMMON_OBJECT/GET_LIST_OBJECT_PROPERTIES_REQUEST',
    GET_LIST_OBJECT_PROPERTIES_SUCCESS = '@@MODULES/COMMON_OBJECT/GET_LIST_OBJECT_PROPERTIES_SUCCESS',
    GET_LIST_OBJECT_PROPERTIES_FAILED = '@@MODULES/COMMON_OBJECT/GET_LIST_OBJECT_PROPERTIES_FAILED',
    SET_MODAL_VISIBLE = '@@MODULES/COMMON_OBJECT/SET_MODAL_VISIBLE',
}
