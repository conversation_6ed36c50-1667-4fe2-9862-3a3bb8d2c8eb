import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import { createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, downloadTemplate, downloadTemplateSuccess, getDetailFailed, getDetailRequest, getDetailSuccess, getListActorFailed, getListActorRequest, getListActorSuccess, getListCBRFailed, getListCBRRequest, getListCBRSuccess, getListEmailFailed, getListEmailRequest, getListEmailSuccess, getListFailed, getListMessageFailed, getListMessageRequest, getListMessageSuccess, getListObjectFailed, getListObjectFilter, getListObjectFilterSuccess, getListObjectRequest, getListObjectSuccess, getListOtherReqFailed, getListOtherReqRequest, getListOtherReqSuccess, getListRequest, getListScreenFilter, getListScreenFilterSuccess, getListSuccess, getListUrFailed, getListUrRequest, getListUrSuccess, importFunction, importFunctionFailure, importFunctionValidate, importFunctionValidateFailure, importFunctionValidateSuccess, updateFailed, updateRequest, updateSuccess } from './action'



function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.USE_CASE + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}


function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.USE_CASE + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.function')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.function')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.USE_CASE, request as any)
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.function')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.function')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.USE_CASE + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.function')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.function')
    }
  }
}

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.USE_CASE}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}





function* handleImportFunctionValidate(action: Action) {
  if (importFunctionValidate.match(action)) {
    try {
      const fileList = action.payload
      const url = API_URLS.IMPORT_FILE_VALIDATE_USECASE;
      const res = yield call(apiCall, 'POST', url, fileList)
      yield put(importFunctionValidateSuccess(res.data));
      ShowAppMessage(null, MESSAGE_TYPES.IMPORT_VALIDATE, 'common.artefact.use-case')
    } catch (err) {
      ShowAppMessage(err, null, 'common.artefact.use-case')
      yield put(importFunctionValidateFailure(null))
    }
  }
}

function* handleDownload(action: Action) {
  if (downloadTemplate.match(action)) {
    try {
      window.open(API_URLS.IMPORT_USECASE, '_blank');
      yield put(downloadTemplateSuccess(null))
    } catch (err) {
      ShowAppMessage(err, null, 'common.artefact.use-case')
    }
  }
}

function* handleImportFunction(action: Action) {
  if (importFunction.match(action)) {
    try {
      const qualifiedData = action.payload
      const url = API_URLS.IMPORT_FILE_USECASE;
      const res = yield call(apiCall, 'POST', url, qualifiedData)
      ShowAppMessage(null, MESSAGE_TYPES.IMPORT, 'common.artefact.use-case')
    } catch (err) {
      importFunctionFailure(null)
      ShowAppMessage(err, null, 'common.artefact.use-case')
    }
  }
}

function* getListActorFlow(action: Action) {
  if (getListActorRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_ACTORS
      const res = yield call(apiCall, 'GET', url)
      yield put(getListActorSuccess(res.data))
    } catch (err) {
      yield put(getListActorFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}


function* getListObjectFlow(action: Action) {
  if (getListObjectRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_OBJECTS
      const res = yield call(apiCall, 'GET', url)
      yield put(getListObjectSuccess(res.data))
    } catch (err) {
      yield put(getListObjectFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* getListUrFlow(action: Action) {
  if (getListUrRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_USER_REQUIREMENTS);
      yield put(getListUrSuccess(res.data))
    } catch (err) {
      yield put(getListUrFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* getListOtherReqFlow(action: Action) {
  if (getListOtherReqRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_OTHER_REQUIREMENTS
      const res = yield call(apiCall, 'GET', url)
      yield put(getListOtherReqSuccess(res.data))
    } catch (err) {
      yield put(getListOtherReqFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* getListCBRFlow(action: Action) {
  if (getListCBRRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_BUSINESS_RULES
      const res = yield call(apiCall, 'GET', url)
      yield put(getListCBRSuccess(res.data))
    } catch (err) {
      yield put(getListCBRFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* getListMessagesFlow(action: Action) {
  if (getListMessageRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_MESSAGES
      const res = yield call(apiCall, 'GET', url)
      yield put(getListMessageSuccess(res.data))
    } catch (err) {
      yield put(getListMessageFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* getListEmailFlow(action: Action) {
  if (getListEmailRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_EMAIL_TEMPLATES
      const res = yield call(apiCall, 'GET', url)
      yield put(getListEmailSuccess(res.data))
    } catch (err) {
      yield put(getListEmailFailed(null))
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListObjectFilter(action: Action) {
  if (getListObjectFilter.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_OBJECTS)
      yield put(getListObjectFilterSuccess(res.data))
    } catch {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListScreenFilter(action: Action) {
  if (getListScreenFilter.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_SCREENS)
      yield put(getListScreenFilterSuccess(res.data))
    } catch {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(importFunctionValidate.type, handleImportFunctionValidate)
  yield takeLatest(importFunction.type, handleImportFunction)
  yield takeLatest(downloadTemplate.type, handleDownload)
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
  yield takeLatest(getListActorRequest.type, getListActorFlow)
  yield takeLatest(getListObjectRequest.type, getListObjectFlow)
  yield takeLatest(getListUrRequest.type, getListUrFlow)
  yield takeLatest(getListOtherReqRequest.type, getListOtherReqFlow)
  yield takeLatest(getListCBRRequest.type, getListCBRFlow)
  yield takeLatest(getListMessageRequest.type, getListMessagesFlow)
  yield takeLatest(getListEmailRequest.type, getListEmailFlow)
  yield takeLatest(getListObjectFilter.type, handleGetListObjectFilter)
  yield takeLatest(getListScreenFilter.type, handleGetListScreenFilter)
}
export default function* FunctionSaga() {
  yield all([fork(watchFetchRequest)])
}
