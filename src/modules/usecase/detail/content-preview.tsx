import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Typography } from "antd"
import intl from "../../../config/locale.config"
import LavAttachmentPreview from "../../../helper/component/lav-attachment-preview"
import LavReferences from "../../../helper/component/lav-references"
import { renderStatusBadge } from "../../../helper/share"
import TableComponent from './table'

const { Title, Text } = Typography

const UseCaseDetailInfo = ({ data }) => {
    return <Space direction="vertical">
        <Space size="large">
            {/* <span>
                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
            </span> */}
            {renderStatusBadge(data?.status)}
        </Space>

        <Card
            title={
                <Title level={5}>
                    {`${intl.formatMessage({
                        id: 'function.usecase-information',
                    })}`}
                </Title>
            }
            bordered={true}
        >
            <Row gutter={[16, 4]}>
                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-use-case-details.label.description',
                        })}
                    </Text>
                </Col>
                <Col span={20} className="description">
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: data?.description }}
                    ></div>
                </Col>

                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-use-case-details.label.trigger',
                        })}:
                    </Text>

                </Col>
                <Col span={20}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{
                            __html: data?.trigger,
                        }}
                    ></div>
                </Col>

                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-use-case-details.label.pre-condition',
                        })}:
                    </Text>

                </Col>
                <Col span={20}>
                    {data?.preConditions?.map((item: any) => (
                        <div
                            key={item.id}
                            className="tableDangerous"
                            dangerouslySetInnerHTML={{ __html: item.description }}
                        ></div>
                    ))}
                </Col>

                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-use-case-details.label.post-condition',
                        })}:
                    </Text>

                </Col>
                <Col span={20}>{data?.postCondition}</Col>

                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-use-case-details.label.activity-flow',
                        })}
                    </Text>

                </Col>
                <Col span={24}>
                    <LavAttachmentPreview attachment={data?.activeFlowPath} isCommon={false} />
                </Col>

                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-use-case-details.label.business-rule',
                        })}
                    </Text>

                </Col>

                <Col span={24}>
                    <TableComponent businessRule={data?.businessRules}></TableComponent>
                </Col>
            </Row>
        </Card>

        <LavReferences data={data} />
    </Space>
}
export default UseCaseDetailInfo