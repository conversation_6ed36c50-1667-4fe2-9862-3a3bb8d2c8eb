import { CommentState } from '@/modules/_shared/comment/type'
import AppState from '@/store/types'
import {
    Breadcrumb, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import React, { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../../../constants'
import AssignTaskDetail from '../../../../../helper/component/assign-task-detail'
import DeleteButton from '../../../../../helper/component/commonButton/DeleteButton'
import LavAttachmentPreview from '../../../../../helper/component/lav-attachment-preview'
import LavAuditTrail from '../../../../../helper/component/lav-audit-trail'
import LavButtons from '../../../../../helper/component/lav-buttons'
import LavEffortEstimation from '../../../../../helper/component/lav-efffort-estimation'
import LavImpact from '../../../../../helper/component/lav-impact'
import LavReferences from '../../../../../helper/component/lav-references'
import LavRelatedLinks from '../../../../../helper/component/lav-related-links'
import LavVersion from '../../../../../helper/component/lav-version/form'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../../../helper/share'
import TriggerComment from '../../../../_shared/comment/trigger-comment'
import { initComment, initCommentScreen, openComment } from '../../../../_shared/comment/action'
import TableComponent from '../../table'
import { LeftOutlined, RightOutlined, UpOutlined } from '@ant-design/icons'
import debounce from 'lodash.debounce'
import HistoryNavigation from '../../../../../modules/history/navigation'

const { Text, Title } = Typography
interface HistoryDetailsProps {
    data: any | [],
    id: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    isCollapsed? : boolean,
    setCollapsed: (boolean) => void,  
    setSelectedRowVersion: (version: string) => void,  
    setScreenMode: (mode: SCREEN_MODE) => void,
    onDismiss: () => void | null,
}
const HistoryDetails = ({ data, id, onChange, setSelectedRowVersion, isLoading, isModalShow, isCollapsed, setCollapsed, setScreenMode, onDismiss }: HistoryDetailsProps) => {
    const dispatch = useDispatch();
    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    //#region COMMENT INIT
    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'actor', title: intl.formatMessage({ id: 'view-use-case-details.label.actor' }), },
            { field: 'description', title: intl.formatMessage({ id: 'view-use-case-details.label.description' }), },
            { field: 'trigger', title: intl.formatMessage({ id: 'view-use-case-details.label.trigger' }), },
            { field: 'pre-condition', title: intl.formatMessage({ id: 'view-use-case-details.label.pre-condition' }), },
            { field: 'post-condition', title: intl.formatMessage({ id: 'view-use-case-details.label.post-condition' }), },
            { field: 'activity-flow', title: intl.formatMessage({ id: 'view-use-case-details.label.activity-flow' }), },
            { field: 'business-rule', title: intl.formatMessage({ id: 'view-use-case-details.label.business-rule' }), },
            { field: 'object', title: intl.formatMessage({ id: 'view-use-case-details.label.object' }), },
            { field: 'user-requirement', title: intl.formatMessage({ id: 'view-use-case-details.label.user-requirement' }), },
            { field: 'other-requirement', title: intl.formatMessage({ id: 'view-use-case-details.label.other-requirement' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'common.assign-task.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
            { field: 'message', title: intl.formatMessage({ id: 'view-use-case-details.label.message' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-use-case-details.label.req-elicitation' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-use-case-details.label.documentation' }), },
            { field: 'implementation', title: intl.formatMessage({ id: 'view-use-case-details.label.implementation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
            // { field: 'email-template', title: intl.formatMessage({ id: 'view-use-case-details.label.email-template' }), },
            // { field: 'cbr', title: intl.formatMessage({ id: 'view-use-case-details.label.cbr' }), },
            // { field: 'screen', title: intl.formatMessage({ id: 'view-use-case-details.label.screen' }), },
            // { field: 'workflow', title: intl.formatMessage({ id: 'view-use-case-details.label.workflow' }), },
            // { field: 'state-transition', title: intl.formatMessage({ id: 'view-use-case-details.label.statetransition' }), },
        ];
        data?.businessRules?.forEach((e) => {
            fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
        })

        data?.preConditions?.forEach((e) => {
            fields.push({ field: e.id ? e.id.toString() : '', title: e?.type })
        })
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.USE_CASE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + data?.name; 
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id && co?.artefactType === REQ_ARTEFACT_TYPE_ID.USECASE) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])

    //#endregion COMMENT INIT
    return data ? (
        <>
            <Space
                direction="vertical"
                size="middle"
                className="record-detail-right-control-container p-1rem"
            >
                <Row align="middle" justify="space-between">
                    <div>
                        <Breadcrumb className='rq-breadcrumb' separator=">">
                            <Breadcrumb.Item>
                                <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                            </Breadcrumb.Item>
                        </Breadcrumb>
                        <Title level={3} className='rq-page-title'>{data?.code}-{data?.name}</Title>
                    </div>
                    <Space size="small">
                        <LavButtons
                        url={`${API_URLS.SCREENS}/${data?.id}`}
                        reviewer={`${data?.reviewer}`}
                        customer={`${data?.customer}`}
                        artefact_type="common.artefact.common-usecase"
                        status={data?.status}
                        artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN}
                        id={id}
                        changePage={() => onChange()}>
                            <Button onClick={debounce(onDismiss, 500)}>
                                {intl.formatMessage({ id: 'common.action.close' })}
                            </Button>
                        </LavButtons>
                    </Space>
                </Row>
                <Divider className="mt-0 mb-0" />                      
                { data?.nextPrevious.latestVersion === data?.version ? <></>:
                   <HistoryNavigation data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.function"} artefactType={REQ_ARTEFACT_TYPE_ID.USECASE}/>
                }
                <Spin spinning={isLoading}>
                    <Scrollbars
                        autoHide
                    >
                        <Space direction="vertical">
                            <Space size="large">
                                <span >
                                    <TriggerComment field='version'>
                                        <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
                                    </TriggerComment>
                                </span>

                                {renderStatusBadge(data?.status)}
                            </Space>

                            <Card
                                title={
                                    <Title level={5}>
                                        {`${intl.formatMessage({
                                            id: 'function.usecase-information',
                                        })}`}
                                    </Title>
                                }
                                bordered={true}
                            >
                                <Row gutter={[16, 4]}>
                                    <Col span={4}>
                                        <TriggerComment field='actor'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.actor',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20} className="description">
                                        {data?.actors?.map((e, index) => {
                                            let content = ''
                                            if (index == 0) {
                                                content += e?.name
                                            } else {
                                                content += ', ' + e?.name
                                            }
                                            return content
                                        })}
                                    </Col>

                                    <Col span={4}>
                                        <TriggerComment field='description'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.description',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20} className="description">
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.description,
                                            }}
                                        ></div>
                                    </Col>

                                    <Col span={4}>
                                        <TriggerComment field='trigger'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.trigger',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20}>
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.trigger,
                                            }}
                                        ></div>
                                    </Col>

                                    <Col span={4}>
                                        <TriggerComment field='pre-condition'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.pre-condition',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20}>
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.preCondition,
                                            }}
                                        ></div>
                                    </Col>

                                    <Col span={4}>
                                        <TriggerComment field='post-condition'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.post-condition',
                                                })}:
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={20}>
                                        <div
                                            className="tableDangerous"
                                            dangerouslySetInnerHTML={{
                                                __html: data?.postCondition,
                                            }}
                                        ></div>
                                    </Col>

                                    <Col span={24}>
                                        <TriggerComment field='activity-flow'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.activity-flow',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>
                                    <Col span={24}>
                                        <LavAttachmentPreview attachment={data?.activeFlowPath} isCommon={false} />
                                    </Col>

                                    <Col span={24}>
                                        <TriggerComment field='business-rule'>
                                            <Text type="secondary">
                                                {intl.formatMessage({
                                                    id: 'view-use-case-details.label.business-rule',
                                                })}
                                            </Text>
                                        </TriggerComment>
                                    </Col>

                                    <Col span={24} className="use-case-br">
                                        <TableComponent businessRule={data?.businessRules}></TableComponent>
                                    </Col>
                                </Row>
                            </Card>

                            <LavReferences data={data} />

                            <AssignTaskDetail data={data} />
                            {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.USECASE} onChange={() => { }} isViewMode={true} />}
                            {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.USECASE} onChange={() => { }} isViewMode={true} /> : <></>} */}

                            <Row justify="space-between">
                                <Col span={8}>
                                    <LavEffortEstimation data={data} />
                                </Col>
                                <Col span={15}>
                                    <LavRelatedLinks data={data} />
                                </Col>
                            </Row>

                            <Row>
                                <Col span={24}>
                                    <LavVersion data={data?.versionHistories} screenMode={SCREEN_MODE.VIEW} />
                                </Col>
                            </Row>
                            <Row>
                                <Col span={24}>
                                    <LavAuditTrail data={data?.auditTrail} />                                    
                                </Col>                                
                            </Row>
                            <Button
                                            type="primary"
                                            shape="circle"
                                            icon={
                                                isCollapsed ?    <RightOutlined /> : <LeftOutlined />
                                            }
                                            style={{
                                                position: "fixed",
                                                bottom: 35,
                                                height:40,
                                                width:40,
                                            }}
                                            onClick={() => setCollapsed(!isCollapsed)}
                                            />
                        </Space>
                        
                    </Scrollbars>
                </Spin>
            </Space>
            {/* <Divider /> */}
        </>
    ) : <></>
}

export default HistoryDetails