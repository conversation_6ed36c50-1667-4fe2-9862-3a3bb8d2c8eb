import { SCREEN_MODE } from '../../../../constants'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { Table } from 'antd'
import React from 'react'
import intl from '../../../../config/locale.config'
import './style.css'

const TableComponent = (props) => {
  const columns = [
    {
      title: `${intl.formatMessage({
        id: 'view-use-case-details.column.step',
      })}`,
      dataIndex: 'step',
      width: '7%',
      sorter: (
        item1: any,
        item2: any,
      ) => {
        return item1.step - item2.step
      },
      render: (step: string, record: any) => {
        return <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(record?.id)}>
          <p>{`(${step})`}</p>
          </TriggerComment>
      },
    },
    {
      title: `${intl.formatMessage({
        id: 'view-use-case-details.column.brCode',
      })}`,
      dataIndex: 'code',
      width: '10%',
      // align: 'center',
      render:  (text) => {
        return text
      }
      
    },
    {
      title: `${intl.formatMessage({
        id: 'view-use-case-details.column.description',
      })}`,
      dataIndex: 'content',
      render: (content: string) => {
        return (
          <div
            className="tableDangerous"
            dangerouslySetInnerHTML={{ __html: content }}
          ></div>
        )
      },
    },
  ]
  const dataSource = props.businessRule;
  const data: any = []
  dataSource?.map((element) => {
    let { step, name, content, code, id } = element
    content = `<strong><u>${name}</u></strong>${content ? content : ''}`
    const loadData = {
      id: id,
      code: code,
      step: step,
      content: content
    }
    data.push(loadData)
  })

  return (
    <>
      <Table
        bordered
        dataSource={data}
        columns={columns}
        rowKey="id"
        pagination={false}
      />
    </>
  )
}

export default React.memo(TableComponent)
