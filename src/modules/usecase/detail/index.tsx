import AppState from '@/store/types'
import { LeftOutlined, MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined, RightOutlined, UpOutlined } from '@ant-design/icons'
import { Space, Switch } from "antd";
import { <PERSON><PERSON>, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import FunctionFormPage from '../form/form'
import { FunctionState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'
import HistoryScreen from '../../history';
import HistoryDetails from './history/details';
import AppCommonService from '../../../services/app.service';

const UseCaseDetailPage = (props) => {
  const dispatch = useDispatch()
  const history = useHistory()
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [collapsed, setCollapsed] = useState(false);
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  const state = useSelector<AppState | null>(
    (s) => s?.Function
  ) as FunctionState

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.useCaseID) {
      dispatch(getDetailRequest(props.match.params.useCaseID))      
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.USE_CASE + '/version/' + props.match.params.useCaseID +  '/' + selectedRowVersion).then((e) => {        
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);  
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.useCaseID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col style={{
            overflow: collapsed? 'hidden' : '', 
            maxWidth: collapsed ? '0%' : '20.83333333%', 
            transition: 'max-width 0.3s ease' }} span={5}>
          
            <LavLeftControl
              activeId={props.match.params.useCaseID}
              apiUrl={API_URLS.REFERENCES_FUNCTIONS}
              route={APP_ROUTES.USECASE_DETAIL}
              artefactType={REQ_ARTEFACT_TYPE_ID.USECASE}
              title='function.header.title'
              reload={reload}
              reloadSuccess={() => setReload(false)}
              handleCreate={handleCreate}
            >
              {
                (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && (<Button ghost={true}
                  type='primary'
                  className='lav-btn-create'
                  icon={<PlusOutlined />}
                  onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'function.button.create-uc' })}
                </Button>)
              }               
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>            
            <Col style={{ 
              maxWidth: collapsed ? '100%' : '79.16666667%', 
              transition: 'max-width 0.3s ease' }} span={collapsed ? 24 : 19}>
              <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} id={props.match.params.useCaseID} isModalShow={state?.isModalShow} setCollapsed={setCollapsed} isCollapsed={collapsed} setScreenMode={setScreenMode} />
            </Col>
          </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <FunctionFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <FunctionFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
              }} id={props.match.params.useCaseID} />
          </Col> : <></>
      }      
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col style={{ 
              maxWidth: collapsed ? '100%' : '79.16666667%', 
              transition: 'max-width 0.3s ease' }} span={collapsed ? 24 : 19}>
              <HistoryScreen artefact_type = "common.artefact.function"
                            apiURL = {API_URLS.HISTORY}
                            artefactType = {REQ_ARTEFACT_TYPE_ID.USECASE}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + state?.selectedData?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
                <Row>                                              
                  <Button
                        type="primary"
                        shape="circle"
                        icon={
                          collapsed ?    <RightOutlined /> : <LeftOutlined />
                        }
                        style={{
                            position: "fixed",
                            bottom: 35,
                            height:40,
                            width:40,
                            marginLeft: "30px"
                        }}
                        onClick={() => setCollapsed(!collapsed)}
                        /> 
                </Row>
            </Col>
          </>: <></>
      }      
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col style={{ 
              maxWidth: collapsed ? '100%' : '79.16666667%', 
              transition: 'max-width 0.3s ease' }} span={collapsed ? 24 : 19}>
              <HistoryDetails id={props.match.params.useCaseID} setSelectedRowVersion = {setSelectedRowVersion} isModalShow={state?.isModalShow} setCollapsed={setCollapsed} isCollapsed={collapsed}  onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
    </Row>
  )
}

export default UseCaseDetailPage
