import AppCommonService from '../../services/app.service'
import { Button, Select, Space, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import LavTable from '../../helper/component/lav-table'
import ExportButton from '../../helper/component/lav-table/export'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import Import from './import'
import './style.css'
import { PlusOutlined, CommentOutlined } from '@ant-design/icons'
import CustomSvgIcons from '../../helper/component/custom-icons'
import FunctionFormPage from './form/form'

const UsecasePage = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  const [reload, setReload] = useState(false)
  const [loadedColumns, setLoadedColumns] = useState<any>([])




  const getMember = async () => {
    const a = await Promise.all([
      AppCommonService.getMembers(),
      AppCommonService.getMembersBA(),
      AppCommonService.getCustomers()
    ])
    setLoadedColumns(configColumns(a[0].data, a[1].data, a[2].data))  
  }
  useEffect(() => {
    getMember()
    // AppCommonService.getMembers().then(res => setMembers(res.data)).catch((err) => setMembers([]));
    // AppCommonService.getMembersBA().then(res => setMemberReviewers(res.data)).catch((err) => setMemberReviewers([]));
    // AppCommonService.getCustomers().then(res => setCustomers(res.data)).catch((err) => setCustomers([]));
  }, [])

  useEffect(() => {       
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'function.header.title'}); 
  }, [screenMode])

  const onChangeAuthor = (e, record) => {
    const payload = {
      artefactType: REQ_ARTEFACT_TYPE_ID.USECASE,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefacts,
        artefactId: record?.id,
        author: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  // useEffect(() => {
  //   if(members.length != 0 && memberReviewer.length != 0 && customers.length != 0) {
  //     setLoadedColumns(configColumns(members, memberReviewer, customers))
  //   }
  // }, [members, memberReviewer, customers])
  const onChangeReviewer = (e, record) => {
    const payload = {
      artefactType: REQ_ARTEFACT_TYPE_ID.USECASE,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefacts,
        artefactId: record?.id,
        reviewer: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  const onChangeCustomer = (e, record) => {
    const payload = {
      artefactType: REQ_ARTEFACT_TYPE_ID.USECASE,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefacts,
        artefactId: record?.id,
        customer: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }
  const configColumns = (members, memberReviewer, customers) => [
    {
      title: intl.formatMessage({ id: 'common.action.code' }),
      dataIndex: 'code',
      width: '85px',
      editable: true,
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (code: any, record: any) => {
        return (
          <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DETAIL}${record.id}`}>{code} </Link>
        )
      }
    },
    {
      title: intl.formatMessage({ id: 'function.column.function-name' }),
      dataIndex: 'name',
      width: '45%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
      render: (text, record) => {
        return <Tooltip title={<div
          className="tableDangerous"
          style={{ color: 'white' }}
          dangerouslySetInnerHTML={{ __html: record?.description }}
        ></div>
        }>{text}</Tooltip>
      }
    },
    {
      title: intl.formatMessage({ id: 'function.column.author' }),
      width: '10%',
      dataIndex: 'author',
      ...getColumnDropdownFilterProps(members.map(e => { return { value: e.userName, text: e.fullName } }), 'author', true, false),
      render: (text, record) => {
        return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ? <Select
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          allowClear
          defaultValue={text}
          onChange={(e) => onChangeAuthor(e, record)}
          className='full-width assign-select'>
          {
            members.map(member => (
              <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
            ))
          }
        </Select> : text
      }
    },
    {
      title: intl.formatMessage({ id: 'function.column.reviewer' }),
      width: '10%',
      dataIndex: 'reviewer',
      ...getColumnDropdownFilterProps(memberReviewer.map(e => { return { value: e.userName, text: e.fullName } }), 'reviewer', true, false),
      render: (text, record) => {
        return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ? <Select
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          allowClear
          defaultValue={text}
          onChange={(e) => onChangeReviewer(e, record)}
          className='full-width assign-select'>
          {
            memberReviewer.map(member => (
              <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
            ))
          }
        </Select> : text
      }
    },
    {
      title: intl.formatMessage({ id: 'function.column.customer' }),
      width: '10%',
      dataIndex: 'customer',
      ...getColumnDropdownFilterProps(customers.map(e => { return { value: e.userName, text: e.fullName } }), 'customer', true, false),
      render: (text, record) => {
        return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ? <Select
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          allowClear
          defaultValue={text}
          onChange={(e) => onChangeCustomer(e, record)}
          className='full-width assign-select'>
          {
            customers.map(member => (
              <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
            ))
          }
        </Select> : text
      }
    },
    {
      title: intl.formatMessage({ id: 'function.column.status' }),
      dataIndex: 'status',
      width: '80px',
      ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      sorter: true,
      render: (record) => renderStatusBadge(record),
    },
  ]


  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'function.button.create-uc' })}
      </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (
      record.status !== STATUS.DELETE &&
      (hasRole(APP_ROLES.PM) ||
        hasRole(APP_ROLES.BA))
    ) ?
      children : <></>
  }
  const ImportComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && <Import onFinish={() => handleDataChange()} />
  }

  const ExportComponent: React.FC<any> = ({ handleExport }) => {
    return <ExportButton fileName='function.header.title' onExport={handleExport} title='function.button.export-function' />
  }

  const CommentComponent: React.FC<any> = ({ handleShowComment }) => {
    return <Button
      type='primary'
      className='lav-btn-create'
      icon={<CommentOutlined />}
      ghost={true}
      onClick={handleShowComment} >Comment</Button>
  }
  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {loadedColumns.length != 0 && screenMode === SCREEN_MODE.VIEW ? <LavTable
        title="function.header.title"
        artefact_type="common.artefact.use-case"
        apiUrl={API_URLS.USE_CASE}
        columns={loadedColumns}
        artefactType={REQ_ARTEFACT_TYPE_ID.USECASE}
        deleteComponent={DeleteComponent}
        updateComponent={UpdateComponent}
        createComponent={CreateComponent}
        exportComponent={ExportComponent}
        importComponent={ImportComponent}
        commentComponent={CommentComponent}
        isReload={reload}
        reloadSuccess={() => setReload(false)}
        hasComment={true}
      /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <FunctionFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <FunctionFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} id={id} /> : <></>
      }
    </Space>
  )
}

export default UsecasePage
