import { VersionType } from "../../constants"

export interface Detail {
  id: number | null
  code: string
  description: string
  product: any
  storage: string
  jira: string
  confluence: string
  name: string
  trigger: string
  postCondition: string
  activeFlowPath: any
  reqElicitation: number | null
  documentation: number | null
  implementation: number | null
  actors: any[]
  objects: any[]
  userRequirements: any[]
  otherRequirements: any[]
  commonBusinessRules: any[]
  messages: any[]
  emailTemplates: any[]
  status: number | null
  preCondition: string
  businessRules: any[]
  author: string
  reviewer: string
  customer: string
  completeDate: any
  dueDate: any
  projectId?: number
  impacts?: string
  versionHistories?: VersionType []
}


export interface FunctionState {
  isLoading: boolean
  detail: Detail | null
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  isLoadingList?: boolean
  listData?: any,
  selectedData?: Detail | null,
  listActors: any[]
  listObjects: any[]
  listUserRequirements: any[]
  listOtherRequirements: any[]
  listCommonBusinessRules: any[]
  listMessages: any[],
  listEmailTemplates: any[],
  importValidateResponse: any
  importResponse: any

  isModalShow?: boolean
}

export const defaultState: FunctionState = {
  isLoading: false,
  detail: {
    id: null,
    code: '',
    description: '',
    product: {},
    storage: '',
    jira: '',
    confluence: '',
    name: '',
    trigger: '',
    postCondition: '',
    activeFlowPath: '',
    reqElicitation: 0,
    documentation: 0,
    implementation: 0,
    actors: [],
    objects: [],
    userRequirements: [],
    otherRequirements: [],
    commonBusinessRules: [],
    messages: [],
    emailTemplates: [],
    status: null,
    preCondition: '',
    businessRules: [],
    author: '',
    reviewer: '',
    customer: '',
    completeDate: '',
    dueDate: '',
    impacts: '',
    versionHistories: [],
  },
  listActors: [],
  listObjects: [],
  listUserRequirements: [],
  listOtherRequirements: [],
  listCommonBusinessRules: [],
  listMessages: [],
  listEmailTemplates: [],
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  importValidateResponse: '',
  importResponse: '',
}

export enum PreConditionType {
  userPermission = 0,
  security = 1,
}

export const DEFAULT_DATA_BR = {
  editting: true,
  type: '',
  step: '',
  name: '',
  content: '',
}




export const DEFAULT_DATA_PRE = {
  editting: true,
  type: '',
  description: '',
}

export const preOptions: any = [
  { type: PreConditionType.userPermission, value: 'User Permission' },
  { type: PreConditionType.security, value: 'Security' },
]

export enum BRRule {
  screenDisplayingRules = 0,
  confirmationRules = 1,
  validatingRules = 2,
  creatingRules = 3,
  deletingRules = 4,
  updatingRules = 5,
  sendingEmailNotificationRules = 6,
  sendingMessageNotificationRules = 7,
  loggingInRules = 8,
  loggingOutRules = 9,
}
export const brOptions: any = [
  { type: BRRule.screenDisplayingRules, value: 'Screen Displaying Rules' },
  { type: BRRule.confirmationRules, value: 'Confirmation Rules' },
  { type: BRRule.validatingRules, value: 'Validating Rules' },
  { type: BRRule.creatingRules, value: 'Creating Rules' },
  { type: BRRule.deletingRules, value: 'Deleting Rules' },
  { type: BRRule.updatingRules, value: 'Updating Rules' },
  { type: BRRule.sendingEmailNotificationRules, value: 'Sending Email Notification Rules' },
  { type: BRRule.sendingMessageNotificationRules, value: 'Sending Message Notification Rules' },
  { type: BRRule.loggingInRules, value: 'Logging in Rules' },
  { type: BRRule.loggingOutRules, value: 'Logging out Rules' }
]

export enum ActionEnum {
  RESET_STATE = '@@MODULES/FUNCTION/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/FUNCTION/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/FUNCTION/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/FUNCTION/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/FUNCTION/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/FUNCTION/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/FUNCTION/UPDATE_FAILED',

  DELETE_REQUEST = '@@MODULES/FUNCTION/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/FUNCTION/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/FUNCTION/DELETE_FAILED',

  SAVE_IMG_ID = '@@MODULES/FUNCTION/SAVE_IMG_ID',

  GET_DETAIL_REQUEST = '@@MODULES/FUNCTION/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/FUNCTION/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/FUNCTION/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/FUNCTION/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/FUNCTION/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/FUNCTION/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/FUNCTION/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/FUNCTION/GET_LIST_FAILED',

  IMPORT_FILE_VALIDATE = '@@MODULES/FUNCTION/IMPORT_FILE_VALIDATE',
  IMPORT_FILE_VALIDATE_SUCCESS = '@@MODULES/FUNCTION/IMPORT_FILE_VALIDATE_SUCCESS',
  IMPORT_FILE_VALIDATE_FAILURE = '@@MODULES/FUNCTION/IMPORT_FILE_VALIDATE_FAILURE',
  IMPORT_FILE = '@@MODULES/FUNCTION/IMPORT_FILE',
  IMPORT_FILE_SUCCESS = '@@MODULES/FUNCTION/IMPORT_FILE_SUCCESS',
  IMPORT_FILE_FAILURE = '@@MODULES/FUNCTION/IMPORT_FILE_FAILURE',
  DOWNLOAD_FILE = '@@MODULES/FUNCTION/DOWNLOAD_FILE',
  DOWNLOAD_FILE_SUCCESS = '@@MODULES/FUNCTION/DOWNLOAD_FILE_SUCCESS',
  DOWNLOAD_FILE_FAILURE = '@@MODULES/FUNCTION/DOWNLOAD_FILE_FAILURE',

  GET_LIST_ACTORS_REQUEST = '@@MODULES/FUNCTION/GET_LIST_ACTORS_REQUEST',
  GET_LIST_ACTORS_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_ACTORS_SUCCESS',
  GET_LIST_ACTORS_FAILED = '@@MODULES/FUNCTION/GET_LIST_ACTORS_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/FUNCTION/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/FUNCTION/GET_LIST_OBJECTS_FAILED',

  GET_LIST_UR_REQUEST = '@@MODULES/FUNCTION/GET_LIST_UR_REQUEST',
  GET_LIST_UR_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_UR_SUCCESS',
  GET_LIST_UR_FAILED = '@@MODULES/FUNCTION/GET_LIST_UR_FAILED',

  GET_LIST_OTHER_REQ_REQUEST = '@@MODULES/FUNCTION/GET_LIST_OTHER_REQ_REQUEST',
  GET_LIST_OTHER_REQ_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_OTHER_REQ_SUCCESS',
  GET_LIST_OTHER_REQ_FAILED = '@@MODULES/FUNCTION/GET_LIST_OTHER_REQ_FAILED',

  GET_LIST_CBR_REQUEST = '@@MODULES/FUNCTION/GET_LIST_CBR_REQUEST',
  GET_LIST_CBR_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_CBR_SUCCESS',
  GET_LIST_CBR_FAILED = '@@MODULES/FUNCTION/GET_LIST_CBR_FAILED',

  GET_LIST_MESSAGES_REQUEST = '@@MODULES/FUNCTION/GET_LIST_MESSAGES_REQUEST',
  GET_LIST_MESSAGES_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_MESSAGES_SUCCESS',
  GET_LIST_MESSAGES_FAILED = '@@MODULES/FUNCTION/GET_LIST_MESSAGES_FAILED',

  GET_LIST_EMAIL_REQUEST = '@@MODULES/FUNCTION/GET_LIST_EMAIL_REQUEST',
  GET_LIST_EMAIL_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_EMAIL_SUCCESS',
  GET_LIST_EMAIL_FAILED = '@@MODULES/FUNCTION/GET_LIST_EMAIL_FAILED',

  GET_LIST_OBJECT_FILTER = '@@MODULES/FUNCTION/GET_LIST_OBJECT_FILTER',
  GET_LIST_OBJECT_FILTER_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_OBJECT_FILTER_SUCCESS',
  GET_LIST_OBJECT_FILTER_FAILURE = '@@MODULES/FUNCTION/GET_LIST_OBJECT_FILTER_FAILURE',

  GET_LIST_SCREEN_FILTER = '@@MODULES/FUNCTION/GET_LIST_SCREEN_FILTER',
  GET_LIST_SCREEN_FILTER_SUCCESS = '@@MODULES/FUNCTION/GET_LIST_SCREEN_FILTER_SUCCESS',
  GET_LIST_SCREEN_FILTER_FAILURE = '@@MODULES/FUNCTION/GET_LIST_SCREEN_FILTER_FAILURE',

  SET_MODAL_VISIBLE = '@@MODULES/FUNCTION/SET_MODAL_VISIBLE',
}
