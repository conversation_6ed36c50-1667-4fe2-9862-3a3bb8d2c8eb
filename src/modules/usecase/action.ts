import { createAction } from '@reduxjs/toolkit';
import { ActionEnum } from './type';
export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<any>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<any>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<any>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<any>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const createRequest = createAction<any>(ActionEnum.CREATE_REQUEST);
export const createSuccess = createAction<any>(ActionEnum.CREATE_SUCCESS);
export const createFailed = createAction<any>(ActionEnum.CREATE_FAILED);

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST);
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS);
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED);

export const deleteRequest = createAction<any>(ActionEnum.DELETE_REQUEST);
export const deleteSuccess = createAction<any>(ActionEnum.DELETE_SUCCESS);
export const deleteFailed = createAction<any>(ActionEnum.DELETE_FAILED);

export const importFunctionValidate = createAction<any>(ActionEnum.IMPORT_FILE_VALIDATE)
export const importFunctionValidateSuccess = createAction<any>(ActionEnum.IMPORT_FILE_VALIDATE_SUCCESS)
export const importFunctionValidateFailure = createAction<any>(ActionEnum.IMPORT_FILE_VALIDATE_FAILURE)

export const importFunction = createAction<any>(ActionEnum.IMPORT_FILE)
export const importFunctionSuccess = createAction<any>(ActionEnum.IMPORT_FILE_SUCCESS)
export const importFunctionFailure = createAction<any>(ActionEnum.IMPORT_FILE_FAILURE)

export const downloadTemplate = createAction<any>(ActionEnum.DOWNLOAD_FILE)
export const downloadTemplateSuccess = createAction<any>(ActionEnum.DOWNLOAD_FILE_SUCCESS)
export const downloadTemplateFailure = createAction<any>(ActionEnum.DOWNLOAD_FILE_FAILURE)

export const getListActorRequest = createAction<any>(ActionEnum.GET_LIST_ACTORS_REQUEST);
export const getListActorSuccess = createAction<any>(ActionEnum.GET_LIST_ACTORS_SUCCESS);
export const getListActorFailed = createAction<any>(ActionEnum.GET_LIST_ACTORS_FAILED);

export const getListObjectRequest = createAction<any>(ActionEnum.GET_LIST_OBJECTS_REQUEST);
export const getListObjectSuccess = createAction<any>(ActionEnum.GET_LIST_OBJECTS_SUCCESS);
export const getListObjectFailed = createAction<any>(ActionEnum.GET_LIST_OBJECTS_FAILED);

export const getListUrRequest = createAction<any>(ActionEnum.GET_LIST_UR_REQUEST);
export const getListUrSuccess = createAction<any>(ActionEnum.GET_LIST_UR_SUCCESS);
export const getListUrFailed = createAction<any>(ActionEnum.GET_LIST_UR_FAILED);

export const getListOtherReqRequest = createAction<any>(ActionEnum.GET_LIST_OTHER_REQ_REQUEST);
export const getListOtherReqSuccess = createAction<any>(ActionEnum.GET_LIST_OTHER_REQ_SUCCESS);
export const getListOtherReqFailed = createAction<any>(ActionEnum.GET_LIST_OTHER_REQ_FAILED);

export const getListCBRRequest = createAction<any>(ActionEnum.GET_LIST_CBR_REQUEST);
export const getListCBRSuccess = createAction<any>(ActionEnum.GET_LIST_CBR_SUCCESS);
export const getListCBRFailed = createAction<any>(ActionEnum.GET_LIST_CBR_FAILED);

export const getListMessageRequest = createAction<any>(ActionEnum.GET_LIST_MESSAGES_REQUEST);
export const getListMessageSuccess = createAction<any>(ActionEnum.GET_LIST_MESSAGES_SUCCESS);
export const getListMessageFailed = createAction<any>(ActionEnum.GET_LIST_MESSAGES_FAILED);

export const getListEmailRequest = createAction<any>(ActionEnum.GET_LIST_EMAIL_REQUEST);
export const getListEmailSuccess = createAction<any>(ActionEnum.GET_LIST_EMAIL_SUCCESS);
export const getListEmailFailed = createAction<any>(ActionEnum.GET_LIST_EMAIL_FAILED);

export const getListObjectFilter = createAction<any>(ActionEnum.GET_LIST_OBJECT_FILTER)
export const getListObjectFilterSuccess = createAction<any>(ActionEnum.GET_LIST_OBJECT_FILTER_SUCCESS)
export const getListObjectFilterFailure = createAction<any>(ActionEnum.GET_LIST_OBJECT_FILTER_FAILURE)

export const getListScreenFilter = createAction<any>(ActionEnum.GET_LIST_SCREEN_FILTER)
export const getListScreenFilterSuccess = createAction<any>(ActionEnum.GET_LIST_SCREEN_FILTER_SUCCESS)
export const getListScreenFilterFailure = createAction<any>(ActionEnum.GET_LIST_SCREEN_FILTER_FAILURE)
export const setModalVisible = createAction<any>(ActionEnum.SET_MODAL_VISIBLE)
