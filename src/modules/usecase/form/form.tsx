import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import { CheckOutlined, ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons'
import {
    Button, Card, Checkbox, Col, Form,
    Input, InputNumber, Modal, Row, Select, Space,
    Spin,
    Table,
    Tag,
    Tooltip,
    Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import ReactDragListView from 'react-drag-listview'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomAutoCompleteBR from '../../../helper/component/customBusinessRule'
import FormGroup from '../../../helper/component/form-group'
import LavAttachmentUpload from '../../../helper/component/lav-attachment-upload'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavImpact from '../../../helper/component/lav-impact'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, currentUserName, extractProjectCode, getProjectName, getReferencesFromEditor, hasRole, renderStatusBadge, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import AppState from '../../../store/types'
import { createRequest, getDetailRequest, getListActorRequest, getListCBRRequest, getListEmailRequest, getListMessageRequest, getListObjectRequest, getListOtherReqRequest, getListUrRequest, resetState, updateRequest } from '../action'
import { DEFAULT_DATA_BR, FunctionState } from '../type'
import { initComment, initCommentScreen } from './../../_shared/comment/action'
import LavVersion from '../../../helper/component/lav-version/form'
import AppCommonService from '../../../services/app.service'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select

interface FunctionFormModalProps {
    id?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}


const FunctionFormPage = ({ id, screenMode = SCREEN_MODE.CREATE, onFinish, onDismiss }: FunctionFormModalProps) => {
    const [form] = Form.useForm()
    const dispatch = useDispatch()
    const state = useSelector<AppState | null>(
        (s) => s?.Function
    ) as FunctionState
    const getCkeditorData: any = createRef()
    const getCkeditorDataDes: any = createRef()
    const businessRuleRef = createRef()
    const preConditionRef: any = createRef()
    const postConditionRef: any = createRef()
    const [attachment, setAttachment] = useState(null) as any
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const { height: windowHeight } = useWindowDimensions()
    const modalConfirmConfig = useModalConfirmationConfig()
    const attachmentRef = useRef<any>()
    const businessRulesRef = useRef<any>()
    const actorsRef = useRef<any>()
    const triggerRef = useRef<any>()
    const objectRef = useRef<any>()
    const conditionRef = useRef<any>()
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const [businessRules, setBusinessRules] = useState<any>([])
    // Destroy
    useEffect(() => {
        dispatch(getListActorRequest(null))
        dispatch(getListObjectRequest(null))
        dispatch(getListUrRequest(null))
        dispatch(getListCBRRequest(null))
        dispatch(getListOtherReqRequest(null))
        dispatch(getListMessageRequest(null))
        dispatch(getListEmailRequest(null))
        form.setFieldsValue({
            assignee: currentUserName(),
        })
        return () => {
            dispatch(resetState(null));
            form.resetFields()
            setBusinessRules([])

        }
    }, [])

    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(id))
        }
        
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'function.title-create' : 'function.title-update' }); 
    }, [screenMode, id])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }


    useEffect(() => {
        if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            const cloneBusinessRules = [...state.detail?.businessRules]
            const newBusinessRules = cloneBusinessRules.map((item, index) => {
                return { ...item, order: index }
            })


            setBusinessRules(newBusinessRules)

            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);
            form.setFieldsValue({
                name: state.detail?.name,
                description: state.detail?.description,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
                req: state.detail?.reqElicitation,
                documentation: state.detail?.documentation,
                implementation: state.detail?.implementation,
                postCondition: state.detail?.postCondition,
                trigger: state.detail?.trigger,
                preCondition: state.detail?.preCondition,
                actors: state.detail?.actors.map(
                    (actor: any) => actor?.name
                ),
                objs: state.detail?.objects.map(
                    (object: any) => object?.name
                ),
                userRequirement: state.detail?.userRequirements.map(
                    (userRequirement: any) => userRequirement?.name
                ),
                otherRequirement: state.detail?.otherRequirements.map(
                    (or: any) => or?.name
                ),
                assignee: state.detail?.author,
                reviewer: state.detail?.reviewer || '',
                customer: state.detail?.customer || '',
                dueDate: state.detail?.dueDate ? moment(new Date(state.detail?.dueDate)) : '',
                completeDate: state.detail?.completeDate ? moment(new Date(state.detail?.completeDate)) : '',
            })
            setAttachment(state.detail?.activeFlowPath)
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            const version = form.getFieldValue('version')
            const changeDescription = form.getFieldValue('changeDescription')

            if (version && version !== '') {
                const payload = {
                    version: version.substring(version.length - 1) === "." ? `${version}0` : version,
                    description: changeDescription,
                    artefactCode: state.detail?.code,
                }
                AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.USECASE, state.detail?.id).then((e) => {
                    if (isCreateMore) {
                        form.resetFields();
                        setBusinessRules([])

                        setAttachment(null)
                        form.setFieldsValue({
                            assignee: currentUserName(),
                            createMore: isCreateMore
                        })
                    } else {
                        if (onFinish) {
                            onFinish();
                        }
                        onDismiss();
                    }
                    setIsDraft(null);
                    setIsCreateMore(false);
                })
            } else {
                if (isCreateMore) {
                    form.resetFields();
                    setBusinessRules([])

                    setAttachment(null)
                    form.setFieldsValue({
                        assignee: currentUserName(),
                        createMore: isCreateMore
                    })
                } else {
                    if (onFinish) {
                        onFinish();
                    }
                    onDismiss();
                }
                setIsDraft(null);
                setIsCreateMore(false);
            }
        }
    }, [state.createSuccess, state.updateSuccess])
    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }
    useBeforeUnload()

    const onSubmit = debounce(async (values: any, st?: string) => {
        const lstActor: any[] = [];
        const lstObject: any[] = [];
        const lstUserRequirement: any[] = [];
        const lstOtherRequirement: any[] = [];
        values.actors?.forEach((actors) => {
            const actor: any = state.listActors.find(
                (item: any) => item.name === actors
            )
            if (actor) lstActor.push(actor?.id)
        })
        values.objs?.forEach((objs) => {
            const object: any = state.listObjects.find(
                (item: any) => item.name === objs
            )
            if (object) lstObject.push(object?.id)
        })
        values.userRequirement?.forEach((ur) => {
            const urObj: any = state.listUserRequirements.find(
                (item: any) => item.name === ur
            )
            if (urObj) lstUserRequirement.push(urObj?.id)
        })
        values.otherRequirement?.forEach((or) => {
            const orObj: any = state.listOtherRequirements.find(
                (item: any) => item.name === or
            )
            if (orObj) lstOtherRequirement.push(orObj?.id)
        })

        const triggerData = Object.assign(getCkeditorData?.current?.props?.data || '')
        // Collect all references
        let mentionReferences = getReferencesFromEditor(triggerData);
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(getCkeditorDataDes.current?.props?.data))

        try {
            businessRules.filter((e) => e.status !== -1)?.forEach((e) => {
                mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.content));
            })
        } catch (err) {
            console.log(err);
        }

        const checkBr = businessRules.filter(e => e.step == "" || e.name == "" || e.content == "")

        if (checkBr.length) {
            ShowMessgeAdditionalSubmit('EMSG_16');
            return;
        }

        let requestData: any = {
            id: id,
            name: values.name,
            code: null,
            trigger: getCkeditorData.current?.props?.data,
            status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            preCondition: preConditionRef.current?.props.data,
            businessRules: businessRules.filter((e) => e.status !== -1).map((e, index) => {
                return {
                    ...e,
                    order: index,
                    step: parseFloat(e.step)
                }
            }),
            postCondition: postConditionRef.current?.props.data,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
            version: values.version,
            description: getCkeditorDataDes?.current?.props?.data,
            reqElicitation: values.req,
            documentation: values.documentation,
            implementation: values.implementation,
            type: 0,
            activeFlowPath: attachment?.id,
            actors: lstActor ? lstActor : [],
            objects: lstObject ? lstObject : [],
            userRequirements: lstUserRequirement ? lstUserRequirement : [],
            otherRequirements: lstOtherRequirement ? lstOtherRequirement : [],
            author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
            reviewer: values.reviewer,
            customer: values.customer,
            dueDate: values.dueDate ? values.dueDate?.toDate() : null,
            completeDate: values.completeDate ? values.completeDate?.toDate() : null,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
            impacts: impacts
        };
        setIsCreateMore(values.createMore);
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            const checkBr = businessRules.filter(e => e.step == "" || e.name == "" || e.content == "")

            if (checkBr.length) {
                ShowMessgeAdditionalSubmit('EMSG_16');
                return;
            }
            // if (!attachment?.id) {
            //     attachmentRef.current.scrollIntoView('file')
            //     ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.use-case');
            //     return;
            // }

            if (!businessRules || businessRules.length === 0) {
                businessRulesRef.current.scrollIntoView('businessRules')
                ShowMessgeAdditionalSubmit('EMSG_16');
                return;
            }
            if (!requestData.actors || requestData.actors?.length === 0) {
                actorsRef.current.scrollIntoView('actors')
                ShowMessgeAdditionalSubmit('EMSG_17');
                return;
            }

            if (Object.keys(triggerData).length === 0) {
                triggerRef.current.scrollIntoView('trigger')
                ShowMessgeAdditionalSubmit('EMSG_18');
                return;
            }
            if (!requestData.objects || requestData.objects?.length === 0) {
                objectRef.current.scrollIntoView('objs')
                ShowMessgeAdditionalSubmit('EMSG_19');
                return;
            }

            confirm({
                ...modalConfirmConfig,
                content: `${intl.formatMessage(
                    { id: isDraft ? 'CFD_6_2' : 'CFD_6' },
                    {
                        Artefact: `${intl.formatMessage({
                            id: 'common.artefact.use-case',
                        })}`,
                    }
                )}`,
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() { },
            })
        }
    }, 500)

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }
    // handle Business Rule
    const handleAddBr = () => {
        setBusinessRules(
            [...businessRules, { order: businessRules.length, ...DEFAULT_DATA_BR }]
        );
    };


    const updateRecordBr = (order, partialRecord) => {
        const list = businessRules.map(record => {
            let nxtRecord = record;
            if (record.order === order) {
                nxtRecord = { ...nxtRecord, ...partialRecord };
            }
            return nxtRecord;
        })
        setBusinessRules(list)
    };

    const handleChangeNameBr = (order, name, type) => {
        const list = businessRules.map(record => {
            let nxtRecord = record;
            if (record.order === order) {
                nxtRecord = {
                    ...nxtRecord,
                    name: name,
                    type: name
                };
            }
            return nxtRecord;
        })
        setBusinessRules(list)
    }

    const handleChangeDesctiptionBr = (order, description) => {
        const list = businessRules.map(record => {
            let nxtRecord = record;
            if (record.order === order) {
                nxtRecord = {
                    ...nxtRecord,
                    content: description,
                };
            }
            return nxtRecord;
        })
        setBusinessRules(list)
    }

    const handleAntdCompChangeBr = (order, prop) => ({ target }) => {
        //check step
        const regex = /^\d*\.?\d*$/.test(target.value)
        if (!regex) {
            ShowMessgeAdditionalSubmit('EMSG_41');
        } else {
            updateRecordBr(order, { [prop]: target.value });
        }
    };

    /* const deleteRowBr = (index) => {
        const list = [...businessRules]
        list.splice(index, 1);
        setBusinessRules(list)
    } */

    const deleteRowBr = (rowIndex) => {
        confirm({
          ...modalConfirmConfig,
          content: `${intl.formatMessage({ id: 'CFD_1' })}`,
          onOk() {
            let currentSource: any = Object.assign([], businessRules);
            if (currentSource.length == 1) {
              currentSource = [];
            } else {
              currentSource.splice(rowIndex, 1);
            }
    
            let list = currentSource.map((e, index) => {
              return {
                ...e,
                order: index + 1,
              }
            })
            setBusinessRules(list);
          },
          onCancel() { },
        })  
      }

    const editRowBr = (order, data: any = {}) => {
        if ((data.step == "" || data.name == "" || data.content == "") && (data.editting)) {
            ShowMessgeAdditionalSubmit('EMSG_16');
            return;
        }

        // if (!data.step && !data.name && !data.content) {
        //     ShowMessgeAdditionalSubmit('EMSG_16');
        //     return;
        // }
        // if (!data.step) {
        //     ShowMessgeAdditionalSubmit('EMSG_16A');
        //     return;
        // }
        // if (!data.name) {
        //     ShowMessgeAdditionalSubmit('EMSG_16B');
        //     return;
        // }
        // if (!data.content) {
        //     ShowMessgeAdditionalSubmit('EMSG_16B');
        //     return;
        // }
        const list = businessRules.map(record => {
            let nxtRecord = record;
            if (record.order === order) {
                nxtRecord = {
                    ...nxtRecord,
                    editting: !nxtRecord.editting,
                };
            }
            return nxtRecord;
        })
        setBusinessRules(list)
    }

    const dragProps = {
        onDragEnd(fromIndex, toIndex) {
            const data = [...businessRules]
            const item = data.splice(fromIndex, 1)[0]
            data.splice(toIndex, 0, item)
            setBusinessRules(data)
        },
        handleSelector: 'tr',
        ignoreSelector: 'tr.ant-table-expanded-row',
        nodeSelector: 'tr.ant-table-row',
        enableScroll: true,
        scrollSpeed: 4,
    }



    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'actor', title: intl.formatMessage({ id: 'function.form.actor' }), },
            { field: 'description', title: intl.formatMessage({ id: 'function.form.description' }), },
            { field: 'trigger', title: intl.formatMessage({ id: 'function.form.trigger' }), },
            { field: 'pre-condition', title: intl.formatMessage({ id: 'function.form.pre-condition' }), },
            { field: 'post-condition', title: intl.formatMessage({ id: 'function.form.post-condition' }), },
            { field: 'activity-flow', title: intl.formatMessage({ id: 'function.form.activity-flow' }), },
            // { field: 'message', title: intl.formatMessage({ id: 'function.form.messages' }), },
            // { field: 'email-template', title: intl.formatMessage({ id: 'function.form.email-templates' }), },
            // { field: 'common-business-rule', title: intl.formatMessage({ id: 'function.form.common-business-rule' }), },
            { field: 'business-rule', title: intl.formatMessage({ id: 'view-use-case-details.label.business-rule' }), },
            { field: 'object', title: intl.formatMessage({ id: 'function.form.objects' }), },
            { field: 'user-requirement', title: intl.formatMessage({ id: 'function.form.user-requirement' }), },
            { field: 'other-requirement', title: intl.formatMessage({ id: 'function.form.other-requirement' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'common.assign-task.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'function.req-elicitation' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'function.documentation' }), },
            { field: 'implementation', title: intl.formatMessage({ id: 'function.implementation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];

        state.detail?.businessRules?.forEach((e) => {
            fields.push({ field: e.id ? e.id.toString() : '', title: e?.name })
        })

        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.USE_CASE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    const tagRender = (props) => {
        const { label, name, value, closable, onClose } = props;


        return (
            <Tag
                // color={value}
                // onMouseDown={onPreventMouseDown}
                closable={closable}
                onClose={onClose}
                style={{
                    marginRight: 3,
                    border: 'none',
                }}
                title={label}
            >
                {label.length > 20 ? label.substring(0, 20) + '...' : label}
            </Tag>
        );
    };


    //#endregion COMMENT INIT
    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);
    return <Spin spinning={state.isLoading}>
        <div>
            <Form
                form={form}
                onFinish={onSubmit}
                labelCol={{
                    span: 2,
                    offset: 0,
                }}
                scrollToFirstError={{ block: 'center' }}
            >
                <LavPageHeader
                    showBreadcumb
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'function.title-create' : 'function.title-update' })}
                >
                    <Space size="small">
                        {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                            style={{ marginBottom: '0px' }}
                            valuePropName="checked"
                            name="createMore"
                            wrapperCol={{ span: 24 }}
                        >
                            <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                        </Form.Item> : <></>}
                        <Button onClick={confirmCancel}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>

                        {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button htmlType="submit" type="primary" ghost onClick={() => {
                                    setIsDraft(false)
                                    setIsSubmitForm(true)
                                }}>
                                    {intl.formatMessage({ id: 'common.action.submit' })}
                                </Button>
                            </Form.Item> : <></>
                        }

                        <Form.Item style={{ marginBottom: '0px' }}>
                            <Button
                                className="success-btn"
                                htmlType="submit"
                                onClick={() => {
                                    setIsDraft(true)
                                    setIsSubmitForm(true)
                                }}
                            >
                                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                            </Button>
                        </Form.Item>
                    </Space>
                </LavPageHeader>
                <br />
                <Row align="middle">
                    {screenMode === SCREEN_MODE.EDIT ?
                        <Col span={5}>
                            <div className='status-container'>
                                <div>
                                    {intl.formatMessage({ id: 'common.field.status' })}
                                </div>
                                <div>
                                    {renderStatusBadge(state.detail?.status)}
                                </div>
                            </div>
                        </Col> : <></>
                    }
                </Row>
                <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                    <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'function.usecase-information' })}>
                        {screenMode == SCREEN_MODE.EDIT &&
                            <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                                <Form.Item rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                    <Input
                                        value={state.detail?.code}
                                        disabled
                                    ></Input>
                                </Form.Item>
                            </FormGroup>
                        }
                        <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                            <Form.Item
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: intl.formatMessage({ id: 'IEM_1' }),
                                    },
                                    { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                    {
                                        validator: async (rule, value) => {
                                            if (value && value.trim().length === 0) {
                                                throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                            }
                                        },
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={`${intl.formatMessage({
                                        id: `function.place-holder.use-case-name`,
                                    })}${intl.formatMessage({
                                        id: `common.mandatory.*`,
                                    })}`}
                                    maxLength={255}
                                />
                            </Form.Item>
                        </FormGroup>
                        <div ref={actorsRef}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={3} controlSpan={21} label={
                                <TriggerComment screenMode={screenMode} field='actor'>
                                    {intl.formatMessage({ id: 'function.form.actor' })}
                                </TriggerComment>}>
                                <Form.Item name="actors" labelAlign="left" rules={[{ required: isDraft ? false : true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                    <Select
                                        mode="multiple"
                                        style={{ width: '100%' }}
                                        optionLabelProp="label"
                                        allowClear
                                    >
                                        {state.listActors?.map(
                                            (item: any, index: number) =>
                                                item.status !== STATUS.DELETE &&
                                                item.status !== STATUS.CANCELLED && (
                                                    <Option key={index} value={item.name}>
                                                        {item.name}
                                                    </Option>
                                                )
                                        )}
                                    </Select>
                                </Form.Item>
                            </FormGroup>
                        </div>

                        <FormGroup className="rq-fg-comment" inline labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field='description'>
                                {intl.formatMessage({ id: 'function.form.description' })}
                            </TriggerComment>}>
                            <Form.Item
                                name="description"
                                labelAlign="left"
                                rules={[{
                                    validator: async (rule, value) => {
                                        const description = getCkeditorDataDes?.current?.props?.data
                                        if (isDraft == false && (description == '' || description == undefined)) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    }
                                }]}
                                wrapperCol={{ span: 24 }}
                            >
                                <CkeditorMention
                                    ref={getCkeditorDataDes}
                                    data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
                                />
                            </Form.Item>
                        </FormGroup>

                        <div ref={triggerRef} style={{ marginTop: 10 }}>
                            <FormGroup inline labelSpan={3} controlSpan={21} label={
                                <TriggerComment screenMode={screenMode} field='trigger'>
                                    {intl.formatMessage({ id: 'function.form.trigger' })}
                                </TriggerComment>}>
                                <Form.Item
                                    name="trigger"
                                    labelAlign="left"
                                    wrapperCol={{ span: 24 }}
                                >
                                    <CkeditorMention
                                        ref={getCkeditorData}
                                        data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.trigger}
                                    />
                                </Form.Item>
                            </FormGroup>
                        </div>

                        <div ref={conditionRef} style={{ marginBottom: '10px' }}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={3} controlSpan={21} label={
                                <div className='tooltips-container'>
                                    <TriggerComment screenMode={screenMode} field='pre-condition'>
                                        {intl.formatMessage({ id: 'function.form.pre-condition' })}
                                    </TriggerComment>
                                </div>}

                            >
                                <Form.Item
                                    name="preCondition"
                                    labelAlign="left"
                                    rules={[{
                                        validator: async (rule, value) => {
                                            const preCondition = preConditionRef?.current?.props?.data
                                            if (isDraft == false && (preCondition == '' || preCondition == undefined)) {
                                                throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                            }
                                        }
                                    }]}
                                    wrapperCol={{ span: 24 }}
                                >
                                    <CkeditorMention ref={preConditionRef} data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.preCondition} />
                                </Form.Item>
                            </FormGroup>
                        </div>

                        <FormGroup className="rq-fg-comment" inline labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field='post-condition'>
                                {intl.formatMessage({ id: 'function.form.post-condition' })}
                            </TriggerComment>}>
                            <Form.Item
                                name="postCondition"
                                labelAlign="left"
                                wrapperCol={{ span: 24 }}
                                rules={[
                                    {
                                        validator: async (rule, value) => {
                                            if (isDraft == false && postConditionRef.current?.props.data == '') {
                                                throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                            }
                                        },
                                    },
                                ]}
                            >
                                <CkeditorMention ref={postConditionRef} data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.postCondition} />
                            </Form.Item>
                        </FormGroup>

                        <div ref={attachmentRef}>
                            <FormGroup label={
                                <TriggerComment screenMode={screenMode} field='activity-flow'>
                                    {intl.formatMessage({ id: 'function.form.activity-flow' })}
                                </TriggerComment>}>
                                <Form.Item name="image">
                                    <LavAttachmentUpload artefactType={REQ_ARTEFACT_TYPE_ID.USECASE} attachment={attachment} isCommon={false} name="file" supportPDF onChange={setAttachment} />
                                </Form.Item>
                            </FormGroup>
                        </div>

                        <div ref={businessRulesRef} style={{ marginBottom: '10px' }}>
                            <FormGroup className='scrollButtonUC' inline label={
                                <div className='tooltips-container'>
                                    <TriggerComment screenMode={screenMode} field='business-rule'>
                                        {intl.formatMessage({ id: 'function.form.br-rule' })}
                                    </TriggerComment>
                                    <Tooltip title={intl.formatMessage({ id: 'common.usercase.tooltip-br' })} className='tooltips-icon'>
                                        <ExclamationCircleOutlined />
                                    </Tooltip>
                                </div>}>
                                <Space direction="vertical" size="small">
                                    <Row justify='end'>
                                        <Button type="primary" onClick={handleAddBr} icon={<PlusOutlined />} >
                                            {
                                                intl.formatMessage({
                                                    id: 'function.form.new-business-rule',
                                                })
                                            }
                                        </Button>{" "}
                                    </Row>
                                </Space>
                            </FormGroup>
                            <div style={{ marginTop: 10 }}>
                                <ReactDragListView {...dragProps}>
                                    <Table
                                        pagination={false}
                                        bordered
                                        columns={
                                            [
                                                {
                                                    title: "Step",
                                                    dataIndex: "step",
                                                    width: '5%',
                                                    align: 'center',
                                                    render: (step,
                                                        {
                                                            order,
                                                            editting = false,
                                                            id
                                                        }) => {
                                                        //if (!editting) {
                                                        //    return <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(id)}>
                                                        //        {step}
                                                        //    </TriggerComment>
                                                        //} return (
                                                        //    <Input
                                                        //        style={{ textAlign: 'center' }}
                                                        //        value={step}
                                                        //        maxLength={4}
                                                        //       onChange={
                                                        //            handleAntdCompChangeBr(order,
                                                        //                "step")
                                                        //        } />
                                                        //    
                                                        //    );
                                                        return (
                                                            <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(id)}>
                                                                {editting ? (
                                                                    <Input
                                                                        style={{ textAlign: 'center'}}
                                                                        value={step}
                                                                        maxLength={4}
                                                                        onChange={handleAntdCompChangeBr(order, "step")}
                                                                    />
                                                                ): (
                                                                    step
                                                                )}
                                                            </TriggerComment>
                                                        );
                                                    }
                                                },
                                                {
                                                    title: "BR Code",
                                                    dataIndex: "code",
                                                    align: 'center',
                                                    width: '7.5%',
                                                },
                                                {
                                                    title: "Description",
                                                    dataIndex: "content",
                                                    width: '84.5%',
                                                    render: (description,
                                                        {
                                                            order,
                                                            editting = false,
                                                            name
                                                        }) => {
                                                        if (!editting) {
                                                            const content = `<strong><u>${name}</u></strong>${description}`
                                                            return <div
                                                                className="tableDangerous"
                                                                dangerouslySetInnerHTML={{ __html: content }}
                                                            ></div>
                                                        }
                                                        return (<Space direction="vertical" size="small" >
                                                            <CustomAutoCompleteBR
                                                                order={order}
                                                                defaultValue={name}
                                                                handleChangeNameBr={handleChangeNameBr}
                                                            />
                                                            <CkeditorMention ref={businessRuleRef} data={description} saveDataPre={(e) => { handleChangeDesctiptionBr(order, e) }} placeholder={`${intl.formatMessage({ id: `function.place-holder-brDescription` })}`} />
                                                        </Space>);
                                                    }
                                                },
                                                {
                                                    title: "Action",
                                                    dataIndex: "action",
                                                    align: 'center',
                                                    width: '3%',
                                                    render: (data, record, index) => {
                                                        return (<div style={{ display: 'flex' }}>
                                                            {
                                                                record.editting ? <Button icon={<CheckOutlined name="EditCustomIcon" />} type="link" onClick={() => editRowBr(record.order, record)}></Button>
                                                                    : <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={() => editRowBr(record.order, record)}></Button>
                                                            }
                                                            <Button type="text" icon={<CustomSvgIcons name="DeleteCustomIcon" />} onClick={() => deleteRowBr(index)} />
                                                        </div>)
                                                    }
                                                }
                                            ]
                                        } dataSource={businessRules} />
                                </ReactDragListView>
                            </div>
                        </div>
                    </Card >

                    <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'function.reference' })}>
                        <div ref={objectRef}>
                            <FormGroup inline labelSpan={3} controlSpan={21} label={
                                <TriggerComment screenMode={screenMode} field='object'>
                                    {intl.formatMessage({ id: 'function.form.objects' })}
                                </TriggerComment>}>
                                <Form.Item name="objs" wrapperCol={{ span: 24 }}>
                                    <Select
                                        filterOption={(input, option: any) =>
                                            option.children
                                                .toLowerCase()
                                                .indexOf(input.toLowerCase()) >= 0
                                        }
                                        mode="multiple"
                                        optionLabelProp="label"
                                        showSearch
                                        allowClear
                                        tagRender={tagRender}
                                    >
                                        {state.listObjects?.map(
                                            (item: any, index: number) =>
                                                item.status !== STATUS.DELETE &&
                                                item.status !== STATUS.CANCELLED && (
                                                    <Option key={index} value={item.name} label={item.name}>{item.name}</Option>
                                                )
                                        )}
                                    </Select>
                                </Form.Item>
                            </FormGroup>
                        </div>

                        <FormGroup inline labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field='user-requirement'>
                                {intl.formatMessage({ id: 'function.form.user-requirement' })}
                            </TriggerComment>}>
                            <Form.Item name="userRequirement" wrapperCol={{ span: 24 }}>
                                <Select mode="multiple" optionLabelProp="label" tagRender={tagRender}>
                                    {state.listUserRequirements?.map(
                                        (item: any, index: number) =>
                                            item.status !== STATUS.DELETE &&
                                            item.status !== STATUS.CANCELLED && (
                                                <Option key={index} value={item.name} label={item.name}>
                                                    {item.name}
                                                </Option>
                                            )
                                    )}
                                </Select>
                            </Form.Item>
                        </FormGroup>

                        <FormGroup inline labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field='other-requirement'>
                                {intl.formatMessage({ id: 'function.form.other-requirement' })}
                            </TriggerComment>}>
                            <Form.Item name="otherRequirement" wrapperCol={{ span: 24 }}>
                                <Select mode="multiple" optionLabelProp="label" tagRender={tagRender}>
                                    {state.listOtherRequirements?.map(
                                        (item: any, index: number) =>
                                            item.status !== STATUS.DELETE &&
                                            item.status !== STATUS.CANCELLED && (
                                                <Option key={index} value={item.name} label={item.name}>
                                                    {item.name}
                                                </Option>
                                            )
                                    )}
                                </Select>
                            </Form.Item>
                        </FormGroup>
                    </Card>
                    <AssignTaskComponent
                        form={form}
                        data={screenMode === SCREEN_MODE.EDIT ?
                            state.detail :
                            null}
                        isSubmit={!isDraft}
                        screenMode={screenMode}
                    />
                    {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.USECASE} onChange={onChange} isSubmitForm={isSubmitForm} />}

                    <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'label-effort-estimation-hour' })}>
                        <Row>
                            <Col span={8}>
                                <FormGroup inline labelSpan={9} controlSpan={10} label={
                                    <TriggerComment screenMode={screenMode} field="req-elicitation">
                                        {intl.formatMessage({ id: 'label-req-elicitation' })}
                                    </TriggerComment>}>
                                    <Form.Item name="req">
                                        <InputNumber min={0} maxLength={2} max={99} />
                                    </Form.Item>
                                </FormGroup>
                            </Col>
                            <Col span={8}>
                                <div style={{ paddingLeft: 10 }}>
                                    <FormGroup inline labelSpan={10} controlSpan={10} label={
                                        <TriggerComment screenMode={screenMode} field="documentation">
                                            {intl.formatMessage({ id: 'label-documentation' })}
                                        </TriggerComment>}>
                                        <Form.Item name="documentation">
                                            <InputNumber min={0} maxLength={2} max={99} />
                                        </Form.Item>
                                    </FormGroup>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div style={{ paddingLeft: 0 }}>
                                    <FormGroup inline labelSpan={8} controlSpan={10} label={
                                        <TriggerComment screenMode={screenMode} field="implementation">
                                            {intl.formatMessage({ id: 'label-implementation' })}
                                        </TriggerComment>}>
                                        <Form.Item name="implementation">
                                            <InputNumber min={0} maxLength={2} max={99} />
                                        </Form.Item>
                                    </FormGroup>
                                </div>
                            </Col>
                        </Row>
                    </Card>

                    <LavRelatedLinksForm form={form} screenMode={screenMode} />

                    {
                        screenMode === SCREEN_MODE.EDIT ? <LavVersion screenMode={screenMode} data={state?.detail?.versionHistories} form={form}></LavVersion> : <></>
                    }
                </Space >
            </Form >
        </div >
    </Spin >
}

export default FunctionFormPage
