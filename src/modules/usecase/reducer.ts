import { createReducer } from '@reduxjs/toolkit'
import {
  createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, downloadTemplate,
  downloadTemplateFailure,
  downloadTemplateSuccess,
  getDetailFailed,
  getDetailRequest,
  getDetailSuccess,
  getListActorFailed,
  getList<PERSON>ctorRequest,
  getListActorSuccess,
  getListCBRFailed,
  getListCBRRequest,
  getListCBRSuccess,
  getListEmailFailed,
  getListEmailRequest,
  getListEmailSuccess,
  getListFailed,
  getListMessageFailed,
  getListMessageRequest,
  getListMessageSuccess,
  getListObjectFailed,
  getListObjectFilter,
  getListObjectFilterFailure,
  getListObjectFilterSuccess,
  getListObjectRequest,
  getListObjectSuccess,
  getListOtherReqFailed,
  getListOtherReqRequest,
  getListOtherReqSuccess,
  getListRequest, getListScreenFilter, getListScreenFilterFailure, getListScreenFilterSuccess, getListSuc<PERSON>, getListUrFailed, getListUrRequest, getListUrSuccess, importFunction, importFunctionFailure, importFunctionSuccess, importFunctionValidate,
  importFunctionValidateFailure,
  importFunctionValidateSuccess, resetState, setModalVisible, updateFailed, updateRequest, updateSuccess
} from './action'
import { defaultState, FunctionState } from './type'


const initState: FunctionState = defaultState

const reducer = createReducer(initState, (builder) => {
  return builder
  .addCase(resetState, (state, action?) => {
    Object.assign(state, {
      ...defaultState,
      selectedData: state.selectedData,
      listData: state.listData
    });
  })

  .addCase(getListRequest, (state, action?) => {
    state.isLoadingList = true;
  })
  .addCase(getListSuccess, (state, action) => {
    state.isLoadingList = false
    state.listData = action.payload
  })
  .addCase(getListFailed, (state, action) => {
    state.isLoadingList = false
    state.listData = null
  })
  
  .addCase(createRequest, (state, action?) => {
    state.isLoading = true;
    state.createSuccess = false;
  })
  .addCase(createSuccess, (state, action) => {
    state.isLoading = false;
    state.createSuccess = true;
  })
  .addCase(createFailed, (state, action) => {
    state.isLoading = false;
    state.createSuccess = false;
  })
  .addCase(updateRequest, (state, action?) => {
    state.isLoading = true;
    state.updateSuccess = false;
  })
  .addCase(updateSuccess, (state, action) => {
    state.isLoading = false;
    state.updateSuccess = true;
  })
  .addCase(updateFailed, (state, action) => {
    state.isLoading = false;
    state.updateSuccess = false;
  })


  .addCase(deleteRequest, (state, action?) => {
    state.deleteSuccess = false;
  })
  .addCase(deleteSuccess, (state, action) => {
    state.deleteSuccess = true;
  })
  .addCase(deleteFailed, (state, action) => {
    state.deleteSuccess = false;
  })

  .addCase(getDetailRequest, (state, action?) => {
    state.isLoading = true;
  })
  .addCase(getDetailSuccess, (state, action) => {
    state.isLoading = false
    state.detail = action.payload
    state.selectedData = action.payload
  })
  .addCase(getDetailFailed, (state, action) => {
    state.isLoading = false
    state.detail = null
    state.selectedData = null
  })

    .addCase(importFunctionValidate, (state, action) => {
      state.isLoading = true
    })

    .addCase(importFunctionValidateSuccess, (state, action) => {
      state.isLoading = false
      state.importValidateResponse = action.payload
    })

    .addCase(importFunctionValidateFailure, (state, action) => {
      state.isLoading = false
      state.importValidateResponse = action.payload
    })

    .addCase(importFunction, (state, action) => {
      state.isLoading = true
    })

    .addCase(importFunctionSuccess, (state, action) => {
      state.isLoading = false
      state.importResponse = action.payload
    })

    .addCase(importFunctionFailure, (state, action) => {
      state.isLoading = false
      state.importResponse = action.payload
    })

    .addCase(downloadTemplate, (state, action) => {
      state.isLoading = true
    })
    .addCase(downloadTemplateSuccess, (state, action) => {
      state.isLoading = false
    })
    .addCase(downloadTemplateFailure, (state, action) => {
      state.isLoading = false
    })

    .addCase(getListActorRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getListActorSuccess, (state, action) => {
      state.isLoading = false
      state.listActors = action.payload
    })
    .addCase(getListActorFailed, (state, action) => {
      state.isLoading = false
      state.listActors = []
    })

    .addCase(getListObjectRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getListObjectSuccess, (state, action) => {
      state.isLoading = false
      state.listObjects = action.payload
    })
    .addCase(getListObjectFailed, (state, action) => {
      state.isLoading = false
      state.listObjects = []
    })

    .addCase(getListUrRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getListUrSuccess, (state, action) => {
      state.isLoading = false
      state.listUserRequirements = action.payload
    })
    .addCase(getListUrFailed, (state, action) => {
      state.isLoading = false
      state.listUserRequirements = []
    })

    .addCase(getListOtherReqRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getListOtherReqSuccess, (state, action) => {
      state.isLoading = false
      state.listOtherRequirements = action.payload
    })
    .addCase(getListOtherReqFailed, (state, action) => {
      state.isLoading = false
      state.listOtherRequirements = []
    })

    .addCase(getListCBRRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getListCBRSuccess, (state, action) => {
      state.isLoading = false
      state.listCommonBusinessRules = action.payload
    })
    .addCase(getListCBRFailed, (state, action) => {
      state.isLoading = false
      state.listCommonBusinessRules = []
    })

    .addCase(getListMessageRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getListMessageSuccess, (state, action) => {
      state.isLoading = false
      state.listMessages = action.payload
    })
    .addCase(getListMessageFailed, (state, action) => {
      state.isLoading = false
      state.listMessages = []
    })

    .addCase(getListEmailRequest, (state, action?) => {
      state.isLoading = true;
    })
    .addCase(getListEmailSuccess, (state, action) => {
      state.isLoading = false
      state.listEmailTemplates = action.payload
    })
    .addCase(getListEmailFailed, (state, action) => {
      state.isLoading = false
      state.listEmailTemplates = []
    })

    .addCase(getListObjectFilter, (state, action) => {
      state.isLoading = true
    })

    .addCase(setModalVisible, (state, action) => {
      state.isModalShow = action.payload
      if(!action.payload){
        state.createSuccess = false;
        state.updateSuccess = false;
      }
    })
})

export default reducer
export { initState as FunctionState }

