import { VersionType } from "../../constants"

export interface ObjectRelationshipDiagramState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: ObjectRelationshipDiagramDetail | null,
  selectedData?: ObjectRelationshipDiagramDetail | null,
  isModalShow?: boolean
}
export interface ObjectRelationshipDiagramDetail {
  id?: number | null,
  code: string,
  name: string,
  diagram: number | null,
  description: string,
  storage: string
  jira: string
  confluence: string
  reqElicitation: string,
  documentation: string,
  status: number,
  author: string,
  reviewer: string,
  customer: string
  dueDate: string,
  completeDate: string,
  projectId?: number,
  impacts: string
  versionHistories?: VersionType []
}

export const defaultState : ObjectRelationshipDiagramState = {
  detail: {
    id: null,
    code: '',
    name: '',
    diagram: null,
    description: '',
    storage: '',
    jira: '',
    confluence: '',
    reqElicitation: '',
    documentation: '',
    status: 0,
    author: '',
    reviewer: '',
    dueDate: '',
    customer: '',
    completeDate: '',
    impacts: '',
    versionHistories: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/OBJECT_RELATIONSHIP_DIAGRAM/SET_MODAL_VISIBLE',
}
