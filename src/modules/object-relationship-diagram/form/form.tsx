import LavVersion from '../../../helper/component/lav-version/form'
import AppState from '@/store/types'
import {
    Button,
    Card, Checkbox, Col, Form, Input, Modal, Row, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavAttachmentUpload from '../../../helper/component/lav-attachment-upload'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { ObjectRelationshipDiagramState } from '../type'
import AppCommonService from '../../../services/app.service'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'
const { Text } = Typography
const { confirm } = Modal

interface ObjectRelationshipDiagramFormModalProps {
    objectRelationshipID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}
const ObjectRelationshipDiagramFormPage = ({ objectRelationshipID, screenMode, onFinish, onDismiss }: ObjectRelationshipDiagramFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.ObjectRelationship) as ObjectRelationshipDiagramState
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const { height: windowHeight } = useWindowDimensions()
    const modalConfirmConfig = useModalConfirmationConfig()
    const getCkeditorDataDes: any = createRef()
    const [attachment, setAttachment] = useState(null) as any
    const attachmentRef = useRef<any>()
    // Destroy
    useEffect(() => {
        form.setFieldsValue({
            assignee: currentUserName()
        })
        return () => {
            dispatch(resetState(null));
            setAttachment(null)
            resetForm();
            form.resetFields(['createMore']);
        }
    }, [])

    useEffect(() => {
        if (objectRelationshipID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(objectRelationshipID))
        }
        
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'ord.title-create' : 'ord.title-update' });
    }, [screenMode, objectRelationshipID])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }

    useEffect(() => {
        if (objectRelationshipID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);

            form.setFieldsValue({
                code: state.detail.code,
                objectrelationshipName: state.detail.name,
                description: state.detail.description,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
                req: state.detail.reqElicitation,
                documentation: state.detail.documentation,
            })
            setAttachment(state.detail?.diagram)
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            const version = form.getFieldValue('version')
            const changeDescription = form.getFieldValue('changeDescription')

            if (version && version !== '') {
                const payload = {
                    version: version.substring(version.length - 1) === "." ? `${version}0` : version,
                    description: changeDescription,
                    artefactCode: state.detail?.code,
                }
                AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM, state.detail?.id).then((e) => {
                    if (isCreateMore) {
                        resetForm();
                        form.setFieldsValue({
                            assignee: currentUserName(),
                            dueDate: moment(new Date()),
                        })
                    } else {
                        if (onFinish) {
                            onFinish();
                        }
                        onDismiss();
                    }
                    setIsDraft(null);
                    setIsCreateMore(false);
                })
            } else {
                if (isCreateMore) {
                    resetForm();
                    form.setFieldsValue({
                        assignee: currentUserName(),
                        dueDate: moment(new Date()),
                    })
                } else {
                    if (onFinish) {
                        onFinish();
                    }
                    onDismiss();
                }
                setIsDraft(null);
                setIsCreateMore(false);
            }
        }
    }, [state.createSuccess, state.updateSuccess])

    useBeforeUnload()

    const onSubmit = debounce(async (values: any, st?: string) => {
        let mentionReferences = getReferencesFromEditor(getCkeditorDataDes.current?.props?.data);
        const requestData: any = {
            id: objectRelationshipID || null,
            name: values.objectrelationshipName,
            version: values.version,
            diagram: attachment?.id,
            description: getCkeditorDataDes?.current?.props?.data,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
            reqElicitation: values.req,
            documentation: values.documentation,
            status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
            reviewer: values.reviewer || '',
            customer: values.customer || '',
            dueDate: values.dueDate ? values.dueDate?.toDate() : null,
            completeDate: values.completeDate ? values.completeDate?.toDate() : null,
            impacts: impacts,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
        }
        setIsCreateMore(values.createMore);
        if (requestData.status === STATUS.SUBMITTED || requestData.status === STATUS.ENDORSE) {
            if (!attachment?.id) {
                attachmentRef.current.scrollIntoView('img')
                ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.object-relationship')
                return
            }
        }
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.object-relationship' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
    }, 500)

    const onFinishFailed = (errorInfo: any) => {

    }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        setAttachment(null)
        setIsDraft(null);
        form.resetFields([
            'version',
            'changeDescription',
            'code',
            'objectrelationshipName',
            'img',
            'description',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
            'req',
            'documentation',
            'reviewer',
            'customer',
            'dueDate',
            'completeDate',


        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })

    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'diagram', title: intl.formatMessage({ id: 'ord.column.ord' }), },
            { field: 'description', title: intl.formatMessage({ id: 'ord.ord-description' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'ord.documentation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.OBJECT_RELATIONSHIP_DIAGRAM,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    //#endregion COMMENT INIT

    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'ord.title-create' : 'ord.title-update' })}
                >
                    <Space size="small">
                        {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                            style={{ marginBottom: '0px' }}
                            valuePropName="checked"
                            name="createMore"
                            wrapperCol={{ span: 24 }}
                        >
                            <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                        </Form.Item> : <></>}
                        <Button onClick={debounce(confirmCancel, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>

                        {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                            <Button type="primary" ghost htmlType="submit" onClick={() => {
                                setIsDraft(false)
                                setIsSubmitForm(true)
                            }}>
                                {intl.formatMessage({ id: 'common.action.submit' })}
                            </Button> : <></>
                        }

                        <Button onClick={() => {
                            setIsDraft(true)
                            setIsSubmitForm(true)
                        }} className="success-btn" htmlType="submit">
                            {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                        </Button>
                    </Space>
                </LavPageHeader>
            </div>
            <Row align="middle" style={{ marginTop: 10 }}>
                {/* <Col span={2}>
                    <TriggerComment screenMode={screenMode} field="version">
                        <Text>
                            {intl.formatMessage({
                                id: 'createobject.place-holder.version',
                            })}
                        </Text>
                    </TriggerComment>
                </Col>
                <Col span={2}>
                    <Form.Item
                        className="mb-0"
                        name="version"
                        rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                    >
                        <Input maxLength={255} />
                    </Form.Item>
                </Col> */}
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderStatusBadge(state.detail?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>

            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'ord.infor' })}>
                    {
                        screenMode === SCREEN_MODE.EDIT ?
                            <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                                <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                    <Input disabled maxLength={255} />
                                </Form.Item>
                            </FormGroup> : <></>
                    }
                    <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                        <Form.Item
                            name="objectrelationshipName"
                            rules={[
                                {
                                    required: true,
                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                },
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        >
                            <Input
                                placeholder={`${intl.formatMessage({
                                    id: `ord.place-holder.ord`,
                                })}${intl.formatMessage({
                                    id: `common.mandatory.*`,
                                })}
                            `}
                                maxLength={255}
                            />
                        </Form.Item>
                    </FormGroup>
                    <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="description">
                            {intl.formatMessage({ id: 'ord.ord-description' })}
                        </TriggerComment>}>
                        <Form.Item
                            name="description"
                            labelAlign="left"
                            wrapperCol={{ span: 24 }}
                        >
                            <CkeditorMention
                                ref={getCkeditorDataDes}
                                data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
                            />
                        </Form.Item>
                    </FormGroup>
                    <FormGroup label={
                        <TriggerComment screenMode={screenMode} field="diagram">
                            {intl.formatMessage({ id: 'ord.column.ord' })}
                        </TriggerComment>}>
                        <div ref={attachmentRef}>
                            <Form.Item name="img">
                                <LavAttachmentUpload artefactType={REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM} attachment={attachment} supportPDF onChange={setAttachment} name="file" isCommon={false} />
                            </Form.Item>
                        </div>
                    </FormGroup>
                </Card>
                <AssignTaskComponent form={form} data={screenMode == SCREEN_MODE.EDIT ? state.detail : null} isSubmit={isDraft == false} screenMode={screenMode} />

                {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM} onChange={onChange} isSubmitForm={isSubmitForm} />}

                <LavEffortEstimationForm
                    screenMode={screenMode}
                    hasDevelopment={state?.detail?.hasOwnProperty('development')}
                    hasImplementation={state?.detail?.hasOwnProperty('implementation')}
                />

                {/* <Card className='rq-form-block' title={intl.formatMessage({ id: 'ord.effort-estimation' })}>
            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="req-elicitation">
                {intl.formatMessage({ id: 'ord.req-elicitation' })}
              </TriggerComment>}>
              <Form.Item name="req">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>
            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="documentation">
                {intl.formatMessage({ id: 'ord.documentation' })}
              </TriggerComment>}>
              <Form.Item name="documentation">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>
          </Card> */}

                <LavRelatedLinksForm form={form} screenMode={screenMode} />
                {
                    screenMode === SCREEN_MODE.EDIT ?
                        <LavVersion screenMode={screenMode} data={state?.detail?.versionHistories} form={form} /> : <></>
                }
            </Space>
        </Form >
    </Spin>
}

export default ObjectRelationshipDiagramFormPage
