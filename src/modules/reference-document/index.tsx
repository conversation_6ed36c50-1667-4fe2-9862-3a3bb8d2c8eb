import { PlusOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, DATE_FORMAT, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import { currentUserName, extractProjectCode, getColumnSearchProps, hasRole } from '../../helper/share'
import ReferenceDocumentFormPage from './form/form'

const ReferenceDocument = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  useEffect(() => {
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'reference_document.header.title' });
  }, [screenMode]);

  const columns = [
    {
      title: intl.formatMessage({ id: 'email.column.email-code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'reference_document.column.reference_document' }),
      dataIndex: 'name',
      width: '60%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT)
    },
    {
      title: intl.formatMessage({ id: 'reference_document.column.sender' }),
      dataIndex: 'sender',
      width: '32%',
      sorter: true,
      ...getColumnSearchProps('sender', SEARCH_TYPE.TEXT)
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.send-date' }),
      dataIndex: 'sendDate',
      width: '7%',
      sorter: true,
      ...getColumnSearchProps('sendDate', SEARCH_TYPE.DATE),
      render: (text) => {
        // lavdate
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'reference_document.button.add-reference_document' })}
      </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (
      ((((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer || hasRole(APP_ROLES.PM)) && record?.status === STATUS.SUBMITTED)) &&
        record.status !== STATUS.CANCELLED &&
        record.status !== STATUS.DELETE &&
        record.status !== STATUS.ENDORSE
      ) ?
        <Button ghost={screenMode === SCREEN_MODE.EDIT}
          style={{ border: 'none' }}
          icon={<CustomSvgIcons name="EditCustomIcon" />}
          onClick={() => {
            setScreenMode(SCREEN_MODE.EDIT)
            setId(record.id)
          }} /> : <></>)
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM) && record.status !== STATUS.DELETE) ?
      children : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ?
        <LavTable
          title="reference_document.header.title"
          artefact_type="common.artefact.reference-document"
          apiUrl={API_URLS.REFERENCE_DOCUMENT}
          columns={columns}
          artefactType={REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT}
          updateComponent={UpdateComponent}
          createComponent={CreateComponent}
          deleteComponent={DeleteComponent}
        /> : <></>}
      {
        screenMode === SCREEN_MODE.CREATE ? <ReferenceDocumentFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <ReferenceDocumentFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} documentID={id} /> : <></>
      }


    </Space>
  )
}

export default ReferenceDocument
