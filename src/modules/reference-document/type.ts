
export interface ReferenceDocumentState {
  isLoading: boolean,
  detail: ReferenceDocumentDetail | null,
  selectedData: ReferenceDocumentDetail | null,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  isModalShow?:boolean
}
export interface ReferenceDocumentDetail {
  id?: number | null,
  code?: string,
  status?: number | null,
  name?: string
  sender?: string
  sendDate?: Date | null,
  refDocument?: number | null,
  description?: string
  storage: string
  jira: string
  confluence: string
  projectId: number | null
}

export const defaultState: ReferenceDocumentState = {
  detail: {
    code: '',
    status: 0,
    name: '',
    sender: '',
    sendDate: null,
    refDocument: null,
    description: '',
    storage: '',
    jira: '',
    confluence: '',
    projectId: null,
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/REFERENCE_DOCUMENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/REFERENCE_DOCUMENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/REFERENCE_DOCUMENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/REFERENCE_DOCUMENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/REFERENCE_DOCUMENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/REFERENCE_DOCUMENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/REFERENCE_DOCUMENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/REFERENCE_DOCUMENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/REFERENCE_DOCUMENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/REFERENCE_DOCUMENT/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/REFERENCE_DOCUMENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/REFERENCE_DOCUMENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/REFERENCE_DOCUMENT/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/REFERENCE_DOCUMENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/REFERENCE_DOCUMENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/REFERENCE_DOCUMENT/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/REFERENCE_DOCUMENT/SET_MODAL_VISIBLE',
}
