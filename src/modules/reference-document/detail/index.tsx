import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailRequest, resetState } from '../action'
import ReferenceDocumentFormPage from '../form/form'
import { ReferenceDocumentState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'

const ReferenceDocumentDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const state = useSelector<AppState | null>((s) => s?.ReferenceDocument) as ReferenceDocumentState;

  useEffect(() => {
    return () => {
      dispatch(resetState(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.referenceDocumentID) {
      dispatch(getDetailRequest(props.match.params.referenceDocumentID))
    }
  }, [props])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REFERENCE_DOCUMENT}`)
    }
  }, [state])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.referenceDocumentID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {screenMode === SCREEN_MODE.VIEW ? <>
        <Col span={5}>
          <LavLeftControl
            activeId={props.match.params.referenceDocumentID}
            apiUrl={API_URLS.REFERENCES_DOCUMENT}
            route={APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}
            title='reference_document.header.title'
            reload={reload}
            artefactType={REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT}
            reloadSuccess={() => setReload(false)}
            hideStatus
            handleCreate={handleCreate}
          >
            {
            (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) ?
            <Button ghost={true}
              type='primary'
              className='lav-btn-create'
              icon={<PlusOutlined />}
              onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'reference_document.button.add-reference_document' })}
            </Button> : <></>
}
          </LavLeftControl>
        </Col>
        <Col span={19}>
          <RightControl setScreenMode={() => setScreenMode(SCREEN_MODE.EDIT)} onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} referenceDocumentID={props.match.params.referenceDocumentID} isModalShow={state?.isModalShow} />
        </Col>
      </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <ReferenceDocumentFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <ReferenceDocumentFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
            }} documentID={props.match.params.referenceDocumentID} />
          </Col> : <></>
      }

    </Row>
  )
}

export default ReferenceDocumentDetail
