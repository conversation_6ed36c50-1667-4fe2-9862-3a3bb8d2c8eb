
import AppState from '@/store/types'
import {
    <PERSON>read<PERSON><PERSON>b, Button, Card, Col,
    Divider, Row, Space, Spin, Typography
} from 'antd'
import moment from 'moment'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, DATE_FORMAT, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, STATUS } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAttachmentPreview from '../../../helper/component/lav-attachment-preview'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../_shared/comment/action'
import TriggerComment from '../../_shared/comment/trigger-comment'
import { CommentState } from '../../_shared/comment/type'
import { deleteRequest } from '../action'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    referenceDocumentID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}

const RightControl = ({ data, referenceDocumentID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);


    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;
    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + data?.name; 
        
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id && co?.artefactType == REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'sender', title: intl.formatMessage({ id: 'reference_document.column.sender' }), },
            { field: 'send-date', title: intl.formatMessage({ id: 'reference_document.column.send-date' }), },
            { field: 'reference_document', title: intl.formatMessage({ id: 'reference_document.column.reference_document' }), },
            { field: 'description', title: intl.formatMessage({ id: 'reference_document.column.description' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];

        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));
        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.REFERENCE_DOCUMENTS,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT

    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code}-{data?.name}
                    </Title>
                </div>

                <Space size="small">
                    {/* Delete record */}
                    {((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && data?.status !== STATUS.DELETE) ? <DeleteButton
                        type={BUTTON_TYPE.TEXT}
                        content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.reference-document' }) })}
                        okCB={() => dispatch(deleteRequest(referenceDocumentID))}
                        confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
                    }

                    {/* Update record */}
                    {((((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && data?.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer || hasRole(APP_ROLES.PM)) && data?.status === STATUS.SUBMITTED)) &&
                        data?.status !== STATUS.CANCELLED &&
                        data?.status !== STATUS.DELETE &&
                        data?.status !== STATUS.ENDORSE
                    ) ?
                        <Button
                            type='primary'
                            className='lav-btn-create'
                            onClick={() => {
                                setScreenMode()
                            }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                    }
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Card
                            title={
                                <Title level={5}>
                                    {intl.formatMessage({ id: 'reference_document.label.reference_document-infomation' })}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={4}>
                                    <TriggerComment field="sender">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'reference_document.column.sender',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>{data?.sender}</Col>

                                <Col span={4}>
                                    <TriggerComment field="send-date">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'reference_document.column.send-date',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    {/* lavdate */}
                                    {data?.sendDate ? moment(data?.sendDate).format(DATE_FORMAT) : ''}
                                </Col>

                                <Col span={4}>
                                    <TriggerComment field="reference_document">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'reference_document.column.reference_document',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>

                                <Col span={20}>
                                    {/* // Upload step 7 */}
                                    <LavAttachmentPreview attachment={data?.refDocument} isCommon={false} />
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field="description">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'reference_document.column.description',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24} className="description">
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.description }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>
                                            
                        <LavReferences data={data} />
                        <Col span={24}>
                            <LavRelatedLinks data={data} />
                        </Col>

                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
