import LavPageHeader from '../../../helper/component/lav-breadcumb'
import AppState from '@/store/types'
import {
    Button,
    Card, Checkbox, Col, DatePicker, Form, Input, Modal, Row, Space, Spin
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { ARTEFACT_COMMENT, DATE_FORMAT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import FormGroup from '../../../helper/component/form-group'
import LavAttachmentUpload from '../../../helper/component/lav-attachment-upload'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import TextAreaBullet from '../../../helper/component/textAreaBullet'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, renderStatusBadge, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { ReferenceDocumentState } from '../type'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'
const { confirm } = Modal

interface ReferenceDocumentFormModalProps {
    documentID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}
const ReferenceDocumentFormPage = ({ documentID, screenMode, onFinish, onDismiss }: ReferenceDocumentFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm();
    const state = useSelector<AppState | null>((s) => s?.ReferenceDocument) as ReferenceDocumentState;
    const [isDraft, setIsDraft] = useState(false);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const { height: windowHeight } = useWindowDimensions();
    const modalConfirmConfig = useModalConfirmationConfig();
    // Upload step 1
    const [attachment, setAttachment] = useState(null) as any
    const attachmentRef = useRef<any>()
    const getCkeditorDataDes: any = createRef()
    
    useBeforeUnload();
    // Destroy
    useEffect(() => {
        return () => {
            dispatch(resetState(null));
            resetForm();
            form.resetFields(['createMore']);
        }
    }, [])

    useEffect(() => {
        if (documentID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(documentID))
        }
        
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'reference-document.page_create_title' : 'reference-document.page_update_title' }); 
    }, [screenMode, documentID])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }

    useEffect(() => {
        if (documentID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);
            form.setFieldsValue({
                ...state.detail,
                // lavdate
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
                sendDate: state.detail.sendDate ? moment(new Date(state.detail.sendDate)) : '',
            })
            // Upload step 2
            setAttachment(state.detail?.refDocument)
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            if (isCreateMore) {
                resetForm();
                form.setFieldsValue({
                    assignee: currentUserName(),
                    dueDate: moment(new Date()),
                })
            } else {
                if (onFinish) {
                    onFinish();
                }
                onDismiss();
            }
            setIsDraft(false);
            setIsCreateMore(false);
        }
    }, [state.createSuccess, state.updateSuccess])

    const onSubmit = debounce(async (values: any, st?: string) => {
        let mentionReferences = getReferencesFromEditor(getCkeditorDataDes.current?.props?.data);
        const requestData: any = {
            ...values,
            id: screenMode === SCREEN_MODE.CREATE ? null : state?.detail?.id,
            status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS.DRAFT : state.detail?.status) : STATUS.SUBMITTED,
            // Upload step 3
            refDocument: attachment?.id,
            sendDate: values.sendDate ? values.sendDate.toDate() : null,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
            description: getCkeditorDataDes?.current?.props?.data,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
        }
        setIsCreateMore(values.createMore);
        if (requestData.status === STATUS.SUBMITTED || requestData.status === STATUS.ENDORSE) {
            // Upload step 4 (optional by artefact)
            if (!attachment?.id) {
                attachmentRef.current.scrollIntoView('refDocument')
                ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.reference-document')
                return
            }
        }
        requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
        if (isDraft) {
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: screenMode === SCREEN_MODE.CREATE ? 'CFD_6_1' : 'CFD_6_2' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.reference-document' }) }
                ),
                onOk() {
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
    }, 500)

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage({ id: 'CFD_3' })}`,
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsDraft(true);
        setIsCreateMore(false);
        // Upload step 5
        setAttachment(null);
        form.resetFields([
            'name',
            'code',
            'sender',
            'sendDate',
            'refDocument',
            'description',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
        ])
    }
    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'sender', title: intl.formatMessage({ id: 'reference_document.column.sender' }), },
            { field: 'send-date', title: intl.formatMessage({ id: 'user-requirement.column.send-date' }), },
            { field: 'reference_document', title: intl.formatMessage({ id: 'reference_document.column.reference_document' }), },
            { field: 'description', title: intl.formatMessage({ id: 'createobject.label.description' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.REFERENCE_DOCUMENTS,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    //#region COMMENT INIT

    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name="createReference"
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb={false}
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'reference-document.page_create_title' : 'reference-document.page_update_title' })}
                >
                    <Space size="small">
                        {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                            style={{ marginBottom: '0px' }}
                            valuePropName="checked"
                            name="createMore"
                            wrapperCol={{ span: 24 }}
                        >
                            <Checkbox disabled={state.isLoading}>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                        </Form.Item> : <></>}
                        <Button size='middle' onClick={debounce(confirmCancel, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>
                        <Button size='middle' onClick={() => setIsDraft(false)} className="success-btn" htmlType="submit">
                            {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.create' : 'common.action.save' })}
                        </Button>
                    </Space>
                </LavPageHeader>
            </div>

            <Spin spinning={screenMode === SCREEN_MODE.EDIT && state.isLoading}>
                <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                    <Card className='rq-form-block' title={intl.formatMessage({ id: 'reference_document.label.reference_document-infomation' })}>
                        {
                            screenMode === SCREEN_MODE.EDIT ?
                                <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                                    <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                        <Input disabled value={state?.detail?.code} maxLength={255} />
                                    </Form.Item>
                                </FormGroup>
                                : <></>
                        }
                        <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                            <Form.Item
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: intl.formatMessage({ id: 'IEM_1' }),
                                    },
                                    { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                    {
                                        validator: async (rule, value) => {
                                            if (value && value.trim().length === 0) {
                                                throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                            }
                                        },
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={`${intl.formatMessage({
                                        id: `reference-document.place-holder`,
                                    })}${intl.formatMessage({
                                        id: `common.mandatory.*`,
                                    })}`}
                                    maxLength={255}
                                />
                            </Form.Item>
                        </FormGroup>
                        <FormGroup inline labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field="sender">
                                {intl.formatMessage({ id: 'reference_document.column.sender' })}
                            </TriggerComment>
                        }>
                            <Form.Item
                                name="sender"
                                rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                            >
                                <Input maxLength={255} />
                            </Form.Item>
                        </FormGroup>

                        <FormGroup inline labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field="send-date">
                                {intl.formatMessage({ id: 'user-requirement.column.send-date' })}
                            </TriggerComment>
                        }>
                            <Form.Item validateTrigger="onBlur" name="sendDate">
                                <DatePicker format={DATE_FORMAT} />
                            </Form.Item>
                        </FormGroup>
                        <div ref={attachmentRef}>
                            <FormGroup label={
                                <TriggerComment screenMode={screenMode} field="reference_document">
                                    {intl.formatMessage({ id: 'reference_document.column.reference_document' })}
                                </TriggerComment>
                            }>
                                <Form.Item name="refDocument">
                                    {/* // Upload step 6 */}
                                    <LavAttachmentUpload artefactType={REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT} attachment={attachment} isCommon={false} name="file" supportAllType onChange={setAttachment} />
                                </Form.Item>
                            </FormGroup>
                        </div>
                        <FormGroup className='rd-des' inline required labelSpan={3} controlSpan={21} label={
                            <TriggerComment screenMode={screenMode} field="description">
                                {intl.formatMessage({ id: 'createobject.label.description' })}
                            </TriggerComment>
                        }>
                            <Form.Item
                                name="description"
                                labelAlign="left"
                                // rules={[{
                                //     validator: async (rule, value) => {
                                //         const description = getCkeditorDataDes?.current?.props?.data
                                //         if ((description == '' || description == undefined)) {
                                //             throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                //         }
                                //     }
                                // }]}
                                wrapperCol={{ span: 24 }}
                            >
                                <CkeditorMention
                                    ref={getCkeditorDataDes}
                                    data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
                                />
                            </Form.Item>
                        </FormGroup>
                    </Card>

                    <LavRelatedLinksForm form={form} screenMode={screenMode} />
                </Space>
            </Spin>
        </Form >
    </Spin>
}

export default ReferenceDocumentFormPage
