import { Action } from '@reduxjs/toolkit';
import { all, call, fork, put, takeLatest } from 'redux-saga/effects';
import { API_URLS, MESSAGE_TYPE } from '../../../constants';
import { apiCall } from '../../../helper/api/aloApi';
import { ShowAppMessage } from '../../../helper/share';
import { getScopeChange, getScopeChangeSuccess, getScopeCoverage, getScopeCoverageSuccess } from './action';

function* handleGetScopeCoverage(action: Action) {
    if (getScopeCoverage.match(action)) {
        try {
            const url = API_URLS.GET_SCOPE_COVERAGE
            const res = yield call(apiCall, 'GET', url)
            yield put(getScopeCoverageSuccess(res.data))
        } catch (err) {
            ShowAppMessage(MESSAGE_TYPE.ERROR)
        }
    }
}
function* handleGetScopeChange(action: Action) {
    if (getScopeChange.match(action)) {
        try {
            const url = API_URLS.GET_SCOPE_CHANGE + action.payload
            const res = yield call(apiCall, 'GET', url)
            yield put(getScopeChangeSuccess(res.data))
        } catch (err) {
            ShowAppMessage(MESSAGE_TYPE.ERROR)
        }
    }
}
function* watchFetchRequest() {
    yield takeLatest(getScopeCoverage.type, handleGetScopeCoverage)
    yield takeLatest(getScopeChange.type, handleGetScopeChange)
}

export default function* DashboardSaga() {
    yield all([fork(watchFetchRequest)])
}
