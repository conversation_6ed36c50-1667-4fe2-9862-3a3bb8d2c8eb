import { Column } from '@ant-design/plots';
import { Empty } from 'antd';
import React, { useEffect, useState } from 'react';
import intl from '../../../../config/locale.config';
import { SCOPE_TYPE } from '../../../../constants';

interface DashboardStackColumnChartProps {
    data: any
}

const DashboardStackColumnChart = ({ data }: DashboardStackColumnChartProps) => {
    const [stackColumnChartData, setStackColumnChartData] = useState([]);
    const [isNoData, setIsNoData] = useState(false);
    useEffect(() => {
        let newData: any = [];
        let total = 0;
        if (data) {
            data.forEach(e => {
                newData.push({ label: e.xasis, type: SCOPE_TYPE.CHANGE_REQUEST.label, value: e.totalChangeRequest });
                newData.push({ label: e.xasis, type: SCOPE_TYPE.ORIGINAL.label, value: e.totalOriginal });
                total += e.totalChangeRequest + e.totalOriginal
            });
        }
        setIsNoData(total === 0);
        setStackColumnChartData(newData);
    }, [data]);

    const stackColumnChartConfig = {
        data: stackColumnChartData,
        isStack: true,
        xField: 'label',
        yField: 'value',
        seriesField: 'type',
        color: ['#A8A7A8', '#EE7E32', '#5B9DD5'],
    }

    return (
        <div className='dashboard-chart' >
            {!isNoData ? <Column {...stackColumnChartConfig} /> : <div className='dashboard-chart-empty'>
                <Empty description={intl.formatMessage({ id: 'dashboard.no_data' })} />
            </div>}
        </div >
    )
}

export default DashboardStackColumnChart
