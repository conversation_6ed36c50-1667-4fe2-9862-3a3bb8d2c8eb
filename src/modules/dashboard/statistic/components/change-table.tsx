import { Table } from 'antd';
import React, { useEffect, useState } from 'react';
import intl from '../../../../config/locale.config';
import { SCOPE_TYPE } from '../../../../constants';

interface DashboardChangeTableProps {
    data: any
}
const DashboardChangeTable = ({ data }: DashboardChangeTableProps) => {
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        let newData = [];
        if (data) {
            newData = data.map((e, idx) => { return { ...e, id: idx } })
        }
        setDataSource(newData);
    }, [data]);

    const changeColumns = [
        {
            title: intl.formatMessage({ id: 'dashboard.scope_change.table.scope_change' }),
            dataIndex: 'scopeChange',
            key: 'scopeChange',
            render: text =>
                text === SCOPE_TYPE.CHANGE_REQUEST.value ? SCOPE_TYPE.CHANGE_REQUEST.label :
                    text === SCOPE_TYPE.ORIGINAL.value ? SCOPE_TYPE.ORIGINAL.label : ''
        },
        { title: intl.formatMessage({ id: 'dashboard.scope_change.table.in_scope' }), dataIndex: 'inScope', key: 'inScope' },
        { title: intl.formatMessage({ id: 'dashboard.scope_change.table.out_scope' }), dataIndex: 'outScope', key: 'outScope' },
        { title: intl.formatMessage({ id: 'dashboard.scope_change.table.tentative' }), dataIndex: 'tentative', key: 'tentative' },
    ];

    return (
        <div className='dashboard-table'>
            <h4>{intl.formatMessage({ id: 'dashboard.scope_change.table.title' })}</h4>
            <Table pagination={false} bordered dataSource={dataSource} columns={changeColumns} rowKey="id" />
        </div>
    )
}

export default DashboardChangeTable
