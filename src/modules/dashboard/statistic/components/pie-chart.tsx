import { Pie } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';
import intl from '../../../../config/locale.config';
import { COVERAGE_TYPE } from '../../../../constants';

interface DashboardPieChartProps {
    covered: number
    uncovered: number
}
const DashboardPieChart = ({ covered, uncovered }: DashboardPieChartProps) => {
    const pieChartData = (covered || uncovered) ? [
        { type: COVERAGE_TYPE.COVERED.label, value: covered },
        { type: COVERAGE_TYPE.UNCOVERED.label, value: uncovered }
    ] : [];
    const pieChartConfig = {
        appendPadding: 10,
        data: pieChartData,
        angleField: 'value',
        colorField: 'type',
        radius: 0.9,
        color: ['#5B9DD5', '#EE7E32'],
        label: {
            type: 'inner',
            offset: '-30%',
            content: function content(_ref) {
                return `${(_ref.value * 100 / (covered + uncovered)).toFixed(2)}%`;
            },
            style: {
                fontSize: 14,
                textAlign: 'center',
            },
        },
        interactions: [
            {
                type: 'element-active',
            },
        ],
    };

    return (
        <div className='dashboard-chart'>
            {pieChartData?.length > 0 ? <Pie {...pieChartConfig} /> : <div className='dashboard-chart-empty'>
                <Empty description={intl.formatMessage({ id: 'dashboard.no_data' })} />
            </div>}
        </div>
    )
}

export default DashboardPieChart
