import { extractProjectCode } from '../../../../helper/share';
import { Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import intl from '../../../../config/locale.config';
import { APP_ROUTES, PROJECT_PREFIX, SCOPE_TYPE } from '../../../../constants';

interface DashboardCoverageTableProps {
    data: any
}
const DashboardCoverageTable = ({ data }: DashboardCoverageTableProps) => {
    const [dataSource, setDataSource] = useState([]);
    // useEffect(() => {
    //     let newData = [];
    //     if (data) {
    //         newData = data.map((e, idx) => { return { ...e, id: idx } })
    //     }
    //     setDataSource(newData);
    // }, [data]);

    const coverageColumns = [
        {
            title: intl.formatMessage({ id: 'dashboard.scope_coverage.table.code' }),
            dataIndex: 'code',
            key: 'code',
            width:'85px',
            render: (text, item) => <Link style={{ cursor: 'pointer' }} to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USER_REQUIREMENT_DETAIL}${item.id}`}>{text}</Link>
        },
        {
            title: intl.formatMessage({ id: 'dashboard.scope_coverage.table.user_requirement' }),
            dataIndex: 'name',
            key: 'requirement',
        },
        {
            title: intl.formatMessage({ id: 'dashboard.scope_coverage.table.type' }),
            dataIndex: 'type',
            key: 'type',
            render: text =>
                text === SCOPE_TYPE.CHANGE_REQUEST.value ? SCOPE_TYPE.CHANGE_REQUEST.label :
                    text === SCOPE_TYPE.ORIGINAL.value ? SCOPE_TYPE.ORIGINAL.label : ''
        },
    ];

    return (
        <div className='dashboard-table'>
            <h4>{intl.formatMessage({ id: 'dashboard.scope_coverage.table.title' })}</h4>
            <Table bordered dataSource={data?.userRequirements} columns={coverageColumns} rowKey="id" />
        </div>
    )
}

export default DashboardCoverageTable
