export interface DashboardState {
  isScopeCoverageLoading: boolean
  isScopeChangeLoading: boolean
  isSucess: boolean
  scopeCoverage: any
  scopeChange: any
}
export enum ActionEnum {
  INIT_SCREEN = '@@MODULES/DASHBOARD/INIT_SCREEN',
  GET_SCOPE_COVERAGE = '@@MODULES/DASHBOARD/GET_SCOPE_COVERAGE',
  GET_SCOPE_COVERAGE_SUCCESS = '@@MODULES/DASHBOARD/GET_SCOPE_COVERAGE_SUCCESS',
  GET_SCOPE_CHANGE = '@@MODULES/DASHBOARD/GET_SCOPE_CHANGE',
  GET_SCOPE_CHANGE_SUCCESS = '@@MODULES/DASHBOARD/GET_SCOPE_CHANGE_SUCCESS',
}
