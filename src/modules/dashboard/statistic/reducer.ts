import { createReducer } from '@reduxjs/toolkit'
import { getScopeChangeSuccess, getScopeCoverageSuccess } from './action'
import { DashboardState } from './type'

const initState: DashboardState = {
  isScopeCoverageLoading: true,
  isScopeChangeLoading: true,
  isSucess: false,
  scopeCoverage:null,
  scopeChange:null
}

const reducer = createReducer(initState, (builder) => {
  return builder
    .addCase(getScopeCoverageSuccess, (state, action) => {
      state.isScopeCoverageLoading = false
      state.scopeCoverage = action.payload
    })
    .addCase(getScopeChangeSuccess, (state, action) => {
      state.isScopeChangeLoading = false
      state.scopeChange = action.payload
    })
})

export default reducer
export { initState as DashboardState }

