.dashboard-page{
    padding: 0 16px;
}
.dashboard-page .dashboard-page-title{
    text-transform: uppercase;
    font-weight: bold;
    font-size: 24px;
    padding-top: 20px;
    margin-bottom: 10px;
}
.dashboard-page .ant-card.ant-card-bordered{
    border-color: #91d5ff;
}
.dashboard-page .ant-card .ant-card-head{
    background: #e6f7ff;
}
.dashboard-page .ant-card .ant-card-head .ant-card-head-title{
    font-weight: bold;
}
.dashboard-page .ant-card-body{
    position: relative;
    padding-top: 60px;
}
.dashboard-page .dashboard-chart{
    margin-bottom: 24px;
    height: 360px;
}
.dashboard-page .dashboard-filter{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 16px;
    position: absolute;
    top: 15px;
    right: 15px;
}
.dashboard-page .dashboard-filter>span{
    margin-right: 10px;
    font-weight: bold;
}
.dashboard-page .dashboard-filter .ant-select {
    width: 200px;
}
.dashboard-page .dashboard-table{

}
.dashboard-page .dashboard-table h4{
    margin-bottom: 10px;
    font-weight: bold;
}
.dashboard-page .ant-table-thead > tr > th{
    font-weight: bold;
}
.dashboard-page .ant-table-thead > tr > th, 
.dashboard-page .ant-table-tbody > tr > td{
    padding-top: 10px;
    padding-bottom: 10px;
}
.dashboard-page .ant-table-tbody > tr > td a{
    text-decoration: underline;
}
.dashboard-page .ant-table-thead > tr > th{
    background-color: rgba(0, 0, 0, .05);
}
.dashboard-page .ant-table-tbody > tr:nth-child(2n + 1){
    background-color: #fafafa;
}