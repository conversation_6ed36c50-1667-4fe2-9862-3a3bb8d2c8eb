import { createAction } from '@reduxjs/toolkit'
import { ActionEnum } from './type'

export const initScreen = createAction<any>(ActionEnum.INIT_SCREEN)
export const getScopeCoverage = createAction<void>(ActionEnum.GET_SCOPE_COVERAGE)
export const getScopeCoverageSuccess = createAction<any>(ActionEnum.GET_SCOPE_COVERAGE_SUCCESS)
export const getScopeChange = createAction<number>(ActionEnum.GET_SCOPE_CHANGE)
export const getScopeChangeSuccess = createAction<any>(ActionEnum.GET_SCOPE_CHANGE_SUCCESS)
