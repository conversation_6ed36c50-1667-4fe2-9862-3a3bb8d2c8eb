import { CommentOutlined } from "@ant-design/icons"
import { Breadcrumb, Button, Col, DatePicker, Row, Select, Space, Tooltip, Typography } from "antd"
import moment from "moment"
import { useEffect, useState } from "react"
import { <PERSON> } from "react-router-dom"
import intl from "../../../../config/locale.config"
import { API_URLS, APP_ROLES, APP_ROUTES, ARTEFACT_NAME, DATE_FORMAT, MESSAGE_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from "../../../../constants"
import CustomSvgIcons from "../../../../helper/component/custom-icons"
import LavTable from "../../../../helper/component/lav-table"
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, getProjectName, hasRole, renderStatusBadge, ShowAppMessage } from "../../../../helper/share"
import ActorFormPage from "../../../../modules/actor/form/form"
import BusinessRuleFormPage from "../../../../modules/business-rule/form/form"
import DataMigrationFormPage from "../../../../modules/data-migration/form/form"
import EmailTemplateFormPage from "../../../../modules/email-templates/form/form"
import MockupScreenFormPage from "../../../../modules/mockup-screen/form/form"
import NonFunctionalRequirementFormPage from "../../../../modules/non-functional-requirement/form/form"
import ObjectRelationshipDiagramFormPage from "../../../../modules/object-relationship-diagram/form/form"
import ObjectsFormPage from "../../../../modules/objects/form/form"
import OtherRequirementFormPage from "../../../../modules/other-requirement/form/form"
import PermissionMatrixFormPage from "../../../../modules/permission-matrix/form/form"
import StateTransitionFormPage from "../../../../modules/state-transition/form/form"
import UseCaseDiagramFormPage from "../../../../modules/usecase-diagram/form/form"
import FunctionFormPage from "../../../../modules/usecase/form/form"
import UserRequirementFormPage from "../../../../modules/user-requirement/form/form"
import UserStoryManagementFormPage from "../../../../modules/user-story-management/form/form"
import WorkFlowFormPage from "../../../../modules/workflow/form/form"
import AppCommonService from "../../../../services/app.service"

const { Title } = Typography
const MyAssignedTask = (props) => {
  const [id, setId] = useState<number>(0)
  const [members, setMembers] = useState<any[]>([])
  const [memberReviewer, setMemberReviewers] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const projectCode = extractProjectCode()
  const projectName = getProjectName(projectCode)
  const [columns, setColumns] = useState<any>([]);
  const [artefactType, setArtefactType] = useState(0)
  const [reload, setReload] = useState(false)

  const handleDueDateChange = (e, record) => {
    const payload = {
      artefactType: record?.artefactType,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefactType,
        artefactId: record?.id,
        dueDate: e?._d,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  const getMember = async () => {
    const listMember = await Promise.all([
      AppCommonService.getMembers(),
      AppCommonService.getMembersBA(),
      AppCommonService.getCustomers()
    ])
    setMembers(listMember[0].data)
    setMemberReviewers(listMember[1].data)
    setCustomers(listMember[2].data)
    setColumns(columnConfig(null, null, listMember[0].data, listMember[1].data, listMember[2].data))
  }

  useEffect(() => {
    getMember()
    document.title = extractProjectCode() + "-" + intl.formatMessage({ id: 'assigned_task.header.title' });
  }, [])
  
  const columnConfig = (status, date, members, memberReviewer, customers) => {
    return [
      {
        title: intl.formatMessage({ id: 'assigned_task.column.code' }),
        dataIndex: 'code',
        ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
        sorter: true,
        render: (text: string, record: any) => {
          const artefact = ARTEFACT_NAME.find(
            (item) => item.key === record.artefactType
          )
          if (artefact) {
            const href = `${PROJECT_PREFIX}${extractProjectCode()}${artefact.details}${artefact.key === ARTEFACT_NAME[7].key ? '' : record.id}`
            return <Link to={href}>{text || '(NO TITLE)'}</Link>
          } else {
            return text
          }
        },
      },
      {
        title: intl.formatMessage({ id: 'assigned_task.column.artefact' }),
        dataIndex: 'artefactType',
        width: '35%',
        ...getColumnDropdownFilterProps(
          ARTEFACT_NAME.filter(e => e.key !== REQ_ARTEFACT_TYPE_ID.ACTOR).map((item) => {
            return { text: item.value, value: item.key }
          })
        ),
        sorter: true,
        render: (text: any) => {
          return (
            <>{ARTEFACT_NAME.map((item) => item.key === text && item.value)}</>
          )
        },
      },
      {
        title: intl.formatMessage({ id: 'assigned_task.column.artefact-name' }),
        dataIndex: 'name',
        filterSearch: true,
        width: '35%',
        sorter: true,
        ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
        render: (text, record) => {
          return <Tooltip title={<div
            className="tableDangerous"
            style={{ color: 'white' }}
            dangerouslySetInnerHTML={{ __html: record?.description }}
          ></div>}>{text}</Tooltip>
        }
      },
      {
        title: intl.formatMessage({ id: 'assigned_task.column.status' }),
        dataIndex: 'status',
        defaultFilteredValue: status != null ? status : null,
        ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
        sorter: true,
        width: '150px',
        render: (record) => renderStatusBadge(record),
      },
      {
        title: intl.formatMessage({ id: 'assigned_task.column.due' }),
        dataIndex: 'dueDate',
        width: '15%',
        sorter: true,
        sortOrder: 'ascend',
        ...getColumnSearchProps('dueDate', SEARCH_TYPE.DATE),
        render: (text, record) => {
          // lavdate
          const data = record.dueDate ? moment(record.dueDate).format(DATE_FORMAT) : ''
          const defaultValue = (data) => data !== '' ? {
            defaultValue: moment(data, DATE_FORMAT)
          } : null
          return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
            && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
            record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE &&
            record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE
          ) ? <DatePicker onChange={(e) => handleDueDateChange(e, record)} {...defaultValue(data)} className='full-width ant-picker-custom' format={DATE_FORMAT} /> : (record?.dueDate ? moment(record?.dueDate).format(DATE_FORMAT) : '')
        }
      },
      {
        title: intl.formatMessage({ id: 'assigned_task.column.comment' }),
        width: '10%',
        render: (text, record, index) => {
          return (
            <>
              <CommentOutlined /> {record?.openComments && record?.openComments > 0 ? record?.openComments : ""}
            </>
          )
        },
      },
      {
        title: intl.formatMessage({ id: 'function.column.author' }),
        dataIndex: 'author',
        width: '10%',
        sorter: true,
        filterSearch: true,
        ...getColumnSearchProps('author', SEARCH_TYPE.TEXT),
        render: (text, record) => {
          return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
            && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
            record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE &&
            record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE
          ) ? <Select
            filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            showSearch
            allowClear
            defaultValue={text}
            onChange={(e) => onChangeAuthor(e, record)}
            className='full-width'
            disabled={record.artefactType == REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT ? true : false}
          >
            {
              members.map(member => (
                <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
              ))
            }
          </Select> : text
        }
      },
      {
        title: intl.formatMessage({ id: 'function.column.reviewer' }),
        width: '10%',
        dataIndex: 'reviewer',
        ...getColumnSearchProps('reviewer', SEARCH_TYPE.TEXT),
        render: (text, record) => {
          return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
            && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
            record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE &&
            record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE
          ) ? <Select
            filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            showSearch
            allowClear
            defaultValue={text}
            onChange={(e) => onChangeReviewer(e, record)}
            className='full-width'>
            {
              memberReviewer.map(member => (
                <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
              ))
            }
          </Select> : text
        }
      },
      {
        title: intl.formatMessage({ id: 'function.column.customer' }),
        width: '10%',
        dataIndex: 'customer',
        ...getColumnSearchProps('customer', SEARCH_TYPE.TEXT),
        render: (text, record) => {
          return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
            && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
            record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE &&
            record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
            record.status !== STATUS.DELETE
          ) ? <Select
            filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            showSearch
            allowClear
            defaultValue={text}
            onChange={(e) => onChangeCustomer(e, record)}
            className='full-width'
            disabled={
              record.artefactType === REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT ||
                record.artefactType === REQ_ARTEFACT_TYPE_ID.USER_STORY
                ? true : false

            }
          >
            {
              customers.map(member => (
                <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
              ))
            }
          </Select> : text
        }
      },
    ]
  }
  const onChangeAuthor = (e, record) => {
    const payload = {
      artefactType: record?.artefactType,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefactType,
        artefactId: record?.id,
        author: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  const onChangeReviewer = (e, record) => {
    const payload = {
      artefactType: record?.artefactType,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefactType,
        artefactId: record?.id,
        reviewer: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  const onChangeCustomer = (e, record) => {
    const payload = {
      artefactType: record?.artefactType,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefactType,
        artefactId: record?.id,
        customer: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([])

  const onSelectChange = (selectedRows, items) => {
    setSelectedRowKeys(selectedRows)
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  }

  const handleFilterStatus = (e) => {
    let existPagination = JSON.parse(localStorage.getItem('paginationState') || '')
    existPagination.paginationProps.filters = { 3: e }
    existPagination.paginationProps.sorter = { field: "dueDate", order: "ascend" }
    localStorage.setItem('paginationState', JSON.stringify(existPagination))
    setColumns(columnConfig(e, null, members, memberReviewer, customers))
  }
  const handleFilterDate = (e) => {
    let existPagination = JSON.parse(localStorage.getItem('paginationState') || '')
    existPagination.paginationProps.filters = { 4: [e] }
    existPagination.paginationProps.sorter = { field: "dueDate", order: "ascend" }
    localStorage.setItem('paginationState', JSON.stringify(existPagination))
    setColumns(columnConfig([], e, members, memberReviewer, customers))
  }

  const CommentComponent: React.FC<any> = ({ handleShowComment }) => {
    return <Button
      type='primary'
      className='lav-btn-create'
      icon={<CommentOutlined />}
      ghost={true}
      onClick={handleShowComment} >Comment</Button>
  }

  const UpdateComponent: React.FC<any> = ({ record }) => {
    switch (record.artefactType) {
      case ARTEFACT_NAME[0].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.ACTOR)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[1].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[2].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[3].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[4].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.OBJECT)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[5].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[6].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[7].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[8].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.SCREEN)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[9].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[10].key: {
        return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.USECASE)
              setId(record.id)
            }} /> : <></>
      }
      case ARTEFACT_NAME[11].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[12].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.WORKFLOW)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[13].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.USER_STORY)
              setId(record.id)
            }} /> : <></>
        )
      }
      case ARTEFACT_NAME[14].key: {
        return (((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ?
          <Button ghost={true}
            style={{ border: 'none' }}
            icon={<CustomSvgIcons name="EditCustomIcon" />}
            onClick={() => {
              setArtefactType(REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT)
              setId(record.id)
            }} /> : <></>
        )
      }
    }
    return <></>
  }

  const handleDataChange = (e) => {
    setSelectedRowKeys([]);
  }

  return (
    <>
      <Space
        direction="vertical"
        size={'small'}
        className="my-assigned-task full-width p-20px"
      >
        {artefactType == 0 ?
          <div className="rq-page-heading" style={{ marginBottom: 10 }}>
            <Row align="middle" justify="space-between">
              <div>
                <Breadcrumb className="rq-breadcrumb" separator=">">
                  <Breadcrumb.Item>
                    <Link
                      className="breadcrumb-link-btn"
                      to={`${PROJECT_PREFIX}${projectCode}/dashboard`}
                    >
                      {projectCode} - {projectName}
                    </Link>
                  </Breadcrumb.Item>
                </Breadcrumb>
                <div className="title-page-heading">
                  <Space size="large">
                    <Title className="rq-tab rq-tab-active" level={4}>
                      {intl.formatMessage({ id: 'assigned_task.header.title' })}
                    </Title>
                    <Link
                      className="rq-tab"
                      to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REVIEW_TASK
                        }`}
                    >
                      <Title level={4}>
                        {intl.formatMessage({ id: 'assigned_task.header.text' })}
                      </Title>
                    </Link>
                    <Link className="rq-tab" to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.QUALITY_REPORT
                      }`}>
                      <Title level={4}>
                        {intl.formatMessage({ id: 'asigned_task.header.report' })}
                      </Title>
                    </Link>
                  </Space>
                </div>
              </div>
            </Row>
          </div> : <></>
        }
        {
          artefactType == 0 ?
            <Row gutter={16}>
              <Col span={24}>
                {columns?.length != 0 && <LavTable
                  showHeader={false}
                  isAssignTask={true}
                  title="assigned_task.header.title"
                  artefact_type="common.artefact.my-assigned-task"
                  apiUrl={API_URLS.MY_ASSIGNED_TASK}
                  columns={columns}
                  updateComponent={UpdateComponent}
                  commentComponent={CommentComponent}
                  artefactType={REQ_ARTEFACT_TYPE_ID.MY_ASSIGNED_TASK}
                  rowSelection={rowSelection}
                  isReload={reload}
                  reloadSuccess={() => setReload(false)}
                  handleFilterDate={handleFilterDate}
                  handleFilterStatus={handleFilterStatus}
                  onDataChange={handleDataChange}
                  hasComment={true}
                />}
              </Col>
            </Row>
            : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.USECASE ? <FunctionFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } id={id} /> : <></>
        }
        {
          artefactType == REQ_ARTEFACT_TYPE_ID.SCREEN ? <MockupScreenFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } screenID={id} /> : <></>
        }
        {
          artefactType == REQ_ARTEFACT_TYPE_ID.ACTOR ? <ActorFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } actorID={id} /> : <></>
        }
        {
          artefactType == REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE ? <BusinessRuleFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } id={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION ? <DataMigrationFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } id={id} /> : <></>
        }
        {
          artefactType == REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE ? <EmailTemplateFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } id={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT ? <NonFunctionalRequirementFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } id={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.OBJECT ? <ObjectsFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } objectID={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM ? <ObjectRelationshipDiagramFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } objectRelationshipID={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT ? <OtherRequirementFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } id={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX ? <PermissionMatrixFormPage
            onFinish={() => {
              setArtefactType(0)
            }
            } /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION ? <StateTransitionFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } stateTransitionID={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION ? <StateTransitionFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } stateTransitionID={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM ? <UseCaseDiagramFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } useCaseDiagramID={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT ? <UserRequirementFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } userRequirementID={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.USER_STORY ? <UserStoryManagementFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } id={id} /> : <></>
        }

        {
          artefactType == REQ_ARTEFACT_TYPE_ID.WORKFLOW ? <WorkFlowFormPage screenMode={SCREEN_MODE.EDIT}
            onDismiss={() => {
              setArtefactType(0)
            }} onFinish={() => {
              setArtefactType(0)
            }
            } workFlowID={id} /> : <></>
        }

      </Space>
    </>
  )
}

export default MyAssignedTask
