import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Typography } from 'antd'
import moment from 'moment'
import { <PERSON> } from 'react-router-dom'
import intl from '../../../../config/locale.config'
import {
  API_URLS,
  APP_ROUTES,
  ARTEFACT_NAME,
  DATE_FORMAT,
  PROJECT_PREFIX,
  REQ_ARTEFACT_TYPE_ID,
  SEARCH_TYPE
} from '../../../../constants'
import LavTable from '../../../../helper/component/lav-table'
import {
  extractProjectCode,
  getColumnDropdownFilterProps, getColumnSearchProps,
  getProjectName,
  renderStatusBadge
} from '../../../../helper/share'
import { useEffect } from 'react'
const { Title, Text } = Typography

const ReviewTask = () => {
  const projectCode = extractProjectCode();
  useEffect(() => {
    document.title = projectCode + "-" + intl.formatMessage({ id: 'assigned_task.header.text' });
  }, [])
  const projectName = getProjectName(projectCode);
  const columns = [
    {
      title: intl.formatMessage({
        id: 'my-pending_review_task.column.code',
      }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const artefact = ARTEFACT_NAME.find(
          (item) => item.key === record.artefactType
        )
        if (artefact) {
          return (
            <Link
              to={`${PROJECT_PREFIX}${extractProjectCode()}${artefact.details}${record.id
                }`}
            >
              {text}
            </Link>
          )
        } else {
          return text
        }
      },
    },
    {
      title: intl.formatMessage({
        id: 'my-pending_review_task.column.artefact',
      }),
      dataIndex: 'artefactType',
      width: '30%',
      sorter: true,
      ...getColumnDropdownFilterProps(ARTEFACT_NAME.map((item) => { return { text: item.value, value: item.key } })),
      render: (text: any) => {
        return (
          <>{ARTEFACT_NAME.map((item) => item.key === text && item.value)}</>
        )
      },
    },
    {
      title: intl.formatMessage({ id: 'my-pending_review_task.column.artefact-name' }),
      dataIndex: 'name',
      width: '45%',
      sorter: true,
      ...getColumnSearchProps('artefactsName', SEARCH_TYPE.TEXT),
      render: (text) => (
        <div
          className="tableDangerous"
          dangerouslySetInnerHTML={{ __html: text }}
        ></div>
      ),
    },
    {
      title: intl.formatMessage({ id: 'assigned_task.column.due' }),
      dataIndex: 'dueDate',
      width: '8%',
      sorter: true,
      sortOrder: 'ascend',
      ...getColumnSearchProps('dueDate', SEARCH_TYPE.DATE),
      render: (text, record) => {
        // lavdate
        return record?.dueDate ? moment(record.dueDate).format(DATE_FORMAT) : ''
      },
    },
    {
      title: intl.formatMessage({ id: 'my-pending_review_task.column.reviewer' }),
      dataIndex: 'reviewer',
      width: '8%',
      sorter: true,
      filterSearch: true,
      ...getColumnSearchProps('reviewer', SEARCH_TYPE.TEXT)
    },
  ]
  return (
    <>
      <Space
        direction="vertical"
        size={'small'}
        className="my-assigned-task full-width p-20px"
      >
        <div className='rq-page-heading' style={{ marginBottom: 10 }}>
          <Row align="middle" justify="space-between">
            <div>
              <Breadcrumb className='rq-breadcrumb' separator=">">
                <Breadcrumb.Item>
                  <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                </Breadcrumb.Item>
              </Breadcrumb>
              <div className='title-page-heading'>
                <Space size="large">
                  <Link className='rq-tab' to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MY_ASSIGNED_TASK}`}>
                    <Title level={4}>{intl.formatMessage({ id: 'assigned_task.header.title' })}</Title>
                  </Link>
                  <Title className='rq-tab rq-tab-active' level={4}>{intl.formatMessage({ id: 'assigned_task.header.text' })}</Title>
                  <Link className="rq-tab " to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.QUALITY_REPORT
                    }`}>
                    <Title level={4}>
                      {intl.formatMessage({ id: 'asigned_task.header.report' })}
                    </Title>
                  </Link>
                </Space>
              </div>
            </div>
          </Row>
        </div>

        <LavTable
          showHeader={false}
          title="assigned_task.header.title"
          artefact_type="common.artefact.my-assigned-task"
          artefactType={REQ_ARTEFACT_TYPE_ID.MY_PENDING_REVIEW_TASK}
          apiUrl={API_URLS.MY_PENDING_REVIEW}
          columns={columns}
          isAssignTask
        />
      </Space>
    </>
  )
}

export default ReviewTask
