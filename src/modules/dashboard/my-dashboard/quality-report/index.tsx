import {
    currentUserName,
    extractProjectCode,
    getColumnDropdownFilterProps,
    getColumnSearchProps,
    getProjectName,
    ShowAppMessage,
} from '../../../../helper/share'
import intl from '../../../../config/locale.config'
import { Breadcrumb, Button, Row, Space, Typography, notification, Modal } from 'antd'
import LavTable from '../../../../helper/component/lav-table'
import {
    API_URLS,
    APP_ROUTES,
    ARTEFACT_NAME,
    MESSAGE_TYPE,
    PROJECT_PREFIX,
    REQ_ARTEFACT_TYPE_ID,
    SEARCH_TYPE
} from '../../../../constants'
import { Link } from 'react-router-dom'
import moment from 'moment'
import AppCommonService from '../../../../services/app.service'
import { saveAs } from 'file-saver'
import { useEffect, useState } from 'react'
import useModalConfirmationConfig from './../../../../helper/hooks/useModalConfirmationConfig';
const { Title, Text } = Typography
const { confirm } = Modal


const QualityReport = () => {
    const projectCode = extractProjectCode();
    useEffect(() => {
        document.title = projectCode + "-" + intl.formatMessage({ id: 'asigned_task.header.report' });
      }, [])
    const projectName = getProjectName(projectCode);
    const [isModalVisible, setIsModalVisible] = useState(false)
    const modalConfirmConfig = useModalConfirmationConfig()

    const columns = [
        {
            title: intl.formatMessage({ id: 'quality-report.column.stt' }),
            dataIndex: '',
            render: (text, record, index: number) => <Text>{index + 1}</Text>,
        },
        {
            title: intl.formatMessage({
                id: 'quality-report.column.code',
            }),
            dataIndex: 'code',
            sorter: true,
            width:'85px',
            ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
            render: (text: string, record: any) => {
                const artefact = ARTEFACT_NAME.find(
                    (item) => item.key === record.artefacts
                )
                if (artefact) {
                    return (
                        <Link
                            to={`${PROJECT_PREFIX}${extractProjectCode()}${artefact.details}${record.id
                                }`}
                        >
                            {text}
                        </Link>
                    )
                } else {
                    return text
                }
            },
        },
        {
            title: intl.formatMessage({
                id: 'quality-report.column.artefact',
            }),
            dataIndex: 'artefactType',
            sorter: true,
            ...getColumnDropdownFilterProps(ARTEFACT_NAME.map((item) => { return { text: item.value, value: item.key } })),
            render: (text: any) => {
                return (
                    <>{ARTEFACT_NAME.map((item) => item.key === text && item.value)}</>
                )
            },
        },
        {
            title: intl.formatMessage({ id: 'quality-report.column.total_comment' }),
            dataIndex: 'totalComments',
        },
        {
            title: intl.formatMessage({ id: 'quality-report.column.bug_comment' }),
            dataIndex: 'totalBugComments',
        },
        {
            title: intl.formatMessage({ id: 'quality-report.column.qa_comment' }),
            dataIndex: 'totalQAComments',
        },
        {
            title: intl.formatMessage({ id: 'quality-report.column.suggestion_comment' }),
            dataIndex: 'totalSuggestionComments',
        },
    ]
    const handleGenerateQualityReport = () => {
        const reqNoti = notification
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_13' }),
            onOk() {
                setIsModalVisible(false)
                AppCommonService
                    .exportQualityReport()
                    .then((res: any) => {
                        var blob = new Blob([res.data])
                        let fName = `${currentUserName()}_${intl.formatMessage({ id: 'quality-report.export_file' })}_${moment().format(
                            'YYMMDDHHMM'
                        )}.xlsx`
                        saveAs(blob, fName)
                    })
                    .catch((e) => {
                        try {
                            let responseMess: any = e.response.data
                            const uint8Array: any = new Uint8Array(responseMess)
                            const messId = String.fromCharCode.apply(null, uint8Array)
                            reqNoti['error']({
                                description: intl.formatMessage({ id: messId }),
                                message: intl.formatMessage({ id: 'common.message.error' }),
                                placement: 'bottomRight'
                            })
                        } catch (err) {
                            ShowAppMessage(MESSAGE_TYPE.ERROR)
                        }
                    })
            },
            onCancel() { },
        })

    }
    return (
        <Space
            direction="vertical"
            size={'small'}
            className="my-assigned-task full-width p-20px"
        >
            <div className='rq-page-heading' style={{ marginBottom: 10 }}>
                <Row align="middle" justify="space-between">
                    <div>
                        <Breadcrumb className='rq-breadcrumb' separator=">">
                            <Breadcrumb.Item>
                                <Link
                                    className="breadcrumb-link-btn"
                                    to={`${PROJECT_PREFIX}${projectCode}/dashboard`}
                                >
                                    {projectCode} - {projectName}
                                </Link>
                            </Breadcrumb.Item>
                        </Breadcrumb>
                        <div className='title-page-heading'>
                            <Space size="large">
                                <Link className='rq-tab ' to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MY_ASSIGNED_TASK}`}>
                                    <Title level={4}>{intl.formatMessage({ id: 'assigned_task.header.title' })}</Title>
                                </Link>
                                <Link className="rq-tab" to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REVIEW_TASK
                                    }`}>
                                    <Title className='rq-tab' level={4}>{intl.formatMessage({ id: 'assigned_task.header.text' })}</Title>
                                </Link>
                                <Title className="rq-tab rq-tab-active" level={4}>
                                    {intl.formatMessage({ id: 'asigned_task.header.report' })}
                                </Title>

                            </Space>
                        </div>
                    </div>
                </Row>
            </div>
            <Button type="primary" onClick={handleGenerateQualityReport}>
                {intl.formatMessage({ id: 'quality-report.button' })}
            </Button>
            <LavTable
                showHeader={false}
                title="assigned_task.header.title"
                artefact_type="common.artefact.my-assigned-task"
                artefactType={REQ_ARTEFACT_TYPE_ID.QUALITY_REPORT}
                apiUrl={API_URLS.QUALITY_REPORT_TOTAL_COMMENT}
                columns={columns}
            />
        </Space>
    )
}

export default QualityReport;