
export interface EmailTemplateState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: EmailTemplateDetail | null,
  selectedData?: EmailTemplateDetail | null,
  isLoadingActors?: boolean,
  sendToAndCCList?: any | []
  isModalShow?: boolean
}
export interface EmailTemplateDetail {
  id?: number | null,
  code: string,
  status: number,
  objective: string,
  sendTo: number[],
  sendToActors: number[],
  sendToObjectProperties: number[],
  sendToOthers: string,
  cc: number[],
  ccActors: number[],
  ccObjectProperties: number[],
  ccOthers: string,
  subject: string,
  body: string,
  remark: string,
  storage: string,
  jira: string,
  confluence: string,
  reqElicitation: string,
  documentation: number | null
  assignee: string
  reviewer: string
  customer: string
  dueDate: any
  completeDate: any
  impacts: string,
  projectId: number | null,
}

export const defaultState: EmailTemplateState = {
  detail: {
    id: null,
    code: '',
    status: 0,
    objective: '',
    sendTo: [],
    sendToActors: [],
    sendToObjectProperties: [],
    sendToOthers: '',
    cc: [],
    ccActors: [],
    ccObjectProperties: [],
    ccOthers: '',
    subject: '',
    body: '',
    remark: '',
    storage: '',
    jira: '',
    confluence: '',
    reqElicitation: '',
    documentation: 0,
    assignee: '',
    reviewer: '',
    dueDate: '',
    customer: '',
    completeDate: '',
    impacts: '',
    projectId: null,
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  isLoadingActors: false,
  sendToAndCCList: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/EMAIL_TEMPLATE/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/EMAIL_TEMPLATE/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/EMAIL_TEMPLATE/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/EMAIL_TEMPLATE/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/EMAIL_TEMPLATE/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/EMAIL_TEMPLATE/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/EMAIL_TEMPLATE/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/EMAIL_TEMPLATE/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/EMAIL_TEMPLATE/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/EMAIL_TEMPLATE/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/EMAIL_TEMPLATE/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/EMAIL_TEMPLATE/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/EMAIL_TEMPLATE/GET_LIST_FAILED',

  GET_LIST_ACTORS_REQUEST = '@@MODULES/EMAIL_TEMPLATE/GET_LIST_ACTORS_REQUEST',
  GET_LIST_ACTORS_SUCCESS = '@@MODULES/EMAIL_TEMPLATE/GET_LIST_ACTORS_SUCCESS',
  GET_LIST_ACTORS_FAILED = '@@MODULES/EMAIL_TEMPLATE/GET_LIST_ACTORS_FAILED',

  DELETE_REQUEST = '@@MODULES/EMAIL_TEMPLATE/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/EMAIL_TEMPLATE/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/EMAIL_TEMPLATE/DELETE_FAILED',
  SET_MODAL_VISIBLE = '@@MODULES/EMAIL_TEMPLATE/SET_MODAL_VISIBLE',
}
