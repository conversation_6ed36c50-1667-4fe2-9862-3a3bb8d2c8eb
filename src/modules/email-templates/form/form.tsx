import { CommentState } from '@/modules/_shared/comment/type'
import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import CustomModal from '../../../helper/component/custom-modal'
import FormGroup from '../../../helper/component/form-group'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { createRequest, getDetailRequest, getListActorsRequest, resetState, setModalVisible, updateRequest } from '../action'
import { EmailTemplateState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text, Title } = Typography
const { confirm } = Modal
const { Option } = Select

interface EmailTemplateFormProps {
  id?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
}
interface EmailTemplateFormModalProps {
  id?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const EmailTemplateFormPage = ({ id, screenMode, onFinish, onDismiss }: EmailTemplateFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorDataBody: any = createRef()
  const getCkeditorDataRemarks: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.EmailTemplate) as EmailTemplateState
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [impacts, setImpacts] = useState<any>(false)
  const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
  const [bodyMessage, setBodyMessage] = useState('');
  const [remarksEmail, setRemarksEmail] = useState('');

  useBeforeUnload()
  // Destroy
  useEffect(() => {
    dispatch(getListActorsRequest(null))
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
    document.title = intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'update-email.label.update-email' : 'create-email.label.create-email' });
  }, [screenMode, id])

  const isJsonString = (data) => {
    try {
      JSON.parse(data);
    } catch (e) {
      return '';
    }
    return JSON.parse(data);
  }

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      let toValues: any[] = [];
      let ccValues: any[] = [];
      state.detail.sendTo?.forEach((e: any) => {
        toValues.push(e.actorId)
      })
      state.detail.sendToOthers?.split(';')?.forEach((e: any) => {
        if (e) {
          toValues.push(e)
        }
      })
      state.detail.cc?.forEach((e: any) => {
        ccValues.push(e.actorId)
      })
      state.detail.ccOthers?.split(';')?.forEach((e: any) => {
        if (e) {
          ccValues.push(e)
        }
      })
      const storage = isJsonString(state.detail?.storage);
      const jira = isJsonString(state.detail?.jira);
      const confluence = isJsonString(state.detail?.confluence);
      form.setFieldsValue({
        ...state.detail,
        'sendTo': toValues,
        'cc': ccValues,
        storageLinkText: storage ? storage?.textToDisplay : storage,
        storageWebLink: storage ? storage?.address : storage,
        jiraLinkText: jira ? jira?.textToDisplay : jira,
        jiraWebLink: jira ? jira?.address : jira,
        confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
        confluenceWebLink: confluence ? confluence?.address : confluence,
        req: state.selectedData?.reqElicitation
      })
      setBodyMessage(state.detail.body)
      setRemarksEmail(state.detail.remark)
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm();
        form.setFieldsValue({
          assignee: currentUserName(),
          dueDate: moment(new Date()),
        })
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss();
      }
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])
  const onChange = (e) => {
    setImpacts(JSON.stringify(e))
  }
  const onSubmit = debounce(async (values: any, st?: string) => {
    let sendToData = values.sendTo
    let sendToActor: number[] = []
    let sendToFreeText: string = ''
    sendToData?.forEach(element => {
      if (typeof (element) == 'number') {
        sendToActor.push(element)
      } else {
        sendToFreeText += `${element};`
      }
    });

    let CcData = values.cc
    let ccActor: number[] = []
    let ccFreeText: string = ''
    CcData?.forEach(element => {
      if (typeof (element) == 'number') {
        ccActor.push(element)
      } else {
        ccFreeText += `${element};`
      }
    });

    let bodyData = ''
    let remarkData = ''
    if (getCkeditorDataBody.current?.props?.data) {
      bodyData = getCkeditorDataBody.current.props.data
    }
    if (getCkeditorDataRemarks.current?.props?.data) {
      remarkData = getCkeditorDataRemarks.current.props.data
    }

    let mentionReferences = getReferencesFromEditor(bodyData);
    mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(remarkData));

    const requestData: any = {
      code: values.code,
      version: values.version,
      objective: values.objective,
      sendToActors: sendToActor,
      sendToObjectProperties: [],
      sendToOthers: sendToFreeText,
      ccActors: ccActor,
      ccObjectProperties: [],
      ccOthers: ccFreeText,
      subject: values.subject,
      body: bodyData,
      remarks: remarkData,
      storage: JSON.stringify({
        textToDisplay: values?.storageLinkText || '',
        address: values?.storageWebLink || '',
      }),
      jira: JSON.stringify({
        textToDisplay: values?.jiraLinkText || '',
        address: values?.jiraWebLink || '',
      }),
      confluence: JSON.stringify({
        textToDisplay: values?.confluenceLinkText || '',
        address: values?.confluenceWebLink || '',
      }),
      reqElicitation: values.req,
      documentation: values.documentation,
      id: id || null,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
      impacts: impacts
    }
    setIsCreateMore(values.createMore);
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
  }, 500)
  //   const handleTrim = (e) => {
  //     if(e && e.trim().length === 0){

  // console.log("1123")
  //     }

  //   }
  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setBodyMessage('');
    setRemarksEmail('');
    form.resetFields([
      'version',
      'code',
      'objective',
      'sendTo',
      'cc',
      'subject',
      'body',
      'remarks',
      'req',
      'documentation',
      'storageLinkText',
      'storageWebLink',
      'jiraLinkText',
      'jiraWebLink',
      'confluenceLinkText',
      'confluenceWebLink',
      'reviewer',
      'dueDate',
      'completeDate'
    ])
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'objective', title: intl.formatMessage({ id: 'create-email.label.objective' }), },
      { field: 'send-to', title: intl.formatMessage({ id: 'create-email.label.send-to' }), },
      { field: 'cc', title: intl.formatMessage({ id: 'create-email.label.cc' }), },
      { field: 'subject', title: intl.formatMessage({ id: 'create-email.label.subject' }), },
      { field: 'body', title: intl.formatMessage({ id: 'create-email.label.body' }), },
      { field: 'remarks', title: intl.formatMessage({ id: 'create-email.label.remarks' }), },
      { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
      { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.EMAIL_TEMPLATE,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  const handleSelect = (e) => {
    const sendToValues = form.getFieldValue('sendTo');
    let newValues: any[] = [];
    sendToValues.forEach(element => {
      // Fill-in value
      if (!Number(element)) {
        let optvalue = element.toString().trim()
        if (optvalue !== '') {
          newValues.push(optvalue);
        }
      } else {
        // Option value
        newValues.push(element)
      }
    });
    form.setFieldsValue({ 'sendTo': newValues })
  }

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={10}>
            <Space size="large">
              <Title level={4}>{intl.formatMessage({ id: screenMode === SCREEN_MODE.EDIT ? 'update-email.label.update-email' : 'create-email.label.create-email' })}</Title>

            </Space>
          </Col>

          <Col span={14}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={debounce(confirmCancel, 500)}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>

                <Button onClick={() => {
                  setIsSubmitForm(true)
                }} className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: 'common.action.save' })}
                </Button>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>


      {/* <Row align="middle">
          <Col span={2}>
            <TriggerComment screenMode={screenMode} field="version">
              <Text>
                {intl.formatMessage({
                  id: 'createobject.place-holder.version',
                })}
              </Text>
            </TriggerComment>
          </Col>

          <Col span={2}>
            <Form.Item
              name="version"
              className="mb-0"
              rules={[
                // { required: true, message: intl.formatMessage({ id: 'IEM_1' }) },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
              ]}
            >
              <Input
                placeholder={`${intl.formatMessage({
                  id: `createobject.place-holder.version`,
                })}`}
                maxLength={255}
              />
            </Form.Item>
          </Col>
        </Row> */}
      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Row align="middle">
          {screenMode === SCREEN_MODE.EDIT ?
            <Col span={5}>
              <div className='status-container'>
                <div>
                  {intl.formatMessage({ id: 'common.field.status' })}
                </div>
                <div>
                  {renderStatusBadge(state.detail?.status)}
                </div>
              </div>
            </Col> : <></>
          }
        </Row>
        <Card className='rq-form-block' title={intl.formatMessage({ id: 'create-email.card.email-infomation' })}>
          {
            screenMode === SCREEN_MODE.EDIT ? <Row>
              <Col span={6}>
                <FormGroup label={intl.formatMessage({ id: 'common.label.code' })}>
                  <Form.Item rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input maxLength={255} value={state.detail?.code} disabled></Input>
                  </Form.Item>
                </FormGroup>
              </Col>
            </Row> : <></>
          }

          <FormGroup inline className="rq-fg-comment" required label={
            <TriggerComment screenMode={screenMode} field="objective">
              {intl.formatMessage({ id: 'create-email.label.objective' })}
            </TriggerComment>}
            labelSpan={3}
            controlSpan={21}
          >
            <Form.Item
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
              name="objective"
            >
              <Input maxLength={255} />
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" required label={
            <TriggerComment screenMode={screenMode} field="send-to">
              {intl.formatMessage({ id: 'create-email.label.send-to' })}
            </TriggerComment>}>
            <div className='rq-form-note'>
              {intl.formatMessage({ id: 'email.label.sendToAndCc' })}
            </div>
            <Form.Item name="sendTo"
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
              ]}
            >
              <Select
                filterOption={(input, option: any) => option.children?.toLowerCase().indexOf(input?.toLowerCase()) >= 0}
                showSearch
                mode="tags"
                onSelect={handleSelect}
              >
                {state.sendToAndCCList.map((item: any, idx: number) => {
                  return (
                    <Option key={idx} value={item.id}>
                      {item.name}
                    </Option>
                  )
                })}
              </Select>
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="cc">
              {intl.formatMessage({ id: 'create-email.label.cc' })}
            </TriggerComment>}>
            <div className='rq-form-note'>
              {intl.formatMessage({ id: 'email.label.sendToAndCc' })}
            </div>
            <Form.Item name="cc">
              <Select
                filterOption={(input, option: any) => option.children?.toLowerCase().indexOf(input?.toLowerCase()) >= 0}
                showSearch
                mode="tags"
              >
                {state.sendToAndCCList?.map((item: any, idx: number) => {
                  return (
                    <Option key={idx} value={item.id}>
                      {item.name}
                    </Option>
                  )
                })}
              </Select>
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} required className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="subject">
              {intl.formatMessage({ id: 'create-email.label.subject' })}
            </TriggerComment>}>
            <Form.Item name="subject" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }
              , {
              required: true,
              message: intl.formatMessage({ id: 'IEM_1' }),
            },
            {
              validator: async (rule, value) => {
                if (value && value.trim().length === 0) {
                  throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                }
              },
            },
            ]}>
              <Input maxLength={255} />
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} required className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="body">
              {intl.formatMessage({ id: 'create-email.label.body' })}
            </TriggerComment>}>
            <div className='rq-form-note'>
              {intl.formatMessage({ id: 'email.label.body' })}
            </div>
            <Form.Item name="body" rules={[{
              validator: async (rule, value) => {
                const body = getCkeditorDataBody?.current?.props?.data
                if (body == '' || body == undefined) {
                  throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                }
              }
            }]}>
              <CkeditorMention
                ref={getCkeditorDataBody}
                data={bodyMessage}
              />
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="remarks">
              {intl.formatMessage({ id: 'create-email.label.remarks' })}
            </TriggerComment>}>
            <Form.Item name="remarks">
              <CkeditorMention
                ref={getCkeditorDataRemarks}
                data={remarksEmail}
              />
            </Form.Item>
          </FormGroup>
        </Card>
        {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE} onChange={onChange} isSubmitForm={isSubmitForm} />}

        <LavEffortEstimationForm
          screenMode={screenMode}
          hasDevelopment={state?.selectedData?.hasOwnProperty('development')}
          hasImplementation={state?.selectedData?.hasOwnProperty('implementation')}
        />
        {/* <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.effort' })}>
            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="req-elicitation">
                {intl.formatMessage({ id: 'createscreen.label.req' })}
              </TriggerComment>}>
              <Form.Item name="reqElicitation">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>

            <FormGroup className="rq-fg-comment" inline label={
              <TriggerComment screenMode={screenMode} field="documentation">
                {intl.formatMessage({ id: 'createscreen.label.documentation' })}
              </TriggerComment>}>
              <Form.Item name="documentation">
                <InputNumber min={0} maxLength={2} />
              </Form.Item>
            </FormGroup>
          </Card> */}

        <LavRelatedLinksForm form={form} screenMode={screenMode} />
      </Space>

    </Form>
  </Spin>
}
// const EmailTemplateForm = ({ id, onFinish, screenMode = SCREEN_MODE.CREATE, buttonType = BUTTON_TYPE.TEXT }: EmailTemplateFormProps) => {
//   const dispatch = useDispatch();
//   const [isModalVisible, setIsModalVisible] = useState<any>(null)

//   useEffect(() => {
//     if (isModalVisible !== null) {
//       dispatch(setModalVisible(isModalVisible))
//     }
//   }, [isModalVisible])

//   return (
//     <>
//       {
//         buttonType === BUTTON_TYPE.TEXT ?
//           <Button
//             ghost={screenMode === SCREEN_MODE.CREATE}
//             type='primary'
//             className={`lav-btn-${screenMode === SCREEN_MODE.CREATE ? 'create' : 'update'}`}
//             onClick={() => setIsModalVisible(true)}
//             icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <></>}
//           >
//             {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'email.button.create-email' : 'common.action.update' })}
//           </Button> :
//           buttonType === BUTTON_TYPE.ICON ?
//             <Button type="text" onClick={() => setIsModalVisible(true)} icon={screenMode === SCREEN_MODE.CREATE ? <PlusOutlined /> : <CustomSvgIcons name="EditCustomIcon" />} /> :
//             <></>
//       }
//       {isModalVisible === true ? <EmailTemplateFormModal id={id} onFinish={onFinish} screenMode={screenMode} onDismiss={() => setIsModalVisible(false)} /> : <></>}
//     </>
//   )
// }
export default EmailTemplateFormPage
