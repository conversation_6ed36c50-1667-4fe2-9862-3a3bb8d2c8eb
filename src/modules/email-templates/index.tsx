import { PlusOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import EmailTemplateFormPage from './form/form'
const { Text } = Typography

const EmailTemplate = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  useEffect(() => {       
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'email.header.title'}); 
  }, [screenMode])

  const columns = [
    {
      title: intl.formatMessage({ id: 'email.column.email-code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MAIL_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'email.column.email-objective' }),
      dataIndex: 'objective',
      ...getColumnSearchProps('objective', SEARCH_TYPE.TEXT),
      render: (text) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'email.column.email-subject' }),
      dataIndex: 'subject',
      sorter: true,
      ...getColumnSearchProps('subject', SEARCH_TYPE.TEXT),
      render: (text) => {
        return <Text>{text}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'email.column.send-to' }),
      dataIndex: 'sendTo',
      ...getColumnSearchProps('sendTo', SEARCH_TYPE.TEXT),
      render: (sendTo: any, record: any) => {
        const freeTextFinal: string[] = []
        let checkOtherArtefact = false
        sendTo?.map((item) => {
          if (item.sendToFreeText) {
            const freeText = item.sendToFreeText
            const listFree = freeText.split(';')
            listFree?.map((item: string, index) => {
              if (index !== listFree.length - 1) {
                freeTextFinal.push(item)
              }
            })
          } else {
            checkOtherArtefact = true
          }
        })
        return (
          <>
            {sendTo?.map(
              (e: any, index) =>
                (e.actorId || e.objectId) && (
                  <Link
                    key={e.actorId ? `${e.actorId} + _${index}` : `${e.objectId}+ _${index}`}
                    to={
                      e.actorId
                        ? `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.ACTOR_DETAIL}${e.actorId}`
                        : `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_DETAIL}${e.objectId}`
                    }
                  >
                    {sendTo[index - 1]?.sendToFreeText === null && index !== 0
                      ? `,`
                      : ``}{' '}
                    {e.actorId ? e.actorName : e.objectName}
                  </Link>
                )
            )}

            {freeTextFinal.length > 0 &&
              freeTextFinal.map((item, index) => (
                <Text key={index}>
                  {checkOtherArtefact || index !== 0 ? ',' : ''} {item}
                </Text>
              ))}
          </>
        )
      },
    },
    // {
    //   title: intl.formatMessage({ id: 'email.column.status' }),
    //   dataIndex: 'status',
    //   width: '80px',
    //   ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
    //   sorter: true,
    //   render: (record) => renderStatusBadge(record),
    // },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'epic.create' })}
      </Button> : <></>
    // return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <EmailTemplateForm onFinish={() => handleDataChange()} /> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer || hasRole(APP_ROLES.PM)) && record?.status === STATUS.SUBMITTED)) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (record.status !== STATUS.DELETE && (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? children : <></>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ? <LavTable
        title="email.header.title"
        artefact_type="common.artefact.email"
        apiUrl={API_URLS.EMAILS}
        columns={columns}
        artefactType={REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE}
        updateComponent={UpdateComponent}
        createComponent={CreateComponent}
        deleteComponent={DeleteComponent}
      /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <EmailTemplateFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <EmailTemplateFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} id={id} /> : <></>
      }
    </Space>
  )
}

export default EmailTemplate
