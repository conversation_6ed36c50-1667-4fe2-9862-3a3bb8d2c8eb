import LavImpact from '../../../helper/component/lav-impact'
import AppState from '@/store/types'
import {
    <PERSON>read<PERSON>rumb, Button, Card, Col, Divider, Modal, Row, Space, Spin, Typography
} from 'antd'
import { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROLES, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, STATUS } from '../../../constants'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavEffortEstimation from '../../../helper/component/lav-efffort-estimation'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { deleteRequest, updateRequest } from '../action'

const { Title, Text } = Typography
const { confirm } = Modal

interface RightControlProps {
    data: any | [],
    emailID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}
const RightControl = ({ data, emailID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const modalConfirmConfig = useModalConfirmationConfig()

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    const sendToDisplay = (sendTo) => {
        const freeTextFinal: string[] = []
        let checkOtherArtefact = false
        sendTo?.map((item) => {
            if (item.sendToFreeText) {
                const freeText = item.sendToFreeText
                const listFree = freeText.split(';')
                listFree?.map((item: string, index) => {
                    if (index !== listFree.length - 1) {
                        freeTextFinal.push(item)
                    }
                })
            } else {
                checkOtherArtefact = true
            }
        })
        return (
            <>
                {sendTo?.map(
                    (e: any, index) =>
                        (e.actorId || e.objectParentId) && (
                            <Link
                                key={e.actorId ? e.actorId : e.objectParentId}
                                to={
                                    e.actorId
                                        ? `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.ACTOR_DETAIL}${e.actorId}`
                                        : `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_DETAIL}${e.objectParentId}`
                                }
                            >
                                {sendTo[index - 1]?.sendToFreeText === null && index !== 0
                                    ? `,`
                                    : ``}{' '}
                                {e.actorId ? e.actorName : e.objectName}
                            </Link>
                        )
                )}

                {freeTextFinal.length > 0 &&
                    freeTextFinal.map((item) => (
                        <Text key={item}>
                            {' '}
                            {checkOtherArtefact ? ',' : ''} {item}{' '}
                        </Text>
                    ))}
            </>
        )
    }

    const handleCancelRecord = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage(
                { id: 'CFD_6_4' },
                { Artefact: intl.formatMessage({ id: 'common.artefact.email' }) }
            ),
            onOk() {
                let toValues: any[] = [];
                let ccValues: any[] = [];
                data.sendTo?.forEach((elm: any) => {
                    toValues.push(elm.actorId)
                })
                data.cc?.forEach((elm: any) => {
                    ccValues.push(elm.actorId)
                })
                let requestData: any = {
                    code: data.code,
                    version: data.version,
                    status: STATUS.CANCELLED,
                    objective: data.objective,
                    sendToActors: toValues,
                    sendToObjectProperties: [],
                    sendToOthers: data.sendToOthers,
                    ccActors: ccValues,
                    ccObjectProperties: [],
                    ccOthers: data.ccOthers,
                    subject: data.subject,
                    body: data.body,
                    remarks: data.remark,
                    storage: data.storage,
                    jira: data.jira,
                    confluence: data.confluence,
                    reqElicitation: data.reqElicitation,
                    documentation: data.documentation,
                    id: data.id
                }
                requestData.messageAction = MESSAGE_TYPES.CANCEL
                dispatch(updateRequest(requestData))
            },
            onCancel() { },
        })
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;


    useEffect(() => {
        if(data)
            document.title = intl.formatMessage({ id: `create-email.title.create-email` }) + "-" + data?.code;

        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'status', title: intl.formatMessage({ id: 'common.field.status' }), },
            { field: 'objective', title: intl.formatMessage({ id: 'create-email.label.objective' }), },
            { field: 'send-to', title: intl.formatMessage({ id: 'create-email.label.send-to' }), },
            { field: 'cc', title: intl.formatMessage({ id: 'create-email.label.cc' }), },
            { field: 'subject', title: intl.formatMessage({ id: 'create-email.label.subject' }), },
            { field: 'body', title: intl.formatMessage({ id: 'create-email.label.body' }), },
            { field: 'remarks', title: intl.formatMessage({ id: 'create-email.label.remarks' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
            { field: 'screen', title: intl.formatMessage({ id: 'view-screen-list.label.screen' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.EMAIL_TEMPLATE,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT

    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {intl.formatMessage({ id: `create-email.title.create-email` })} - {data?.code}
                    </Title>
                </div>

                <Space size="small">
                    {/* Delete record */}
                    {((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE) ? <DeleteButton
                        type={BUTTON_TYPE.TEXT}
                        content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.email' }) })}
                        okCB={() => dispatch(deleteRequest(emailID))}
                        confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
                    }

                    {/* Update record */}
                    {((((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && data?.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer || hasRole(APP_ROLES.PM)) && data?.status === STATUS.SUBMITTED)) &&
                        data?.status !== STATUS.CANCELLED &&
                        data?.status !== STATUS.DELETE &&
                        data?.status !== STATUS.ENDORSE
                    ) ? <Button
                        type='primary'
                        className='lav-btn-create'
                        onClick={() => {
                            setScreenMode()
                        }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                    }

                    {/* Cancel record */}
                    {(hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && (data?.status === STATUS.SUBMITTED) ? <Button className='cancel-btn' onClick={() => handleCancelRecord()}>
                        {intl.formatMessage({ id: `common.action.cancel` })}</Button> : <></>
                    }
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'create-email.card.email-infomation',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={3}>
                                    <TriggerComment field="objective">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.objective',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <Text>{data?.objective}</Text>
                                </Col>

                                <Col span={3}>
                                    <TriggerComment field="send-to">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.send-to',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    {data?.id !== -1 && sendToDisplay(data?.sendTo)}
                                    {data?.sendToOthers?.split(';')?.map((item, index) => {
                                        let content = ''
                                        if (item && index !== 0) {
                                            content += `, ${item}`
                                        } else if (item && index === 0 && data?.sendTo.length == 0) {
                                            content += `${item}`
                                        } else if (item && index === 0 && data?.sendTo.length !== 0) {
                                            content += `, ${item}`
                                        }
                                        return content ? <Text>{content}</Text> : <></>
                                    })}
                                </Col>
                                <Col span={3}>
                                    <TriggerComment field="cc">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.cc',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    {data?.id !== -1 && sendToDisplay(data?.cc)}
                                    {data?.ccOthers?.split(';')?.map((item, index) => {
                                        let content = ''
                                        if (item && index !== 0) {
                                            content += `, ${item}`
                                        } else if (item && index === 0 && data?.cc.length == 0) {
                                            content += `${item}`
                                        } else if (item && index === 0 && data?.cc.length !== 0) {
                                            content += `, ${item}`
                                        }
                                        return content ? <Text>{content}</Text> : <></>
                                    })}
                                </Col>
                                <Col span={3}>
                                    <TriggerComment field="subject">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.subject',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>{data?.subject}</Col>
                                <Col span={3}>
                                    <TriggerComment field="body">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.body',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.body }}
                                    ></div>
                                </Col>
                                <Col span={3}>
                                    <TriggerComment field="remarks">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'create-email.label.remarks',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={21}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{ __html: data?.remark }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>

                        <LavReferences data={data} />
                        {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE} onChange={() => { }} isViewMode={true} />}
                        {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE} onChange={() => { }} isViewMode={true} /> : <></>} */}

                        <Row justify="space-between">
                            <Col span={8}>
                                <LavEffortEstimation data={data} />
                            </Col>
                            <Col span={15}>
                                <LavRelatedLinks data={data} />
                            </Col>
                        </Row>

                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
