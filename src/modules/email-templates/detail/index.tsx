import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import EmailTemplateFormPage from '../form/form'
import { EmailTemplateState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'

const EmailTemplateDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.EmailTemplate) as EmailTemplateState;
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.emailID) {
      dispatch(getDetailRequest(props.match.params.emailID))
    }
  }, [props])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MAIL}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.emailID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if(isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MAIL_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
        {
        screenMode === SCREEN_MODE.VIEW ? 
        <> 
      <Col span={5}>
        <LavLeftControl
          activeId={props.match.params.emailID}
          apiUrl={API_URLS.REFERENCES_EMAIL_TEMPLATES}
          route={APP_ROUTES.MAIL_DETAIL}
          title='email.header.title'
          reload={reload}
          artefactType={REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE}
          reloadSuccess={() => setReload(false)}
          handleCreate={handleCreate}
          hideStatus={true}
        >
           {
            (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) &&
            <Button ghost={true}
              type='primary'
              className='lav-btn-create'
              icon={<PlusOutlined />}
              onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'epic.create' })}
            </Button> 
          }
          {/* {
            ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <EmailTemplateForm onFinish={() => {setReload(true); setIsCreate(true)}} /> : <></>} */}
        </LavLeftControl>
      </Col>
      <Col span={19}>
        <RightControl setScreenMode={()=> setScreenMode(SCREEN_MODE.EDIT)} onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} emailID={props.match.params.emailID} isModalShow={state?.isModalShow} />
      </Col>
      </> : <></>
}
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <EmailTemplateFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <EmailTemplateFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
              }} id={props.match.params.emailID} />
          </Col> : <></>
      }
    </Row>
  )
}

export default EmailTemplateDetail
