import { Card, Col, Row, Space, Typography } from 'antd'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { APP_ROUTES, PROJECT_PREFIX } from '../../../constants'
import LavReferences from '../../../helper/component/lav-references'
import { extractProjectCode, renderStatusBadge } from '../../../helper/share'
import { Regex } from '../../../helper/share/type'

const { Title, Text } = Typography

interface RightControlProps {
  data: any | [],
}
const EmailDetailInfo = ({ data }: RightControlProps) => {
  const sendToDisplay = (sendTo) => {
    const freeTextFinal: string[] = []
    let checkOtherArtefact = false
    sendTo?.map((item) => {
      if (item.sendToFreeText) {
        const freeText = item.sendToFreeText
        const listFree = freeText.split(Regex.CcRegex)
        listFree?.map((item: string, index) => {
          if (index !== listFree.length - 1) {
            freeTextFinal.push(item)
          }
        })
      } else {
        checkOtherArtefact = true
      }
    })
    return (
      <>
        {sendTo?.map(
          (e: any, index) =>
            (e.actorId || e.objectParentId) && (
              <Link
                key={e.actorId ? e.actorId : e.objectParentId}
                to={
                  e.actorId
                    ? `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.ACTOR_DETAIL}${e.actorId}`
                    : `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_DETAIL}${e.objectParentId}`
                }
              >
                {sendTo[index - 1]?.sendToFreeText === null && index !== 0
                  ? `,`
                  : ``}{' '}
                {e.actorId ? e.actorName : e.objectName}
              </Link>
            )
        )}

        {freeTextFinal.length > 0 &&
          freeTextFinal.map((item) => (
            <Text key={item}>
              {' '}
              {checkOtherArtefact ? ',' : ''} {item}{' '}
            </Text>
          ))}
      </>
    )
  }




  return (

    <Space direction="vertical" size="middle">
      <Space size="large">
        {/* <span>
          <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
        </span> */}
        {renderStatusBadge(data?.status)}
      </Space>
      <Card
        title={
          <Title level={5}>
            {`${intl.formatMessage({
              id: 'create-email.card.email-infomation',
            })}`}
          </Title>
        }
        bordered={true}
      >
        <Row gutter={[16, 4]}>
          <Col span={3}>
            <Text type="secondary">
              {intl.formatMessage({
                id: 'create-email.label.objective',
              })}:
            </Text>
          </Col>
          <Col span={21}>
            <Text>{data?.objective}</Text>
          </Col>

          <Col span={3}>
            <Text type="secondary">
              {intl.formatMessage({
                id: 'create-email.label.send-to',
              })}:
            </Text>
          </Col>
          <Col span={21}>
            {data?.id !== -1 && sendToDisplay(data?.sendTo)}
            {data?.sendToOthers?.split(';')?.map((item, index) => (
              item ? <Text key={index}>
                {`, ${item}`}
              </Text> : <></>
            ))}
          </Col>
          <Col span={3}>
            <Text type="secondary">
              {intl.formatMessage({
                id: 'create-email.label.cc',
              })}:
            </Text>
          </Col>
          <Col span={21}>
            {data?.id !== -1 && sendToDisplay(data?.cc)}
            {data?.ccOthers?.split(';')?.map((item, index) => (
              item ? <Text key={index}>
                {`, ${item}`}
              </Text> : <></>
            ))}
          </Col>
          <Col span={3}>
            <Text type="secondary">
              {intl.formatMessage({
                id: 'create-email.label.subject',
              })}:
            </Text>
          </Col>
          <Col span={21}>{data?.subject}</Col>
          <Col span={3}>
            <Text type="secondary">
              {intl.formatMessage({
                id: 'create-email.label.body',
              })}:
            </Text>
          </Col>
          <Col span={21}>
            <div
              className="tableDangerous"
              dangerouslySetInnerHTML={{ __html: data?.body }}
            ></div>
          </Col>
          <Col span={3}>
            <Text type="secondary">
              {intl.formatMessage({
                id: 'create-email.label.remarks',
              })}:
            </Text>
          </Col>
          <Col span={21}>
            <div
              className="tableDangerous"
              dangerouslySetInnerHTML={{ __html: data?.remark }}
            ></div>
          </Col>
        </Row>
      </Card>

      <LavReferences data={data} />
    </Space>
  )
}

export default EmailDetailInfo
