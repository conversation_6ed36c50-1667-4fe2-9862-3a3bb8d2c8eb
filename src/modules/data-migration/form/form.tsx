import AppState from '@/store/types'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTask from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import LavVersion from '../../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import AppCommonService from '../../../services/app.service'
import { createRequest, getDetailRequest, getListObjectsRequest, getListUserRequirementsRequest, resetState, updateRequest } from '../action'
import { DataMigrationState } from '../type'
import LavPageHeader from './../../../helper/component/lav-breadcumb/index'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select

interface DataMigrationFormProps {
  id?: number,
  onFinish?: () => void | null,
  buttonType?: BUTTON_TYPE.ICON | BUTTON_TYPE.TEXT,
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE,
}
interface DataMigrationFormModalProps {
  id?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const DataMigrationFormPage = ({ id, screenMode, onFinish, onDismiss }: DataMigrationFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorData: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.DataMigration) as DataMigrationState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const [impacts, setImpacts] = useState<any>(false)
  const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [dataMigrationRule, setDataMigrationRule] = useState('');

  useBeforeUnload()
  // Destroy
  useEffect(() => {
    dispatch(getListObjectsRequest(null))
    dispatch(getListUserRequirementsRequest(null))
    form.setFieldsValue({
      assignee: currentUserName()
    })
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
    document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'data.create.data' : 'data.update.data' });
  }, [screenMode, id])

  const isJsonString = (data) => {
    try {
      JSON.parse(data);
    } catch (e) {
      return '';
    }
    return JSON.parse(data);
  }

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {

      const storage = isJsonString(state.detail?.storage);
      const jira = isJsonString(state.detail?.jira);
      const confluence = isJsonString(state.detail?.confluence);
      form.setFieldsValue({
        code: state.detail.code,
        dataMigration: state.detail.title,
        object: state.detail.baObject?.id,
        req: state.detail.reqElicitation,
        documentation: state.detail.documentation,
        storageLinkText: storage ? storage?.textToDisplay : storage,
        storageWebLink: storage ? storage?.address : storage,
        jiraLinkText: jira ? jira?.textToDisplay : jira,
        jiraWebLink: jira ? jira?.address : jira,
        confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
        confluenceWebLink: confluence ? confluence?.address : confluence,
        userRequirements: state.detail.userRequirements?.id,
      })
      setDataMigrationRule(state.detail.content);
    }
  }, [state.detail])


  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      const version = form.getFieldValue('version')
      const changeDescription = form.getFieldValue('changeDescription')

      if (version && version !== '' ) {
        const payload = {
          version: version.substring(version.length - 1) === "." ? `${version}0` : version,
          description: changeDescription,
          artefactCode: state.detail?.code,
        }
        AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION, state.detail?.id).then((e) => {
          if (isCreateMore) {
            resetForm();
            form.setFieldsValue({
              assignee: currentUserName(),
              dueDate: moment(new Date()),

            })
          } else {
            if (onFinish) {
              onFinish();
            }
            onDismiss();
          }
          setIsDraft(null);
          setIsCreateMore(false);
        })
      } else {
        if (isCreateMore) {
          resetForm();
          form.setFieldsValue({
            assignee: currentUserName(),
            dueDate: moment(new Date()),

          })
        } else {
          if (onFinish) {
            onFinish();
          }
          onDismiss();
        }
        setIsDraft(null);
        setIsCreateMore(false);
      }
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce(async (values: any, st?: string) => {
    const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
    const requestData: any = {
      id: id || null,
      title: values.dataMigration,
      content: getCkeditorData.current?.props?.data,
      status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
      storage: JSON.stringify({
        textToDisplay: values?.storageLinkText || '',
        address: values?.storageWebLink || '',
      }),
      jira: JSON.stringify({
        textToDisplay: values?.jiraLinkText || '',
        address: values?.jiraWebLink || '',
      }),
      confluence: JSON.stringify({
        textToDisplay: values?.confluenceLinkText || '',
        address: values?.confluenceWebLink || '',
      }),
      version: values.version,
      baObject: values.object,
      userRequirements: values.userRequirements,
      code: state.detail?.code,
      reqElicitation: values.req,
      documentation: values.documentation,
      useCase: values.useCase,
      workFlow: values.workFlow,
      submittedBy: state.detail?.submittedBy,
      author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
      reviewer: values.reviewer,
      customer: values.customer || '',
      dueDate: values.dueDate ? values.dueDate?.toDate() : null,
      completeDate: values.completeDate ? values.completeDate?.toDate() : null,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
      impacts: impacts
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.data-migration' }) }
        ),
        onOk() {
          requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(null);
    setDataMigrationRule('')
    form.resetFields([
      'version',
      'code',
      'dataMigration',
      'dataMigrationRule',
      'object',
      'req',
      'description',
      'storageLinkText',
      'storageWebLink',
      'jiraLinkText',
      'jiraWebLink',
      'confluenceLinkText',
      'confluenceWebLink',
      'req',
      'documentation',
      'reviewer',
      'customer',
      'dueDate',
      'completeDate',
      'userRequirements'
    ])
    form.setFieldsValue({
      assignee: currentUserName()
    })
  }

  //#region COMMENT INIT
  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;
  const onChange = (e) => {
    setImpacts(JSON.stringify(e))
  }
  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'rule', title: intl.formatMessage({ id: 'data.rule' }), },
      { field: 'object', title: intl.formatMessage({ id: 'data.object' }), },
      { field: 'userRequirements', title: intl.formatMessage({ id: 'data.user-requirement' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
      { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
      { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
      { field: 'req-elicitation', title: intl.formatMessage({ id: 'data.req-elicitation' }), },
      { field: 'documentation', title: intl.formatMessage({ id: 'data.documentation' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.DATA_MIGRATION_REQUIREMENT,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <LavPageHeader
        showBreadcumb
        title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'data.create.data' : 'data.update.data' })}
      >

        <Space size="small">
          {screenMode === SCREEN_MODE.CREATE ? <Form.Item
            style={{ marginBottom: '0px' }}
            valuePropName="checked"
            name="createMore"
            wrapperCol={{ span: 24 }}
          >
            <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
          </Form.Item> : <></>}
          <Button onClick={debounce(confirmCancel, 500)}>
            {intl.formatMessage({ id: 'common.action.close' })}
          </Button>

          {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
            <Button type="primary" ghost htmlType="submit" onClick={() => {
              setIsDraft(false)
              setIsSubmitForm(true)
            }}>
              {intl.formatMessage({ id: 'common.action.submit' })}
            </Button> : <></>
          }

          <Button onClick={() => {
            setIsDraft(true)
            setIsSubmitForm(true)
          }} className="success-btn" htmlType="submit">
            {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
          </Button>
        </Space>
      </LavPageHeader>

      <Row align='middle' style={{ marginTop: 10 }}>
        {screenMode === SCREEN_MODE.EDIT ?
          <Col span={5}>
            <div className='status-container'>
              <div>
                {intl.formatMessage({ id: 'common.field.status' })}
              </div>
              <div>
                {renderStatusBadge(state.detail?.status)}
              </div>
            </div>
          </Col> : <></>
        }
      </Row>
      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Card className='rq-form-block' title={intl.formatMessage({ id: 'data.info' })}>
          {
            screenMode === SCREEN_MODE.EDIT ? <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
              <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                <Input disabled maxLength={255} />
              </Form.Item>
            </FormGroup> : <></>
          }
          <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
            <Form.Item
              name="dataMigration"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'IEM_1' }),
                },
                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                {
                  validator: async (rule, value) => {
                    if (value && value.trim().length === 0) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  },
                },
              ]}
            >
              <Input
                placeholder={`${intl.formatMessage({
                  id: `data.column.data-migration`,
                })}${intl.formatMessage({
                  id: `common.mandatory.*`,
                })}`}
                maxLength={255}
              />
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="rule">
              {intl.formatMessage({ id: 'data.rule' })}
            </TriggerComment>}>
            <Form.Item
              name="dataMigrationRule"
              labelAlign="left"
              wrapperCol={{ span: 24 }}
            >
              <CkeditorMention
                ref={getCkeditorData}
                data={dataMigrationRule}
              />
            </Form.Item>
          </FormGroup>
        </Card>

        <Card className='rq-form-block' title={intl.formatMessage({ id: 'data.reference' })}>
          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="object">
              {intl.formatMessage({ id: 'state.object' })}
            </TriggerComment>}>
            <Form.Item name="object">
              <Select
                filterOption={(input, option: any) =>
                  option.children
                    .toLowerCase()
                    .indexOf(input.toLowerCase()) >= 0
                }
                showSearch
              >
                {state?.listObjects?.map(
                  (item: any) =>
                    item.status !== STATUS.DELETE &&
                    item.status !== STATUS.CANCELLED && (
                      <Option key={item.id} value={item.id}>
                        {item.name}
                      </Option>
                    )
                )}
              </Select>
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="userRequirements">
              {intl.formatMessage({ id: 'state.user-requirement' })}
            </TriggerComment>}>
            <Form.Item name="userRequirements">
              <Select
                filterOption={(input, option: any) =>
                  option.children
                    .toLowerCase()
                    .indexOf(input.toLowerCase()) >= 0
                }
                showSearch
              >
                {state?.listUserRequirements?.data?.map(
                  (item: any) =>
                    item.status !== STATUS.DELETE &&
                    item.status !== STATUS.CANCELLED && (
                      <Option key={item.id} value={item.id}>
                        {item.name}
                      </Option>
                    )
                )}
              </Select>
            </Form.Item>
          </FormGroup>
        </Card>
        <AssignTask
          form={form}
          data={screenMode === SCREEN_MODE.EDIT ? state.detail : null}
          isSubmit={isDraft == false}
          screenMode={screenMode}
        />
        {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION} onChange={onChange} isSubmitForm={isSubmitForm} />}

        <LavEffortEstimationForm
          screenMode={screenMode}
          hasDevelopment={state?.detail?.hasOwnProperty('development')}
          hasImplementation={state?.detail?.hasOwnProperty('implementation')}
        />
        <LavRelatedLinksForm form={form} screenMode={screenMode} />
        {
          screenMode === SCREEN_MODE.EDIT ?
            <LavVersion screenMode={screenMode} data={state?.detail?.versionHistories} form={form}/> : <></>
        }
      </Space>
    </Form>
  </Spin>
}

export default DataMigrationFormPage
