import { VersionType } from "../../constants"

export interface DataMigrationState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  listObjects?: any,
  listUserRequirements?: any,
  isLoadingList?: boolean,
  detail?: DataMigrationDetail | null,
  selectedData?: DataMigrationDetail | null,
  isLoadingListObject?: boolean,
  isLoadingListUserRequirement?: boolean,
  isModalShow?:boolean
}
export interface DataMigrationDetail {
  id?: number | null,
  title: string,
  content: string,
  status: number,
  storage: string,
  jira: string,
  confluence: string,
  baObject: any | null,
  userRequirements: any | null,
  code: string,
  reqElicitation: string,
  documentation: number | null,
  submittedBy: string
  assignee: string
  reviewer: string
  customer: string
  dueDate: any
  completeDate: any
  projectId?: number
  impacts: string
  versionHistories?: VersionType[]
}

export const defaultState : DataMigrationState = {
  detail: {
    id: null,
    title: '',
    content: '',
    status: 0,
    storage: '',
    jira: '',
    confluence: '',
    baObject: 0,
    userRequirements: 0,
    code: '',
    reqElicitation: '',
    documentation: 0,
    submittedBy: '',
    assignee: '',
    reviewer: '',
    dueDate: '',
    customer: '',
    completeDate: '',
    impacts: '',
    versionHistories: []
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  isLoadingListObject: false,
  isLoadingListUserRequirement: false,
  listObjects: [],
  listUserRequirements: [],
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/DATA_MIGRATION/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/DATA_MIGRATION/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/DATA_MIGRATION/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/DATA_MIGRATION/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/DATA_MIGRATION/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/DATA_MIGRATION/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/DATA_MIGRATION/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/DATA_MIGRATION/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/DATA_MIGRATION/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/DATA_MIGRATION/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/DATA_MIGRATION/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/DATA_MIGRATION/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/DATA_MIGRATION/GET_LIST_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/DATA_MIGRATION/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/DATA_MIGRATION/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/DATA_MIGRATION/GET_LIST_OBJECTS_FAILED',

  GET_LIST_USER_REQUIREMENTS_REQUEST = '@@MODULES/DATA_MIGRATION/GET_LIST_USER_REQUIREMENTS_REQUEST',
  GET_LIST_USER_REQUIREMENTS_SUCCESS = '@@MODULES/DATA_MIGRATION/GET_LIST_USER_REQUIREMENTS_SUCCESS',
  GET_LIST_USER_REQUIREMENTS_FAILED = '@@MODULES/DATA_MIGRATION/GET_LIST_USER_REQUIREMENTS_FAILED',

  DELETE_REQUEST = '@@MODULES/DATA_MIGRATION/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/DATA_MIGRATION/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/DATA_MIGRATION/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/DATA_MIGRATION/SET_MODAL_VISIBLE',
}
