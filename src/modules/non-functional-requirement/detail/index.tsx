import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import NonFunctionalRequirementFormPage from '../form/form'
import { listCategoryDetail, NonFunctionalRequirementState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'
import HistoryScreen from '../../../modules/history'
import NonFunctionReqVersionDetails from './history/details'
import AppCommonService from '../../../services/app.service'

const NonFunctionalRequirementDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const state = useSelector<AppState | null>((s) => s?.NonFunctionalRequirement) as NonFunctionalRequirementState;  
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)
    
  const category = listCategoryDetail.find(
      (category: any) => category.id === state.selectedData?.category
  )

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.nonFunctionalID) {
      dispatch(getDetailRequest(props.match.params.nonFunctionalID))
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.NON_FUNCTIONS + '/version/' + props.match.params.nonFunctionalID +  '/' + selectedRowVersion).then((e) => {
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);    
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.NONFUNTIONAL_REQ}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.nonFunctionalID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if(isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.NONFUNTIONAL_REQ_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col span={5}>
            <LavLeftControl
              activeId={props.match.params.nonFunctionalID}
              apiUrl={API_URLS.REFERENCES_NON_FUNCTIONAL_REQUIREMENT}
              route={APP_ROUTES.NONFUNTIONAL_REQ_DETAIL}
              title='non.functional.nfrlist'
              reload={reload}
              artefactType={REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT}
              reloadSuccess={() => setReload(false)}
              handleCreate={handleCreate}
            >
              {/* {
                ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA))) ? <NonFunctionalRequirementForm onFinish={() => { setReload(true); setIsCreate(true) }} /> : <></>
              } */}
              {
                (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) &&
                <Button ghost={true}
                  type='primary'
                  className='lav-btn-create'
                  icon={<PlusOutlined />}
                  onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'non.functional.createnfr'})}
                </Button> 
              }
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {
        screenMode === SCREEN_MODE.VIEW ? 
        <>      
          <Col span={19}>
            <RightControl onChange={handleReloadData} setScreenMode={setScreenMode} isLoading={state?.isLoading} data={state?.selectedData} nonFunctionalID={props.match.params.nonFunctionalID} isModalShow={state?.isModalShow} />
          </Col>
        </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <NonFunctionalRequirementFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{padding: '20px 10px 0 10px'}}>
            <NonFunctionalRequirementFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
              }} id={props.match.params.nonFunctionalID} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.non-functional"
                            apiURL = {API_URLS.HISTORY}
                            artefactType = {REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + category?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
            <NonFunctionReqVersionDetails nonFunctionalID={props.match.params.nonFunctionalID} setSelectedRowVersion = {setSelectedRowVersion} isModalShow={state?.isModalShow} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
    </Row>
  )
}

export default NonFunctionalRequirementDetail
