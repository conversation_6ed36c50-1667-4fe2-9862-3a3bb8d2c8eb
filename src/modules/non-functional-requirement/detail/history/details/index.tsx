import AppState from '@/store/types'
import {
    Bread<PERSON><PERSON>b, Button, Card, Col, Divider, Row, Space, Spin, Typography
} from 'antd'
import React, { useEffect } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../../config/locale.config'
import { API_URLS, APP_ROLES, ARTEFACT_COMMENT, BUTTON_TYPE, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../../../constants'
import AssignTaskDetail from '../../../../../helper/component/assign-task-detail'
import DeleteButton from '../../../../../helper/component/commonButton/DeleteButton'
import LavAuditTrail from '../../../../../helper/component/lav-audit-trail'
import LavButtons from '../../../../../helper/component/lav-buttons'
import LavEffortEstimation from '../../../../../helper/component/lav-efffort-estimation'
import LavImpact from '../../../../../helper/component/lav-impact'
import LavReferences from '../../../../../helper/component/lav-references'
import LavRelatedLinks from '../../../../../helper/component/lav-related-links'
import LavVersion from '../../../../../helper/component/lav-version/form'
import useWindowDimensions from '../../../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../../modules/_shared/comment/type'
import { deleteRequest } from '../../../action'
import { listCategoryDetail, listSubCategoryDetail, listTypeDetail, listVariaDetail } from '../../../type'
import debounce from 'lodash.debounce'
import HistoryNavigation from '../../../../../modules/history/navigation'

const { Title, Text } = Typography

interface NonFunctionReqVersionDetailsProps {
    data: any | [],
    nonFunctionalID: number,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean,
    setScreenMode: any,
    setSelectedRowVersion: (version: string) => void, 
    onDismiss: () => void | null, 
}
const NonFunctionReqVersionDetails = ({ data, nonFunctionalID, onChange, isLoading, isModalShow, setScreenMode, setSelectedRowVersion, onDismiss }: NonFunctionReqVersionDetailsProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);
    const category = listCategoryDetail.find(
        (category: any) => category.id === data?.category
    )
    const subcategory = listSubCategoryDetail.find(
        (subcategory: any) => subcategory.id === data?.subCategory
    )
    const type = listTypeDetail.find(
        (type: any) => type.id === data?.type
    )
    const varias = listVariaDetail.filter((varia: any) =>
        data?.variables?.includes(varia.id)
    )

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;


    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + category?.name;
        
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'category', title: intl.formatMessage({ id: 'nfr.column.category' }), },
            { field: 'sub-category', title: intl.formatMessage({ id: 'nfr.column.sub-category' }), },
            { field: 'type', title: intl.formatMessage({ id: 'nfr.column.type' }), },
            { field: 'varia', title: intl.formatMessage({ id: 'nfr.column.varia' }), },
            { field: 'remark', title: intl.formatMessage({ id: 'nfr.column.remark' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'userRequirements', title: intl.formatMessage({ id: 'data.user-requirement' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.NON_FUNCTION_REQUIREMENT,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {`${data?.code} - ${category?.name}`}
                    </Title>
                </div>

                <Space className='lav-buttons' size="small">
                    <LavButtons
                        url={`${API_URLS.NON_FUNCTIONS}/${data?.id}`}
                        reviewer={`${data?.reviewer}`}
                        customer={`${data?.customer}`}
                        artefact_type="common.artefact.non-functional"
                        status={data?.status}
                        artefactType={REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT}
                        id={nonFunctionalID}
                        changePage={() => onChange()}>                                         
                        <Button onClick={debounce(onDismiss, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>
                    </LavButtons>
                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />                 
            { data?.nextPrevious.latestVersion === data?.version ? <></>:
                   <HistoryNavigation data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.non-functional"} artefactType={REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT} />
                }
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical">
                        <Space size="large">
                            {/* <span>
                                <TriggerComment field="version">
                                    <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.field.status` })}</Text>
                                </TriggerComment>
                            </span> */}
                            {renderStatusBadge(data?.status)}
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'non.functional.info',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]}>
                                <Col span={4}>
                                    <TriggerComment field="category">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'nfr.column.category',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    {category?.name}
                                </Col>

                                <Col span={4}>
                                    <TriggerComment field="sub-category">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'nfr.column.sub-category',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    {subcategory?.subName}
                                </Col>

                                <Col span={4}>
                                    <TriggerComment field="type">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'nfr.column.type',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    {type?.typeName}
                                </Col>
                                <Col span={4}>
                                    <TriggerComment field="varia">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'nfr.column.varia',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    {/* <h3>{state.nonFunctionalInfo.Variable}</h3> */}
                                    {varias?.map((item: any, index) => (
                                        <p key={item.id}>
                                            {item.variaName}
                                        </p>
                                    ))}
                                </Col>
                                <Col span={24}>
                                    <TriggerComment field="remark">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'nfr.column.remark',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{
                                            __html: data?.description,
                                        }}
                                    ></div>
                                </Col>
                            </Row>
                        </Card>
                        <LavReferences data={data} />
                        <AssignTaskDetail data={data} />
                        {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT} onChange={() => { }} isViewMode={true} />}
                        {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT} onChange={() => { }} isViewMode={true} /> : <></>} */}

                        <Row justify="space-between">
                            <Col span={8}>
                                <LavEffortEstimation data={data} />
                            </Col>
                            <Col span={15}>
                                <LavRelatedLinks data={data} />
                            </Col>
                        </Row>
                        <Col span={24}>
                            <LavVersion screenMode={SCREEN_MODE.VIEW} data={data.versionHistories} />
                        </Col>
                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default NonFunctionReqVersionDetails
