import { PlusOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import { listCategoryDetail } from '../common/non-functional-requirement/type'
import NonFunctionalRequirementFormPage from './form/form'
import {
  listTypeDetail, listVariaDetail, NFRCategoryEnum,
  NFRCategoryValueEnum,
  NFRTypeEnum, NFRTypeValueEnum
} from './type'
const { Text } = Typography

const NonFunctionalRequirement = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  useEffect(() => {       
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'non.functional.list'}); 
  }, [screenMode])

  const columns = [
    {
      title: intl.formatMessage({ id: 'common.action.code' }),
      dataIndex: 'code',
      width: '85px',
      editable: true,
      sorter: true,
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.NONFUNTIONAL_REQ_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'nfr.column.category' }),
      dataIndex: 'category',
      sortOrder: 'descend',
      sorter: true,
      width: '200px',
      ...getColumnDropdownFilterProps(
        listCategoryDetail.map((item) => {
          return { text: item.name, value: item.id }
        })
      ),
      render: (record) => {
        let content

        switch (record) {
          case NFRCategoryEnum.PERFORMANCEREQUIREMENT:
            content = (
              <Text>
                {
                  NFRCategoryValueEnum[NFRCategoryEnum.PERFORMANCEREQUIREMENT]
                    .text
                }
              </Text>
            )

            break
          case NFRCategoryEnum.SAFETYREQUIREMENTS:
            content = (
              <Text>
                {NFRCategoryValueEnum[NFRCategoryEnum.SAFETYREQUIREMENTS].text}
              </Text>
            )

            break
          case NFRCategoryEnum.SECURITYREQUIREMENTS:
            content = (
              <Text>
                {
                  NFRCategoryValueEnum[NFRCategoryEnum.SECURITYREQUIREMENTS]
                    .text
                }
              </Text>
            )

            break
          case NFRCategoryEnum.SOFTWAREQUALITYATTRIBUTES:
            content = (
              <Text>
                {
                  NFRCategoryValueEnum[
                    NFRCategoryEnum.SOFTWAREQUALITYATTRIBUTES
                  ].text
                }
              </Text>
            )

            break
        }
        return content
        // return <Badge text={content} key={record.status}></Badge>
      },
    },
    {
      title: intl.formatMessage({ id: 'nfr.column.type' }),
      dataIndex: 'type',
      sortOrder: 'descend',
      width: '170px',
      sorter: true,
      ...getColumnDropdownFilterProps(
        listTypeDetail.map((item) => {
          return { text: item.typeName, value: item.id }
        })
      ),
      render: (record) => {
        let content
        switch (record) {
          case NFRTypeEnum.RESPONSETIME:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.RESPONSETIME].text}</Text>
            )

            break
          case NFRTypeEnum.WORKLOAD:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.WORKLOAD].text}</Text>
            break
          case NFRTypeEnum.SCALABILITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.SCALABILITY].text}</Text>
            )

            break
          case NFRTypeEnum.PLATFORM:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.PLATFORM].text}</Text>
            break
          case NFRTypeEnum.AUTHENTICITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.AUTHENTICITY].text}</Text>
            )

            break
          case NFRTypeEnum.PRIVACY:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.PRIVACY].text}</Text>
            break
          case NFRTypeEnum.AUTHENTICATION:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.AUTHENTICATION].text}</Text>
            )

            break
          case NFRTypeEnum.ACCESSIBILITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.ACCESSIBILITY].text}</Text>
            )

            break
          case NFRTypeEnum.INTERNATIONALISATION:
            content = (
              <Text>
                {NFRTypeValueEnum[NFRTypeEnum.INTERNATIONALISATION].text}
              </Text>
            )

            break
          case NFRTypeEnum.EASEOFUSE:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.EASEOFUSE].text}</Text>
            )

            break
          case NFRTypeEnum.AVAILABILITY:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.AVAILABILITY].text}</Text>
            )

            break
          case NFRTypeEnum.BACKUP:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.BACKUP].text}</Text>
            break
          case NFRTypeEnum.ACCURACY:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.ACCURACY].text}</Text>
            break
          case NFRTypeEnum.COMPLETENESS:
            content = (
              <Text>{NFRTypeValueEnum[NFRTypeEnum.COMPLETENESS].text}</Text>
            )

            break
          case NFRTypeEnum.REGULATORYCOMPLIANCE:
            content = (
              <Text>
                {NFRTypeValueEnum[NFRTypeEnum.REGULATORYCOMPLIANCE].text}
              </Text>
            )

            break
          case NFRTypeEnum.PRICE:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.PRICE].text}</Text>
            break
          case NFRTypeEnum.TIMELINE:
            content = <Text>{NFRTypeValueEnum[NFRTypeEnum.TIMELINE].text}</Text>
            break
        }
        return content
      },
    },
    {
      title: intl.formatMessage({ id: 'common.nfr.column.varia' }),
      dataIndex: 'variables',
      width: '350px',
      render: (record) => {
        let data = ''
        const varias = listVariaDetail.filter((varia: any) => record?.includes(varia.id))
        varias.forEach((e, index) => {
          if (index == 0) {
            data += e.variaName
          } else {
            data += ', ' + e.variaName
          }
        })
        return <>
          {varias.map((e) => <>- {e.variaName}<br /></>)}
        </>
      }
    },
    {
      title: intl.formatMessage({ id: 'common.nfr.column.remark' }),
      dataIndex: 'description',
      render: (text) => (
        <div
          className="tableDangerous"
          dangerouslySetInnerHTML={{ __html: text }}
        ></div>
      ),
    },
    {
      title: intl.formatMessage({ id: 'nfr.column.status' }),
      width: '80px',
      dataIndex: 'status',
      ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      // defaultFilteredValue: [0, 1, 2, 3],
      sorter: true,
      render: (record) => renderStatusBadge(record),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'non.functional.createnfr' })}
      </Button> : <></>
    // return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <NonFunctionalRequirementForm onFinish={() => handleDataChange()} /> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
    // return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && (record.status !== STATUS.CANCELLED && record.status !== STATUS.DELETE) ?
    //   <NonFunctionalRequirementForm onFinish={() => handleDataChange()} buttonType={BUTTON_TYPE.ICON} nonFunctionalID={record.id} screenMode={SCREEN_MODE.EDIT}/> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (
      record.status !== STATUS.DELETE &&
      (hasRole(APP_ROLES.PM) ||
        hasRole(APP_ROLES.BA))
    ) ?
      children : <></>
  }
  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ? <LavTable
        title="non.functional.list"
        artefact_type="common.artefact.non-functional"
        apiUrl={API_URLS.NON_FUNCTIONS}
        columns={columns}
        artefactType={REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT}
        deleteComponent={DeleteComponent}
        updateComponent={UpdateComponent}
        createComponent={CreateComponent}
      /> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ? <NonFunctionalRequirementFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <NonFunctionalRequirementFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} id={id} /> : <></>
      }
    </Space>
  )
}

export default NonFunctionalRequirement
