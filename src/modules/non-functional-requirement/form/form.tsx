import AppCommonService from '../../../services/app.service'
import AppState from '@/store/types'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import LavVersion from '../../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, getListUserRequirementsRequest, resetState, updateRequest } from '../action'
import { nfrCategory, NonFunctionalRequirementState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text, Title } = Typography
const { confirm } = Modal
const { Option } = Select


interface NonFunctionalRequirementFormModalProps {
  id?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const NonFunctionalRequirementFormPage = ({ id, screenMode, onFinish, onDismiss }: NonFunctionalRequirementFormModalProps) => {
  const dispatch = useDispatch();
  const getCkeditorData: any = createRef()
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.NonFunctionalRequirement) as NonFunctionalRequirementState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [description, setDescription] = useState('');
  const [subCategoryList, setSubCategoryList] = useState<any>([])
  const [typeList, setTypeList] = useState<any[]>([])
  const [variaList, setVariaList] = useState<any[]>([])
  const [filled, setFilled] = useState<any>(false)
  const [impacts, setImpacts] = useState<any>(false)
  const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
  const [categorySelected, setCategorySelected] = useState<number>(0)

  useBeforeUnload()
  // Destroy
  useEffect(() => {
    form.setFieldsValue({
      assignee: currentUserName()
    })
    dispatch(getListUserRequirementsRequest(null))
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (id && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(id))
    }
    document.title = intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'non.functional.create' : 'non.functional.update' });
  }, [screenMode, id])

  const isJsonString = (data) => {
    try {
      JSON.parse(data);
    } catch (e) {
      return '';
    }
    return JSON.parse(data);
  }

  useEffect(() => {
    setCategorySelected(state.detail?.category)
    if (id && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      let listSub: any = []
      let listType: any = []
      let listVariable: any = []
      if (
        state.detail?.category ||
        state.detail?.category === 0
      ) {
        nfrCategory?.map((item) => {
          if (item.id === state.detail?.category) {
            listSub = item?.listNFRSub
            if (
              item?.listNFRSub?.length > 0 &&
              (state.detail?.subCategory ||
                state.detail?.subCategory === 0)
            ) {
              item?.listNFRSub?.map((sub) => {
                if (sub.id === state.detail?.subCategory) {
                  listType = sub?.listNFRtype
                  if (
                    state.detail?.type ||
                    state.detail?.type === 0
                  ) {
                    sub?.listNFRtype?.map((type) => {
                      if (type.id === state.detail?.type) {
                        listVariable = type.listVarias
                      }
                    })
                  }
                }
              })
            } else {
              listType = item?.listNFRtype
              if (
                state.detail?.type ||
                state.detail?.type === 0
              ) {
                listType?.map((type) => {
                  if (type.id === state.detail?.type) {
                    listVariable = type.listVarias
                  }
                })
              }
            }
          }
        })
      } else {
      }
      setSubCategoryList(listSub)
      setTypeList(listType)
      setVariaList(listVariable)
      const storage = isJsonString(state.detail?.storage);
      const jira = isJsonString(state.detail?.jira);
      const confluence = isJsonString(state.detail?.confluence);

      form.setFieldsValue({
        code: state.detail.code,
        category: state.detail.category,
        subcategory: state.detail.subCategory,
        type: state.detail.type,
        variables: state.detail?.variables,
        req: state.detail.reqElicitation,
        documentation: state.detail.documentation,
        implementation: state.detail.implementation,
        storageLinkText: storage ? storage?.textToDisplay : storage,
        storageWebLink: storage ? storage?.address : storage,
        jiraLinkText: jira ? jira?.textToDisplay : jira,
        jiraWebLink: jira ? jira?.address : jira,
        confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
        confluenceWebLink: confluence ? confluence?.address : confluence,
        userRequirements: state.detail?.userRequirements?.map(
          (userRequirement: any) => userRequirement?.name
        ),
      })
      setDescription(state.detail.description);
    }
  }, [state.detail])

  const onChange = (e) => {
    setImpacts(JSON.stringify(e))
  }
  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      const version = form.getFieldValue('version')
      const changeDescription = form.getFieldValue('changeDescription')

      if (version && version !== '') {
        const payload = {
          version: version.substring(version.length - 1) === "." ? `${version}0` : version,
          description: changeDescription,
          artefactCode: state.detail?.code,
        }
        AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT, state.detail?.id).then((e) => {
          if (isCreateMore) {
            resetForm();
            form.setFieldsValue({
              assignee: currentUserName(),
              dueDate: moment(new Date()),
            })
          } else {
            if (onFinish) {
              onFinish();
            }
            onDismiss();
          }
          setIsDraft(null);
          setIsCreateMore(false);
        })
      } else {
        if (isCreateMore) {
          resetForm();
          form.setFieldsValue({
            assignee: currentUserName(),
            dueDate: moment(new Date()),
          })
        } else {
          if (onFinish) {
            onFinish();
          }
          onDismiss();
        }
        setIsDraft(null);
        setIsCreateMore(false);
      }

    }
  }, [state.createSuccess, state.updateSuccess])


  const changeCategory = (e) => {
    form.setFieldsValue({
      subcategory: '',
      type: '',
      variables: undefined,
    })
    setFilled(true)
    const nfrCategoryIndex = nfrCategory.findIndex((item: any) => e === item.id)
    if (nfrCategoryIndex !== -1) {
      setCategorySelected(nfrCategoryIndex)
      if (nfrCategory[nfrCategoryIndex]?.listNFRSub.length === 0) {
        const data: any = nfrCategory[nfrCategoryIndex]?.listNFRtype
        setTypeList(data)
        setSubCategoryList([])
      } else {
        setTypeList([])
        const dataSubCategory: any = nfrCategory[nfrCategoryIndex]?.listNFRSub
        setSubCategoryList(dataSubCategory)
      }
    }
  }

  const changeSubCategory = (e) => {
    form.setFieldsValue({
      type: undefined,
      variables: undefined,
    })
    const nfrSubCategoryIndex: any = nfrCategory[categorySelected]?.listNFRSub?.find((item: any) => e === item.id)
    setTypeList(nfrSubCategoryIndex?.listNFRtype)
  }

  const changeType = (e) => {
    form.setFieldsValue({
      variables: undefined,
    })
    let nfrTypeIndex: any = -1
    const values = form.getFieldsValue()
    const subCategoryID = values.subcategory

    if (subCategoryID !== null && subCategoryID > -1 && subCategoryID !== "") {
      const subCategory = nfrCategory[categorySelected]?.listNFRSub?.find((item: any) => subCategoryID === item.id)
      if (subCategory) {
        nfrTypeIndex = subCategory?.listNFRtype?.find(
          (item: any) => e === item.id
        )
      }
    } else {
      console.log(nfrTypeIndex = nfrCategory[categorySelected]?.listNFRtype);
      nfrTypeIndex = nfrCategory[categorySelected]?.listNFRtype?.find(
        (item: any) => e === item.id
      )
    }
    setVariaList(nfrTypeIndex?.listVarias)
  }

  const onSubmit = debounce(async (values: any, st?: string) => {
    const mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
    let lstUserRequirement: any = []
    values.userRequirements?.forEach((ur) => {
      const urObj: any = state.listUserRequirements?.find(
        (item: any) => item.name === ur
      )
      if (urObj) lstUserRequirement.push(urObj?.id)
    })
    const requestData: any = {
      id: id || null,
      version: values.version,
      description: getCkeditorData.current?.props?.data,
      storage: JSON.stringify({
        textToDisplay: values?.storageLinkText || '',
        address: values?.storageWebLink || '',
      }),
      jira: JSON.stringify({
        textToDisplay: values?.jiraLinkText || '',
        address: values?.jiraWebLink || '',
      }),
      confluence: JSON.stringify({
        textToDisplay: values?.confluenceLinkText || '',
        address: values?.confluenceWebLink || '',
      }),
      userRequirements: lstUserRequirement ? lstUserRequirement : [],
      reqElicitation: values.req,
      documentation: values.documentation,
      category: values.category,
      status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
      subCategory: values.subcategory,
      type: values.type,
      variable: values.variables?.join(', '),
      author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
      reviewer: values.reviewer || '',
      customer: values.customer || '',
      dueDate: values.dueDate ? values.dueDate?.toDate() : null,
      completeDate: values.completeDate ? values.completeDate?.toDate() : null,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
      impacts: impacts
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.non-functional' }) }
        ),
        onOk() {
          requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(null);
    setDescription('')
    form.resetFields([
      'version',
      'code',
      'category',
      'subcategory',
      'type',
      'variables',
      'remarks',
      'req',
      'documentation',
      'storageLinkText',
      'storageWebLink',
      'jiraLinkText',
      'jiraWebLink',
      'confluenceLinkText',
      'confluenceWebLink',
      'reviewer',
      'customer',
      'dueDate',
      'completeDate',
      'userRequirements'
    ])
    form.setFieldsValue({
      assignee: currentUserName()
    })
  }


  const tagRender = (props) => {
    const { label, name, value, closable, onClose } = props;


    return (
      <Tag
        // color={value}
        // onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 3,
          border: 'none',
        }}
        title={label}
      >
        {label.length > 20 ? label.substring(0, 20) + '...' : label}
      </Tag>
    );
  };
  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'category', title: intl.formatMessage({ id: 'nfr.column.category' }), },
      { field: 'sub-category', title: intl.formatMessage({ id: 'nfr.column.sub-category' }), },
      { field: 'type', title: intl.formatMessage({ id: 'nfr.column.type' }), },
      { field: 'varia', title: intl.formatMessage({ id: 'nfr.column.varia' }), },
      { field: 'remark', title: intl.formatMessage({ id: 'nfr.column.remark' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
      { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
      { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
      { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
      { field: 'userRequirements', title: intl.formatMessage({ id: 'data.user-requirement' }), },
      { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.NON_FUNCTION_REQUIREMENT,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <Row>
          <Col span={12}>
            <Space size="middle">
              <Title level={4}>
                {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'non.functional.create' : 'non.functional.update' })}
              </Title>
            </Space>
          </Col>

          <Col span={12}>
            <Row justify="end">
              <Space size="small">
                {screenMode === SCREEN_MODE.CREATE ? <Form.Item
                  style={{ marginBottom: '0px' }}
                  valuePropName="checked"
                  name="createMore"
                  wrapperCol={{ span: 24 }}
                >
                  <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
                </Form.Item> : <></>}
                <Button onClick={confirmCancel}>
                  {intl.formatMessage({ id: 'common.action.close' })}
                </Button>
                {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                  <Form.Item style={{ marginBottom: '0px' }}>
                    <Button htmlType="submit" onClick={() => {
                      setIsDraft(false)
                      setIsSubmitForm(true)
                    }}>
                      {intl.formatMessage({ id: 'common.action.submit' })}
                    </Button>
                  </Form.Item> : <></>
                }

                <Form.Item style={{ marginBottom: '0px' }}>
                  <Button
                    onClick={() => {
                      setIsDraft(true)
                      setIsSubmitForm(true)
                    }}
                    className="success-btn"
                    htmlType="submit"
                  >
                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                  </Button>
                </Form.Item>
              </Space>
            </Row>
          </Col>
        </Row>
      </div>
      <Row align="middle">
        {screenMode === SCREEN_MODE.EDIT ?
          <Col span={5}>
            <div className='status-container'>
              <div>
                {intl.formatMessage({ id: 'common.field.status' })}
              </div>
              <div>
                {renderStatusBadge(state.detail?.status)}
              </div>
            </div>
          </Col> : <></>
        }
      </Row>

      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'non.functional.req' })}>
          {
            screenMode === SCREEN_MODE.EDIT ? <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
              <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                <Input disabled maxLength={255} />
              </Form.Item>
            </FormGroup> : <></>
          }


          <FormGroup required inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="category">
              {intl.formatMessage({ id: 'nfr.column.category' })}
            </TriggerComment>}>
            <Form.Item name="category" rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
              <Select onChange={changeCategory}>
                {nfrCategory?.map((item: any) => (
                  <Option
                    key={item.id}
                    value={item.id}
                  // onChange={() => setIdCategory(item.id)}
                  >
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="sub-category">
              {intl.formatMessage({ id: 'nfr.column.sub-category' })}
            </TriggerComment>}>
            <Form.Item name="subcategory">
              <Select onChange={changeSubCategory}>
                {subCategoryList?.map((item: any) => (
                  <Option key={item.id} value={item.id}>
                    {item.subName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </FormGroup>
          {filled && subCategoryList.length === 0 && <Text type="warning">{intl.formatMessage({ id: 'non.functional.message-warning' })}</Text>}

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="type">
              {intl.formatMessage({ id: 'nfr.column.type' })}
            </TriggerComment>}>
            <Form.Item name="type">
              <Select onChange={changeType}>
                {typeList?.map((item: any) => (
                  <Option key={item.id} value={item.id}>
                    {item.typeName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="varia">
              {intl.formatMessage({ id: 'nfr.form.variables-criteria' })}
            </TriggerComment>}>
            <Form.Item name="variables">
              <Select mode="multiple">
                {variaList?.map((item: any) => (
                  <Option key={item.id} value={item.id}>
                    {item.variaName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </FormGroup>

          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="remark">
              {intl.formatMessage({ id: 'nfr.form.remarks' })}
            </TriggerComment>}>
            <Form.Item
              name="remarks"
              labelAlign="left"
              wrapperCol={{ span: 24 }}
            >
              <CkeditorMention
                ref={getCkeditorData}
                data={description || ''}
              />
            </Form.Item>
          </FormGroup>
        </Card>

        <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'data.reference' })}>
          <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
            <TriggerComment screenMode={screenMode} field="userRequirements">
              {intl.formatMessage({ id: 'state.user-requirement' })}
            </TriggerComment>}>
            <Form.Item name="userRequirements">
              <Select
                mode="multiple"
                optionLabelProp="label"
                tagRender={tagRender}
              >
                {state.listUserRequirements?.map(
                  (item: any, index: number) =>
                    item.status !== STATUS.DELETE &&
                    item.status !== STATUS.CANCELLED && (
                      <Option key={index} value={item.name} label={item.name}>
                        {item.name}
                      </Option>
                    )
                )}
              </Select>
            </Form.Item>
          </FormGroup>
        </Card>
        <AssignTaskComponent
          form={form}
          data={screenMode === SCREEN_MODE.EDIT ? state.detail : null}
          isSubmit={isDraft == false}
          screenMode={screenMode}
        />
        {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT} onChange={onChange} isSubmitForm={isSubmitForm} />}

        <LavEffortEstimationForm
          screenMode={screenMode}
          hasDevelopment={state?.detail?.hasOwnProperty('development')}
          hasImplementation={state?.detail?.hasOwnProperty('implementation')}
        />

        <LavRelatedLinksForm form={form} screenMode={screenMode} />
        {
          screenMode === SCREEN_MODE.EDIT ?
            <LavVersion screenMode={screenMode} data={state?.detail?.versionHistories} form={form}/> : <></>
        }
      </Space>
    </Form>
  </Spin>
}
export default NonFunctionalRequirementFormPage
