import { VersionType } from "@/constants"
import intl from "../../config/locale.config"

export interface NonFunctionalRequirementState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: NonFunctionalRequirementDetail | null,
  selectedData?: NonFunctionalRequirementDetail | null,
  isModalShow?:boolean
  listUserRequirements: any[]
}
export interface NonFunctionalRequirementDetail {
  id?: number | null,
  code: string,
  description: string,
  storage: string
  jira: string
  confluence: string
  reqElicitation: string,
  documentation: number | null,
  category: any | null,
  status: number,
  subCategory: any | null,
  type: any | null,
  variables: any | null,
  implementation: number | null,
  submittedBy: string
  assignee: string
  reviewer: string
  customer: string
  dueDate: any
  completeDate: any
  projectId?: number
  impacts: string,
  userRequirements: any[]
  versionHistories?: VersionType[]
}

export const defaultState: NonFunctionalRequirementState = {
  detail: {
    id: null,
    code: '',
    description: '',
    storage: '',
    jira: '',
    confluence: '',
    reqElicitation: '',
    documentation: 0,
    category: null,
    status: 0,
    subCategory: null,
    type: null,
    variables: null,
    implementation: 0,
    submittedBy: '',
    assignee: '',
    reviewer: '',
    customer: '',
    dueDate: '',
    completeDate: '',
    impacts: '',
    userRequirements: [],
    versionHistories: []
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  listUserRequirements: [],
}

export const nfrCategory = [
  {
    id: 0,
    name: `${intl.formatMessage({
      id: 'nfr.category.performance-requirements',
    })}`,
    listNFRSub: [],
    listNFRtype: [
      {
        id: 0,
        typeName: `${intl.formatMessage({ id: 'nfr.type.response-time' })}`,
        listVarias: [
          {
            id: 0,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Measurement-Point',
            })}`,
          },
          {
            id: 1,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Statistic-Type',
            })}`,
          },
          {
            id: 2,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Measurement-Period',
            })}`,
          },
          {
            id: 3,
            variaName: `${intl.formatMessage({ id: 'nfr.varia.Platform' })}`,
          },
          {
            id: 4,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Error-Rate',
            })}`,
          },
        ],
      },
      {
        id: 1,
        typeName: `${intl.formatMessage({ id: 'nfr.type.workload' })}`,
        listVarias: [
          {
            id: 5,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Workload percentage at peak time',
            })}`,
          },
          {
            id: 6,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Workload percentage at off-peak time',
            })}`,
          },
        ],
      },
      {
        id: 2,
        typeName: `${intl.formatMessage({ id: 'nfr.type.Scalability' })}`,
        listVarias: [
          {
            id: 7,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Ease of Scalability',
            })}`,
          },
        ],
      },
      {
        id: 3,
        typeName: `${intl.formatMessage({ id: 'nfr.type.Platform' })}`,
        listVarias: [
          {
            id: 8,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Hardware',
            })}`,
          },
          {
            id: 9,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Software',
            })}`,
          },
          {
            id: 10,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.External System Integration',
            })}`,
          },
        ],
      },
    ],
  },
  {
    id: 1,
    name: `${intl.formatMessage({
      id: 'nfr.category.safety-requirements',
    })}`,
    listNFRSub: [],
    listNFRtype: [
      {
        id: 4,
        typeName: `${intl.formatMessage({ id: 'nfr.type.Authenticity' })}`,
        listVarias: [
          {
            id: 11,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Mode of authentication',
            })}`,
          },
        ],
      },
      {
        id: 5,
        typeName: `${intl.formatMessage({ id: 'nfr.type.Privacy' })}`,
        listVarias: [
          {
            id: 12,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Password and message encryption',
            })}`,
          },
        ],
      },
    ],
  },
  {
    id: 2,
    name: `${intl.formatMessage({
      id: 'nfr.category.security-requirements',
    })}`,
    listNFRSub: [],
    listNFRtype: [
      {
        id: 6,
        typeName: `${intl.formatMessage({ id: 'nfr.type.Authentication' })}`,
        listVarias: [
          {
            id: 13,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Success rate in authentication',
            })}`,
          },
          {
            id: 14,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Resistance to known attacks',
            })}`,
          },
          {
            id: 15,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Probability/time/resources to detect an attack',
            })}`,
          },
          {
            id: 16,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Percentage of useful services still available during an attack',
            })}`,
          },
          {
            id: 17,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Percentage of successful attacks',
            })}`,
          },
          {
            id: 18,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Lifespan of a password, of a session',
            })}`,
          },
          {
            id: 19,
            variaName: `${intl.formatMessage({
              id: 'nfr.varia.Encryption level',
            })}`,
          },
        ],
      },
    ],
  },
  {
    id: 3,
    name: `${intl.formatMessage({
      id: 'nfr.category.software-quality-attributes',
    })}`,
    listNFRSub: [
      {
        id: 1,
        subName: `${intl.formatMessage({
          id: 'nfr.subcategory.usability',
        })}`,
        listNFRtype: [
          {
            id: 7,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Accessibility',
            })}`,
            listVarias: [
              {
                id: 20,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Color-blind users',
                })}`,
              },
              {
                id: 21,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Voice-over for hearing impaired users',
                })}`,
              },
            ],
          },
          {
            id: 8,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Internationalisation',
            })}`,
            listVarias: [
              {
                id: 22,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Time zone adaptation',
                })}`,
              },
              {
                id: 23,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Support multiple currencies',
                })}`,
              },
              {
                id: 24,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Support multiple languages',
                })}`,
              },
            ],
          },
          {
            id: 9,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Ease-of-Use',
            })}`,
            listVarias: [
              {
                id: 25,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Maximum number of clicks to complete any operation',
                })}`,
              },
              {
                id: 26,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Learnability',
                })}`,
              },
              {
                id: 27,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Efficiency',
                })}`,
              },
              {
                id: 28,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Memorability',
                })}`,
              },
              {
                id: 29,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Error Avoidance',
                })}`,
              },
              {
                id: 30,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.User Satisfaction',
                })}`,
              },
            ],
          },
        ],
      },
      {
        id: 2,
        subName: 'Reliability',
        listNFRtype: [
          {
            id: 10,
            typeName: 'Availability',
            listVarias: [
              {
                id: 31,
                variaName: 'Percentage of time available',
              },
            ],
          },
          {
            id: 11,
            typeName: 'Backup',
            listVarias: [
              {
                id: 32,
                variaName: 'Backup frequency',
              },
            ],
          },
        ],
      },
      {
        id: 3,
        subName: `${intl.formatMessage({
          id: 'nfr.subcategory.functional-suitability',
        })}`,
        listNFRtype: [
          {
            id: 12,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Accuracy',
            })}`,
            listVarias: [
              {
                id: 33,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Accuracy Rate',
                })}`,
              },
            ],
          },
          {
            id: 13,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Completeness',
            })}`,
            listVarias: [
              {
                id: 34,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Completeness Percentage',
                })}`,
              },
            ],
          },
        ],
      },
      {
        id: 4,
        subName: `${intl.formatMessage({
          id: 'nfr.subcategory.compliance',
        })}`,
        listNFRtype: [
          {
            id: 14,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Regulatory-Compliance',
            })}`,
            listVarias: [
              {
                id: 35,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Data Protection',
                })}`,
              },
            ],
          },
        ],
      },
      {
        id: 5,
        subName: `${intl.formatMessage({
          id: 'nfr.subcategory.constraint',
        })}`,
        listNFRtype: [
          {
            id: 15,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Price',
            })}`,
            listVarias: [
              {
                id: 36,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Target price for the solution',
                })}`,
              },
              {
                id: 37,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Limit for extensions',
                })}`,
              },
              {
                id: 38,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Subscription allowance',
                })}`,
              },
            ],
          },
          {
            id: 16,
            typeName: `${intl.formatMessage({
              id: 'nfr.type.Timeline',
            })}`,
            listVarias: [
              {
                id: 39,
                variaName: `${intl.formatMessage({
                  id: 'nfr.varia.Release duration',
                })}`,
              },
            ],
          },
        ],
      },
    ],
  },
]

export enum NFRCategoryEnum {
  PERFORMANCEREQUIREMENT,
  SAFETYREQUIREMENTS,
  SECURITYREQUIREMENTS,
  SOFTWAREQUALITYATTRIBUTES,
}

export const NFRCategoryValueEnum = {
  [NFRCategoryEnum.PERFORMANCEREQUIREMENT]: {
    text: intl.formatMessage({
      id: 'nfr.category.performance-requirements',
    }),
  },
  [NFRCategoryEnum.SAFETYREQUIREMENTS]: {
    text: intl.formatMessage({
      id: 'nfr.category.safety-requirements',
    }),
  },
  [NFRCategoryEnum.SECURITYREQUIREMENTS]: {
    text: intl.formatMessage({
      id: 'nfr.category.security-requirements',
    }),
  },
  [NFRCategoryEnum.SOFTWAREQUALITYATTRIBUTES]: {
    text: intl.formatMessage({
      id: 'nfr.category.software-quality-attributes',
    }),
  },
}

export enum NFRTypeEnum {
  RESPONSETIME,
  WORKLOAD,
  SCALABILITY,
  PLATFORM,
  AUTHENTICITY,
  PRIVACY,
  AUTHENTICATION,
  ACCESSIBILITY,
  INTERNATIONALISATION,
  EASEOFUSE,
  AVAILABILITY,
  BACKUP,
  ACCURACY,
  COMPLETENESS,
  REGULATORYCOMPLIANCE,
  PRICE,
  TIMELINE,
}

export const NFRTypeValueEnum = {
  [NFRTypeEnum.RESPONSETIME]: {
    text: intl.formatMessage({
      id: 'nfr.type.response-time',
    }),
  },
  [NFRTypeEnum.WORKLOAD]: {
    text: intl.formatMessage({
      id: 'nfr.type.workload',
    }),
  },
  [NFRTypeEnum.SCALABILITY]: {
    text: intl.formatMessage({
      id: 'nfr.type.Scalability',
    }),
  },
  [NFRTypeEnum.PLATFORM]: {
    text: intl.formatMessage({
      id: 'nfr.type.Platform',
    }),
  },
  [NFRTypeEnum.AUTHENTICITY]: {
    text: intl.formatMessage({
      id: 'nfr.type.Authenticity',
    }),
  },
  [NFRTypeEnum.PRIVACY]: {
    text: intl.formatMessage({
      id: 'nfr.type.Privacy',
    }),
  },
  [NFRTypeEnum.AUTHENTICATION]: {
    text: intl.formatMessage({
      id: 'nfr.type.Authentication',
    }),
  },
  [NFRTypeEnum.ACCESSIBILITY]: {
    text: intl.formatMessage({
      id: 'nfr.type.Accessibility',
    }),
  },
  [NFRTypeEnum.INTERNATIONALISATION]: {
    text: intl.formatMessage({
      id: 'nfr.type.Internationalisation',
    }),
  },
  [NFRTypeEnum.EASEOFUSE]: {
    text: intl.formatMessage({
      id: 'nfr.type.Ease-of-Use',
    }),
  },
  [NFRTypeEnum.AVAILABILITY]: {
    text: intl.formatMessage({
      id: 'nfr.type.Availability',
    }),
  },

  [NFRTypeEnum.BACKUP]: {
    text: intl.formatMessage({
      id: 'nfr.type.Backup',
    }),
  },
  [NFRTypeEnum.ACCURACY]: {
    text: intl.formatMessage({
      id: 'nfr.type.Accuracy',
    }),
  },
  [NFRTypeEnum.COMPLETENESS]: {
    text: intl.formatMessage({
      id: 'nfr.type.Completeness',
    }),
  },
  [NFRTypeEnum.REGULATORYCOMPLIANCE]: {
    text: intl.formatMessage({
      id: 'nfr.type.Regulatory-Compliance',
    }),
  },
  [NFRTypeEnum.PRICE]: {
    text: intl.formatMessage({
      id: 'nfr.type.Price',
    }),
  },
  [NFRTypeEnum.TIMELINE]: {
    text: intl.formatMessage({
      id: 'nfr.type.Timeline',
    }),
  },
}

export const listCategoryDetail = [
  {
    id: 0,
    name: `${intl.formatMessage({
      id: 'nfr.category.performance-requirements',
    })}`,
  },
  {
    id: 1,
    name: `${intl.formatMessage({
      id: 'nfr.category.safety-requirements',
    })}`,
  },
  {
    id: 2,
    name: `${intl.formatMessage({
      id: 'nfr.category.security-requirements',
    })}`,
  },
  {
    id: 3,
    name: `${intl.formatMessage({
      id: 'nfr.category.software-quality-attributes',
    })}`,
  },
]
export const listSubCategoryDetail = [
  {
    id: 1,
    subName: `${intl.formatMessage({
      id: 'nfr.subcategory.usability',
    })}`,
  },
  {
    id: 2,
    subName: 'Reliability',
  },
  {
    id: 3,
    subName: `${intl.formatMessage({
      id: 'nfr.subcategory.functional-suitability',
    })}`,
  },
  {
    id: 4,
    subName: `${intl.formatMessage({
      id: 'nfr.subcategory.compliance',
    })}`,
  },
  {
    id: 5,
    subName: `${intl.formatMessage({
      id: 'nfr.subcategory.constraint',
    })}`,
  },
]
export const listTypeDetail = [
  {
    id: 0,
    typeName: `${intl.formatMessage({ id: 'nfr.type.response-time' })}`,
  },
  {
    id: 1,
    typeName: `${intl.formatMessage({ id: 'nfr.type.workload' })}`,
  },
  {
    id: 2,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Scalability' })}`,
  },
  {
    id: 3,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Platform' })}`,
  },
  {
    id: 4,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Authenticity' })}`,
  },
  {
    id: 5,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Privacy' })}`,
  },
  {
    id: 6,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Authentication' })}`,
  },
  {
    id: 7,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Accessibility' })}`,
  },
  {
    id: 8,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Internationalisation' })}`,
  },
  {
    id: 9,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Ease-of-Use' })}`,
  },
  {
    id: 10,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Availability' })}`,
  },
  {
    id: 11,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Backup' })}`,
  },
  {
    id: 12,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Accuracy' })}`,
  },
  {
    id: 13,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Completeness' })}`,
  },
  {
    id: 14,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Regulatory-Compliance' })}`,
  },
  {
    id: 15,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Price' })}`,
  },
  {
    id: 16,
    typeName: `${intl.formatMessage({ id: 'nfr.type.Timeline' })}`,
  },
]
export const listVariaDetail = [
  {
    id: 0,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Measurement-Point',
    })}`,
  },
  {
    id: 1,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Statistic-Type',
    })}`,
  },
  {
    id: 2,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Measurement-Period',
    })}`,
  },
  {
    id: 3,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Platform',
    })}`,
  },
  {
    id: 4,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Error-Rate',
    })}`,
  },
  {
    id: 5,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Workload percentage at peak time',
    })}`,
  },
  {
    id: 6,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Workload percentage at off-peak time',
    })}`,
  },
  {
    id: 7,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Ease of Scalability',
    })}`,
  },
  {
    id: 8,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Hardware',
    })}`,
  },
  {
    id: 9,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Software',
    })}`,
  },
  {
    id: 10,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.External System Integration',
    })}`,
  },
  {
    id: 11,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Mode of authentication',
    })}`,
  },
  {
    id: 12,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Password and message encryption',
    })}`,
  },
  {
    id: 13,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Success rate in authentication',
    })}`,
  },
  {
    id: 14,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Resistance to known attacks',
    })}`,
  },
  {
    id: 15,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Probability/time/resources to detect an attack',
    })}`,
  },
  {
    id: 16,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Percentage of useful services still available during an attack',
    })}`,
  },
  {
    id: 17,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Percentage of successful attacks',
    })}`,
  },
  {
    id: 18,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Lifespan of a password, of a session',
    })}`,
  },
  {
    id: 19,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Time zone adaptation',
    })}`,
  },
  {
    id: 20,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Color-blind users',
    })}`,
  },
  {
    id: 21,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Voice-over for hearing impaired users',
    })}`,
  },
  {
    id: 22,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Time zone adaptation',
    })}`,
  },
  {
    id: 23,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Support multiple currencies',
    })}`,
  },
  {
    id: 24,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Support multiple languages',
    })}`,
  },
  {
    id: 25,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Maximum number of clicks to complete any operation',
    })}`,
  },
  {
    id: 26,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Learnability',
    })}`,
  },
  {
    id: 27,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Efficiency',
    })}`,
  },
  {
    id: 28,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Memorability',
    })}`,
  },
  {
    id: 29,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Error Avoidance',
    })}`,
  },
  {
    id: 30,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.User Satisfaction',
    })}`,
  },
  {
    id: 31,
    variaName: 'Percentage of time available',
  },
  {
    id: 32,
    variaName: 'Backup frequency',
  },
  {
    id: 33,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Accuracy Rate',
    })}`,
  },
  {
    id: 34,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Completeness Percentage',
    })}`,
  },
  {
    id: 35,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Data Protection',
    })}`,
  },
  {
    id: 36,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Target price for the solution',
    })}`,
  },
  {
    id: 37,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Limit for extensions',
    })}`,
  },
  {
    id: 38,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Subscription allowance',
    })}`,
  },
  {
    id: 39,
    variaName: `${intl.formatMessage({
      id: 'nfr.varia.Release duration',
    })}`,
  },
]

export enum ActionEnum {
  RESET_STATE = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/DELETE_FAILED',

  GET_LIST_USER_REQUIREMENTS_REQUEST = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_LIST_USER_REQUIREMENTS_REQUEST',
  GET_LIST_USER_REQUIREMENTS_SUCCESS = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_LIST_USER_REQUIREMENTS_SUCCESS',
  GET_LIST_USER_REQUIREMENTS_FAILED = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/GET_LIST_USER_REQUIREMENTS_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/NON_FUNCTIONAL_REQUIREMENT/SET_MODAL_VISIBLE',
}
