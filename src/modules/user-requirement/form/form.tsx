import AppState from '@/store/types'
import {
  But<PERSON>,
  Card, Checkbox, Col, DatePicker, Form, Input, Modal, Row, Select, Space, Spin
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { ALL_SCOPE_LIST, APP_ROLES, ARTEFACT_COMMENT, DATE_FORMAT, MESSAGE_TYPES, PIORITY_OPTIONS, REQ_ARTEFACT_TYPE_ID, SCOPE_TYPE, SCOPE_TYPE_LIST, SCREEN_MODE, SOURCE_LIST, SOURCE_TYPE, STATUS } from '../../../constants'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { concatMentionReferences, currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import AppCommonService from '../../../services/app.service'
import { createRequest, getDetailRequest, getListDocumentRequest, getListMeetingRequest, resetState, updateRequest } from '../action'
import { UserRequirementState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'
const { confirm } = Modal
const { Option } = Select


interface UserRequirementFormModalProps {
  userRequirementID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}

const UserRequirementFormPage = ({ userRequirementID, screenMode, onFinish, onDismiss }: UserRequirementFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const getCkeditorData: any = createRef()
  const getCkeditorData2: any = createRef()
  const state = useSelector<AppState | null>((s) => s?.UserRequirement) as UserRequirementState
  const [isDraft, setIsDraft] = useState(false);
  const [isCreateMore, setIsCreateMore] = useState(false);
  const { height: windowHeight } = useWindowDimensions()
  const modalConfirmConfig = useModalConfirmationConfig()
  const [sourceType, setSourceType] = useState<any>(1)
  const [type, setType] = useState<any>(-1)
  const [scope, setScope] = useState<any>(-1)
  const [source, setSource] = useState<any>(-1)
  const [impacts, setImpacts] = useState('')
  const descriptionRef = useRef<any>()
  const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
  const [members, setMembers] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  useEffect(() => {
    form.setFieldsValue({
      reviewer: '',
      type: SCOPE_TYPE.ORIGINAL.value,
    })
    setType(SCOPE_TYPE.ORIGINAL.value);
    AppCommonService.getBaLeadPm().then(res => setMembers(res.data)).catch((err) => setMembers([]));
    AppCommonService.getReferenceProduct().then(res => setProducts(res.data)).catch((err) => setProducts([]));
  }, [])

  useBeforeUnload();
  // Destroy
  useEffect(() => {
    dispatch(getListMeetingRequest(null))
    dispatch(getListDocumentRequest(null))
    form.setFieldsValue({
      assignee: currentUserName()
    })
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (userRequirementID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(userRequirementID))
    }
    if (screenMode === SCREEN_MODE.CREATE) {
      form.setFieldsValue({
        'sendDate': moment(new Date(), DATE_FORMAT),
      })
    }
        
    document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'user-requirement.title-create' : 'user-requirement.title-update' }); 
  }, [screenMode, userRequirementID])


  const getList = (data: any) => {
    if (data?.source === 1) {
      const listMeetings = data?.meetingMinutes?.map((e) => e?.name)
      return listMeetings
    } else if (data?.source === 2) {
      const listReferences = data?.referenceDocuments?.map((e) => e.name)
      return listReferences
    } else if (data?.source === 3) {
      const sourceOther = data?.sourceValueOther
      return sourceOther
    }
  }
  const onChange = (e) => {
    setImpacts(JSON.stringify(e))
  }

  const isJsonString = (data) => {
    try {
      JSON.parse(data);
    } catch (e) {
      return '';
    }
    return JSON.parse(data);
  }

  useEffect(() => {
    if (userRequirementID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
      const storage = isJsonString(state.detail?.storage);
      const jira = isJsonString(state.detail?.jira);
      const confluence = isJsonString(state.detail?.confluence);
      form.setFieldsValue({
        ...state.detail,
        sourceValue: getList(state?.detail),
        req: state.detail.reqElicitation,
        // lavdate
        storageLinkText: storage ? storage?.textToDisplay : storage,
        storageWebLink: storage ? storage?.address : storage,
        jiraLinkText: jira ? jira?.textToDisplay : jira,
        jiraWebLink: jira ? jira?.address : jira,
        confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
        confluenceWebLink: confluence ? confluence?.address : confluence,
        sendDate: state.detail.sendDate ? moment(new Date(state.detail.sendDate)) : null,
        dueDate: state.detail.dueDate ? moment(new Date(state.detail.dueDate)) : null,
        priority: state.detail.priority || null,
        isCovered: state.detail.isCovered,
        reviewer: state.detail.reviewer || '',
        productId: state.detail?.product?.id || null
      })
      setType(state.detail?.type)
      setSourceType(state.detail?.source)
    }
  }, [state.detail])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      if (isCreateMore) {
        resetForm();
      } else {
        if (onFinish) {
          onFinish();
        }
        onDismiss();
      }
      setIsDraft(false);
      setIsCreateMore(false);
    }
  }, [state.createSuccess, state.updateSuccess])

  const onSubmit = debounce(async (values: any, st?: string) => {
    const listMeetings = values.sourceValue?.map((m) => {
      const meeting: any = state.listMeeting?.find(
        (item: any) => item.name === m
      )
      return meeting?.id
    })
    const listDocuments = values.sourceValue?.map((m) => {
      const document: any = state.listDocument?.find(
        (item: any) => item.name === m
      )
      return document?.id
    })
    let mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data)
    mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(getCkeditorData2.current?.props?.data));
    const requestData: any = {
      id: userRequirementID || null,
      code: values.code,
      name: values.name,
      order: values.order,
      status: isDraft ? (screenMode === SCREEN_MODE.CREATE ? STATUS.DRAFT : state.detail?.status) : STATUS.SUBMITTED,
      type: values.type,
      source: values.source,
      sourceValueOther: values.sourceValueOther,
      scope: values.scope ? values.scope : null,
      productId: values.productId ? values.productId : null,
      sender: values.sender,
      sendDate: values.sendDate ? values.sendDate.toDate() : null,
      reviewer: values.reviewer,
      customer: values.customer || '',
      description: getCkeditorData?.current?.props?.data,
      changeReason: getCkeditorData2?.current?.props?.data,
      storage: JSON.stringify({
        textToDisplay: values?.storageLinkText || '',
        address: values?.storageWebLink || '',
      }),
      jira: JSON.stringify({
        textToDisplay: values?.jiraLinkText || '',
        address: values?.jiraWebLink || '',
      }),
      confluence: JSON.stringify({
        textToDisplay: values?.confluenceLinkText || '',
        address: values?.confluenceWebLink || '',
      }),
      dueDate: values.dueDate ? values.dueDate.toDate() : null,
      isCovered: values.isCovered,
      priority: values.priority,
      reqElicitation: values.req,
      documentation: values.documentation,
      development: values.development,
      actionStatus: values.actionStatus,
      meetingMinuteIds: values.source == 1 ? listMeetings : [],
      referenceDocumentIds: values.source == 2 ? listDocuments : [],
      sourceOther: values.source == 3 ? values.sourceOther : '',
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
      impacts: impacts,
    }
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage({ id: 'CFD_6_8' }),
        onOk() {
          requestData.messageAction = MESSAGE_TYPES.SUBMIT_CR;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)

  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const changeSourceType = (value) => {
    setSourceType(value)
    form.setFieldsValue({
      'sourceValue': [],
    })
  }

  const changeType = (value) => {
    setType(value)
    form.setFieldsValue({
      'scope': ''
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(false);

    form.resetFields([
      'code',
      'name',
      'type',
      'source',
      'sourceValue',
      'sourceValueOther',
      'scope',
      'sender',
      'sendDate',
      'reviewer',
      'customer',
      'description',
      'changeReason',
      'storageLinkText',
      'storageWebLink',
      'jiraLinkText',
      'jiraWebLink',
      'confluenceLinkText',
      'confluenceWebLink',
      'req',
      'documentation',
      'development',
      "dueDate",
      "priority",
      "isCovered",
    ])
    if (screenMode === SCREEN_MODE.CREATE) {
      form.setFieldsValue({
        'source': sourceType,
        'sendDate': moment(new Date(), DATE_FORMAT),
        'assignee': currentUserName(),
        'type': type,
        'scope': scope == -1 ? null : scope,
        'sourceValue': source,
        'dueDate': moment(new Date()),
      })
    }
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;



  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'type', title: intl.formatMessage({ id: 'user-requirement.column.type' }), },
      { field: 'scope', title: intl.formatMessage({ id: 'user-requirement.column.scope' }), },
      { field: 'source-type', title: intl.formatMessage({ id: 'user-requirement.column.source-type' }), },
      { field: 'source', title: intl.formatMessage({ id: 'user-requirement.column.source' }), },
      { field: 'sender', title: intl.formatMessage({ id: 'user-requirement.column.sender' }), },
      { field: 'send-date', title: intl.formatMessage({ id: 'user-requirement.column.send-date' }), },
      { field: 'user-requirement-details', title: intl.formatMessage({ id: 'user-requirement.label.user-requirement-details' }), },
      { field: 'change-reason', title: intl.formatMessage({ id: 'user-requirement.label.change-reason' }), },
      { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
      { field: 'documentation', title: intl.formatMessage({ id: 'createscreen.label.documentation' }), },
      { field: 'development', title: intl.formatMessage({ id: 'createscreen.label.development' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.USER_REQUIREMENTS,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#region COMMENT INIT

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <LavPageHeader
          showBreadcumb
          title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'user-requirement.title-create' : 'user-requirement.title-update' })}
        >
          <Space size="small">
            {screenMode === SCREEN_MODE.CREATE ? <Form.Item
              style={{ marginBottom: '0px' }}
              valuePropName="checked"
              name="createMore"
              wrapperCol={{ span: 24 }}
            >
              <Checkbox>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
            </Form.Item> : <></>}
            <Button onClick={debounce(confirmCancel, 500)}>
              {intl.formatMessage({ id: 'common.action.close' })}
            </Button>

            {
              (
                (form.getFieldValue('type') === SCOPE_TYPE_LIST[1].id) &&
                (screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)))
              ) ?
                <Button type="primary" ghost htmlType="submit" onClick={() => {
                  setIsDraft(false)
                  setIsSubmitForm(true)
                }}>
                  {intl.formatMessage({ id: 'common.action.submit' })}
                </Button> : <></>
            }

            {
              (screenMode === SCREEN_MODE.CREATE ?
                <>
                  {
                    form.getFieldValue('type') ?
                      <Button onClick={() => setIsDraft(true)} className="success-btn" htmlType="submit">
                        {intl.formatMessage({ id: form.getFieldValue('type') === SCOPE_TYPE_LIST[1].id ? 'common.action.save-as-draft' : 'common.action.create' })}
                      </Button> : <></>
                  }
                </> :
                <Button onClick={() => {
                  setIsDraft(true)
                  setIsSubmitForm(true)
                }} className="success-btn" htmlType="submit">
                  {intl.formatMessage({ id: 'common.action.update' })}
                </Button>)
            }
          </Space>
        </LavPageHeader>
      </div>

      <Row align="middle">
        {screenMode === SCREEN_MODE.EDIT ?
          <Col span={5}>
            <div className='status-container'>
              <div>
                {intl.formatMessage({ id: 'common.field.status' })}
              </div>
              <div>
                {renderStatusBadge(state.detail?.status, true)}
              </div>
            </div>
          </Col> : <></>
        }
      </Row>
      <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
        <Card className='rq-form-block' title={intl.formatMessage({ id: 'user-requirement.label.user-requirement-infomation' })}>
          <Row gutter={[20, 10]}>
            <Col span={12}>
              <FormGroup inline labelSpan={6} controlSpan={18} required label={intl.formatMessage({ id: 'common.label.name' })}>
                <Form.Item
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage({ id: 'IEM_1' }),
                    },
                    { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                    {
                      validator: async (rule, value) => {
                        if (value && value.trim().length === 0) {
                          throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                        }
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={`${intl.formatMessage({
                      id: `user-requirement.place-holder.name`,
                    })}${intl.formatMessage({
                      id: `common.mandatory.*`,
                    })}`}
                    maxLength={255}
                  />
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={12}>
              {
                screenMode === SCREEN_MODE.EDIT ?
                  <FormGroup inline labelSpan={4} controlSpan={18} label={intl.formatMessage({ id: 'common.label.code' })}>
                    <Form.Item rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                      <Input value={state.detail?.code} disabled maxLength={255} />
                    </Form.Item>
                  </FormGroup>
                  : <></>
              }
            </Col>
            <Col span={12}>
              <FormGroup inline labelSpan={6} controlSpan={18} className="rq-fg-comment" required label={<TriggerComment screenMode={screenMode} field="type">
                {intl.formatMessage({ id: 'user-requirement.column.type' })}
              </TriggerComment>}>
                <Form.Item name="type" validateTrigger="onBlur"
                  rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                  <Select onChange={changeType}>
                    {SCOPE_TYPE_LIST.map((item: any) => (
                      <Option key={item.id} value={item.id}>{item.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={12}>
              <FormGroup inline labelSpan={4} controlSpan={18} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="scope">
                {intl.formatMessage({ id: 'user-requirement.column.scope' })}
              </TriggerComment>}>
                <Form.Item validateTrigger="onBlur" name="scope">
                  <Select onChange={(e) => setScope(e)}>
                    {
                      ALL_SCOPE_LIST.filter((x) => x.type.includes(type)).map((s) => {
                        return <Option key={s.id} value={s.id}>{s.name}</Option>
                      })
                    }
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={12}>
              <FormGroup inline labelSpan={6} controlSpan={18} className="rq-fg-comment" label={<TriggerComment screenMode={screenMode} field="source-type">
                {intl.formatMessage({ id: 'user-requirement.column.source-type' })}
              </TriggerComment>}>
                <Form.Item
                  validateTrigger="onBlur"
                  name="source"
                >
                  <Select onChange={changeSourceType}>
                    {SOURCE_LIST.map((item: any) =>
                      <Option key={item.id} value={item.id}>
                        {item.name}
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={12}>
              <FormGroup inline labelSpan={4} controlSpan={18} className="rq-fg-comment"
                label={(sourceType === SOURCE_TYPE.MEETING || sourceType === SOURCE_TYPE.OTHER || sourceType === SOURCE_TYPE.REFERENCE_DOCUMENT) ? <TriggerComment screenMode={screenMode} field="source">
                  {intl.formatMessage({ id: 'user-requirement.column.source' })}
                </TriggerComment> : ''}>
                {sourceType === SOURCE_TYPE.MEETING && (
                  <Form.Item
                    validateTrigger="onBlur"
                    name="sourceValue"
                  >
                    <Select
                      filterOption={(input, option: any) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                      mode='multiple'
                      optionLabelProp="label"
                      placeholder={`${intl.formatMessage({
                        id: 'user-requirement.label.select-a-meeting-minutes',
                      })}`}
                      onChange={(e) => setSource(e)}
                    >
                      {state.listMeeting?.map(
                        (item: any) =>
                          item.status !== STATUS.CANCELLED &&
                          item.status !== STATUS.DELETE && (
                            <Option key={item.id} value={item.name}>
                              {item.name}
                            </Option>
                          )
                      )}
                    </Select>
                  </Form.Item>
                )}

                {sourceType === SOURCE_TYPE.REFERENCE_DOCUMENT && (
                  <Form.Item
                    validateTrigger="onBlur"
                    name="sourceValue"
                  >
                    <Select
                      filterOption={(input, option: any) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                      mode='multiple'
                      showSearch
                      showArrow
                      optionLabelProp="label"
                      placeholder={`${intl.formatMessage({
                        id: 'user-requirement.label.select-a-reference-document',
                      })}`}
                      onChange={(e) => setSource(e)}
                    >
                      {state.listDocument?.map(
                        (item: any) =>
                          item.status !== STATUS.CANCELLED &&
                          item.status !== STATUS.DELETE && (
                            <Option key={item.id} value={item.name}>
                              {item.name}
                            </Option>
                          )
                      )}
                    </Select>
                  </Form.Item>
                )}

                {sourceType === SOURCE_TYPE.OTHER && (
                  <Form.Item
                    validateTrigger="onBlur"
                    name="sourceOther"
                  >
                    <Input
                      placeholder={`${intl.formatMessage({
                        id: 'user-requirement.label.please-specify-a-source',
                      })}`}
                      maxLength={50}
                      onChange={(e) => setSource(e)}
                    />
                  </Form.Item>
                )}
              </FormGroup>
            </Col>
            <Col span={12}>
              <FormGroup inline labelSpan={6} controlSpan={18} className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="sender">
                  {intl.formatMessage({ id: 'user-requirement.column.sender' })}
                </TriggerComment>
              }>
                <Form.Item
                  name="sender"
                  rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                >
                  <Input maxLength={255} />
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={12}>
              <FormGroup inline labelSpan={4} controlSpan={19} className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="send-date">
                  {intl.formatMessage({ id: 'user-requirement.column.send-date' })}
                </TriggerComment>
              }>
                <Form.Item validateTrigger="onBlur" name="sendDate">
                  <DatePicker format={DATE_FORMAT} />
                </Form.Item>
              </FormGroup>
            </Col>

            {
              form.getFieldValue('type') === SCOPE_TYPE_LIST[1].id ?
                <Col span={12}>
                  <FormGroup required inline labelSpan={6} controlSpan={18} className="rq-fg-comment" label={
                    <TriggerComment screenMode={screenMode} field="reviewer">
                      {intl.formatMessage({ id: 'common.assign-task.reviewer' })}
                    </TriggerComment>}>
                    <Form.Item name='reviewer' rules={[{ required: (form.getFieldValue('type') === SCOPE_TYPE_LIST[1].id), message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                      <Select
                        className='full-width'
                        filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                        showSearch
                        allowClear>
                        {
                          members.map(member => (
                            <Option key={member.userName} value={member.userName}>{member.fullName}</Option>
                          ))
                        }
                      </Select>
                    </Form.Item>
                  </FormGroup>
                </Col> : <></>
            }

            <Col span={12}>
              <FormGroup required inline labelSpan={type === SCOPE_TYPE_LIST[1].id ? 4 : 6} controlSpan={18} className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="priority">
                  {intl.formatMessage({ id: 'user-requirement.column.priority' })}
                </TriggerComment>}>
                <Form.Item name='priority' rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                  <Select
                    className='full-width'
                    allowClear>
                    {
                      PIORITY_OPTIONS.map(ele => (
                        <Option key={ele.value} value={ele.value}>{ele.text}</Option>
                      ))
                    }
                  </Select>
                </Form.Item>
              </FormGroup>
            </Col>

            <Col span={12}>
              <FormGroup inline labelSpan={type === SCOPE_TYPE_LIST[1].id ? 6 : 4} controlSpan={18} className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="dueDate">
                  {intl.formatMessage({ id: 'user-requirement.column.dueDate' })}
                </TriggerComment>}>
                <Form.Item name='dueDate'>
                  <DatePicker format={DATE_FORMAT} />
                </Form.Item>
              </FormGroup>
            </Col>

            <Col span={12}>
              <FormGroup inline labelSpan={type === SCOPE_TYPE_LIST[1].id ? 4 : 6} controlSpan={18} className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="isCovered">
                  {intl.formatMessage({ id: 'user-requirement.column.isCovered' })}
                </TriggerComment>}>
                <Form.Item name='isCovered' valuePropName="checked">
                  <Checkbox />
                </Form.Item>
              </FormGroup>
            </Col>
            <Col span={24}>
              <FormGroup inline label={`${intl.formatMessage({ id: 'user-requirement.label.product' })}`}
              labelSpan={3}
              controlSpan={21}>
              <Form.Item name="productId">
                <Select
                  filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                  showSearch
                  allowClear
                  className='full-width'>
                  {
                    products.map((products, idx) => (
                      <Option key={idx} value={products.id}>{products.name}</Option>
                      ))
                  }
                </Select>
              </Form.Item>
            </FormGroup>
            </Col>

            <Col span={24}>
              <div ref={descriptionRef}>
                <FormGroup className="rq-fg-comment" labelSpan={3} controlSpan={21} inline label={
                  <TriggerComment screenMode={screenMode} field="user-requirement-details">
                    {intl.formatMessage({ id: 'user-requirement.label.user-requirement-details' })}
                  </TriggerComment>
                }>
                  <Form.Item name="description">
                    <CkeditorMention
                      ref={getCkeditorData}
                      data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.description}
                    />
                  </Form.Item>
                </FormGroup>
              </div>
            </Col>

            <Col span={24}>
              {
                form.getFieldValue('type') === SCOPE_TYPE_LIST[1].id ? <FormGroup inline labelSpan={3} controlSpan={21} className="rq-fg-comment" label={
                  <TriggerComment screenMode={screenMode} field="change-reason">
                    {intl.formatMessage({ id: 'user-requirement.label.change-reason' })}
                  </TriggerComment>
                }>
                  <Form.Item name="changeReason">
                    <CkeditorMention
                      ref={getCkeditorData2}
                      data={screenMode == SCREEN_MODE.CREATE ? '' : state.detail?.changeReason}
                    />
                  </Form.Item>
                </FormGroup> : <></>
              }
            </Col>
          </Row>


        </Card>
        {/* type # original && view edit */}
        {screenMode === SCREEN_MODE.EDIT && <LavImpact editable={state.detail?.type !== 1 ? true : false} dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT} onChange={onChange} isSubmitForm={isSubmitForm} />}

        <LavEffortEstimationForm
          screenMode={screenMode}
          hasDevelopment={state?.detail?.hasOwnProperty('development')}
          hasImplementation={state?.detail?.hasOwnProperty('implementation')}
        />
        <LavRelatedLinksForm form={form} screenMode={screenMode} />
      </Space>
    </Form>
  </Spin>
}

export default UserRequirementFormPage
