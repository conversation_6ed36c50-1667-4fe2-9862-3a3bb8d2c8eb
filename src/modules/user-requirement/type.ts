
export interface UserRequirementState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: UserRequirementDetail | null
  selectedData?: UserRequirementDetail | null
  listMeeting?: any[] | [],
  isLoadingListMeeting?: boolean,
  listDocument?: any[] | [],
  isLoadingListDocument?: boolean,
  importValidateResponse: any
  importResponse: any
  isModalShow?: boolean
}
export interface UserRequirementDetail {
  id?: number | null,
  code: string,
  name: string,
  order: number | null,
  status: number | null,
  type: number | null,
  source: number | null,
  sourceValue: number | null,
  sourceValueOther: string,
  scope: number | null,
  sender: string,
  sendDate?: Date | null,
  reviewer: string,
  customer: string
  description: string,
  changeReason: string,
  storage: string
  jira: string
  confluence: string
  dueDate: Date | null,
  priority: number,
  isCovered: boolean
  reqElicitation: string,
  documentation: string,
  development: number | null,
  actionStatus?: number | null,
  meetingMinutes?: any | null,
  referenceDocuments?: any | null,
  projectId?: number | null,
  product: any,

}

export const defaultState: UserRequirementState = {
  detail: {
    id: null,
    code: '',
    name: '',
    order: null,
    status: null,
    type: null,
    source: null,
    sourceValue: null,
    sourceValueOther: '',
    scope: null,
    sender: '',
    sendDate: null,
    reviewer: '',
    customer: '',
    description: '',
    changeReason: '',
    storage: '',
    jira: '',
    dueDate: null,
    priority: 0,
    isCovered: false,
    confluence: '',
    reqElicitation: '',
    documentation: '',
    development: null,
    actionStatus: null,
    meetingMinutes: [],
    referenceDocuments: [],
    product: {},
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  listMeeting: [],
  isLoadingListMeeting: false,
  listDocument: [],
  isLoadingListDocument: false,
  importValidateResponse: '',
  importResponse: '',
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/USER_REQUIREMENT/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/USER_REQUIREMENT/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/USER_REQUIREMENT/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/USER_REQUIREMENT/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/USER_REQUIREMENT/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/USER_REQUIREMENT/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/USER_REQUIREMENT/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/USER_REQUIREMENT/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/USER_REQUIREMENT/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/USER_REQUIREMENT/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/USER_REQUIREMENT/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/USER_REQUIREMENT/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/USER_REQUIREMENT/GET_LIST_FAILED',

  GET_LIST_MEETING_REQUEST = '@@MODULES/USER_REQUIREMENT/GET_LIST_MEETING_REQUEST',
  GET_LIST_MEETING_SUCCESS = '@@MODULES/USER_REQUIREMENT/GET_LIST_MEETING_SUCCESS',
  GET_LIST_MEETING_FAILED = '@@MODULES/USER_REQUIREMENT/GET_LIST_MEETING_FAILED',

  GET_LIST_DOCUMENT_REQUEST = '@@MODULES/USER_REQUIREMENT/GET_LIST_DOCUMENT_REQUEST',
  GET_LIST_DOCUMENT_SUCCESS = '@@MODULES/USER_REQUIREMENT/GET_LIST_DOCUMENT_SUCCESS',
  GET_LIST_DOCUMENT_FAILED = '@@MODULES/USER_REQUIREMENT/GET_LIST_DOCUMENT_FAILED',

  DELETE_REQUEST = '@@MODULES/USER_REQUIREMENT/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/USER_REQUIREMENT/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/USER_REQUIREMENT/DELETE_FAILED',


  IMPORT_FILE_VALIDATE = '@@MODULES/USER_REQUIREMENT/IMPORT_FILE_VALIDATE',
  IMPORT_FILE_VALIDATE_SUCCESS = '@@MODULES/USER_REQUIREMENT/IMPORT_FILE_VALIDATE_SUCCESS',
  IMPORT_FILE_VALIDATE_FAILURE = '@@MODULES/USER_REQUIREMENT/IMPORT_FILE_VALIDATE_FAILURE',
  IMPORT_FILE = '@@MODULES/USER_REQUIREMENT/IMPORT_FILE',
  IMPORT_FILE_SUCCESS = '@@MODULES/USER_REQUIREMENT/IMPORT_FILE_SUCCESS',
  IMPORT_FILE_FAILURE = '@@MODULES/USER_REQUIREMENT/IMPORT_FILE_FAILURE',
  DOWNLOAD_FILE = '@@MODULES/USER_REQUIREMENT/DOWNLOAD_FILE',
  DOWNLOAD_FILE_SUCCESS = '@@MODULES/USER_REQUIREMENT/DOWNLOAD_FILE_SUCCESS',
  DOWNLOAD_FILE_FAILURE = '@@MODULES/USER_REQUIREMENT/DOWNLOAD_FILE_FAILURE',

  SET_MODAL_VISIBLE = '@@MODULES/USER_REQUIREMENT/SET_MODAL_VISIBLE',
}


