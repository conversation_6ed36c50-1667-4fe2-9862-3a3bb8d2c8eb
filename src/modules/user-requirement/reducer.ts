import { createReducer } from '@reduxjs/toolkit'
import {
  getDetailRequest,
  getDetailSuccess,
  getDetailFailed,
  createRequest,
  createSuccess,
  createFailed,
  updateRequest,
  updateSuccess,
  updateFailed,
  getListRequest,
  getListSuccess,
  getListFailed,
  deleteRequest,
  deleteSuccess,
  deleteFailed,
  resetState,
  getListMeetingRequest,
  getListMeetingSuccess,
  getListMeetingFailed,
  getListDocumentRequest,
  getListDocumentSuccess,
  getListDocumentFailed,
  importUrValidate,
  importUrValidateSuccess,
  importUrValidateFailure,
  importUr,
  importUrSuccess,
  importUrFailure,
  downloadTemplate,
  downloadTemplateSuccess,
  downloadTemplateFailure,
  setModalVisible,
} from './action';
import { defaultState, UserRequirementState } from './type';

const initState: UserRequirementState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })

      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })


      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })



      .addCase(getListMeetingRequest, (state, action?) => {
        state.isLoadingListMeeting = true;
      })
      .addCase(getListMeetingSuccess, (state, action) => {
        state.isLoadingListMeeting = false
        state.listMeeting = action.payload
      })
      .addCase(getListMeetingFailed, (state, action) => {
        state.isLoadingListMeeting = false
        state.listMeeting = []
      })

      .addCase(getListDocumentRequest, (state, action?) => {
        state.isLoadingListDocument = true;
      })
      .addCase(getListDocumentSuccess, (state, action) => {
        state.isLoadingListDocument = false
        state.listDocument = action.payload
      })
      .addCase(getListDocumentFailed, (state, action) => {
        state.isLoadingListDocument = false
        state.listDocument = []
      })

      .addCase(importUrValidate, (state, action) => {
        state.isLoading = true
      })
  
      .addCase(importUrValidateSuccess, (state, action) => {
        state.isLoading = false
        state.importValidateResponse = action.payload
      })
  
      .addCase(importUrValidateFailure, (state, action) => {
        state.isLoading = false
        state.importValidateResponse = action.payload
      })
  
      .addCase(importUr, (state, action) => {
        state.isLoading = true
      })
  
      .addCase(importUrSuccess, (state, action) => {
        state.isLoading = false
        state.importResponse = action.payload
      })
  
      .addCase(importUrFailure, (state, action) => {
        state.isLoading = false
        state.importResponse = action.payload
      })
  
      .addCase(downloadTemplate, (state, action) => {
        state.isLoading = true
      })
      .addCase(downloadTemplateSuccess, (state, action) => {
        state.isLoading = false
      })
      .addCase(downloadTemplateFailure, (state, action) => {
        state.isLoading = false
      })
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if(!action.payload){
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })
  )
})

export default reducer
export { initState as UserRequirementState }
