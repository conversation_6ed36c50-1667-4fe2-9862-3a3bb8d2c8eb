import AppState from '@/store/types';
import { InboxOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Radio, Space, Table, Typography, Upload } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import intl from '../../../config/locale.config';
import { API_URLS } from '../../../constants';
import CustomModal from '../../../helper/component/custom-modal';
import AppCommonService from '../../../services/app.service';
import { importUr, importUrValidate } from '../action';
import { UserRequirementState } from '../type';
import { saveAs } from 'file-saver'
const { Text, Link } = Typography

const { Dragger } = Upload;
const Import = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [step, setStep] = useState(1)
  const [dataState, setDataState] = useState(null) as any;
  const [fileList, setFileList] = useState([]) as any
  const [errorData, setErrorData] = useState([]) as any
  const [qualifiedData, setQualifiedData] = useState([]) as any
  const [validateError, setValidateError] = useState(null)
  const [footer, setFooter] = useState<any>([])
  const dispatch = useDispatch();

  const state = useSelector<AppState | null>(
    (s) => s?.UserRequirement
  ) as UserRequirementState

  useEffect(() => {
    if (state.importValidateResponse) {
      setStep(2);

      state.importValidateResponse?.successObj?.errorData ? setErrorData(state.importValidateResponse.successObj?.errorData) : setErrorData([]);
      state.importValidateResponse?.successObj?.qualifiedData ? setQualifiedData(state.importValidateResponse.successObj?.qualifiedData) : setQualifiedData([]);
      setValidateError(null)
    }
  }, [state.importValidateResponse])
  const handleOpenModal = () => {
    setStep(1)
    setIsModalVisible(true)
  }

  const dragConfig = {
    class: 'import-ur-drag',
    name: 'file',
    multiple: false,
    //accept: ".xlsm, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel",
    onChange({ file, fileList }) {
      if (fileList.length <= 1) {
        setFileList(fileList);
      }
    },
    onDrop(e) {
    },
    beforeUpload() {
      return false;
    }
  };

  const handleOk = () => {
    if (fileList && fileList[0]) {
      let formData = new FormData();
      formData.append("uploadedfile", fileList[0].originFileObj || '');
      dispatch(importUrValidate(formData));
    }
  }

  const handleCancel = () => {
    setStep(1)
    setFileList([])
    setErrorData([])
    setQualifiedData([])
    setIsModalVisible(false)
  }

  const handleBack = () => {
    setStep(1)
    setFileList([])
    setErrorData([])
    setQualifiedData([])
  }

  const handleDownload = (e) => {
    e.preventDefault();
    AppCommonService.downloadTemplate(API_URLS.DOWNLOAD_IMPORT_TEMPLATE_UR).then((res) => {
      var blob = new Blob([res.data])
      const fileName = `UserRequirementTemplate_${moment().format('DDMMYYYY_HHMMSS')}.xlsm`;
      saveAs(blob, fileName)
    }).catch((e) => {
      console.error(e)
    })
  }

  useEffect(() => {
    if (qualifiedData.length == 0 && errorData.length != 0) {
      setDataState('errorData')
    } else {
      setDataState('qualifiedData')
    }
  }, [qualifiedData, errorData])
  useEffect(() => {
    setFooter([])
    if (step === 2) {
      setFooter([
        <>
          <Button
            style={{ float: 'left' }}
            key="close"
            onClick={handleCancel}
          >
            {intl.formatMessage({ id: 'common.action.close' })}
          </Button>
          <div>
            <Button
              key="back"
              type="primary"
              onClick={handleBack}
            >
              {intl.formatMessage({ id: 'common.action.back' })}
            </Button>
            <Button
              style={{ backgroundColor: '#00C853', color: 'white' }}
              key="import"
              onClick={handleImport}
              disabled={errorData.length != 0}
            >
              {intl.formatMessage({ id: 'common.action.import' })}
            </Button>
          </div>
        </>,
      ])
    }
  }, [step, errorData?.length])

  const columnsQualified = [
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'user-requirement.column.user-requirement',
          })}
        </Text>
      ),
      dataIndex: 'userRequirement',
      key: 'userRequirement',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.type' })}
        </Text>
      ),
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.scope' })}
        </Text>
      ),
      dataIndex: 'scope',
      key: 'scope',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.type' })}
        </Text>
      ),
      dataIndex: 'sourceType',
      key: 'sourceType',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.source' })}
        </Text>
      ),
      dataIndex: 'source',
      key: 'source',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.sender' })}
        </Text>
      ),
      dataIndex: 'sender',
      key: 'sender',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.send-date' })}
        </Text>
      ),
      dataIndex: 'sendDate',
      key: 'sendDate',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.usrdetails' })}
        </Text>
      ),
      dataIndex: 'userRequirementDetails',
      key: 'userRequirementDetails',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.req-eliciation' })}
        </Text>
      ),
      dataIndex: 'reqElicitation',
      key: 'reqElicitation',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.documentation' })}
        </Text>
      ),
      dataIndex: 'documentation',
      key: 'documentation',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'user-requirement.column.development' })}
        </Text>
      ),
      dataIndex: 'development',
      key: 'development',
    },

  ];

  const columnsError = [
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'user-requirement.column.user-requirement',
          })}
        </Text>
      ),
      dataIndex: 'userRequirement',
      key: 'userRequirement',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'user-requirement.column.row-number',
          })}
        </Text>
      ),
      dataIndex: 'rowNumber',
      key: 'rowNumber',
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'user-requirement.column.error-type',
          })}
        </Text>
      ),
      dataIndex: 'errorType',
      key: 'errorType',
    },
  ]




  const handleImport = () => {
    dispatch(importUr(qualifiedData));
    setIsModalVisible(false);
    if (props.onFinish) {
      props.onFinish()
    }
    setFileList([]);
  }

  return (
    <>
      <Button
        size="middle"
        type="primary"
        ghost
        icon={<UploadOutlined />}
        onClick={handleOpenModal}
      >
        {intl.formatMessage({
          id: 'user-requirement.button.import-user-requirement',
        })}
      </Button>

      {step === 1 && <CustomModal
        size="small"
        visible={isModalVisible}
        className='modal-import-ur'
        title={intl.formatMessage({
          id: 'user-requirement.create-modal-title.import',
        })}
        okText="Next"
        okButtonProps={{ disabled: !fileList || !fileList[0] }}
        onOk={handleOk}
        cancelText="Close"
        maskClosable={false}
        onCancel={handleCancel}
      >
        <>
          <Dragger fileList={fileList} {...dragConfig} maxCount={1}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              {intl.formatMessage({ id: 'common.label.upload-label' })}&nbsp;
              <span style={{ cursor: 'pointer', color: '#2979FF', textDecoration: 'underline' }}>
                {intl.formatMessage({ id: 'common.label.upload-label-browse-file' })}
              </span>
            </p>
          </Dragger>
          <div className='import-ur-note'>
            Please use <a href='#' onClick={handleDownload}>
              Default Template
            </a> to import
          </div>
        </>

      </CustomModal>}
      {step == 2}
      {step === 2 && (
        <CustomModal
          size={!validateError ? 'large' : 'small'}
          visible={isModalVisible}
          className='modal-import-ur'
          onCancel={handleCancel}
          title='Import'
          maskClosable={false}
          footer={footer}
        >
          {!validateError
            ?
            (<>
              {errorData.length !== 0 && <p>There are <Text type='danger'>{errorData.length}</Text> error records. Please correct these records before importing.</p>}
              <Radio.Group value={dataState}>
                <Space>
                  <Radio.Button value='errorData' onClick={() => setDataState('errorData')}>ErrorData ({errorData.length})</Radio.Button>
                  <Radio.Button value='qualifiedData' onClick={() => setDataState('qualifiedData')}>Qualified Data ({qualifiedData.length})</Radio.Button>
                </Space>
              </Radio.Group>
              {dataState === 'qualifiedData' && <Table columns={columnsQualified} dataSource={qualifiedData} />}
              {dataState === 'errorData' && <Table columns={columnsError} dataSource={errorData} />}
            </>)
            :
            (<>
              <Text type='danger'>{intl.formatMessage({ id: validateError })}</Text>
              <div className='import-ur-note'>
                Please use <Link target="_blank" onClick={handleDownload}>
                  Default Template
                </Link> to import
              </div>
            </>)}
        </CustomModal>
      )}
    </>
  )
}

export default Import
