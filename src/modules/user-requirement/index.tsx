import { PlusOutlined } from '@ant-design/icons'
import { Button, Space, Typography } from 'antd'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { ALL_SCOPE_LIST, API_URLS, APP_ROLES, APP_ROUTES, DATE_FORMAT, getPriority, ISCOVERED, PIORITY_OPTIONS, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCOPE_TYPE_LIST, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import ExportButton from '../../helper/component/lav-table/export'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import UserRequirementFormPage from './form/form'
import Import from './import'
const { Text } = Typography

const UserRequirement = () => {
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)

  useEffect(() => {
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'user-requirement.header.title' });
  }, [screenMode]);

  const columns = [
    {
      title: intl.formatMessage({ id: 'user-requirement.column.user-requirement-code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USER_REQUIREMENT_DETAIL}${record.id}`
        return <Link to={href}>{text}</Link>
      }
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.user-requirement' }),
      dataIndex: 'name',
      width: '45%',
      sorter: true,
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.type' }),
      dataIndex: 'type',
      width: '15%',
      sorter: true,
      ...getColumnSearchProps('type', SEARCH_TYPE.SINGLE_CHOICE, SCOPE_TYPE_LIST),
      render: (type: number) => {
        const typeIndex = SCOPE_TYPE_LIST.findIndex((item) => item.id == type)
        if (typeIndex !== -1) {
          return <Text>{SCOPE_TYPE_LIST[typeIndex].name}  </Text>
        } else {
          return <Text></Text>
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.priority' }),
      dataIndex: 'priority',
      width: '15%',
      sorter: true,
      ...getColumnDropdownFilterProps(PIORITY_OPTIONS, 'priority'),
      render: (text: number) => {
        let priority = getPriority(text)
        return priority
      },
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.scope' }),
      dataIndex: 'scope',
      width: '8%',
      sorter: true,
      ...getColumnDropdownFilterProps(
        ALL_SCOPE_LIST.map((item) => {
          return { text: item.name, value: item.id }
        })
      ),
      render: (text, record) => {
        const filter: any = ALL_SCOPE_LIST.filter((item: any) => item.id == text)
        return <Text>{filter[0]?.name}</Text>
      },
    },
    {
      title: intl.formatMessage({ id: 'user-requirement.column.isCovered' }),
      dataIndex: 'isCovered',
      width: '10%',
      sorter: true,
      ...getColumnSearchProps('isCovered', SEARCH_TYPE.SINGLE_CHOICE, ISCOVERED, null, true),
      render: (text) => {
        // lavdate
        return text == false ? intl.formatMessage({id : 'common.label.no'}) : intl.formatMessage({id : 'common.label.yes'})
      },
    },

    {
      title: intl.formatMessage({ id: 'user-requirement.column.dueDate' }),
      dataIndex: 'dueDate',
      width: '10%',
      sorter: true,
      ...getColumnSearchProps('dueDate', SEARCH_TYPE.DATE),
      render: (text) => {
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
    },

    {
      title: intl.formatMessage({ id: 'user-requirement.column.status' }),
      dataIndex: 'status',
      ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      sorter: true,
      width: '80px',
      render: (record) => renderStatusBadge(record, true),
    }
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.BA_LEAD)) ?
      <Button ghost={true}
        type='primary'
        className='lav-btn-create'
        icon={<PlusOutlined />}
        onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'user-requirement.button.create-ur' })}
      </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return (((((hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.BA_LEAD)) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }

  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.BA_LEAD)) && record.status !== STATUS.DELETE) ?
      children : <></>
  }

  const ImportComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.BA_LEAD)) && <Import onFinish={() => handleDataChange()} />
  }

  const ExportComponent: React.FC<any> = ({ handleExport }) => {
    return <ExportButton fileName='user-requirement.header.title' onExport={handleExport} title='user-requirement.button.export-ur'/>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        screenMode === SCREEN_MODE.VIEW ?
          <LavTable
            title="user-requirement.header.title"
            artefact_type="common.artefact.user-requirement"
            apiUrl={API_URLS.USER_REQUIREMENTS}
            columns={columns}
            artefactType={REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT}
            updateComponent={UpdateComponent}
            createComponent={CreateComponent}
            deleteComponent={DeleteComponent}
            importComponent={ImportComponent}
            exportComponent={ExportComponent}
          /> : <></>
      }

      {
        screenMode === SCREEN_MODE.CREATE ? <UserRequirementFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <UserRequirementFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} userRequirementID={id} /> : <></>
      }
    </Space>
  )
}

export default UserRequirement
