import LavImpact from '../../../../../helper/component/lav-impact'
import AppState from '@/store/types'
import {
    Breadcrumb, Button, Card, Col, Divider, Form, Modal, Row, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, DATE_FORMAT, getPriority, MESSAGE_TYPES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCOPE_MEETING_LIST, SCOPE_TYPE, SCOPE_TYPE_LIST, SOURCE_LIST, STATUS } from '../../../../../constants'
import Ckeditor from '../../../../../helper/component/ckeditor'
import DeleteButton from '../../../../../helper/component/commonButton/DeleteButton'
import FormGroup from '../../../../../helper/component/form-group'
import LavAuditTrail from '../../../../../helper/component/lav-audit-trail'
import LavButtons from '../../../../../helper/component/lav-buttons'
import LavEffortEstimation from '../../../../../helper/component/lav-efffort-estimation'
import LavReferences from '../../../../../helper/component/lav-references'
import LavRelatedLinks from '../../../../../helper/component/lav-related-links'
import useModalConfirmationConfig from '../../../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge, ShowAppMessage, ShowMessgeAdditionalSubmit } from '../../../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../../../modules/_shared/comment/action'
import TriggerComment from '../../../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../../../modules/_shared/comment/type'
import ButtonService from '../../../../../services/lav-buttons-service'
import { deleteRequest, updateRequest } from '../../../action'
import HistoryNavigation from '../../../../../modules/history/navigation'

const { Title, Text } = Typography
const { confirm } = Modal

interface UserRequirementVersionDetailsProps {
    data: any | [],
    userRequirementID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any,
    setSelectedRowVersion: (version: string) => void, 
    onDismiss: () => void | null, 
}
const UserRequirementVersionDetails = ({ data, userRequirementID, onChange, isLoading, isModalShow, setScreenMode, setSelectedRowVersion, onDismiss }: UserRequirementVersionDetailsProps) => {
    const dispatch = useDispatch();
    const modalConfirmConfig = useModalConfirmationConfig()
    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);
    const [isModalVisible, setIsModalVisible] = useState(false)
    const [form] = Form.useForm()
    const ref: any = createRef()

    const handleCancelRecord = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_4' },
                {
                    Artefact: `${intl.formatMessage({
                        id: 'common.artefact.user-requirement',
                    })}`,
                }
            )}`,
            onOk() {
                onChange();
                dispatch(updateRequest({ ...data, status: STATUS.CANCELLED, messageAction: MESSAGE_TYPES.CANCEL }))
            },
            onCancel() { },
        })
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + data?.name; 
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        form.resetFields(['reason'])
    }, [isModalVisible])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'type', title: intl.formatMessage({ id: 'user-requirement.column.type' }), },
            { field: 'source-type', title: intl.formatMessage({ id: 'user-requirement.column.source-type' }), },
            { field: 'source', title: intl.formatMessage({ id: 'user-requirement.column.source' }), },
            { field: 'scope', title: intl.formatMessage({ id: 'user-requirement.column.scope' }), },
            { field: 'sender', title: intl.formatMessage({ id: 'user-requirement.column.sender' }), },
            { field: 'send-date', title: intl.formatMessage({ id: 'user-requirement.column.send-date' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'user-requirement.column.reviewer' }), },
            { field: 'user-requirement-details', title: intl.formatMessage({ id: 'user-requirement.label.user-requirement-details' }), },
            { field: 'change-reason', title: intl.formatMessage({ id: 'user-requirement.label.change-reason' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
            { field: 'common-bussiness-rule', title: intl.formatMessage({ id: 'user-requirement.reference.common-bussiness-rule' }), },
            { field: 'reference.meeting', title: intl.formatMessage({ id: 'user-requirement.reference.meeting' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'development', title: intl.formatMessage({ id: 'createscreen.label.development' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.USER_REQUIREMENTS,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT
    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code}-{data?.name}
                    </Title>
                </div>

                <LavButtons
                    url={`${API_URLS.USER_REQUIREMENTS}/${data?.id}`}
                    reviewer={`${data?.reviewer}`}
                    customer={`${data?.customer}`}
                    artefact_type="common.artefact.change-request"
                    status={data?.status}
                    changePage={() => onChange()}>                                                           
                    <Button onClick={debounce(onDismiss, 500)}>
                        {intl.formatMessage({ id: 'common.action.close' })}
                    </Button>
                </LavButtons>

            </Row>
            <Divider className="mt-0 mb-0" />          
            { data?.nextPrevious.latestVersion === data?.version ? <></>:
                   <HistoryNavigation data={data} onChange={onChange} setScreenMode={setScreenMode} setSelectedRowVersion={setSelectedRowVersion} screenArtefact={"common.artefact.user-requirement"} artefactType={REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT} />
                }
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Space size="large">
                            {/* <span>
                                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
                            </span> */}
                            {renderStatusBadge(data?.status, true)}
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'user-requirement.label.user-requirement-infomation',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]} className='rq-card-infor'>
                                <Col span={5}>
                                    <TriggerComment field="type">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.type',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    {SCOPE_TYPE_LIST.find((item) => item.id === data?.type)?.name}
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="source-type">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.source',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19} className="source">
                                    <TriggerComment field="source">
                                        {SOURCE_LIST.find((item) => item.id === data?.source)?.name} {data?.source == 1 ? data?.meetingMinutes?.map((e, index) => {
                                            return <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MEETING_DETAIL}${e.id}`} key={index}>{index !== 0 ? `, ${e.name}` : `${e.name}`}</Link>
                                        }) : data?.source == 2 ? data?.referenceDocuments?.map((e, index) => {
                                            return <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}${e.id}`} key={index}>{index !== 0 ? `, ${e.name}` : `${e.name}`}</Link>
                                        }) : data?.sourceOther}
                                    </TriggerComment>
                                </Col>
                                {/* //co dk */}

                                <Col span={5}>
                                    <TriggerComment field="scope">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.scope',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    {
                                        SCOPE_MEETING_LIST.find(
                                            (item) => item.id === data?.scope
                                        )?.name
                                    }
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="sender">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.sender',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>{data?.sender}</Col>

                                <Col span={5}>
                                    <TriggerComment field="send-date">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.send-date',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                {/* lavdate */}
                                <Col span={19}>{data?.sendDate ? moment(data?.sendDate).format(DATE_FORMAT) : ''}</Col>

                                {
                                    data?.type === SCOPE_TYPE_LIST[1].id ? <>
                                        <Col span={5}>
                                            <TriggerComment field="reviewer">
                                                <Text type="secondary">
                                                    {intl.formatMessage({ id: 'common.assign-task.reviewer' })}:
                                                </Text>
                                            </TriggerComment>
                                        </Col>
                                        {/* lavdate */}
                                        <Col span={19}>{data?.reviewer}</Col>
                                    </> : <></>
                                }

                                <Col span={5}>
                                    <TriggerComment field="user-requirement-details">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.label.user-requirement-details',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: data?.description }}></div>
                                </Col>

                                {
                                    data?.type === SCOPE_TYPE_LIST[1].id ? <>
                                        <Col span={5}>
                                            <TriggerComment field="change-reason">
                                                <Text type="secondary">
                                                    {intl.formatMessage({
                                                        id: 'user-requirement.label.change-reason',
                                                    })}
                                                </Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={19}>
                                            <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: data?.changeReason }}></div>
                                        </Col>

                                    </> : <></>
                                }
                                <Col span={5}>
                                    <TriggerComment field="priority">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.priority',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <Text>{getPriority(data?.priority)}</Text>
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="dueDate">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.dueDate',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <Text>{data?.dueDate ? moment(data?.dueDate).format(DATE_FORMAT) : ''}</Text>
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="isCovered">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.isCovered',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <Text>{data?.isCovered ? intl.formatMessage({ id: 'common.label.yes' }) : intl.formatMessage({ id: 'common.label.no' })}</Text>
                                </Col>

                                <Col span={5}>
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.product',
                                            })}{': '}
                                        </Text>
                                </Col>
                                <Col span={19}>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: data?.product?.name,
                                        }}
                                    ></div>
                                </Col>
                            </Row>

                        </Card>

                        <LavReferences data={data} />
                        {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null' || data?.impacts === `{"unqId":"lav_root","children":[]}` || data?.impacts === "[]") ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT} onChange={() => { }} isViewMode={true} />}
                        {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT} onChange={() => { }} isViewMode={true} /> : <></>} */}

                        <Row justify="space-between">
                            <Col span={8}>
                                <LavEffortEstimation data={data} />
                            </Col>
                            <Col span={15}>
                                <LavRelatedLinks data={data} />
                            </Col>
                        </Row>

                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space >
    ) : <></>
}

export default UserRequirementVersionDetails
