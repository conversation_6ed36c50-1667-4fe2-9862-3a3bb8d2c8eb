import LavImpact from '../../../helper/component/lav-impact'
import AppState from '@/store/types'
import {
    <PERSON><PERSON><PERSON><PERSON>b, Button, Card, Col, Divider, Form, Modal, Row, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, DATE_FORMAT, getPriority, MESSAGE_TYPES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCOPE_MEETING_LIST, SCOPE_TYPE, SCOPE_TYPE_LIST, SCREEN_MODE, SOURCE_LIST, STATUS } from '../../../constants'
import Ckeditor from '../../../helper/component/ckeditor'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import FormGroup from '../../../helper/component/form-group'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavButtons from '../../../helper/component/lav-buttons'
import LavEffortEstimation from '../../../helper/component/lav-efffort-estimation'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge, ShowAppMessage, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import ButtonService from '../../../services/lav-buttons-service'
import { deleteRequest, updateRequest } from '../action'

const { Title, Text } = Typography
const { confirm } = Modal

interface RightControlProps {
    data: any | [],
    userRequirementID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}
const RightControl = ({ data, userRequirementID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const modalConfirmConfig = useModalConfirmationConfig()
    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);
    const [isModalVisible, setIsModalVisible] = useState(false)
    const [form] = Form.useForm()
    const ref: any = createRef()

    const handleCancelRecord = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_4' },
                {
                    Artefact: `${intl.formatMessage({
                        id: 'common.artefact.user-requirement',
                    })}`,
                }
            )}`,
            onOk() {
                onChange();
                dispatch(updateRequest({ ...data, status: STATUS.CANCELLED, messageAction: MESSAGE_TYPES.CANCEL }))
            },
            onCancel() { },
        })
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + data?.name; 
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])
    useEffect(() => {
        form.resetFields(['reason'])
    }, [isModalVisible])
    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'type', title: intl.formatMessage({ id: 'user-requirement.column.type' }), },
            { field: 'source-type', title: intl.formatMessage({ id: 'user-requirement.column.source-type' }), },
            { field: 'source', title: intl.formatMessage({ id: 'user-requirement.column.source' }), },
            { field: 'scope', title: intl.formatMessage({ id: 'user-requirement.column.scope' }), },
            { field: 'sender', title: intl.formatMessage({ id: 'user-requirement.column.sender' }), },
            { field: 'send-date', title: intl.formatMessage({ id: 'user-requirement.column.send-date' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'user-requirement.column.reviewer' }), },
            { field: 'user-requirement-details', title: intl.formatMessage({ id: 'user-requirement.label.user-requirement-details' }), },
            { field: 'change-reason', title: intl.formatMessage({ id: 'user-requirement.label.change-reason' }), },
            { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
            { field: 'common-bussiness-rule', title: intl.formatMessage({ id: 'user-requirement.reference.common-bussiness-rule' }), },
            { field: 'reference.meeting', title: intl.formatMessage({ id: 'user-requirement.reference.meeting' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'development', title: intl.formatMessage({ id: 'createscreen.label.development' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.USER_REQUIREMENTS,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT

    const onSubmit = debounce((values: any, st?: string) => {
        ButtonService
            .rejectUr(`${API_URLS.USER_REQUIREMENTS}/${data?.id}`, `"${values.reason}"`)
            .then((res) => {
                ShowAppMessage(null, MESSAGE_TYPES.REJECT, "common.artefact.change-request");
                onChange()
                setIsModalVisible(false)
            })
            .catch((error) => {
                ShowAppMessage(error, null, "common.artefact.change-request");
            })
    }, 500)

    const approveHandle = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_3' },
                {
                    Artefact: `${intl.formatMessage({
                        id: "common.artefact.change-request",
                    })}`,
                }
            )}`,
            onOk() {
                ButtonService
                    .approve(`${API_URLS.USER_REQUIREMENTS}/${data?.id}`)
                    .then((res) => {
                        ShowAppMessage(null, MESSAGE_TYPES.APPROVE, "common.artefact.change-request");
                        onChange()
                    })
                    .catch((error) => {
                        if (error.response.data?.isSuccess === false) {
                            // setData(error.response?.data?.object || [])
                        } else {
                            ShowAppMessage(error, null, "common.artefact.change-request");
                        }
                    })
            },
            onCancel() { },
        })
    }
    const ApproveComponent: React.FC<any> = ({ record, children }) => {
        return (data?.reviewer == currentUserName() && data?.status == STATUS.SUBMITTED && data?.type === SCOPE_TYPE_LIST[1].id && (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA_LEAD))) ?
            <Button type='primary' className='success-btn' onClick={() => {
                if (data?.dueDate) {
                    approveHandle()
                } else {
                    ShowMessgeAdditionalSubmit('EMSG_38', 'common.artefact.user-requirements')
                }
            }
            }
            >
                {intl.formatMessage({ id: `common.action.approve` })}
            </Button> : <></>
    }

    const RejectComponent: React.FC<any> = ({ record, children }) => {
        return (data?.type === SCOPE_TYPE.CHANGE_REQUEST.value && data?.reviewer == currentUserName() && data?.status == STATUS.SUBMITTED) ? <Button danger onClick={() => setIsModalVisible(true)}>
            {intl.formatMessage({ id: `common.action.reject` })}
        </Button> : <></>
    }

    const reopenHandle = () => {
        ButtonService
            .reOpen(`${API_URLS.USER_REQUIREMENTS}/${data?.id}`)
            .then((res) => {
                ShowAppMessage(null, MESSAGE_TYPES.REOPEN, "common.artefact.change-request");
            })
            .catch((error) => {
                ShowAppMessage(error, null, "common.artefact.change-request");
            })
    }

    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE) ? <DeleteButton
            type={BUTTON_TYPE.TEXT}
            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.user-requirement' }) })}
            okCB={() => dispatch(deleteRequest(userRequirementID))}
            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
    }

    const CancelComponent: React.FC<any> = ({ record, children }) => {
        return (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM)) && (data?.status === STATUS.SUBMITTED) ? <Button className='cancel-btn' onClick={() => handleCancelRecord()}>
            {intl.formatMessage({ id: `common.action.cancel` })}</Button> : <></>
    }
    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code}-{data?.name}
                    </Title>
                </div>

                <LavButtons
                    url={`${API_URLS.USER_REQUIREMENTS}/${data?.id}`}
                    reviewer={`${data?.reviewer}`}
                    customer={`${data?.customer}`}
                    artefact_type="common.artefact.change-request"
                    status={data?.status}
                    isHasEndorse={false}
                    isHasRemove={false}
                    isHasCancel={false}
                    isHasApprove={false}
                    approveButton={ApproveComponent}
                    rejectButton={RejectComponent}
                    deleteButton={DeleteComponent}
                    cancelButton={CancelComponent}
                    changePage={() => onChange()}>

                    <Modal
                        title={intl.formatMessage({ id: `common.action.reject` })}
                        visible={isModalVisible}
                        footer={false}
                        closable={false}>
                        <Form form={form}
                            onFinish={onSubmit}
                            labelCol={{
                                span: 2,
                                offset: 0,
                            }}>
                            <FormGroup>
                                <Form.Item rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]} name='reason'>
                                    <Ckeditor ref={ref} data={''} placeholder={intl.formatMessage({ id: `user-requirement.place-holder.reject-ur` })} />
                                </Form.Item>
                            </FormGroup>
                            <Row justify='end'>
                                <Space>
                                    <Button htmlType='submit' type='primary' className='success-btn'> {intl.formatMessage({ id: `common.action.reject` })}</Button>
                                    <Button onClick={() => setIsModalVisible(false)}> {intl.formatMessage({ id: `common.action.cancel` })}</Button>
                                </Space>
                            </Row>
                           
                            
                        </Form>
                    </Modal>

                    {/* Update record */}
                    {((((hasRole(APP_ROLES.BA) && data.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer)
                        && (data?.status === STATUS.SUBMITTED || data?.status === STATUS.REJECT || data?.status === STATUS.REJECT_CUSTOMER || data?.status === STATUS.APPROVE))) &&
                        data.status !== STATUS.CANCELLED &&
                        data.status !== STATUS.DELETE &&
                        data.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && data.status !== STATUS.CANCELLED &&
                        data.status !== STATUS.DELETE
                    ) ?
                        <Button
                            type='primary'
                            className='lav-btn-create'
                            onClick={() => {
                                setScreenMode(SCREEN_MODE.EDIT)
                            }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                    }
                </LavButtons>

            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Space size="large">            
                            <span>          
                                <TriggerComment field='version'>                               
                                    <a onClick={() => {
                                        setScreenMode(SCREEN_MODE.HISTORY)
                                    }}>
                                        {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                    </a>
                                </TriggerComment>
                            </span>
                            {renderStatusBadge(data?.status, true)}                            
                        </Space>

                        <Card
                            title={
                                <Title level={5}>
                                    {`${intl.formatMessage({
                                        id: 'user-requirement.label.user-requirement-infomation',
                                    })}`}
                                </Title>
                            }
                            bordered={true}
                        >
                            <Row gutter={[16, 4]} className='rq-card-infor'>
                                <Col span={5}>
                                    <TriggerComment field="type">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.type',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    {SCOPE_TYPE_LIST.find((item) => item.id === data?.type)?.name}
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="source-type">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.source',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19} className="source">
                                    <TriggerComment field="source">
                                        {SOURCE_LIST.find((item) => item.id === data?.source)?.name} {data?.source == 1 ? data?.meetingMinutes?.map((e, index) => {
                                            return <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MEETING_DETAIL}${e.id}`} key={index}>{index !== 0 ? `, ${e.name}` : `${e.name}`}</Link>
                                        }) : data?.source == 2 ? data?.referenceDocuments?.map((e, index) => {
                                            return <Link to={`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}${e.id}`} key={index}>{index !== 0 ? `, ${e.name}` : `${e.name}`}</Link>
                                        }) : data?.sourceOther}
                                    </TriggerComment>
                                </Col>
                                {/* //co dk */}

                                <Col span={5}>
                                    <TriggerComment field="scope">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.scope',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    {
                                        SCOPE_MEETING_LIST.find(
                                            (item) => item.id === data?.scope
                                        )?.name
                                    }
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="sender">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.sender',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>{data?.sender}</Col>

                                <Col span={5}>
                                    <TriggerComment field="send-date">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.send-date',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                {/* lavdate */}
                                <Col span={19}>{data?.sendDate ? moment(data?.sendDate).format(DATE_FORMAT) : ''}</Col>

                                {
                                    data?.type === SCOPE_TYPE_LIST[1].id ? <>
                                        <Col span={5}>
                                            <TriggerComment field="reviewer">
                                                <Text type="secondary">
                                                    {intl.formatMessage({ id: 'common.assign-task.reviewer' })}:
                                                </Text>
                                            </TriggerComment>
                                        </Col>
                                        {/* lavdate */}
                                        <Col span={19}>{data?.reviewer}</Col>
                                    </> : <></>
                                }

                                <Col span={5}>
                                    <TriggerComment field="user-requirement-details">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.label.user-requirement-details',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: data?.description }}></div>
                                </Col>

                                {
                                    data?.type === SCOPE_TYPE_LIST[1].id ? <>
                                        <Col span={5}>
                                            <TriggerComment field="change-reason">
                                                <Text type="secondary">
                                                    {intl.formatMessage({
                                                        id: 'user-requirement.label.change-reason',
                                                    })}
                                                </Text>
                                            </TriggerComment>
                                        </Col>
                                        <Col span={19}>
                                            <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: data?.changeReason }}></div>
                                        </Col>

                                    </> : <></>
                                }
                                <Col span={5}>
                                    <TriggerComment field="priority">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.priority',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <Text>{getPriority(data?.priority)}</Text>
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="dueDate">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.dueDate',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <Text>{data?.dueDate ? moment(data?.dueDate).format(DATE_FORMAT) : ''}</Text>
                                </Col>

                                <Col span={5}>
                                    <TriggerComment field="isCovered">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.isCovered',
                                            })}{': '}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={19}>
                                    <Text>{data?.isCovered ? intl.formatMessage({ id: 'common.label.yes' }) : intl.formatMessage({ id: 'common.label.no' })}</Text>
                                </Col>

                                <Col span={5}>
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'user-requirement.column.product',
                                            })}{': '}
                                        </Text>
                                </Col>
                                <Col span={19}>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: data?.product?.name,
                                        }}
                                    ></div>
                                </Col>
                            </Row>

                        </Card>

                        <LavReferences data={data} />
                        {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null' || data?.impacts === `{"unqId":"lav_root","children":[]}` || data?.impacts === "[]") ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT} onChange={() => { }} isViewMode={true} />}
                        {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT} onChange={() => { }} isViewMode={true} /> : <></>} */}

                        <Row justify="space-between">
                            <Col span={8}>
                                <LavEffortEstimation data={data} />
                            </Col>
                            <Col span={15}>
                                <LavRelatedLinks data={data} />
                            </Col>
                        </Row>

                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space >
    ) : <></>
}

export default RightControl
