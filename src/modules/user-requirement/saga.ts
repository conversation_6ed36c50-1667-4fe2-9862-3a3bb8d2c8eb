import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, downloadTemplate, downloadTemplateSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListDocumentFailed, getListDocumentRequest,
  getListDocumentSuccess, getListFailed, getListMeetingFailed, getListMeetingRequest,
  getListMeetingSuccess, getListRequest,
  getListSuccess, importUr, importUrFailure, importUrValidate, importUrValidateFailure, importUrValidateSuccess, updateFailed, updateRequest,
  updateSuccess
} from './action'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.USER_REQUIREMENTS}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);
      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.USER_REQUIREMENTS + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.USER_REQUIREMENTS + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.user-requirement')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.user-requirement')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.USER_REQUIREMENTS, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.user-requirement')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.user-requirement')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.USER_REQUIREMENTS + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.user-requirement')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.user-requirement')
    }
  }
}

function* handleImportUrValidate(action: Action) {
  if (importUrValidate.match(action)) {
    try {
      const fileList = action.payload
      const url = API_URLS.IMPORT_FILE_VALIDATE_UR;
      const res = yield call(apiCall, 'POST', url, fileList)
      yield put(importUrValidateSuccess(res.data));
      ShowAppMessage(null, MESSAGE_TYPES.IMPORT_VALIDATE, 'common.artefact.use-case')
    } catch (err) {
      ShowAppMessage(err, null, 'common.artefact.use-case')
      yield put(importUrValidateFailure(null))
    }
  }
}

function* handleDownload(action: Action) {
  if (downloadTemplate.match(action)) {
    try {
      window.open(API_URLS.IMPORT_USECASE, '_blank');
      yield put(downloadTemplateSuccess(null))
    } catch (err) {
      ShowAppMessage(err, null, 'common.artefact.use-case')
    }
  }
}

function* handleImportUr(action: Action) {
  if (importUr.match(action)) {
    try {
      const qualifiedData = action.payload
      const url = API_URLS.IMPORT_FILE_UR;
      const res = yield call(apiCall, 'POST', url, qualifiedData)
      ShowAppMessage(null, MESSAGE_TYPES.IMPORT, 'common.artefact.use-case')
    } catch (err) {
      importUrFailure(null)
      ShowAppMessage(err, null, 'common.artefact.use-case')
    }
  }
}

function* handleGetListMeeting(action: Action) {
  if (getListMeetingRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_MEETING
      const res = yield call(apiCall, 'GET', url)
      yield put(getListMeetingSuccess(res.data));
    } catch (err) {
      yield put(getListMeetingFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListDocument(action: Action) {
  if (getListDocumentRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_DOCUMENT
      const res = yield call(apiCall, 'GET', url)
      yield put(getListDocumentSuccess(res.data));
    } catch (err) {
      yield put(getListDocumentFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
  yield takeLatest(getListMeetingRequest.type, handleGetListMeeting)
  yield takeLatest(getListDocumentRequest.type, handleGetListDocument)
  yield takeLatest(importUrValidate.type, handleImportUrValidate)
  yield takeLatest(importUr.type, handleImportUr)
  yield takeLatest(downloadTemplate.type, handleDownload)
}
export default function* UserRequirementSaga() {
  yield all([fork(watchFetchRequest)])
}
