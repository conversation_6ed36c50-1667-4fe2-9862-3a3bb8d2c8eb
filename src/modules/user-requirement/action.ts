import { createAction } from '@reduxjs/toolkit';
import {ActionEnum} from './type';

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<any>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<any>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<any>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<any>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const createRequest = createAction<any>(ActionEnum.CREATE_REQUEST);
export const createSuccess = createAction<any>(ActionEnum.CREATE_SUCCESS);
export const createFailed = createAction<any>(ActionEnum.CREATE_FAILED);

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST);
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS);
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED);

export const deleteRequest = createAction<any>(ActionEnum.DELETE_REQUEST);
export const deleteSuccess = createAction<any>(ActionEnum.DELETE_SUCCESS);
export const deleteFailed = createAction<any>(ActionEnum.DELETE_FAILED);

export const getListMeetingRequest = createAction<any>(ActionEnum.GET_LIST_MEETING_REQUEST);
export const getListMeetingSuccess = createAction<any>(ActionEnum.GET_LIST_MEETING_SUCCESS);
export const getListMeetingFailed = createAction<any>(ActionEnum.GET_LIST_MEETING_FAILED);

export const getListDocumentRequest = createAction<any>(ActionEnum.GET_LIST_DOCUMENT_REQUEST);
export const getListDocumentSuccess = createAction<any>(ActionEnum.GET_LIST_DOCUMENT_SUCCESS);
export const getListDocumentFailed = createAction<any>(ActionEnum.GET_LIST_DOCUMENT_FAILED);

export const importUrValidate = createAction<any>(ActionEnum.IMPORT_FILE_VALIDATE)
export const importUrValidateSuccess = createAction<any>(ActionEnum.IMPORT_FILE_VALIDATE_SUCCESS)
export const importUrValidateFailure = createAction<any>(ActionEnum.IMPORT_FILE_VALIDATE_FAILURE)

export const importUr = createAction<any>(ActionEnum.IMPORT_FILE)
export const importUrSuccess = createAction<any>(ActionEnum.IMPORT_FILE_SUCCESS)
export const importUrFailure = createAction<any>(ActionEnum.IMPORT_FILE_FAILURE)

export const downloadTemplate = createAction<any>(ActionEnum.DOWNLOAD_FILE)
export const downloadTemplateSuccess = createAction<any>(ActionEnum.DOWNLOAD_FILE_SUCCESS)
export const downloadTemplateFailure = createAction<any>(ActionEnum.DOWNLOAD_FILE_FAILURE)

export const setModalVisible = createAction<any>(ActionEnum.SET_MODAL_VISIBLE)