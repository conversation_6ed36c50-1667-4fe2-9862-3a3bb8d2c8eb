import { CloseCircleFilled } from "@ant-design/icons";
import { Button, Form, Row, Select, Space } from "antd";
import debounce from 'lodash.debounce';
import { useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import intl from "../../config/locale.config";
import { APP_ROUTES, PROJECT_PREFIX } from "../../constants";
import CustomModal from "../../helper/component/custom-modal";
import FormGroup from "../../helper/component/form-group";
import { extractProjectCode } from "../../helper/share";
import AppCommonService from "../../services/app.service";

const { Option } = Select
interface ValidateScopeButtonProps {
    onDismiss: () => void
}
const ValidateScopeButton = ({ onDismiss }: ValidateScopeButtonProps) => {
    const [members, setMembers] = useState<any[]>([])
    const [form] = Form.useForm()
    const history = useHistory()
    const [currentTitle, setCurrentTitle] = useState<any>(document.title);

    useEffect(() => {
        AppCommonService.getMembers().then((res) => setMembers(res.data))
        form.setFieldsValue({ 'assignee': 'All' })
        
        document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'validate_srs.title.header'}); 
    }, [])

    const onFinish = debounce((values: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.VALIDATION_RESULT}/${values.assignee}/${new Date().getTime()}`;
        onDismiss()
        history.push(href)
    }, 500)

    const onCancel = debounce((values: any) => {
        document.title = currentTitle;
        onDismiss();
    }, 500)

    return (
        <>
            <CustomModal
                className="rq-modal-fill"
                closable={false}
                visible={true}
                footer={null}
                size="small"
                onCancel={onCancel}
            >

                <Form
                    form={form}
                    name="creareworkflow"
                    labelCol={{ offset: 0, span: 2 }}
                    onFinish={onFinish}
                    autoComplete="off"
                    scrollToFirstError={{ block: 'center' }}
                >
                    <div className="rq-modal-header-filled">
                        <Space size="middle">
                            <h4>
                                {intl.formatMessage({ id: 'validate_srs.scope' })}
                            </h4>
                        </Space>
                        <CloseCircleFilled
                            size={24}
                            style={{ cursor: 'pointer' }}
                            onClick={onCancel}
                        />
                    </div>

                    <div
                        className="rq-modal-inner"
                        style={{ paddingTop: 40, paddingBottom: 40 }}
                    >
                        <FormGroup
                            required
                            inline
                            label={intl.formatMessage({
                                id: 'validate_srs.assignee',
                            })}
                        >
                            <Form.Item
                                name="assignee"
                                rules={[
                                    {
                                        required: true,
                                        message: intl.formatMessage({ id: 'IEM_1' }),
                                    },
                                ]}
                            >
                                <Select
                                    filterOption={(input, option: any) =>
                                        option.children
                                            .toLowerCase()
                                            .indexOf(input.toLowerCase()) >= 0
                                    }
                                    showSearch
                                    allowClear
                                    className="full-width"
                                >
                                    <Option value={'All'}>All</Option>
                                    {members.map((member) => (
                                        <Option key={member.userName} value={member.userName}>
                                            {member.fullName}
                                        </Option>
                                    ))}

                                </Select>
                            </Form.Item>
                        </FormGroup>
                    </div>

                    <div className="rq-modal-footer">
                        <Row align="middle" justify="space-between">
                            <Button onClick={onCancel}>
                                {intl.formatMessage({ id: 'validate_srs.button.close' })}
                            </Button>
                            <Button className="success-btn" htmlType="submit">
                                {intl.formatMessage({ id: 'validate_srs.button.validate' })}
                            </Button>

                        </Row>
                    </div>
                </Form>
            </CustomModal>
        </>
    )
}
export default ValidateScopeButton;