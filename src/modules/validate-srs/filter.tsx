import { But<PERSON>, Checkbox, Col, Row, Space } from "antd";
import { useState } from "react";
import { Scrollbars } from 'react-custom-scrollbars';
import intl from "../../config/locale.config";

const FilterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, options, width, minHeight = 0, showAll = true }: any) => {
    const [indeterminateType, setIndeterminateType] = useState(false);
    const [checkAllType, setCheckAllType] = useState(false);
    return (
        <Row style={{ padding: 8, width: width ? width : '', minHeight: minHeight }}>
            <Col span={24}>
                {showAll ? <Checkbox indeterminate={indeterminateType} onChange={(e) => {
                    setSelectedKeys(e.target.checked ? options.map((e) => e.value) : []);
                    setIndeterminateType(false);
                    setCheckAllType(e.target.checked);
                }} checked={checkAllType}>All</Checkbox> : <></>}
                <Scrollbars autoHide autoHeight autoHeightMin={minHeight}>
                    <Checkbox.Group
                        onChange={(e) => {
                            setSelectedKeys(e || [])
                            setIndeterminateType(!!e.length && e.length < options.length);
                            setCheckAllType(e.length === options.length);
                        }}
                        value={selectedKeys}>
                        <Row gutter={[16, 4]}>
                            {
                                options.map((e, idx) => {
                                    return <Col key={idx} span={24}>
                                        <Checkbox id={`chk_${idx}`} value={e.value}>{e.text}</Checkbox>
                                    </Col>
                                })
                            }
                        </Row>
                    </Checkbox.Group>
                </Scrollbars>
            </Col>
            <Col style={{ paddingTop: '16px' }} span={24}>
                <Space size="small">
                    <Button disabled={!selectedKeys || selectedKeys.length <= 0} type="link" style={{ paddingLeft: 0, paddingRight: 0 }} onClick={() => { clearFilters(); setIndeterminateType(false); setCheckAllType(false); }} size="small">{intl.formatMessage({ id: 'common.action.reset' })}</Button>
                    <Button type="primary" onClick={() => confirm()} size="small" >Apply Filter</Button>
                </Space>
            </Col>
        </Row>
    )
}
export default FilterDropdown;