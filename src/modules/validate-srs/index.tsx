import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Checkbox, Col, Collapse, Input, Row, Space, Table, Typography } from "antd";
import { useEffect, useState } from "react";
import { Scrollbars } from 'react-custom-scrollbars';
import { Link } from "react-router-dom";
import intl from "../../config/locale.config";
import { PROJECT_PREFIX, VALIDATE_ARTEFACT_TYPE, VALIDATE_ERROR, VALIDATE_WARNING } from "../../constants";
import { extractProjectCode, getColumnDropdownFilterProps, getProjectName } from "../../helper/share";
import AppCommonService from "../../services/app.service";
import FilterDropdown from './filter';

const { Title, Text } = Typography
const { Panel } = Collapse
const ValidateSRS = (props) => {
    const currentUserProjects = localStorage.getItem('currentUserProjects') || ''
    const currentUserProjectsParse: any = JSON.parse(currentUserProjects)
    const currentProj = currentUserProjectsParse?.projects?.filter((e) => e.projectCode === extractProjectCode())
    const defaultPaging = currentProj[0]?.defaultPaging
    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    const [errorColumns, setErrorColumns] = useState<any>(null)
    const [errorDataSourceCurrent, setErrorDataSourceCurrent] = useState<any>([]);
    const [errorDataSource, setErrorDataSource] = useState<any>([]);
    const [errorPageSize, setErrorPageSize] = useState(defaultPaging ? defaultPaging : 20);

    const [warningColumns, setWarningColumns] = useState<any>(null)
    const [warningDataSourceCurrent, setWarningDataSourceCurrent] = useState<any>([]);
    const [warningDataSource, setWarningDataSource] = useState<any>([]);
    const [warningPageSize, setWarningPageSize] = useState(defaultPaging ? defaultPaging : 20);

    useEffect(() => {
        if (props?.match?.params?.scope) {
            const currentScope = props.match.params.scope;
            AppCommonService.validateSRS((currentScope != 'All' ? '?assignee=' + currentScope : '')).then(res => {
                let dataError: any[] = [];
                let dataWarning: any[] = [];
                res?.forEach((e, idx) => {
                    e.rowId = e.artefactType + '_' + e.artefactId + '_' + idx;
                    e.name = e.name ? e.name : '';
                    e.code = e.code ? e.code : '';
                    if (e.srsValidationType === 1) {
                        dataError.push(e);
                    } else {
                        dataWarning.push(e);
                    }
                });
                dataWarning.sort((a, b) => a.code?.localeCompare(b.code) || a.artefactType - b.artefactType);
                setErrorDataSource(dataError);
                setWarningDataSource(dataWarning);
            }).catch(err => {
                setErrorDataSource([]);
                setWarningDataSource([])
            })
        }
    }, [props?.match?.params?.scope, props?.match?.params?.d])

    useEffect(() => {
        document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'validate_srs.title.header'}); 

        AppCommonService.getMembers().then((e: any) => {
            const members = e?.data?.map(e => { return { text: e.fullName, value: e.userName } });
            const columns1 = configColumns(members, false);
            const columns2 = configColumns(members, true);
            setWarningColumns(columns1);
            setErrorColumns(columns2);
        }).catch(err => {
            setErrorColumns([])
            setWarningColumns([])
        })
    }, [])

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    placeholder={`Search ${dataIndex}`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => confirm()}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => confirm()}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : ''
    });

    const getFilterDropdown = (setSelectedKeys, selectedKeys, confirm, clearFilters, options, width) => {
        return (
            <Row style={{ padding: 8, width: width ? width : '' }}>
                <Col span={24}>
                    <Scrollbars autoHide autoHeight autoHeightMin={0}>
                        <Checkbox.Group
                            onChange={(e) => { setSelectedKeys(e || []) }}
                            value={selectedKeys}>
                            <Row gutter={[16, 4]}>
                                {
                                    options.map((e, idx) => {
                                        return <Col key={idx} span={24}>
                                            <Checkbox id={`chk_${idx}`} value={e.value}>{e.text}</Checkbox>
                                        </Col>
                                    })
                                }
                            </Row>
                        </Checkbox.Group>
                    </Scrollbars>
                </Col>
                <Col style={{ paddingTop: '16px' }} span={24}>
                    <Space size="small">
                        <Button disabled={!selectedKeys || selectedKeys.length <= 0} type="link" style={{ paddingLeft: 0, paddingRight: 0 }} onClick={() => clearFilters()} size="small">{intl.formatMessage({ id: 'common.action.reset' })}</Button>
                        <Button type="primary" onClick={() => confirm()} size="small" >Apply Filter</Button>
                    </Space>
                </Col>
            </Row>
        )
    }

    const configColumns = (assignee, isError) => [
        {
            title: intl.formatMessage({ id: 'validate_srs.column.code' }),
            dataIndex: 'code',
            key: 'code',
            width: '85px',
            ...getColumnSearchProps('code'),
            defaultSortOrder: 'ascend',
            sorter: (a, b) => (a.code || '').localeCompare((b.code || '')),
            render: (text: string, record: any) => {
                const artefactType = VALIDATE_ARTEFACT_TYPE.find((e) => e.value === record.artefactType)?.url
                if (artefactType) {
                    return <Link onClick={props.onDismiss} to={`${PROJECT_PREFIX}${projectCode}${artefactType}${record.artefactId}`}>{text}</Link>
                }
                return text;

            },
        },
        {
            title: intl.formatMessage({ id: 'validate_srs.column.name' }),
            dataIndex: 'name',
            key: 'name',
            ...getColumnSearchProps('name'),
            sorter: (a, b) => a.name?.localeCompare(b.name),
            width: '20%',
        },
        {
            title: intl.formatMessage({ id: 'validate_srs.column.artefact_type' }),
            dataIndex: 'artefactType',
            key: 'artefactType',
            width: '20%',
            defaultSortOrder: 'ascend',
            sorter: (a, b) => a.artefactType - b.artefactType,
            filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => <FilterDropdown options={VALIDATE_ARTEFACT_TYPE} setSelectedKeys={setSelectedKeys} selectedKeys={selectedKeys} confirm={confirm} clearFilters={clearFilters} width='250px' />,
            onFilter: (value, record) => record.artefactType === value,
            render: type => VALIDATE_ARTEFACT_TYPE.find((e) => e.value === type)?.text
        },
        {
            title: intl.formatMessage({ id: 'validate_srs.assignee' }),
            dataIndex: 'assignee',
            key: 'assignee',
            width: '15%',
            ...getColumnDropdownFilterProps(assignee, 'assignee', true, false, 250),
            onFilter: (value, record) => record.assignee === value,
        },
        {
            title: intl.formatMessage({ id: isError ? 'validate_srs.scope.error_message' : 'validate_srs.scope.warning_message' }),
            dataIndex: 'message',
            key: 'message',
            sorter: (a, b) => a.message?.localeCompare(b.message),
            width: '30%',
            filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => getFilterDropdown(
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
                (isError ? VALIDATE_ERROR : VALIDATE_WARNING).map((item) => { return { text: item.text, value: item.text } }),
                '250px'
            ),
            onFilter: (value, record) => record.message.toLowerCase() === value.toLowerCase(),
        },
        {
            title: intl.formatMessage({ id: 'validate_srs.scope.solution' }),
            dataIndex: 'solution',
            key: 'solution',
            width: '30%',
        }
    ]

    const handleErrorTableChange = (pagination, filters, sorter, extra) => {
        setErrorDataSourceCurrent(extra.currentDataSource)
        setErrorPageSize(pagination.pageSize);
    }

    const handleWarningTableChange = (pagination, filters, sorter, extra) => {
        setWarningDataSourceCurrent(extra.currentDataSource)
        setWarningPageSize(pagination.pageSize);
    }

    return (
        <>
            <Space
                direction="vertical"
                size={'small'}
                className="my-assigned-task full-width p-20px"
            >
                <div className="rq-page-heading" style={{ marginBottom: 10 }}>
                    <Row align="middle" justify="space-between">
                        <div>
                            <Breadcrumb className="rq-breadcrumb" separator=">">
                                <Breadcrumb.Item>
                                    <Link
                                        className="breadcrumb-link-btn"
                                        to={`${PROJECT_PREFIX}${projectCode}/dashboard`}
                                    >
                                        {projectCode}  - {projectName}
                                    </Link>
                                </Breadcrumb.Item>
                            </Breadcrumb>
                            <div className="title-page-heading">
                                <Space size="large">
                                    <Title level={4}>
                                        {intl.formatMessage({ id: 'validate_srs.title.header' })}
                                    </Title>
                                </Space>
                            </div>
                        </div>
                    </Row>
                </div>
                <Collapse className="rq-warning-srs" defaultActiveKey={['1']}>
                    <Panel
                        className="description"
                        header={<Title level={5}>{intl.formatMessage({ id: 'validate_srs.scope.error' })}</Title>}
                        key="1"
                    >
                        {errorColumns && <Table
                            locale={{
                                emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                                filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                            }}
                            className="lav-table"
                            bordered
                            dataSource={errorDataSource}
                            columns={errorColumns}
                            rowKey='rowId'
                            onChange={handleErrorTableChange}
                            // errorDataSource?.length <= errorPageSize ? false : 
                            pagination={{
                                pageSize: errorPageSize,
                                total: errorDataSourceCurrent?.length,
                                size: 'small',
                                showSizeChanger: true,
                                showLessItems: true,
                                position: ['topRight'],
                                showTotal: (total, range) =>
                                    `${range[0]}-${range[1]} ${intl.formatMessage({
                                        id: 'common.table.pagination.of',
                                    })} ${total} ${intl.formatMessage({
                                        id: 'common.table.pagination.items',
                                    })}`
                            }}
                            scroll={{ y: 450, x: 650 }}
                            loading={false}
                        />}
                    </Panel>
                </Collapse>
            </Space>
            <Space
                direction="vertical"
                size={'small'}
                className="my-assigned-task full-width p-20px"
            >
                <Collapse className="rq-warning-srs">
                    <Panel
                        className="description"
                        header={<Title level={5}>{intl.formatMessage({ id: 'validate_srs.scope.warning' })}</Title>}
                        key="2"
                    >
                        <Table
                            locale={{
                                emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                                filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                            }}
                            className="lav-table"
                            bordered
                            dataSource={warningDataSource}
                            columns={warningColumns}
                            rowKey='rowId'
                            onChange={handleWarningTableChange}
                            // warningDataSource?.length <= warningPageSize ? false : 
                            pagination={{
                                pageSize: warningPageSize,
                                total: warningDataSourceCurrent?.length,
                                size: 'small',
                                showSizeChanger: true,
                                showLessItems: true,
                                position: ['topRight'],
                                showTotal: (total, range) =>
                                    `${range[0]}-${range[1]} ${intl.formatMessage({
                                        id: 'common.table.pagination.of',
                                    })} ${total} ${intl.formatMessage({
                                        id: 'common.table.pagination.items',
                                    })}`
                            }}
                            scroll={{ y: 450, x: 650 }}
                            loading={false}
                        />
                    </Panel>
                </Collapse>
            </Space>
        </>
    )
}
export default ValidateSRS;
