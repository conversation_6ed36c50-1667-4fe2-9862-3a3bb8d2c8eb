import AppState from '@/store/types'
import { CheckOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Col, Input, Row, Space, Table, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../config/locale.config'
import { API_URLS, BUTTON_TYPE, DEFAULT_PAGE_SIZE, SEARCH_TYPE } from '../../constants'
import DeleteButton from '../../helper/component/commonButton/DeleteButton'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavPageHeader from '../../helper/component/lav-breadcumb'
import { extractProjectCode, getColumnSearchProps, ShowMessgeAdditionalSubmit } from '../../helper/share'
import TableService from '../../services/lav-table-service'
import { createRequest, deleteRequest, updateRequest } from './action'
import { defaultGlossary, GlossaryState } from './type'
import useBeforeUnload from '../../helper/hooks/useBeforeUnload'

const Glossary = () => {
    const [dataSource, setDataSource] = useState<any>([])
    const [currentPageIndex, setCurrentPageIndex] = useState(1)
    const currentUserProjects = localStorage.getItem('currentUserProjects') || ''
    const currentUserProjectsParse: any = JSON.parse(currentUserProjects)
    const [currentFilters, setCurrentFilters] = useState<any>({})
    const [currentSorter, setCurrentSorter] = useState<any>([])
    const currentProj = currentUserProjectsParse?.projects?.filter((e) => e.projectCode === extractProjectCode())
    const [isLoading, setIsLoading] = useState(false)
    let defaultPaging: any
    if (currentProj) {
        defaultPaging = currentProj[0]?.defaultPaging
    }
    const [currentPageSize, setCurrentPageSize] = useState(defaultPaging ? defaultPaging : DEFAULT_PAGE_SIZE)
    const [totalItems, setTotalItems] = useState(0)
    const state = useSelector<AppState | null>((s) => s?.Glossary) as GlossaryState
    const dispatch = useDispatch()
    const columns: any = [
        {
            title: intl.formatMessage({ id: 'glossary.column.code' }),
            dataIndex: 'code',
            width: '5%',
            align: 'center',
            sorter: true,
            defaultSortOrder: 'descend',
            ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
        },
        {
            title: intl.formatMessage({ id: 'glossary.column.term' }),
            dataIndex: 'name',
            width: '30%',
            ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
            render: (text, record: any, order) => {
                return record?.editting ? <>
                    <Input value={text} width={'100%'} onChange={handleChangeGlossaryText(order, 'name')} maxLength={255} />
                    {
                        !record?.hasNameValue ? <Typography.Text type='danger'>{intl.formatMessage({ id: 'IEM_1' })}</Typography.Text> : <></>
                    }
                </>
                    : text
            }
        },
        {
            title: intl.formatMessage({ id: 'glossary.column.definition' }),
            dataIndex: 'description',
            className: 'white-pre',
            width: '60%',
            ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
            render: (text, record: any, order) => {
                return record?.editting ?
                    <>
                        <Input.TextArea value={text} maxLength={500} onChange={handleChangeGlossaryText(order, 'description')}></Input.TextArea>
                        <div>
                            {
                                !record?.hasDesValue ? <Typography.Text type='danger'>{intl.formatMessage({ id: 'IEM_1' })}</Typography.Text> : <></>
                            }
                        </div>
                    </>
                    : <div  dangerouslySetInnerHTML={{ __html: text }} style={{whiteSpace: 'normal'}} ></div>
            }
        },
        {
            title: intl.formatMessage({ id: 'common.table.column.action' }),
            dataIndex: 'action',
            // className: 'rq-action',
            width: '5%',
            align: 'center',
            render: (text, record: any, index: number) => {
                return (
                    <Space size='small'>
                        {
                            record?.editting ? <Button icon={<CheckOutlined name="EditCustomIcon" />} type="link" onClick={() => saveRecord(record, index)}></Button>
                                : <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={() => setEditting(index, record)}></Button>
                        }
                        {
                            !record?.id ? <Button
                                type="text"
                                icon={<CustomSvgIcons name="DeleteCustomIcon" />}
                                onClick={() => deleteRow(record, index)}
                            /> : <></>
                        }
                        {
                            record?.id ?
                                <DeleteButton
                                    type={BUTTON_TYPE.ICON}
                                    content={intl.formatMessage(
                                        { id: 'CFD_7' },
                                        { artefact_type: intl.formatMessage({ id: 'common.artefact.glossary' }) }
                                    )}
                                    okCB={() => deleteRow(record, index)}
                                    confirmButton={intl.formatMessage({ id: 'common.action.ok' })}
                                /> : <></>
                        }
                    </Space>
                )
            },
        },
    ]
    useEffect(() => {
        if (state.createSuccess || state.updateSuccess || state.deleteSuccess) {
            loadData(currentPageIndex, currentPageSize, currentFilters, currentSorter, null)
        }
    }, [state.createSuccess, state.updateSuccess, state.deleteSuccess])
    useEffect(() => {        
        document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'glossary.header.title'}); 

        let defaultSort: any = []
        let defaultFilters: any = null
        columns.forEach((element, index) => {
            // Default sorter
            if (element.defaultSortOrder) {
                defaultSort.push({ field: element.dataIndex, order: element.defaultSortOrder })
                setCurrentSorter(defaultSort)
            }
            // Default filter
            if (element.defaultFilteredValue && element.defaultFilteredValue.length > 0) {
                let obj: any = {};
                obj[index] = element.defaultFilteredValue
                defaultFilters = obj;
                setCurrentFilters(obj);
            }
        })
        loadData(currentPageIndex, currentPageSize, defaultFilters, defaultSort, null)
    }, [])

    useBeforeUnload()

    const handleChangeGlossaryText = (order, prop) => ({ target }) => {
        updateRecordGlossaryProperty(order, { [prop]: target?.value });
    };

    const deleteRow = (record, index) => {
        if (record?.id) {
            dispatch(deleteRequest(record?.id))
        } else {
            let currentSource: any = Object.assign([], dataSource)
            currentSource.splice(index, 1)
            setDataSource(currentSource)
        }
    }
    const updateRecordGlossaryProperty = (order, partialRecord) => {
        let keyNames = Object.keys(partialRecord)

        const list = dataSource.map((record, index) => {
            let nxtRecord = record;

            if (index === order) {
                nxtRecord = {
                    ...nxtRecord,
                    ...partialRecord,
                    hasNameValue: keyNames[0] === 'name' ? (partialRecord?.name.replace(/\s+/g, ' ').trim() === "" ? false : true) : nxtRecord?.hasNameValue,
                    hasDesValue: keyNames[0] === 'description' ? (partialRecord?.description.replace(/\s+/g, ' ').trim() === "" ? false : true) : nxtRecord?.hasDesValue,
                };
            }
            return nxtRecord;
        })
        setDataSource(list)
    };


    const saveRecord = (record, order) => {
        if (record?.name.replace(/\s+/g, ' ').trim() === "" || record?.description.replace(/\s+/g, ' ').trim() === "") {
            const list = dataSource.map((e, index) => {
                if (index === order) {
                    return {
                        ...e,
                        hasNameValue: record?.name.replace(/\s+/g, ' ').trim() === "" ? false : true,
                        hasDesValue: record?.description.replace(/\s+/g, ' ').trim() === "" ? false : true,
                    }
                } else {
                    return e;
                }
            })
            setDataSource(list)
            return;
        }

        const requestData = {
            "id": record?.id,
            "name": record.name,
            "description": record.description,
            "mentionReferences": ""
        }
        if (record?.id) {
            dispatch(updateRequest(requestData))
        } else {
            dispatch(createRequest(requestData))
        }
        // setEditting(order)
    }


    const setEditting = (order, record) => {
        const listFilter = dataSource.filter((e) => e.editting == true)

        if (listFilter.length === 0) {
            const list = dataSource.map((e, index) => {
                if (index === order) {
                    return {
                        ...e,
                        editting: !e.editting,
                    }
                } else {
                    return e
                }
            })
            setDataSource(list)
        }

        if (record?.id !== listFilter[0]?.id && listFilter.length !== 0) {
            ShowMessgeAdditionalSubmit('EMSG_37')
        }

        if (record?.id === listFilter[0]?.id && record?.id !== null) {
            const list = dataSource.map((e, index) => {
                if (index === order) {
                    return {
                        ...e,
                        editting: !e.editting,
                    }
                } else {
                    return e
                }
            })
            setDataSource(list)
        } else {
            //Do nothing
            // ShowMessgeAdditionalSubmit('EMSG_37')
        }
    }

    const loadData = (pageIndex, pageSize, filters, sorter, extra) => {
        let paramFilter = ''
        let paramSorter = ''
        if (sorter && sorter.length) {
            sorter.forEach(element => {
                if (element?.field) {
                    let sortDir =
                        element.order == 'ascend'
                            ? 'asc'
                            : element.order == 'descend'
                                ? 'desc'
                                : ''
                    paramSorter += `&SortField=${element.field}&SortDir=${sortDir}`
                }
            });
        }
        let keys = filters ? Object.keys(filters) : null
        if (keys && keys.length > 0) {
            for (var i = 0; i < keys.length; i++) {
                // Filter Status
                if (filters[keys[i]]) {
                    paramFilter += `&${keys[i]}=${filters[keys[i]]?.[0]}`
                }
            }
        }
        setIsLoading(true)
        TableService
            .getData(API_URLS.GLOSSARY, pageIndex, pageSize, paramFilter, paramSorter)
            .then((res) => {
                let response = res.data.data ? res.data.data : res.data
                setDataSource(response.map((e, index) => {
                    return {
                        ...e,
                        rowId: index,
                        editting: false,
                        hasNameValue: true,
                        hasDesValue: true
                    }
                }))

                setTotalItems(res.data.total)
                setCurrentPageIndex(pageIndex)
                setCurrentPageSize(pageSize)
                setCurrentFilters(filters)
                setCurrentSorter(sorter)
                setIsLoading(false)

            })
            .catch((e) => {
                setDataSource([])
                setTotalItems(0)
                setCurrentPageIndex(1)
                setCurrentPageSize(DEFAULT_PAGE_SIZE)
                setCurrentFilters(null)
                setCurrentSorter(null)
                setIsLoading(false)
            })
    }
    const handleTableChange = (pagination, filters, sorter, extra) => {
        loadData(pagination.current, pagination.pageSize, filters, [sorter], extra)
    }

    const handleCreate = (e) => {
        const listFilter = dataSource.filter((e, index) => e.editting == true)

        if (listFilter.length === 0) {
            if (currentPageSize > totalItems) {
                setDataSource([defaultGlossary, ...dataSource]);
            } else {
                let newDataSource = dataSource?.pop()
                setDataSource([defaultGlossary, ...newDataSource]);
            }
        } else {
            ShowMessgeAdditionalSubmit('EMSG_37')
        }
    }

    return (
        <div className="full-width p-20px">
            <LavPageHeader
                showBreadcumb={true}
                title={intl.formatMessage({ id: 'glossary.header.title' })}
            >
                <Space size="small">
                    <Button ghost={true}
                        type='primary'
                        className='lav-btn-create'
                        icon={<PlusOutlined />}
                        onClick={handleCreate}
                    >{intl.formatMessage({ id: 'glossary.button.create' })}
                    </Button>
                </Space>
            </LavPageHeader>

            <Row gutter={[10, 0]}>
                <Col span={24}>
                    <Table
                        locale={{
                            emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                            filterReset: 'Reset',
                            filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                            filterConfirm: 'Apply Filter',
                            selectAll: 'All',
                            selectionAll: 'Select All',
                        }}
                        className="lav-table"
                        bordered
                        columns={columns}
                        dataSource={dataSource}
                        rowKey="id"
                        onChange={handleTableChange}
                        pagination={{
                            current: currentPageIndex,
                            pageSize: currentPageSize,
                            total: totalItems,
                            size: 'small',
                            showLessItems: true,
                            showSizeChanger: true,
                            position: ['topRight'],
                            showTotal: (total, range) =>
                                `${range[0]}-${range[1]} ${intl.formatMessage({
                                    id: 'common.table.pagination.of',
                                })} ${total} ${intl.formatMessage({
                                    id: 'common.table.pagination.items',
                                })}`,
                        }}
                        loading={state?.isLoading || isLoading}
                        // scroll={{ x: 650 }}
                    />
                </Col>
            </Row>
        </div>
    )
}

export default Glossary