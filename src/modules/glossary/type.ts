export interface GlossaryState {
    isLoading: boolean,
    createSuccess?: boolean,
    updateSuccess?: boolean,
    deleteSuccess?: boolean,
    listData?: any,
    isLoadingList?: boolean,
    detail?: GlossaryDetail | null,
    selectedData?: GlossaryDetail | null,
}

export const defaultGlossary = {
    editting: true,
    code: null,
    id: null,
    name: '',
    description: '',
    hasNameValue: true,
    hasDesValue: true
}

interface GlossaryDetail {
    id?: number | null,
    code: string,
    name: string
    description: string
}

export const defaultState: GlossaryState = {
    isLoading: false,
    createSuccess: false,
    updateSuccess: false,
    deleteSuccess: false,
    listData: [],
    isLoadingList: false,
    detail: {
        id: null,
        code: '',
        name: '',
        description: ''
    },
    selectedData: {
        id: null,
        code: '',
        name: '',
        description: ''
    },
}

export enum ActionEnum {
    RESET_STATE = '@@MODULES/GLOSSARY/RESET_STATE',

    CREATE_REQUEST = '@@MODULES/GLOSSARY/CREATE_REQUEST',
    CREATE_SUCCESS = '@@MODULES/GLOSSARY/CREATE_SUCCESS',
    CREATE_FAILED = '@@MODULES/GLOSSARY/CREATE_FAILED',

    UPDATE_REQUEST = '@@MODULES/GLOSSARY/UPDATE_REQUEST',
    UPDATE_SUCCESS = '@@MODULES/GLOSSARY/UPDATE_SUCCESS',
    UPDATE_FAILED = '@@MODULES/GLOSSARY/UPDATE_FAILED',

    GET_LIST_REQUEST = '@@MODULES/GLOSSARY/GET_LIST_REQUEST',
    GET_LIST_SUCCESS = '@@MODULES/GLOSSARY/GET_LIST_SUCCESS',
    GET_LIST_FAILED = '@@MODULES/GLOSSARY/GET_LIST_FAILED',

    GET_LIST_OBJECTS_REQUEST = '@@MODULES/GLOSSARY/GET_LIST_OBJECTS_REQUEST',
    GET_LIST_OBJECTS_SUCCESS = '@@MODULES/GLOSSARY/GET_LIST_OBJECTS_SUCCESS',
    GET_LIST_OBJECTS_FAILED = '@@MODULES/GLOSSARY/GET_LIST_OBJECTS_FAILED',

    DELETE_REQUEST = '@@MODULES/GLOSSARY/DELETE_REQUEST',
    DELETE_SUCCESS = '@@MODULES/GLOSSARY/DELETE_SUCCESS',
    DELETE_FAILED = '@@MODULES/GLOSSARY/DELETE_FAILED',
}