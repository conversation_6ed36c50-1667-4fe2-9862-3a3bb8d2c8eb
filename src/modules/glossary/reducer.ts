import { createReducer } from '@reduxjs/toolkit'
import { createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, getListFailed, getListRequest, getListSuccess, resetState, updateFailed, updateRequest, updateSuccess } from './action';
import { defaultState } from './type';


const initState = defaultState

const reducer = createReducer(initState, (builder) => {
    return (
        builder
            .addCase(resetState, (state, action?) => {
                Object.assign(state, {
                    ...defaultState,
                    selectedData: state.selectedData,
                    listData: state.listData
                });
            })

            .addCase(getListRequest, (state, action?) => {
                state.isLoadingList = true;
            })
            .addCase(getListSuccess, (state, action) => {
                state.isLoadingList = false
                state.listData = action.payload
            })
            .addCase(getListFailed, (state, action) => {
                state.isLoadingList = false
                state.listData = null
            })

            .addCase(createRequest, (state, action?) => {
                state.isLoading = true;
                state.createSuccess = false;
            })
            .addCase(createSuccess, (state, action) => {
                state.isLoading = false;
                state.createSuccess = true;
            })
            .addCase(createFailed, (state, action) => {
                state.isLoading = false;
                state.createSuccess = false;
            })


            .addCase(updateRequest, (state, action?) => {
                state.isLoading = true;
                state.updateSuccess = false;
            })
            .addCase(updateSuccess, (state, action) => {
                state.isLoading = false;
                state.updateSuccess = true;
            })
            .addCase(updateFailed, (state, action) => {
                state.isLoading = false;
                state.updateSuccess = false;
            })


            .addCase(deleteRequest, (state, action?) => {
                state.deleteSuccess = false;
            })
            .addCase(deleteSuccess, (state, action) => {
                state.deleteSuccess = true;
            })
            .addCase(deleteFailed, (state, action) => {
                state.deleteSuccess = false;
            })
    )
})



export default reducer
export { initState as GlossaryState }