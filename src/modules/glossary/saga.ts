import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import { createFailed, createRequest, createSuccess, deleteFailed, deleteRequest, deleteSuccess, updateFailed, updateRequest, updateSuccess } from './action'

function* handleDelete(action: Action) {
    if (deleteRequest.match(action)) {
        try {
            const url = API_URLS.GLOSSARY + '/' + action.payload
            const res = yield call(apiCall, 'DELETE', url)
            ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.glossary')
            yield put(deleteSuccess(null));
        } catch (err) {
            yield put(deleteFailed(null));
            ShowAppMessage(err, null, 'common.artefact.glossary')
        }
    }
}

function* handleCreate(action: Action) {
    if (createRequest.match(action)) {
        try {
            const request = action.payload
            const res = yield call(apiCall, 'POST', API_URLS.GLOSSARY, request as any)
            yield put(createSuccess(null));
            //checkdone
            ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.glossary')
        } catch (err) {
            yield put(createFailed(null));
            ShowAppMessage(err, null, 'common.artefact.glossary')
        }
    }
}

function* handleUpdate(action: Action) {
    if (updateRequest.match(action)) {
        try {
            const request = action.payload
            const url = API_URLS.GLOSSARY + '/' + request.id
            const res = yield call(apiCall, 'PUT', url, request as any)
            //checkdone
            ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.glossary')
            yield put(updateSuccess(null));
        } catch (err) {
            yield put(updateFailed(null));
            ShowAppMessage(err, null, 'common.artefact.glossary')
        }
    }
}


function* watchFetchRequest() {
    yield takeLatest(createRequest.type, handleCreate)
    yield takeLatest(updateRequest.type, handleUpdate)
    yield takeLatest(deleteRequest.type, handleDelete)
}
export default function* GlossarySaga() {
    yield all([fork(watchFetchRequest)])
}



