import { createReducer } from '@reduxjs/toolkit';
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListActorsFailed, getListActorsRequest,
  getListActorsSuccess, getListEmailTemplatesFailed, getListEmailTemplatesRequest,
  getListEmailTemplatesSuccess, getListFailed, getListFunctionIds, getListFunctionIdsFailed, getListFunctionIdsSuccess, getListFunctionsFailed, getListFunctionsRequest,
  getListFunctionsSuccess, getListMessagesFailed, getListMessagesRequest,
  getListMessagesSuccess, getListObjectFilter, getListObjectFilterFailure, getListObjectFilterSuccess, getListObjectPropertiesFailed, getListObjectPropertiesRequest,
  getListObjectPropertiesSuccess, getListObjectsFailed, getListObjectsRequest,
  getListObjectsSuccess, getListOtherRequirementsFailed, getListOtherRequirementsRequest,
  getListOtherRequirementsSuccess, getListRequest,
  getListSelectProperties,
  getListSelectPropertiesFailure,
  getListSelectPropertiesSuccess,
  getListSuccess,  getListUserRequirementsFailed, getListUserRequirementsRequest,
  getListUserRequirementsSuccess, resetState, setModalVisible, updateFailed, updateRequest,
  updateSuccess, viewDetailFailed, viewDetailRequest,
  viewDetailSuccess
} from './action';
import { defaultState, MockupScreenState } from './type';

const initState: MockupScreenState = defaultState

const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, {
          ...defaultState,
          selectedData: state.selectedData,
          listData: state.listData
        });
      })

      .addCase(getListRequest, (state, action?) => {
        state.isLoadingList = true;
      })
      .addCase(getListSuccess, (state, action) => {
        state.isLoadingList = false
        state.listData = action.payload
      })
      .addCase(getListFailed, (state, action) => {
        state.isLoadingList = false
        state.listData = null
      })

      .addCase(getDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(getDetailSuccess, (state, action) => {
        state.isLoading = false
        state.detail = action.payload
        state.selectedData = action.payload
      })
      .addCase(getDetailFailed, (state, action) => {
        state.isLoading = false
        state.detail = null
        state.selectedData = null
      })

      .addCase(viewDetailRequest, (state, action?) => {
        state.isLoading = true;
      })
      .addCase(viewDetailSuccess, (state, action) => {
        state.isLoading = false
        state.selectedData = action.payload
      })
      .addCase(viewDetailFailed, (state, action) => {
        state.isLoading = false
        state.selectedData = null
      })


      .addCase(createRequest, (state, action?) => {
        state.isLoading = true;
        state.createSuccess = false;
      })
      .addCase(createSuccess, (state, action) => {
        state.isLoading = false;
        state.createSuccess = true;
      })
      .addCase(createFailed, (state, action) => {
        state.isLoading = false;
        state.createSuccess = false;
      })


      .addCase(updateRequest, (state, action?) => {
        state.isLoading = true;
        state.updateSuccess = false;
      })
      .addCase(updateSuccess, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = true;
      })
      .addCase(updateFailed, (state, action) => {
        state.isLoading = false;
        state.updateSuccess = false;
      })


      .addCase(deleteRequest, (state, action?) => {
        state.deleteSuccess = false;
      })
      .addCase(deleteSuccess, (state, action) => {
        state.deleteSuccess = true;
      })
      .addCase(deleteFailed, (state, action) => {
        state.deleteSuccess = false;
      })

      .addCase(getListObjectsRequest, (state, action?) => {
        state.isLoadingObjects = true;
      })
      .addCase(getListObjectsSuccess, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = action.payload
      })
      .addCase(getListObjectsFailed, (state, action) => {
        state.isLoadingObjects = false
        state.listObjects = []
      })

      .addCase(getListFunctionsRequest, (state, action?) => {
        state.isLoadingFunctions = true;
      })
      .addCase(getListFunctionsSuccess, (state, action) => {
        state.isLoadingFunctions = false
        state.listFunctions = action.payload
      })
      .addCase(getListFunctionsFailed, (state, action) => {
        state.isLoadingFunctions = false
        state.listFunctions = []
      })

      .addCase(getListActorsRequest, (state, action?) => {
        state.isLoadingActors = true;
      })
      .addCase(getListActorsSuccess, (state, action) => {
        state.isLoadingActors = false
        state.listActors = action.payload
      })
      .addCase(getListActorsFailed, (state, action) => {
        state.isLoadingActors = false
        state.listActors = []
      })

      .addCase(getListMessagesRequest, (state, action?) => {
        state.isLoadingMessages = true;
      })
      .addCase(getListMessagesSuccess, (state, action) => {
        state.isLoadingMessages = false
        state.listMessages = action.payload
      })
      .addCase(getListMessagesFailed, (state, action) => {
        state.isLoadingMessages = false
        state.listMessages = []
      })

      .addCase(getListEmailTemplatesRequest, (state, action?) => {
        state.isLoadingEmailTemplates = true;
      })
      .addCase(getListEmailTemplatesSuccess, (state, action) => {
        state.isLoadingEmailTemplates = false
        state.listEmailTemplates = action.payload
      })
      .addCase(getListEmailTemplatesFailed, (state, action) => {
        state.isLoadingEmailTemplates = false
        state.listEmailTemplates = []
      })

      .addCase(getListUserRequirementsRequest, (state, action?) => {
        state.isLoadingUserRequirements = true;
      })
      .addCase(getListUserRequirementsSuccess, (state, action) => {
        state.isLoadingUserRequirements = false
        state.listUserRequirements = action.payload
      })
      .addCase(getListUserRequirementsFailed, (state, action) => {
        state.isLoadingUserRequirements = false
        state.listUserRequirements = []
      })

      .addCase(getListOtherRequirementsRequest, (state, action?) => {
        state.isLoadingOtherRequirements = true;
      })
      .addCase(getListOtherRequirementsSuccess, (state, action) => {
        state.isLoadingOtherRequirements = false
        state.listOtherRequirements = action.payload
      })
      .addCase(getListOtherRequirementsFailed, (state, action) => {
        state.isLoadingOtherRequirements = false
        state.listOtherRequirements = []
      })

      .addCase(getListObjectPropertiesRequest, (state, action?) => {
        state.isLoadingListObjectProperties = true;
      })
      .addCase(getListObjectPropertiesSuccess, (state, action) => {
        state.isLoadingListObjectProperties = false
        state.listObjectProperties = action.payload
      })
      .addCase(getListObjectPropertiesFailed, (state, action) => {
        state.isLoadingListObjectProperties = false
        state.listObjectProperties = []
      })

      .addCase(getListFunctionIds, (state, action?) => {
        state.isLoadingFunctionsId = true;
      })
      .addCase(getListFunctionIdsSuccess, (state, action) => {
        state.isLoadingFunctionsId = false
        state.listFunctionsId = action.payload
      })
      .addCase(getListFunctionIdsFailed, (state, action) => {
        state.isLoadingFunctionsId = false
        state.listFunctionsId = []
      })

      .addCase(getListObjectFilter, (state, action) => {
        state.isLoadingObjectsFilter = true
      })
      .addCase(getListObjectFilterSuccess, (state, action) => {
        state.isLoadingObjectsFilter = false
        state.listObjectsFilter = action.payload
      })
      .addCase(getListObjectFilterFailure, (state, action) => {
        state.isLoadingObjectsFilter = false
        state.listObjectsFilter = []
      })

      .addCase(getListSelectProperties, (state, action) => {
        state.isLoading = true
      })
      .addCase(getListSelectPropertiesSuccess, (state, action) => {
        state.isLoading = false
        state.listSelectObjectProperties = action.payload
      })
      .addCase(getListSelectPropertiesFailure, (state, action) => {
        state.isLoading = false
        state.listSelectObjectProperties = []
      })
      .addCase(setModalVisible, (state, action) => {
        state.isModalShow = action.payload
        if(!action.payload){
          state.createSuccess = false;
          state.updateSuccess = false;
        }
      })
  )
})

export default reducer
export { initState as MockupScreenState };

