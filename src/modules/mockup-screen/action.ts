import { createAction } from '@reduxjs/toolkit';
import { ActionEnum } from './type';

export const resetState = createAction<any>(ActionEnum.RESET_STATE);

export const getListRequest = createAction<any>(ActionEnum.GET_LIST_REQUEST);
export const getListSuccess = createAction<any>(ActionEnum.GET_LIST_SUCCESS);
export const getListFailed = createAction<any>(ActionEnum.GET_LIST_FAILED);

export const getDetailRequest = createAction<any>(ActionEnum.GET_DETAIL_REQUEST);
export const getDetailSuccess = createAction<any>(ActionEnum.GET_DETAIL_SUCCESS);
export const getDetailFailed = createAction<any>(ActionEnum.GET_DETAIL_FAILED);

export const viewDetailRequest = createAction<any>(ActionEnum.VIEW_DETAIL_REQUEST);
export const viewDetailSuccess = createAction<any>(ActionEnum.VIEW_DETAIL_SUCCESS);
export const viewDetailFailed = createAction<any>(ActionEnum.VIEW_DETAIL_FAILED);

export const createRequest = createAction<any>(ActionEnum.CREATE_REQUEST);
export const createSuccess = createAction<any>(ActionEnum.CREATE_SUCCESS);
export const createFailed = createAction<any>(ActionEnum.CREATE_FAILED);

export const updateRequest = createAction<any>(ActionEnum.UPDATE_REQUEST);
export const updateSuccess = createAction<any>(ActionEnum.UPDATE_SUCCESS);
export const updateFailed = createAction<any>(ActionEnum.UPDATE_FAILED);

export const deleteRequest = createAction<any>(ActionEnum.DELETE_REQUEST);
export const deleteSuccess = createAction<any>(ActionEnum.DELETE_SUCCESS);
export const deleteFailed = createAction<any>(ActionEnum.DELETE_FAILED);

export const getListObjectsRequest = createAction<any>(ActionEnum.GET_LIST_OBJECTS_REQUEST);
export const getListObjectsSuccess = createAction<any>(ActionEnum.GET_LIST_OBJECTS_SUCCESS);
export const getListObjectsFailed = createAction<any>(ActionEnum.GET_LIST_OBJECTS_FAILED);

export const getListFunctionsRequest = createAction<any>(ActionEnum.GET_LIST_FUNCTIONS_REQUEST);
export const getListFunctionsSuccess = createAction<any>(ActionEnum.GET_LIST_FUNCTIONS_SUCCESS);
export const getListFunctionsFailed = createAction<any>(ActionEnum.GET_LIST_FUNCTIONS_FAILED);

export const getListActorsRequest = createAction<any>(ActionEnum.GET_LIST_ACTORS_REQUEST);
export const getListActorsSuccess = createAction<any>(ActionEnum.GET_LIST_ACTORS_SUCCESS);
export const getListActorsFailed = createAction<any>(ActionEnum.GET_LIST_ACTORS_FAILED);

export const getListMessagesRequest = createAction<any>(ActionEnum.GET_LIST_MESSAGES_REQUEST);
export const getListMessagesSuccess = createAction<any>(ActionEnum.GET_LIST_MESSAGES_SUCCESS);
export const getListMessagesFailed = createAction<any>(ActionEnum.GET_LIST_MESSAGES_FAILED);

export const getListEmailTemplatesRequest = createAction<any>(ActionEnum.GET_LIST_EMAIL_TEMPLATES_REQUEST);
export const getListEmailTemplatesSuccess = createAction<any>(ActionEnum.GET_LIST_EMAIL_TEMPLATES_SUCCESS);
export const getListEmailTemplatesFailed = createAction<any>(ActionEnum.GET_LIST_EMAIL_TEMPLATES_FAILED);

export const getListUserRequirementsRequest = createAction<any>(ActionEnum.GET_LIST_USER_REQUIREMENTS_REQUEST);
export const getListUserRequirementsSuccess = createAction<any>(ActionEnum.GET_LIST_USER_REQUIREMENTS_SUCCESS);
export const getListUserRequirementsFailed = createAction<any>(ActionEnum.GET_LIST_USER_REQUIREMENTS_FAILED);

export const getListOtherRequirementsRequest = createAction<any>(ActionEnum.GET_LIST_OTHER_REQUIREMENTS_REQUEST);
export const getListOtherRequirementsSuccess = createAction<any>(ActionEnum.GET_LIST_OTHER_REQUIREMENTS_SUCCESS);
export const getListOtherRequirementsFailed = createAction<any>(ActionEnum.GET_LIST_OTHER_REQUIREMENTS_FAILED);

export const getListObjectPropertiesRequest = createAction<any>(ActionEnum.GET_LIST_OBJECT_PROPERTIES_REQUEST);
export const getListObjectPropertiesSuccess = createAction<any>(ActionEnum.GET_LIST_OBJECT_PROPERTIES_SUCCESS);
export const getListObjectPropertiesFailed = createAction<any>(ActionEnum.GET_LIST_OBJECT_PROPERTIES_FAILED);

export const getListFunctionIds = createAction<any>(ActionEnum.GET_LIST_FUNCTION_IDS);
export const getListFunctionIdsSuccess = createAction<any>(ActionEnum.GET_LIST_FUNCTION_IDS_SUCCESS);
export const getListFunctionIdsFailed = createAction<any>(ActionEnum.GET_LIST_FUNCTION_IDS_FAILED);

export const getListObjectFilter = createAction<any>(ActionEnum.GET_LIST_OBJECT_FILTER)
export const getListObjectFilterSuccess = createAction<any>(ActionEnum.GET_LIST_OBJECT_FILTER_SUCCESS)
export const getListObjectFilterFailure = createAction<any>(ActionEnum.GET_LIST_OBJECT_FILTER_FAILURE)

export const getListSelectProperties = createAction<any>(ActionEnum.GET_LIST_SELECT_PROPERTIES)
export const getListSelectPropertiesSuccess = createAction<any>(ActionEnum.GET_LIST_SELECT_PROPERTIES_SUCCESS)
export const getListSelectPropertiesFailure = createAction<any>(ActionEnum.GET_LIST_SELECT_PROPERTIES_FAILED)


export const setModalVisible = createAction<any>(ActionEnum.SET_MODAL_VISIBLE)