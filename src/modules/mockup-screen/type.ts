import { VersionType } from "../../constants"

export interface MockupScreenState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: MockupScreenDetail | null,
  selectedData?: MockupScreenDetail | null,
  isModalShow?: boolean

  listObjects?: any[],
  isLoadingObjects?: boolean,

  listFunctions?: any[],
  isLoadingFunctions?: boolean,

  listActors?: any[],
  isLoadingActors?: boolean,

  listMessages?: any[],
  isLoadingMessages?: boolean,

  listEmailTemplates?: any[],
  isLoadingEmailTemplates?: boolean,

  listUserRequirements?: any[],
  isLoadingUserRequirements?: boolean,

  listOtherRequirements?: any[],
  isLoadingOtherRequirements?: boolean,

  listObjectProperties?: any[],
  isLoadingListObjectProperties?: boolean

  listFunctionsId: any[],
  isLoadingFunctionsId: boolean,

  listObjectsFilter: any[] | null,
  isLoadingObjectsFilter: boolean,

  listTargetUseCase?: any[],
  isLoadingTargetUseCase: boolean,

  listTargetScreen?: any[],
  isLoadingTargetScreen: boolean,

  listSelectObjectProperties?: any[],
}
export interface MockupScreenDetail {
  id?: number | null,
  name: string,
  code: string,
  access: string,
  requirement: string,
  reqElicitation: number | null,
  documentation: number | null,
  implementation: number | null,
  storage: string
  jira: string
  confluence: string
  description: string,
  product: any,
  dateCreated: string,
  createdBy: string,
  submittedBy: string,
  dateSubmitted: string,
  status: number,
  screenComponents: ScreenComponent[],
  baObjects: number[],
  actors: number[],
  useCases: number[],
  messages: number[],
  emailTemplates: number[],
  otherRequirements: number[],
  userRequirements: number[],
  mockUpScreen: any,
  assignee: string,
  reviewer: string,
  customer: string
  dueDate: string,
  completeDate: string,
  objects: []
  projectId?: number,
  impacts?: string,
  versionHistories?: VersionType[]
}

export interface ScreenComponent {
  id?: number | null,
  component: string,
  componentType: number,
  componentTypeDetail: number,
  order: number,
  editable: true,
  mandatory: true,
  defaultValue: string,
  description: string,
  objectScreenComponent: number,
  sourceObjectProperties: number,
  screen: number,
  useCase: number,
  status: number,
  listActor: number[],
  listMess: number[],
  listObject: number[],
  listScreen: number[],
  listStateTransition: number[],
  listUseCase: number[],
  listWorkFlow: number[],
  listOtherReq: number[],
  listEmail: number[]

}
export const defaultState: MockupScreenState = {
  detail: {
    id: null,
    name: '',
    code: '',
    access: '',
    mockUpScreen: null,
    requirement: '',
    reqElicitation: null,
    documentation: null,
    implementation: null,
    storage: '',
    jira: '',
    confluence: '',
    description: '',
    product: {},
    dateCreated: '',
    createdBy: '',
    submittedBy: '',
    dateSubmitted: '',
    status: 0,
    screenComponents: [],
    baObjects: [],
    actors: [],
    useCases: [],
    messages: [],
    emailTemplates: [],
    otherRequirements: [],
    userRequirements: [],
    assignee: '',
    reviewer: '',
    dueDate: '',
    customer: '',
    completeDate: '',
    objects: [],
    impacts: '',
    versionHistories: [],
  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: [],
  listObjects: [],
  isLoadingObjects: false,
  listFunctions: [],
  isLoadingFunctions: false,
  listActors: [],
  isLoadingActors: false,
  listMessages: [],
  isLoadingMessages: false,
  listEmailTemplates: [],
  isLoadingEmailTemplates: false,
  listUserRequirements: [],
  isLoadingUserRequirements: false,
  listOtherRequirements: [],
  isLoadingOtherRequirements: false,
  listObjectProperties: [],
  isLoadingListObjectProperties: false,
  listFunctionsId: [],
  isLoadingFunctionsId: false,
  listObjectsFilter: null,
  isLoadingObjectsFilter: false,
  listTargetUseCase: [],
  isLoadingTargetUseCase: false,
  listTargetScreen: [],
  isLoadingTargetScreen: false,
  listSelectObjectProperties: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/MOCKUP_SCREEN/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/MOCKUP_SCREEN/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/MOCKUP_SCREEN/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/MOCKUP_SCREEN/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/MOCKUP_SCREEN/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/MOCKUP_SCREEN/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/MOCKUP_SCREEN/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_DETAIL_FAILED',

  VIEW_DETAIL_REQUEST = '@@MODULES/MOCKUP_SCREEN/VIEW_DETAIL_REQUEST',
  VIEW_DETAIL_SUCCESS = '@@MODULES/MOCKUP_SCREEN/VIEW_DETAIL_SUCCESS',
  VIEW_DETAIL_FAILED = '@@MODULES/MOCKUP_SCREEN/VIEW_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/MOCKUP_SCREEN/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/MOCKUP_SCREEN/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/MOCKUP_SCREEN/DELETE_FAILED',

  GET_LIST_OBJECTS_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECTS_REQUEST',
  GET_LIST_OBJECTS_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECTS_SUCCESS',
  GET_LIST_OBJECTS_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECTS_FAILED',

  GET_LIST_FUNCTIONS_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_FUNCTIONS_REQUEST',
  GET_LIST_FUNCTIONS_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_FUNCTIONS_SUCCESS',
  GET_LIST_FUNCTIONS_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_FUNCTIONS_FAILED',

  GET_LIST_ACTORS_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_ACTORS_REQUEST',
  GET_LIST_ACTORS_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_ACTORS_SUCCESS',
  GET_LIST_ACTORS_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_ACTORS_FAILED',

  GET_LIST_MESSAGES_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_MESSAGES_REQUEST',
  GET_LIST_MESSAGES_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_MESSAGES_SUCCESS',
  GET_LIST_MESSAGES_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_MESSAGES_FAILED',

  GET_LIST_EMAIL_TEMPLATES_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_EMAIL_TEMPLATES_REQUEST',
  GET_LIST_EMAIL_TEMPLATES_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_EMAIL_TEMPLATES_SUCCESS',
  GET_LIST_EMAIL_TEMPLATES_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_EMAIL_TEMPLATES_FAILED',

  GET_LIST_USER_REQUIREMENTS_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_USER_REQUIREMENTS_REQUEST',
  GET_LIST_USER_REQUIREMENTS_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_USER_REQUIREMENTS_SUCCESS',
  GET_LIST_USER_REQUIREMENTS_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_USER_REQUIREMENTS_FAILED',

  GET_LIST_OTHER_REQUIREMENTS_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OTHER_REQUIREMENTS_REQUEST',
  GET_LIST_OTHER_REQUIREMENTS_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OTHER_REQUIREMENTS_SUCCESS',
  GET_LIST_OTHER_REQUIREMENTS_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OTHER_REQUIREMENTS_FAILED',

  GET_LIST_OBJECT_PROPERTIES_REQUEST = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECT_PROPERTIES_REQUEST',
  GET_LIST_OBJECT_PROPERTIES_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECT_PROPERTIES_SUCCESS',
  GET_LIST_OBJECT_PROPERTIES_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECT_PROPERTIES_FAILED',


  GET_LIST_FUNCTION_IDS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_FUNCTION_IDS',
  GET_LIST_FUNCTION_IDS_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_FUNCTION_IDS_SUCCESS',
  GET_LIST_FUNCTION_IDS_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_FUNCTION_IDS_FAILED',

  GET_LIST_OBJECT_FILTER = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECT_FILTER',
  GET_LIST_OBJECT_FILTER_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECT_FILTER_SUCCESS',
  GET_LIST_OBJECT_FILTER_FAILURE = '@@MODULES/MOCKUP_SCREEN/GET_LIST_OBJECT_FILTER_FAILURE',

  GET_LIST_TARGET_USECASE = '@@MODULES/MOCKUP_SCREEN/GET_LIST_TARGET_USECASE',
  GET_LIST_TARGET_USECASE_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_TARGET_USECASE_SUCCESS',
  GET_LIST_TARGET_USECASE_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_TARGET_USECASE_FAILED',

  GET_LIST_TARGET_SCREEN = '@@MODULES/MOCKUP_SCREEN/GET_LIST_TARGET_SCREEN',
  GET_LIST_TARGET_SCREEN_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_TARGET_SCREEN_SUCCESS',
  GET_LIST_TARGET_SCREEN_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_TARGET_SCREEN_FAILED',

  GET_LIST_SELECT_PROPERTIES = '@@MODULES/MOCKUP_SCREEN/GET_LIST_SELECT_PROPERTIES',
  GET_LIST_SELECT_PROPERTIES_SUCCESS = '@@MODULES/MOCKUP_SCREEN/GET_LIST_SELECT_PROPERTIES_SUCCESS',
  GET_LIST_SELECT_PROPERTIES_FAILED = '@@MODULES/MOCKUP_SCREEN/GET_LIST_SELECT_PROPERTIES_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/MOCKUP_SCREEN/SET_MODAL_VISIBLE',
}
