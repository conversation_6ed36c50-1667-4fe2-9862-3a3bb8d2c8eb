import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { API_URLS, MESSAGE_TYPE, MESSAGE_TYPES } from '../../constants'
import { apiCall } from '../../helper/api/aloApi'
import { ShowAppMessage } from '../../helper/share'
import {
  createFailed, createRequest,
  createSuccess, deleteFailed, deleteRequest,
  deleteSuccess, getDetailFailed, getDetailRequest,
  getDetailSuccess, getListActorsFailed, getListActorsRequest,
  getListActorsSuccess, getListEmailTemplatesFailed, getListEmailTemplatesRequest,
  getListEmailTemplatesSuccess, getListFailed, getListFunctionsFailed, getListFunctionsRequest,
  getListFunctionsSuccess, getListMessagesFailed, getListMessagesRequest,
  getListMessagesSuccess, getListObjectFilter,
  getListObjectFilterSuccess, getListObjectPropertiesFailed, getListObjectPropertiesRequest,
  getListObjectPropertiesSuccess, getListObjectsFailed, getListObjectsRequest,
  getListObjectsSuccess, getListOtherRequirementsFailed, getListOtherRequirementsRequest,
  getListOtherRequirementsSuccess, getListRequest,
  getListSelectProperties,
  getListSelectPropertiesSuccess,
  getListSuccess, getListUserRequirementsFailed, getListUserRequirementsRequest,
  getListUserRequirementsSuccess, updateFailed, updateRequest,
  updateSuccess, viewDetailFailed, viewDetailRequest,
  viewDetailSuccess
} from './action'


function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const take = action.payload.take;
      const skip = (action.payload.skip - 1) * take;
      const url = `${API_URLS.MOCKUP_SCREEN}?Take=${take}&Skip=${skip}&SortField=Code&SortDir=desc`;
      const res = yield call(apiCall, 'GET', url);

      yield put(getListSuccess(res.data));
    } catch (err) {
      yield put(getListFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      const url = API_URLS.MOCKUP_SCREEN + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(getDetailSuccess(res.data));
    } catch (err: any) {
      yield put(getDetailFailed(null));
      if (err.response.status !== 404) {
        ShowAppMessage(MESSAGE_TYPE.ERROR)
      }
    }
  }
}

function* handleViewDetail(action: Action) {
  if (viewDetailRequest.match(action)) {
    try {
      const url = API_URLS.MOCKUP_SCREEN + '/' + action.payload
      const res = yield call(apiCall, 'GET', url)
      yield put(viewDetailSuccess(res.data));
    } catch (err) {
      yield put(viewDetailFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}


function* handleDelete(action: Action) {
  if (deleteRequest.match(action)) {
    try {
      const url = API_URLS.MOCKUP_SCREEN + '/' + action.payload
      const res = yield call(apiCall, 'DELETE', url)
      ShowAppMessage(null, MESSAGE_TYPES.DELETE, 'common.artefact.screen')
      yield put(deleteSuccess(null));
    } catch (err) {
      yield put(deleteFailed(null));
      ShowAppMessage(err, null, 'common.artefact.screen')
    }
  }
}

function* handleCreate(action: Action) {
  if (createRequest.match(action)) {
    try {
      const request = action.payload
      const res = yield call(apiCall, 'POST', API_URLS.MOCKUP_SCREEN, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.CREATE, 'common.artefact.screen')
      yield put(createSuccess(null));
    } catch (err) {
      yield put(createFailed(null));
      ShowAppMessage(err, null, 'common.artefact.screen')
    }
  }
}

function* handleUpdate(action: Action) {
  if (updateRequest.match(action)) {
    try {
      const request = action.payload
      const url = API_URLS.MOCKUP_SCREEN + '/' + request.id
      const res = yield call(apiCall, 'PUT', url, request as any)
      //checkdone
      ShowAppMessage(null, request.messageAction || MESSAGE_TYPES.UPDATE, 'common.artefact.screen')
      yield put(updateSuccess(null));
    } catch (err) {
      yield put(updateFailed(null));
      ShowAppMessage(err, null, 'common.artefact.screen')
    }
  }
}

function* handleGetListObjects(action: Action) {
  if (getListObjectsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_OBJECTS);
      yield put(getListObjectsSuccess(res.data));
    } catch (err) {
      yield put(getListObjectsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListFunctionsRequest(action: Action) {
  if (getListFunctionsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_FUNCTIONS);

      yield put(getListFunctionsSuccess(res.data));
    } catch (err) {
      yield put(getListFunctionsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListActorsRequest(action: Action) {
  if (getListActorsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_ACTORS);

      yield put(getListActorsSuccess(res.data));
    } catch (err) {
      yield put(getListActorsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListMessagesRequest(action: Action) {
  if (getListMessagesRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_MESSAGES);

      yield put(getListMessagesSuccess(res.data));
    } catch (err) {
      yield put(getListMessagesFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListEmailTemplatesRequest(action: Action) {
  if (getListEmailTemplatesRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_EMAIL_TEMPLATES);

      yield put(getListEmailTemplatesSuccess(res.data));
    } catch (err) {
      yield put(getListEmailTemplatesFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListUserRequirementsRequest(action: Action) {
  if (getListUserRequirementsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_USER_REQUIREMENTS);

      yield put(getListUserRequirementsSuccess(res.data));
    } catch (err) {
      yield put(getListUserRequirementsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListOtherRequirementsRequest(action: Action) {
  if (getListOtherRequirementsRequest.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_OTHER_REQUIREMENTS);

      yield put(getListOtherRequirementsSuccess(res.data));
    } catch (err) {
      yield put(getListOtherRequirementsFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListObjectPropertiesRequest(action: Action) {
  if (getListObjectPropertiesRequest.match(action)) {
    try {
      const url = API_URLS.REFERENCES_OBJECTS + '/' + action.payload + '/objectproperties'
      const res = yield call(apiCall, 'GET', url)

      yield put(getListObjectPropertiesSuccess(res.data));
    } catch (err) {
      yield put(getListObjectPropertiesFailed(null));
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}

function* handleGetListObjectFilter(action: Action) {
  if (getListObjectFilter.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', API_URLS.REFERENCES_OBJECTS)
      yield put(getListObjectFilterSuccess(res.data))
    } catch {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}


function* handleGetListSelectObjectProperties(action: Action) {
  if (getListSelectProperties.match(action)) {
    try {
      const res = yield call(apiCall, 'GET', `${API_URLS.OBJECT}/${action.payload}/properties`)
      yield put(getListSelectPropertiesSuccess(res.data))
    } catch {
      ShowAppMessage(MESSAGE_TYPE.ERROR)
    }
  }
}


function* watchFetchRequest() {
  yield takeLatest(getListRequest.type, handleGetList)
  yield takeLatest(getDetailRequest.type, handleGetDetail)
  yield takeLatest(viewDetailRequest.type, handleViewDetail)
  yield takeLatest(createRequest.type, handleCreate)
  yield takeLatest(updateRequest.type, handleUpdate)
  yield takeLatest(deleteRequest.type, handleDelete)
  yield takeLatest(getListObjectsRequest.type, handleGetListObjects)
  yield takeLatest(getListFunctionsRequest.type, handleGetListFunctionsRequest)
  yield takeLatest(getListActorsRequest.type, handleGetListActorsRequest)
  yield takeLatest(getListMessagesRequest.type, handleGetListMessagesRequest)
  yield takeLatest(getListEmailTemplatesRequest.type, handleGetListEmailTemplatesRequest)
  yield takeLatest(getListUserRequirementsRequest.type, handleGetListUserRequirementsRequest)
  yield takeLatest(getListOtherRequirementsRequest.type, handleGetListOtherRequirementsRequest)
  yield takeLatest(getListObjectPropertiesRequest.type, handleGetListObjectPropertiesRequest)
  yield takeLatest(getListObjectFilter.type, handleGetListObjectFilter)
  yield takeLatest(getListSelectProperties.type, handleGetListSelectObjectProperties)
  
}
export default function* MockupScreenSaga() {
  yield all([fork(watchFetchRequest)])
}
