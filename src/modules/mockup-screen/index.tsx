import { PlusOutlined } from '@ant-design/icons'
import { <PERSON>ton, Select, Space, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, SEARCH_TYPE, STATUS, STATUS_FILTER } from '../../constants'
import CustomSvgIcons from '../../helper/component/custom-icons'
import LavTable from '../../helper/component/lav-table'
import ExportButton from '../../helper/component/lav-table/export'
import { currentUserName, extractProjectCode, getColumnDropdownFilterProps, getColumnSearchProps, hasRole, renderStatusBadge } from '../../helper/share'
import AppCommonService from '../../services/app.service'
import MockupScreenFormPage from './form/form'

const { Text } = Typography
const MockupScreen = () => {
  const [columns, setColumns] = useState<any>(null)
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [id, setId] = useState<number>(0)
  const [reload, setReload] = useState(false)

  const getMember = async (objs) => {
    const a = await Promise.all([
      AppCommonService.getMembers(),
      AppCommonService.getMembersBA(),
      AppCommonService.getCustomers()
    ])
    setColumns(configColumns(objs, a[0].data, a[1].data, a[2].data));
  }

  useEffect(() => {
    AppCommonService.getData(API_URLS.REFERENCES_OBJECTS).then((e) => {
      getMember(e?.map(e => {
        return { text: e.name, value: e.id }
      }));
    }).catch(err => {
      console.log("Error" , err);
      setColumns([])
    })
  }, [])  

  useEffect(() => {       
    if(screenMode == SCREEN_MODE.VIEW)
      document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'view-screen-list.header.title'}); 
  }, [screenMode])

  const onChangeAuthor = (e, record) => {
    const payload = {
      artefactType: REQ_ARTEFACT_TYPE_ID.SCREEN,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefacts,
        artefactId: record?.id,
        author: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  // useEffect(() => {
  //   if(members.length != 0 && memberReviewer.length != 0 && customers.length != 0) {
  //     setLoadedColumns(configColumns(members, memberReviewer, customers))
  //   }
  // }, [members, memberReviewer, customers])
  const onChangeReviewer = (e, record) => {
    const payload = {
      artefactType: REQ_ARTEFACT_TYPE_ID.SCREEN,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefacts,
        artefactId: record?.id,
        reviewer: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  const onChangeCustomer = (e, record) => {
    const payload = {
      artefactType: REQ_ARTEFACT_TYPE_ID.SCREEN,
      artefactId: record?.id,
      data: {
        artefactType: record?.artefacts,
        artefactId: record?.id,
        customer: e,
      }
    }
    AppCommonService.updateAssignTaskInfor(payload).then((e) => {
      setReload(true)
    })
  }

  const configColumns = (objs, members, memberReviewer, customers) => [
    {
      title: intl.formatMessage({ id: 'view-screen-list.column.screen-code' }),
      dataIndex: 'code',
      width: '85px',
      sorter: true,
      sortOrder: 'descend',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SCREEN_DETAIL}` + record.id
        return <Link to={href}>{text}</Link>
      },
    },
    {
      title: intl.formatMessage({ id: 'view-screen-list.column.screen' }),
      dataIndex: 'name',
      width: '20%',
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: intl.formatMessage({ id: 'view-screen-list.column.description' }),
      dataIndex: 'description',
      width: '45%',
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
      render: (text: string) => {
        return <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
      },
    },
    {
      title: intl.formatMessage({ id: 'view-screen-list.column.object' }),
      dataIndex: 'objects',
      width: '20%',
      ...getColumnDropdownFilterProps(objs, 'objectId', true),
      render: (listObject: any, record: any) => {
        return (
          <>
            {listObject?.map((e: any, index) => (
              index === 0 ? `${e.name}` : `, ${e.name}`
            ))}
          </>
        )
      },
    },
    {
      title: intl.formatMessage({ id: 'function.column.author' }),
      width: '10%',
      dataIndex: 'author',
      ...getColumnDropdownFilterProps(members.map(e => { return { value: e.userName, text: e.fullName } }), 'author', true, false),
      render: (text, record) => {
        return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ? <Select
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          allowClear
          defaultValue={text}
          onChange={(e) => onChangeAuthor(e, record)}
          className='full-width assign-select'>
          {
            members.map(member => (
              <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
            ))
          }
        </Select> : text
      }
    },
    {
      title: intl.formatMessage({ id: 'function.column.reviewer' }),
      width: '10%',
      dataIndex: 'reviewer',
      ...getColumnDropdownFilterProps(memberReviewer.map(e => { return { value: e.userName, text: e.fullName } }), 'reviewer', true, false),
      render: (text, record) => {
        return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ? <Select
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          allowClear
          defaultValue={text}
          onChange={(e) => onChangeReviewer(e, record)}
          className='full-width assign-select'>
          {
            memberReviewer.map(member => (
              <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
            ))
          }
        </Select> : text
      }
    },
    {
      title: intl.formatMessage({ id: 'function.column.customer' }),
      width: '10%',
      dataIndex: 'customer',
      ...getColumnDropdownFilterProps(customers.map(e => { return { value: e.userName, text: e.fullName } }), 'customer', true, false),
      render: (text, record) => {
        return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
          && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
          record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE &&
          record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
          record.status !== STATUS.DELETE
        ) ? <Select
          filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          showSearch
          allowClear
          defaultValue={text}
          onChange={(e) => onChangeCustomer(e, record)}
          className='full-width assign-select'>
          {
            customers.map(member => (
              <Select.Option key={member.userName} value={member.userName}>{member.fullName}</Select.Option>
            ))
          }
        </Select> : text
      }
    },
    {
      title: intl.formatMessage({ id: 'view-screen-list.column.status' }),
      dataIndex: 'status',
      width: '80px',
      ...getColumnDropdownFilterProps(STATUS_FILTER, 'Statuses'),
      render: (record) => renderStatusBadge(record),
    },
  ]

  const CreateComponent: React.FC<any> = ({ handleDataChange }) => {
    return (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <Button ghost={true}
      type='primary'
      className='lav-btn-create'
      icon={<PlusOutlined />}
      onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'view-screen-list.button.create-screen' })}
    </Button> : <></>
  }

  const UpdateComponent: React.FC<any> = ({ record, handleDataChange }) => {
    return ((((hasRole(APP_ROLES.BA) && record.status !== STATUS.SUBMITTED) || ((currentUserName() === record?.reviewer)
      && (record?.status === STATUS.SUBMITTED || record?.status === STATUS.REJECT || record?.status === STATUS.REJECT_CUSTOMER || record?.status === STATUS.APPROVE))) &&
      record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE &&
      record.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && record.status !== STATUS.CANCELLED &&
      record.status !== STATUS.DELETE
    ) ?
      <Button ghost={screenMode === SCREEN_MODE.EDIT}
        style={{ border: 'none' }}
        icon={<CustomSvgIcons name="EditCustomIcon" />}
        onClick={() => {
          setScreenMode(SCREEN_MODE.EDIT)
          setId(record.id)
        }} /> : <></>
  }
  const DeleteComponent: React.FC<any> = ({ record, children }) => {
    return (
      record.status !== STATUS.DELETE &&
      (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM))
    ) ?
      children : <></>
  }

  const ExportComponent: React.FC<any> = ({ handleExport }) => {
    return <ExportButton fileName='view-screen-list.header.title' onExport={handleExport} title='view-screen-list.button.export-screen'/>
  }

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {
        screenMode === SCREEN_MODE.VIEW && columns ? <LavTable
          title="view-screen-list.header.title"
          artefact_type="common.artefact.screen"
          apiUrl={API_URLS.SCREENS}
          columns={columns}
          artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN}
          updateComponent={UpdateComponent}
          createComponent={CreateComponent}
          deleteComponent={DeleteComponent}
          exportComponent={ExportComponent}
          isReload = {reload}
          reloadSuccess={() => setReload(false)}
        /> : <></>
      }

      {
        screenMode === SCREEN_MODE.CREATE ? <MockupScreenFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} /> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ? <MockupScreenFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} screenID={id} /> : <></>
      }
    </Space>
  )
}

export default MockupScreen
