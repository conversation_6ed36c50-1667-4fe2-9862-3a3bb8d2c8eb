import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import MockupScreenFormPage from '../form/form'
import { MockupScreenState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'
import HistoryScreen from '../../history'
import VersionDetails from './history/details'
import AppCommonService from '../../../services/app.service'
import VersionCompare from './history/compare'

const MockupScreenDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const state = useSelector<AppState | null>((s) => s?.MockupScreen) as MockupScreenState;
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedFirstRowData, setSelectedFirstRowData] = useState<any>(null)
  const [selectedSecondRowData, setSelectedSecondRowData] = useState<any>(null)
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.screenID) {
      dispatch(getDetailRequest(props.match.params.screenID))
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.MOCKUP_SCREEN + '/version/' + props.match.params.screenID +  '/' + selectedRowVersion).then((e) => {
        console.log("Selected Version Data : ", e);
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);    
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    console.log("Selected History Row Keys : ", selectedHistoryRowKeys);
    if(selectedHistoryRowKeys.length == 2){
      AppCommonService.getData(API_URLS.MOCKUP_SCREEN + '/' + selectedHistoryRowKeys[0]).then((e) => {
        console.log("First Row Data : ", e);
        setSelectedFirstRowData(e);
      }).catch(err => {
        console.log(err);
      })

      AppCommonService.getData(API_URLS.MOCKUP_SCREEN + '/' + selectedHistoryRowKeys[1]).then((e) => {
        console.log("Second Row Data : ", e);
        setSelectedSecondRowData(e);
      }).catch(err => {
        console.log(err);
      })
    }
  }, [selectedHistoryRowKeys])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SCREEN}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.screenID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SCREEN_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col span={5}>
            <LavLeftControl
              activeId={props.match.params.screenID}
              apiUrl={API_URLS.REFERENCES_SCREENS}
              route={APP_ROUTES.SCREEN_DETAIL}
              title='view-screen-list.header.title'
              artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN}
              reload={reload}
              reloadSuccess={() => setReload(false)}
              handleCreate={handleCreate}
            >
              {
                (hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? <Button ghost={true}
                  type='primary'
                  className='lav-btn-create'
                  icon={<PlusOutlined />}
                  onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'view-screen-list.button.create-screen' })}
                </Button> : <></>
              }
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>            
            <Col span={19}>
              <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} screenID={props.match.params.screenID} setScreenMode={setScreenMode} isModalShow={state?.isModalShow} />
            </Col>
          </> : <></>
      }

      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <MockupScreenFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <MockupScreenFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
            }} screenID={props.match.params.screenID} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.screen"
                            apiURL = {API_URLS.HISTORY}
                            artefactType = {REQ_ARTEFACT_TYPE_ID.SCREEN}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + state?.selectedData?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
            <VersionDetails screenID={props.match.params.screenID} setSelectedRowVersion = {setSelectedRowVersion} isModalShow={state?.isModalShow} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.COMPARE ?
          <>
            <Col span={19}>
                <VersionCompare onChange={handleReloadData} isLoading={state?.isLoading} data={selectedFirstRowData} data2={selectedSecondRowData} screenID={props.match.params.screenID} setScreenMode={setScreenMode} isModalShow={state?.isModalShow} />
            </Col>
          </>: <></>
      }
    </Row>
  )
}

export default MockupScreenDetail
