import AppState from '@/store/types'
import {
    <PERSON>read<PERSON><PERSON>b, Button, Card, Checkbox, Col, Divider, Row, Space, Spin, Table, Typography
} from 'antd'
import { useEffect, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useHistory } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROLES, APP_ROUTES, ARTEFACT_COMMENT, BUTTON_TYPE, COMP_TYPE_LIST, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS } from '../../../constants'
import AssignTaskDetail from '../../../helper/component/assign-task-detail'
import DeleteButton from '../../../helper/component/commonButton/DeleteButton'
import LavAttachmentPreview from '../../../helper/component/lav-attachment-preview'
import LavAuditTrail from '../../../helper/component/lav-audit-trail'
import LavButtons from '../../../helper/component/lav-buttons'
import LavEffortEstimation from '../../../helper/component/lav-efffort-estimation'
import LavImpact from '../../../helper/component/lav-impact'
import LavReferences from '../../../helper/component/lav-references'
import LavRelatedLinks from '../../../helper/component/lav-related-links'
import LavVersion from '../../../helper/component/lav-version/form'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, extractProjectCode, getProjectName, hasRole, renderStatusBadge } from '../../../helper/share'
import { initComment, initCommentScreen, openComment } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { deleteRequest } from '../action'
import { MockupScreenState } from '../type'

const { Title, Text } = Typography

interface RightControlProps {
    data: any | [],
    screenID: string,
    onChange: () => void,
    isLoading: boolean,
    isModalShow?: boolean
    setScreenMode: any
}
const RightControl = ({ data, screenID, onChange, isLoading, isModalShow, setScreenMode }: RightControlProps) => {
    const { height: windowHeight } = useWindowDimensions()
    const dispatch = useDispatch();
    const history = useHistory();
    const state = useSelector<AppState | null>((s) => s?.MockupScreen) as MockupScreenState;
    const [dataS, setDataSource] = useState<any>([])
    const [fieldsComment, setFieldsComment] = useState<any[]>([
        { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
        { field: 'description', title: intl.formatMessage({ id: 'view-screen-details.screen-info.description' }), },
        { field: 'access', title: intl.formatMessage({ id: 'view-screen-details.screen-info.access' }), },
        { field: 'mockup-screen', title: intl.formatMessage({ id: 'view-screen-details.screen-info.mockup-screen' }), },
        { field: 'screen-description', title: intl.formatMessage({ id: 'view-screen-details.screen-info.screen-description' }), },
        { field: 'actor', title: intl.formatMessage({ id: 'view-screen-list.label.actor' }), },
        { field: 'object', title: intl.formatMessage({ id: 'view-screen-list.label.object' }), },
        { field: 'requirement', title: intl.formatMessage({ id: 'view-screen-list.label.requirement' }), },
        { field: 'source-screen', title: intl.formatMessage({ id: 'view-screen-list.label.source-screen' }), },
        { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
        { field: 'message', title: intl.formatMessage({ id: 'view-screen-list.label.message' }), },
        { field: 'email-template', title: intl.formatMessage({ id: 'view-screen-list.label.email-template' }), },
        { field: 'other-requirement', title: intl.formatMessage({ id: 'view-screen-list.label.other-requirement' }), },
        { field: 'target-screen', title: intl.formatMessage({ id: 'view-screen-list.label.target-screen' }), },
        { field: 'state-transition', title: intl.formatMessage({ id: 'view-screen-list.label.state-transition' }), },
        { field: 'assignee', title: intl.formatMessage({ id: 'assigned_task.label.assignee' }), },
        { field: 'reviewer', title: intl.formatMessage({ id: 'assigned_task.label.reviewer' }), },
        { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
        { field: 'due-date', title: intl.formatMessage({ id: 'assigned_task.label.due-date' }), },
        { field: 'complete-date', title: intl.formatMessage({ id: 'assigned_task.label.complete-date' }), },
        { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
        { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
        { field: 'implementation', title: intl.formatMessage({ id: 'view-screen-list.label.implementation' }), },
        { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
        { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
        { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ])
    const [id, setId] = useState(0)
    useEffect(() => {
        if(data)
            document.title = data?.code + "-" + data?.name; 
        setDataSource([])
        if (data?.screenComponents && data?.screenComponents.length > 0) {
            let x: any[] = data.screenComponents
            let currentSC = Object.assign([], data?.screenComponents);
            currentSC.sort((a: any, b: any) => { return a.order - b.order })
            setDataSource(transformDataSource(currentSC))
            const maxID = Math.max.apply(
                Math,
                x.map((o: any) => {
                    return o.id
                })
            )

            if (maxID) {
                setId(maxID + 1)
            }
        }
    }, [data?.screenComponents])

    const projectCode = extractProjectCode();
    const projectName = getProjectName(projectCode);

    const tableType = COMP_TYPE_LIST[20].value;
    const transformDataSource = (data) => {
        let newData: any = [];
        let newIndex = 1;
        data.forEach((e: any, idx) => {
            newData.push({
                ...e,
                rowId: idx,
                rowNumber: newIndex
            })
            if (e.componentType != tableType) {
                newIndex++;
            }
        })
        return newData
    }
    const columns = [
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.order' })}
                </Text>
            ),
            dataIndex: 'order',
            width: '5%',
            render: (text: number, item, index) => {
                return {
                    children: item.componentType === tableType ? <></> : (item.rowNumber),
                    props: {
                        colSpan: 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.component' })}
                </Text>
            ),
            dataIndex: 'component',
            key: 'component',
            width: '10%',
            render: (text, item) => {
                return {
                    children: item.componentType === tableType ? <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(item?.id)}>
                        <div className="tableDangerous" dangerouslySetInnerHTML={{
                            __html: `<div>${item.component}</div>${item?.description}`
                        }}></div>
                    </TriggerComment> : <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(item?.id)}>
                        <Text>{text}</Text>
                    </TriggerComment>,
                    props: {
                        colSpan: item.componentType === tableType ? 6 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.component-type',
                    })}
                </Text>
            ),
            dataIndex: 'componentType',
            key: 'componentType',
            width: '10%',
            render: (text, item) => {
                return {
                    children: <Text>{text}</Text>,
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.editable' })}
                </Text>
            ),
            dataIndex: 'editable',
            key: 'editable',
            width: '10%',
            render: (editable: string, item) => {
                return {
                    children: editable ? (
                        <Checkbox checked={true} disabled />
                    ) : (
                        <Checkbox checked={false} disabled />
                    ),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.mandatory' })}
                </Text>
            ),
            dataIndex: 'mandatory',
            key: 'mandatory',
            width: '10%',
            render: (mandatory: string, item: any) => {
                return {
                    children: mandatory ? (
                        <Checkbox checked={true} disabled />
                    ) : (
                        <Checkbox checked={false} disabled />
                    ),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.default-value',
                    })}
                </Text>
            ),
            dataIndex: 'defaultValue',
            key: 'defaultValue',
            width: '10%',
            render: (value, item) => {
                return {
                    children: value ? value : '',
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.description',
                    })}
                </Text>
            ),
            dataIndex: 'description',
            key: 'description',
            width: '35%',
            render: (text, item) => {
                return {
                    children: <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>,
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
    ]
    const changeMenu = (id: number) => {
        if (screenID.toString() !== id.toString()) {
            const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SCREEN_DETAIL}` + id
            history.push(href)
        }
    }

    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        const getCoString = localStorage.getItem('comment')
        if (getCoString != null) {
            const co = JSON.parse(getCoString || '')
            if (commentState.fields && co?.itemId === data?.id) {
                const fieldObj = commentState.fields.find(o => o.field === co?.field && co?.artefactType == REQ_ARTEFACT_TYPE_ID.SCREEN);
                if (fieldObj) {
                    const indexComment = commentState.comments.findIndex(o => o.field === co?.field);
                    dispatch(openComment({ index: indexComment, title: fieldObj.title, field: co?.field }));
                }
            }
        }
    }, [commentState.fields, data])

    useEffect(() => {
        if (!data?.id || commentState.isLoading || isModalShow) {
            return;
        }

        const otherField: any[] = []
        state.detail?.screenComponents?.forEach((e) => {
            otherField.push({ field: e.id ? e.id.toString() : '', title: e?.component })
        })
        const latestField: any = [
            ...fieldsComment,
            ...otherField,
        ]
        setFieldsComment(latestField)
        dispatch(initComment({ projectId: data.projectId, itemId: data.id, fields: latestField }));

        const payload = {
            projectId: data.projectId,
            itemId: data.id,
            artefact: ARTEFACT_COMMENT.SCREEN,
            fields: latestField.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [data, isModalShow])

    //#endregion COMMENT INIT

    const DeleteComponent: React.FC<any> = ({ record, children }) => {
        return ((hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) && data?.status !== STATUS.DELETE) ? <DeleteButton
            type={BUTTON_TYPE.TEXT}
            content={intl.formatMessage({ id: 'CFD_7' }, { artefact_type: intl.formatMessage({ id: 'common.artefact.screen' }) })}
            okCB={() => dispatch(deleteRequest(screenID))}
            confirmButton={intl.formatMessage({ id: 'common.action.delete' })} /> : <></>
    }

    return data ? (
        <Space
            direction="vertical"
            size="middle"
            className="record-detail-right-control-container p-1rem"
        >
            <Row align="middle" justify="space-between">
                <div>
                    <Breadcrumb className='rq-breadcrumb' separator=">">
                        <Breadcrumb.Item>
                            <Link className="breadcrumb-link-btn" to={`${PROJECT_PREFIX}${projectCode}/dashboard`}>{projectCode} - {projectName}</Link>
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Title level={3} className='rq-page-title'>
                        {data?.code} - {data?.name}
                    </Title>
                </div>

                <Space size="small">

                    <LavButtons
                        url={`${API_URLS.SCREENS}/${data?.id}`}
                        reviewer={`${data?.reviewer}`}
                        customer={`${data?.customer}`}
                        artefact_type="common.artefact.common-screen"
                        status={data?.status}
                        isHasReject={true}
                        isHasCancel={true}
                        isHasApprove={true}
                        isHasEndorse={true}
                        artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN}
                        deleteButton={DeleteComponent}
                        id={screenID}
                        changePage={() => onChange()}>

                        {/* Update record */}
                        {((((hasRole(APP_ROLES.BA) && data.status !== STATUS.SUBMITTED) || ((currentUserName() === data?.reviewer)
                            && (data?.status === STATUS.SUBMITTED || data?.status === STATUS.REJECT || data?.status === STATUS.REJECT_CUSTOMER || data?.status === STATUS.APPROVE))) &&
                            data.status !== STATUS.CANCELLED &&
                            data.status !== STATUS.DELETE &&
                            data.status !== STATUS.ENDORSE) || hasRole(APP_ROLES.PM) && data.status !== STATUS.CANCELLED &&
                            data.status !== STATUS.DELETE
                        ) ?
                            <Button
                                type='primary'
                                className='lav-btn-create'
                                onClick={() => {
                                    setScreenMode(SCREEN_MODE.EDIT)
                                }} >{intl.formatMessage({ id: 'common.action.update' })}</Button> : <></>
                        }
                    </LavButtons>

                </Space>
            </Row>
            <Divider className="mt-0 mb-0" />
            <Spin spinning={isLoading}>
                <Scrollbars
                    autoHide
                >
                    <Space direction="vertical" size="middle">
                        <Space size="large">
                            <span>          
                                <TriggerComment field='version'>                               
                                    <a onClick={() => {
                                        setScreenMode(SCREEN_MODE.HISTORY)
                                    }}>
                                        {intl.formatMessage({ id: `common.label.version` })}  {data?.version || ''}
                                    </a>
                                </TriggerComment>
                            </span>
                            {renderStatusBadge(data?.status)}
                        </Space>

                        <Card title={<Title level={5}>{intl.formatMessage({ id: 'view-screen-details.legend.screen-info' })}</Title>} bordered={true}>
                            <Row gutter={[16, 4]}>
                                <Col span={4}>
                                    <TriggerComment field="description">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'view-screen-details.screen-info.description',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: data?.description }}></div>
                                </Col>

                                <Col span={4}>
                                    <TriggerComment field="access">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'view-screen-details.screen-info.access',
                                            })}:
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={20}>
                                    <div
                                        className="tableDangerous"
                                        dangerouslySetInnerHTML={{
                                            __html: data?.access,
                                        }}
                                    ></div>
                                </Col>

                                <Col span={24}>
                                    <TriggerComment field="mockup-screen">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'view-screen-details.screen-info.mockup-screen',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24}>
                                    <LavAttachmentPreview attachment={data?.mockUpScreen} isCommon={false} />
                                </Col>
                                <Col span={24}>
                                    <TriggerComment field="screen-description">
                                        <Text type="secondary">
                                            {intl.formatMessage({
                                                id: 'view-screen-details.screen-info.screen-description',
                                            })}
                                        </Text>
                                    </TriggerComment>
                                </Col>
                                <Col span={24} style={{ width: '1px', overflowX: 'scroll' }}>
                                    <Table
                                        locale={{ emptyText: 'NO DATA' }}
                                        bordered
                                        dataSource={dataS}
                                        columns={columns}
                                        rowKey="id"
                                        pagination={false}
                                    />
                                </Col>
                            </Row>
                        </Card>

                        <LavReferences data={data} />
                        <AssignTaskDetail data={data} />
                        {(!data?.impacts || data?.impacts === 'false' || data?.impacts === "{}" || data?.impacts === 'null') ? <></> : <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN} onChange={() => { }} isViewMode={true} />}
                        {/* {data?.impacts ? <LavImpact dataDetail={data} artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN} onChange={() => { }} isViewMode={true} /> : <></>} */}
                        <Row justify="space-between">
                            <Col span={8}>
                                <LavEffortEstimation data={data} />
                            </Col>

                            <Col span={15}>
                                <LavRelatedLinks data={data} />
                            </Col>
                        </Row>
                        <Col span={24}>
                            <LavVersion screenMode={SCREEN_MODE.VIEW} data={data.versionHistories} />
                        </Col>
                        <Col span={24}>
                            <LavAuditTrail data={data?.auditTrail} />
                        </Col>
                    </Space>
                </Scrollbars>
            </Spin>
        </Space>
    ) : <></>
}

export default RightControl
