import { Card, Checkbox, Col, Row, Space, Table, Typography } from "antd"
import { useEffect, useRef, useState } from "react"
import intl from "../../../config/locale.config"
import { COMP_TYPE_LIST, ROW_STATUS } from "../../../constants"
import LavAttachmentPreview from "../../../helper/component/lav-attachment-preview"
import LavReferences from "../../../helper/component/lav-references"
import { renderStatusBadge, ShowWarningMessge } from "../../../helper/share"

const { Title, Text } = Typography

interface ScreenDetailInfoProps {
    data: any,
    onChange?: any,
    selectable?: boolean
}
const ScreenDetailInfo = ({ data, onChange, selectable = true }: ScreenDetailInfoProps) => {
    const tableUpdateRef = useRef<any>()
    const [dataS, setDataSource] = useState<any>([])
    const [id, setId] = useState(0)
    useEffect(() => {
        if (data?.screenComponents && data?.screenComponents.length > 0) {
            let x: any[] = data.screenComponents
            let currentSC = Object.assign([], data?.screenComponents);
            currentSC.sort((a: any, b: any) => { return a.order - b.order })
            setDataSource(transformDataSource(currentSC))
            const maxID = Math.max.apply(
                Math,
                x.map((o: any) => {
                    return o.id
                })
            )

            if (maxID) {
                setId(maxID + 1)
            }
        }
    }, [data?.screenComponents])

    const [selectedRowKeys, setSelectedRowKeys] = useState<any>(null)

    useEffect(() => {
        setSelectedRowKeys(null)
        return () => {
            setSelectedRowKeys(null)
        }
    }, [])

    const renderDescription = (text, record) => {
        if (
            record.status &&
            (record.status === ROW_STATUS.CREATE ||
                record.status === ROW_STATUS.UPDATE)
        ) {
            // let property = ''
            // let object = ''

            // if (record.objectScreenComponent) {
            //     let currentObject: any = null;
            //     const listObjects = state.listObjects || []
            //     const existsIndex = listObjects.findIndex((item: any) => record.objectScreenComponent === item.id);
            //     if (existsIndex != -1 && existsIndex < listObjects.length - 1) {
            //         currentObject = listObjects![existsIndex]
            //     }

            //     if (currentObject) {
            //         object = currentObject.name
            //         if (record.sourceObjectProperties) {
            //             const listProperty = state.listObjectProperties || []
            //             const existsIndex = listProperty.findIndex((item: any) => record.sourceObjectProperties === item.id)
            //             if (existsIndex != -1 && existsIndex < listProperty.length - 1) {
            //                 const currentProperty = listProperty[existsIndex]
            //                 if (currentProperty) {
            //                     property = currentProperty.name
            //                 }
            //             }
            //         }
            //     }
            // }
            return <div>
                <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
                {/* {record.objectScreenComponent && (
                    <div>
                        <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object' })}: ${object}`}</Text>
                    </div>
                )}
                {record.sourceObjectProperties && (
                    <div>
                        <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object-property' })}: ${property}`}</Text>
                    </div>
                )} */}
            </div>
        } else {
            return <>
                <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>
                {/* {record.objectScreenComponent && (
                    <div>
                        <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object' })}: ${record.objectScreenComponent.name}`}</Text>
                    </div>
                )}
                {record.sourceObjectProperties &&
                    record.sourceObjectProperties.length > 0 && (
                        <div>
                            <Text>{`-${intl.formatMessage({ id: 'view-screen-details.description-source-object-property' })}: ${record.sourceObjectProperties[0].name}`}</Text>
                        </div>
                    )} */}
            </>
        }
    }
    const tableType = COMP_TYPE_LIST[20].value;
    const transformDataSource = (data) => {
        let newData: any = [];
        let newIndex = 1;
        data.forEach((e: any, idx) => {
            newData.push({
                ...e,
                rowId: idx,
                rowNumber: newIndex
            })
            if (e.componentType != tableType) {
                newIndex++;
            }
        })
        return newData
    }
    const columns = [
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.order' })}
                </Text>
            ),
            dataIndex: 'order',
            width: '5%',
            render: (text: number, item, index) => {
                return {
                    children: item.componentType === tableType ? <></> : (item.rowNumber),
                    props: {
                        colSpan: 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.component' })}
                </Text>
            ),
            dataIndex: 'component',
            key: 'component',
            width: '10%',
            render: (text, item) => {
                return {
                    children: item.componentType === tableType ? <>
                        <div>{item.component}</div>
                        <div>{renderDescription(item.description, item)}</div>
                    </> : <Text>{text}</Text>,
                    props: {
                        colSpan: item.componentType === tableType ? 6 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.component-type',
                    })}
                </Text>
            ),
            dataIndex: 'componentType',
            key: 'componentType',
            width: '10%',
            render: (text, item) => {
                return {
                    children: <Text>{text}</Text>,
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.editable' })}
                </Text>
            ),
            dataIndex: 'editable',
            key: 'editable',
            width: '10%',
            render: (editable: string, item) => {
                return {
                    children: editable ? (
                        <Checkbox checked={true} disabled />
                    ) : (
                        <Checkbox checked={false} disabled />
                    ),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({ id: 'createscreen.column.mandatory' })}
                </Text>
            ),
            dataIndex: 'mandatory',
            key: 'mandatory',
            width: '10%',
            render: (mandatory: string, item: any) => {
                return {
                    children: mandatory ? (
                        <Checkbox checked={true} disabled />
                    ) : (
                        <Checkbox checked={false} disabled />
                    ),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.default-value',
                    })}
                </Text>
            ),
            dataIndex: 'defaultValue',
            key: 'defaultValue',
            width: '10%',
            render: (value, item) => {
                return {
                    children: value ? value : '',
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            }
        },
        {
            title: (
                <Text strong>
                    {intl.formatMessage({
                        id: 'createscreen.column.description',
                    })}
                </Text>
            ),
            dataIndex: 'description',
            key: 'description',
            width: '35%',
            render: (text, item) => {
                return {
                    children: renderDescription(text, item),
                    props: {
                        colSpan: item.componentType === tableType ? 0 : 1,
                    }
                }
            },
        },
    ]



    return <Space direction="vertical" size="middle">
        <Space size="large">
            <span>
                <Text style={{ marginRight: '10px' }}>{intl.formatMessage({ id: `common.label.version` })}</Text>{data?.version || ''}
            </span>
            {renderStatusBadge(data?.status)}
        </Space>

        <Card title={<Title level={5}>{intl.formatMessage({ id: 'view-screen-details.legend.screen-info' })}</Title>} bordered={true}>
            <Row gutter={[16, 4]}>
                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-screen-details.screen-info.description',
                        })}:
                    </Text>
                </Col>
                <Col span={20}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: data?.description }}
                    ></div>
                </Col>

                <Col span={4}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-screen-details.screen-info.access',
                        })}:
                    </Text>
                </Col>
                <Col span={20}>
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{
                            __html: data?.access,
                        }}
                    ></div>
                </Col>

                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-screen-details.screen-info.mockup-screen',
                        })}
                    </Text>
                </Col>
                <Col span={24}>
                    <LavAttachmentPreview attachment={data?.mockUpScreen} isCommon={false} />
                </Col>
                <Col span={24}>
                    <Text type="secondary">
                        {intl.formatMessage({
                            id: 'view-screen-details.screen-info.screen-description',
                        })}
                    </Text>
                </Col>
                <Col span={24} style={{ width: '1px', overflowX: 'scroll' }}>
                    {
                        selectable ? <Table
                            rowSelection={{
                                selectedRowKeys: selectedRowKeys,
                                onChange: (selectedRowKeys: any, selectedRows: any) => {
                                    if (selectedRowKeys?.length <= 0) {
                                        tableUpdateRef.current.scrollIntoView()
                                        ShowWarningMessge('common.message.require-select')
                                    } else {
                                        setSelectedRowKeys(selectedRowKeys);
                                        onChange(selectedRowKeys);
                                    }
                                }
                            }}
                            locale={{ emptyText: 'NO DATA' }}
                            bordered
                            dataSource={dataS}
                            columns={columns}
                            rowKey="id"
                            pagination={false}
                        /> : <Table
                            locale={{ emptyText: 'NO DATA' }}
                            bordered
                            dataSource={dataS}
                            columns={columns}
                            rowKey="id"
                            pagination={false}
                        />
                    }
                    <div ref={tableUpdateRef}></div>
                </Col>
            </Row>
        </Card>

        <LavReferences data={data} />
    </Space >
}
export default ScreenDetailInfo