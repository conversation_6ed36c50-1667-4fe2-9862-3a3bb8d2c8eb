import AppState from '@/store/types'
import {
  Button,
  Card, Checkbox, Col, Form, Input, Modal, Row, Select, Space, Spin, Tag, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavAttachmentUpload from '../../../helper/component/lav-attachment-upload'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import LavVersion from '../../../helper/component/lav-version/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { concatMentionReferences, currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import AppCommonService from '../../../services/app.service'
import { createFailed, createRequest, getDetailRequest, getListActorsRequest, getListEmailTemplatesRequest, getListFunctionsRequest, getListMessagesRequest, getListObjectsRequest, getListOtherRequirementsRequest, getListUserRequirementsRequest, resetState, updateFailed, updateRequest } from '../action'
import { MockupScreenState } from '../type'
import ScreenComponents from './screen-components'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'

const { Text } = Typography
const { confirm } = Modal
const { Option } = Select

interface MockupScreenFormModalProps {
  screenID?: number
  screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
  onFinish?: () => void | null
  onDismiss: () => void | null
}
const MockupScreenFormPage = ({ screenID, screenMode, onFinish, onDismiss }: MockupScreenFormModalProps) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const state = useSelector<AppState | null>((s) => s?.MockupScreen) as MockupScreenState
  const [isDraft, setIsDraft] = useState<any>(null);
  const [access, setAccess] = useState('') as any
  const [choosedObj, setChoosedObj] = useState([]) as any
  const [isCreateMore, setIsCreateMore] = useState(false);
  const [attachment, setAttachment] = useState(null) as any
  const [impacts, setImpacts] = useState<any>(false)
  const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
  const [isDupplicate, setIsDupplicate] = useState<boolean>(false)
  const modalConfirmConfig = useModalConfirmationConfig()
  const getCkeditorData: any = createRef()
  const tableRef: any = useRef()
  const getCkeditorDataDes: any = createRef()
  const attachmentRef = useRef<any>()
  const useCaseRef = useRef<any>()
  const tableUpdateRef = useRef<any>()

  // Destroy
  useEffect(() => {
    dispatch(getListObjectsRequest(null));
    dispatch(getListFunctionsRequest(null));
    dispatch(getListActorsRequest(null));
    dispatch(getListMessagesRequest(null));
    dispatch(getListEmailTemplatesRequest(null));
    dispatch(getListUserRequirementsRequest(null));
    dispatch(getListOtherRequirementsRequest(null));
    return () => {
      dispatch(resetState(null));
      resetForm();
      form.resetFields(['createMore']);
    }
  }, [])

  useEffect(() => {
    if (screenID && screenMode === SCREEN_MODE.EDIT) {
      dispatch(getDetailRequest(screenID))
    }
    document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createscreen.page_create_title' : 'createscreen.page_update_title' });
  }, [screenMode, screenID])


  const isJsonString = (data) => {
    try {
      JSON.parse(data);
    } catch (e) {
      return '';
    }
    return JSON.parse(data);
  }

  useEffect(() => {
    if (screenID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {

      const storage = isJsonString(state.detail?.storage);
      const jira = isJsonString(state.detail?.jira);
      const confluence = isJsonString(state.detail?.confluence);


      form.setFieldsValue({
        // ...state.detail,
        code: state.detail.code,
        name: state.detail.name,
        description: state.detail.description,
        // access: state.detail.access,
        req: state.detail.reqElicitation,
        documentation: state.detail.documentation,
        implementation: state.detail.implementation,
        storageLinkText: storage ? storage?.textToDisplay : storage,
        storageWebLink: storage ? storage?.address : storage,
        jiraLinkText: jira ? jira?.textToDisplay : jira,
        jiraWebLink: jira ? jira?.address : jira,
        confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
        confluenceWebLink: confluence ? confluence?.address : confluence,
        baObjects: state.detail.baObjects?.map((e: any) => e.id),
        functions: state.detail.useCases?.map((e: any) => e.id),
        // messages: state.detail.messages?.map((e: any) => e.id),
        // emailTemplates: state.detail.emailTemplates?.map((e: any) => e.id),
        otherRequirements: state.detail.otherRequirements?.map((e: any) => e.id),
        userRequirements: state.detail.userRequirements?.map((e: any) => e.id),
        actors: state.detail.actors?.map((e: any) => e.id),
      })
      setAttachment(state.detail?.mockUpScreen)
      setAccess(state.detail?.access)
    }
  }, [state.detail])

  useEffect(() => {
    if (state?.listObjects) {
      setChoosedObj(state.listObjects)
    }
  }, [state.listObjects])

  useEffect(() => {
    if (state.createSuccess || state.updateSuccess) {
      const version = form.getFieldValue('version')
      const changeDescription = form.getFieldValue('changeDescription')

      if (version && version !== '') {
        const payload = {
          version: version.substring(version.length - 1) === "." ? `${version}0` : version,
          description: changeDescription,
          artefactCode: state.detail?.code,
        }
        AppCommonService.updateVersion(payload, REQ_ARTEFACT_TYPE_ID.SCREEN, state.detail?.id).then((e) => {
          if (isCreateMore) {
            resetForm();
            form.setFieldsValue({
              assignee: currentUserName(),
              dueDate: moment(new Date()),
            })
          } else {
            if (onFinish) {
              onFinish();
            }
            onDismiss();
          }
          setIsDraft(null);
          setIsCreateMore(false);
          dispatch(createFailed(null))
          dispatch(updateFailed(null))
        })
      } else {
        if (isCreateMore) {
          resetForm();
          form.setFieldsValue({
            assignee: currentUserName(),
            dueDate: moment(new Date()),
          })
        } else {
          if (onFinish) {
            onFinish();
          }
          onDismiss();
        }
        setIsDraft(null);
        setIsCreateMore(false);
        dispatch(createFailed(null))
        dispatch(updateFailed(null))
      }
    }
  }, [state.createSuccess, state.updateSuccess])

  useBeforeUnload()

  const onChange = (e) => {
    setImpacts(JSON.stringify(e))
  }
  const onSubmit = debounce(async (values: any, st?: string) => {
    let mentionReferences = getReferencesFromEditor(getCkeditorData.current?.props?.data);
    mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(getCkeditorDataDes.current?.props?.data))
    try {
      tableRef.current.getTableState()?.forEach((e) => {
        mentionReferences = concatMentionReferences(mentionReferences, getReferencesFromEditor(e.description));
      })

      const checkSc = tableRef.current.getTableState()?.filter(e => e.component.replace(/\s+/g, ' ')
        .trim() === "" || e?.componentType.replace(/\s+/g, ' ')
          .trim() === "")

      if (checkSc && checkSc.length > 0) {
        tableUpdateRef.current.scrollIntoView('updatetablescreen')
        ShowMessgeAdditionalSubmit('EMSG_34');
        return;
      }
      let valueArr = tableRef.current.getTableState().map((item) => { return item?.component });
      let isDuplicate = valueArr.some((item, idx) => {
        return valueArr.indexOf(item) != idx
      });
      if (isDuplicate) {
        tableUpdateRef.current.scrollIntoView('updatetablescreen')
        ShowMessgeAdditionalSubmit('EMSG_7', 'common.artefact.component');
        return;
      }
    } catch (err) {
      console.log(err);
    }
    
    const requestData: any = {
      "id": screenID || null,
      "name": values.name,
      "code": values.code,
      "access": getCkeditorData?.current?.props?.data,
      "mockUpScreen": attachment?.id,
      "version": values.version,
      "requirement": '',
      "reqElicitation": values.req,
      "documentation": values.documentation,
      "implementation": values.implementation,
      storage: JSON.stringify({
        textToDisplay: values?.storageLinkText || '',
        address: values?.storageWebLink || '',
      }),
      jira: JSON.stringify({
        textToDisplay: values?.jiraLinkText || '',
        address: values?.jiraWebLink || '',
      }),
      confluence: JSON.stringify({
        textToDisplay: values?.confluenceLinkText || '',
        address: values?.confluenceWebLink || '',
      }),
      "description": getCkeditorDataDes?.current?.props?.data,
      "status": isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
      "screenComponents": tableRef.current?.getTableState()?.map((e, order) => {
        return {
          "id": e.id,
          "name": e.name,
          "component": e.component,
          "componentType": e.componentType,
          "componentTypeDetail": e.componentType,
          "editable": e.editable,
          "order": order,
          "mandatory": e.mandatory,
          "defaultValue": e.defaultValue,
          "description": e.description,
          "targetScreenId": e.screen?.id ? e.screen?.id : e.screen,
          "sourceObjectId": e?.objectScreenComponent?.id ? e.objectScreenComponent?.id : e.objectScreenComponent,
          "sourceObjectPropertyId": e.sourceObjectProperties && e.sourceObjectProperties[0] ? e.sourceObjectProperties[0].id : e.sourceObjectProperties,
          "targetUseCaseId": e?.useCase
        }
      }),
      "baObjectIds": values.baObjects,
      "actorIds": values.actors,
      "functionIds": values.functions,
      // "messageIds": values.messages,
      // "emailTemplateIds": values.emailTemplates,
      "userRequirementIds": values.userRequirements,
      "otherRequirementIds": values.otherRequirements,
      "author": ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
      "reviewer": values.reviewer || '',
      "customer": values.customer || '',
      "dueDate": values.dueDate ? values.dueDate?.toDate() : null,
      "completeDate": values.completeDate ? values.completeDate?.toDate() : null,
      mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
      impacts: impacts
    }
    
    setIsCreateMore(values.createMore);
    if (isDraft) {
      requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;

      dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
    } else {
      if (requestData.status === STATUS.SUBMITTED || requestData.status === STATUS.ENDORSE) {
        if (!attachment?.id) {
          attachmentRef.current.scrollIntoView('file')
          ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.screen')
          return
        }
        if (!requestData.functionIds || requestData.functionIds?.length === 0) {
          useCaseRef.current.scrollIntoView('functions')
          ShowMessgeAdditionalSubmit('EMSG_14');
          return
        }
        if (!requestData.screenComponents || requestData.screenComponents?.length === 0) {
          tableUpdateRef.current.scrollIntoView('updatetablescreen')
          ShowMessgeAdditionalSubmit('EMSG_15')
          return
        }
      }
      confirm({
        ...modalConfirmConfig,
        content: intl.formatMessage(
          { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
          { Artefact: intl.formatMessage({ id: 'common.artefact.screen' }) }
        ),
        onOk() {
          requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
          dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        },
        onCancel() {

        },
      })
    }
  }, 500)


  const onFinishFailed = (errorInfo: any) => { }

  const confirmCancel = () => {
    confirm({
      ...modalConfirmConfig,
      content: intl.formatMessage({ id: 'CFD_3' }),
      onOk() {
        onDismiss();
      },
      onCancel() { },
    })
  }

  const resetForm = () => {
    setIsCreateMore(false);
    setIsDraft(null);
    setAccess('')
    setChoosedObj([])
    form.resetFields([
      'name',
      // 'createMore',
      'version',
      'code',
      'baObjects',
      'description',
      'access',
      'updatetablescreen',
      'actors',
      'mockUpScreen',
      'useCases',
      'functions',
      'userRequirements',
      'otherRequirements',
      'req',
      'documentation',
      'storageLinkText',
      'storageWebLink',
      'jiraLinkText',
      'jiraWebLink',
      'confluenceLinkText',
      'confluenceWebLink',
      'reviewer',
      'dueDate',
      'completeDate'
    ])
    form.setFieldsValue({
      assignee: currentUserName()
    })
    setAttachment(null);
  }

  //#region COMMENT INIT

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState;

  useEffect(() => {
    if (!state.detail?.id || commentState.isLoading) {
      return;
    }

    const fields: { field, title }[] = [
      { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
      { field: 'object', title: intl.formatMessage({ id: 'view-screen-list.label.object' }), },
      { field: 'description', title: intl.formatMessage({ id: 'view-screen-details.screen-info.description' }), },
      { field: 'access', title: intl.formatMessage({ id: 'view-screen-details.screen-info.access' }), },
      { field: 'mockup-screen', title: intl.formatMessage({ id: 'view-screen-details.screen-info.mockup-screen' }), },
      { field: 'screen-description', title: intl.formatMessage({ id: 'view-screen-details.screen-info.screen-description' }), },
      { field: 'use-case', title: intl.formatMessage({ id: 'view-screen-list.label.use-case' }), },
      { field: 'actor', title: intl.formatMessage({ id: 'view-screen-list.label.actor' }), },
      // { field: 'message', title: intl.formatMessage({ id: 'view-screen-list.label.message' }), },
      // { field: 'email-template', title: intl.formatMessage({ id: 'view-screen-list.label.email-template' }), },
      { field: 'requirement', title: intl.formatMessage({ id: 'view-screen-list.label.requirement' }), },
      { field: 'other-requirement', title: intl.formatMessage({ id: 'view-screen-list.label.other-requirement' }), },
      { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
      { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
      { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
      { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
      { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
      { field: 'req-elicitation', title: intl.formatMessage({ id: 'view-screen-list.label.req.' }), },
      { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
      { field: 'implementation', title: intl.formatMessage({ id: 'view-screen-list.label.implementation' }), },
      { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
      { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
      { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
    ];
    state.detail?.screenComponents?.forEach((e) => {
      fields.push({ field: e.id ? e.id.toString() : '', title: e?.component })
    })

    dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

    const payload = {
      projectId: state.detail.projectId,
      itemId: state.detail.id,
      artefact: ARTEFACT_COMMENT.SCREEN,
      fields: fields.map(o => o.field)
    };
    dispatch(initCommentScreen(payload));
  }, [state.detail])

  //#endregion COMMENT INIT
  const handleaddScComponent = () => {

  }

  const tagRender = (props) => {
    const { label, name, value, closable, onClose } = props;


    return (
      <Tag
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 3,
          border: 'none',
        }}
        title={label}
      >
        {label.length > 20 ? label.substring(0, 20) + '...' : label}
      </Tag>
    );
  };

  return <Spin spinning={state?.isLoading}>
    <Form
      form={form}
      name=""
      labelCol={{ offset: 0, span: 2 }}
      onFinish={onSubmit}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      scrollToFirstError={{ block: 'center' }}
    >
      <div className='rq-modal-header'>
        <LavPageHeader
          showBreadcumb={false}
          title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'createscreen.page_create_title' : 'createscreen.page_update_title' })}
        >
          <Space size="small">
            {screenMode === SCREEN_MODE.CREATE ? <Form.Item
              style={{ marginBottom: '0px' }}
              valuePropName="checked"
              name="createMore"
              wrapperCol={{ span: 24 }}
            >
              <Checkbox disabled={state.isLoading}>{intl.formatMessage({ id: 'common.action.create-another' })}</Checkbox>
            </Form.Item> : <></>}
            <Button onClick={debounce(confirmCancel, 500)}>
              {intl.formatMessage({ id: 'common.action.close' })}
            </Button>

            {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
              <Button disabled={state.isLoading} type="primary" ghost htmlType="submit" onClick={() => {
                setIsDraft(false)
                setIsSubmitForm(true)
              }}>
                {intl.formatMessage({ id: 'common.action.submit' })}
              </Button> : <></>
            }

            <Button disabled={state.isLoading} onClick={() => {
              setIsDraft(true)
              setIsSubmitForm(true)
            }} className="success-btn" htmlType="submit">
              {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
            </Button>
          </Space>

        </LavPageHeader>
      </div>

      <Row align="middle">
        {screenMode === SCREEN_MODE.EDIT ?
          <Col span={5}>
            <div className='status-container'>
              <div>
                {intl.formatMessage({ id: 'common.field.status' })}
              </div>
              <div>
                {renderStatusBadge(state.detail?.status)}
              </div>
            </div>
          </Col> : <></>
        }
      </Row>
      <Spin spinning={state.isLoading}>
        <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
          <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.screen-information' })}>
            {
              screenMode === SCREEN_MODE.EDIT ?
                <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                  <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input maxLength={255} disabled />
                  </Form.Item>
                </FormGroup>
                : <></>
            }


            <FormGroup
              inline
              required
              label={intl.formatMessage({ id: 'common.label.name' })}
              labelSpan={3}
              controlSpan={21}
            >
              <Form.Item
                name="name"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'IEM_1' }),
                  },
                  { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                  {
                    validator: async (rule, value) => {
                      if (value && value.trim().length === 0) {
                        throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                      }
                    },
                  },
                ]}
              >
                <Input
                  maxLength={255}
                  placeholder={`${intl.formatMessage({
                    id: `createscreen.input-placehold.screen-name`,
                  })}${intl.formatMessage({
                    id: `common.mandatory.*`,
                  })}`}
                />
              </Form.Item>
            </FormGroup>

            <FormGroup inline controlSpan={21} labelSpan={3} className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="object">
                {intl.formatMessage({ id: 'createscreen.label.object' })}
              </TriggerComment>}>
              <Form.Item name="baObjects">
                <Select
                  filterOption={(input, option: any) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                  showSearch
                  mode="multiple"
                  optionLabelProp="label"
                  tagRender={tagRender}
                >
                  {state.listObjects!.length > 0 &&
                    state.listObjects?.map(
                      (item: any, index: number) =>
                        item.status !== STATUS.DELETE &&
                        item.status !== STATUS.CANCELLED && (
                          <Option key={index} value={item.id} label={item.name}>
                            {item.name}
                          </Option>
                        )
                    )}
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup inline controlSpan={21} labelSpan={3} className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="description">
                {intl.formatMessage({ id: 'createobject.label.description' })}
              </TriggerComment>}>
              <Form.Item
                name="description"
                labelAlign="left"
                rules={[{
                  validator: async (rule, value) => {
                    const description = getCkeditorDataDes?.current?.props?.data
                    if ((description == '' || description == undefined) && (!isDraft || state.detail?.status === STATUS.ENDORSE || state.detail?.status === STATUS.SUBMITTED)) {
                      throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                    }
                  }
                }]}
                wrapperCol={{ span: 24 }}
              >
                <CkeditorMention
                  ref={getCkeditorDataDes}
                  data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
                />
              </Form.Item>
            </FormGroup>

            <FormGroup inline controlSpan={21} labelSpan={3} className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="access">
                {intl.formatMessage({ id: 'createscreen.label.access' })}
              </TriggerComment>}>
              <Form.Item name="access">
                <CkeditorMention
                  ref={getCkeditorData}
                  data={access || ''}
                />
              </Form.Item>
            </FormGroup>

            <div ref={attachmentRef}>
              <FormGroup className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="mockup-screen">
                  {intl.formatMessage({ id: 'createscreen.label.mockup' })}
                </TriggerComment>}>
                <Form.Item name="mockUpScreen">
                  <LavAttachmentUpload artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN} attachment={attachment} isCommon={false} name="file" supportPDF onChange={setAttachment} />
                </Form.Item>
              </FormGroup>
            </div>

            <div ref={tableUpdateRef} style={{ marginBottom: '10px' }}>
              <ScreenComponents
                isCreateMore={isCreateMore}
                form={form}
                ref={tableRef}
                isCreate={screenMode != SCREEN_MODE.EDIT}
                choosedObj={choosedObj}
              />
            </div>
          </Card>

          <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.reference' })} >
            <FormGroup inline controlSpan={21} labelSpan={3} className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="actor">
                {intl.formatMessage({ id: 'createscreen.label.actor' })}
              </TriggerComment>}>
              <Form.Item name="actors">
                <Select
                  mode="multiple"
                  filterOption={(input, option: any) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                  showSearch
                  showArrow
                  optionLabelProp="label"
                  tagRender={tagRender}
                >
                  {state.listActors?.map((item: any) => (
                    <Option key={item.id} value={item.id} label={item.name}>
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </FormGroup>
            <div ref={useCaseRef}>
              <FormGroup inline controlSpan={21} labelSpan={3} className="rq-fg-comment" label={
                <TriggerComment screenMode={screenMode} field="use-case">
                  {intl.formatMessage({ id: 'createscreen.label.usecase' })}
                </TriggerComment>}>
                <Form.Item name="functions" wrapperCol={{ span: 24 }}>
                  <Select mode="multiple"
                    filterOption={(input, option: any) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                    showSearch
                    optionLabelProp="label"
                    tagRender={tagRender}
                  >
                    {state.listFunctions!.length > 0 &&
                      state.listFunctions?.map(
                        (item: any, index: number) =>
                          item.status !== STATUS.DELETE &&
                          item.status !== STATUS.CANCELLED && (
                            <Option key={index} value={item.id} label={item.name}>
                              {item.name}
                            </Option>
                          )
                      )}
                  </Select>
                </Form.Item>
              </FormGroup>
            </div>

            <FormGroup inline controlSpan={21} labelSpan={3} className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="requirement">
                {intl.formatMessage({ id: 'createscreen.label.user-requirement' })}
              </TriggerComment>}>
              <Form.Item name="userRequirements" wrapperCol={{ span: 24 }}>
                <Select mode="multiple" optionLabelProp="label" tagRender={tagRender}>
                  {state.listUserRequirements?.map(
                    (item: any, index: number) =>
                      item.status !== STATUS.DELETE &&
                      item.status !== STATUS.CANCELLED && (
                        <Option key={index} value={item.id} label={item.name}>
                          {item.name}
                        </Option>
                      )
                  )}
                </Select>
              </Form.Item>
            </FormGroup>

            <FormGroup inline controlSpan={21} labelSpan={3} className="rq-fg-comment" label={
              <TriggerComment screenMode={screenMode} field="other-requirement">
                {intl.formatMessage({ id: 'createscreen.label.other-requirement' })}
              </TriggerComment>}>
              <Form.Item name="otherRequirements" wrapperCol={{ span: 24 }}>
                <Select mode="multiple">
                  {state.listOtherRequirements!.length > 0 &&
                    state.listOtherRequirements?.map(
                      (item: any, index: number) =>
                        item.status !== STATUS.DELETE &&
                        item.status !== STATUS.CANCELLED && (
                          <Option key={index} value={item.id}>
                            {item.name}
                          </Option>
                        )
                    )}
                </Select>
              </Form.Item>
            </FormGroup>
          </Card>

          <AssignTaskComponent
            form={form}
            data={screenMode === SCREEN_MODE.EDIT ?
              state.detail :
              null}
            isSubmit={isDraft == false}
            screenMode={screenMode}
          />
          {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.SCREEN} onChange={onChange} isSubmitForm={isSubmitForm} />}
          <LavEffortEstimationForm
            screenMode={screenMode}
            hasDevelopment={state?.detail?.hasOwnProperty('development')}
            hasImplementation={state?.detail?.hasOwnProperty('implementation')}
          />
          {/* <Card className='rq-form-block' title={intl.formatMessage({ id: 'createscreen.card-title.effort' })}>
              <FormGroup className="rq-fg-comment" inline label={
                <TriggerComment screenMode={screenMode} field="req-elicitation">
                  {intl.formatMessage({ id: 'createscreen.label.req' })}
                </TriggerComment>} controlSpan={4}>
                <Form.Item name="reqElicitation">
                  <InputNumber
                    className="input-full-width"
                    min={0}
                    maxLength={2}
                  />
                </Form.Item>
              </FormGroup>

              <FormGroup className="rq-fg-comment" inline label={
                <TriggerComment screenMode={screenMode} field="documentation">
                  {intl.formatMessage({ id: 'createscreen.label.documentation' })}
                </TriggerComment>} controlSpan={4}>
                <Form.Item name="documentation">
                  <InputNumber
                    className="input-full-width"
                    min={0}
                    maxLength={2}
                  />
                </Form.Item>
              </FormGroup>

              <FormGroup className="rq-fg-comment" inline label={
                <TriggerComment screenMode={screenMode} field="implementation">
                  {intl.formatMessage({ id: 'createscreen.label.implementation' })}
                </TriggerComment>} controlSpan={4}>
                <Form.Item name="implementation">
                  <InputNumber
                    className="input-full-width"
                    min={0}
                    maxLength={2}
                  />
                </Form.Item>
              </FormGroup>
            </Card> */}

          <LavRelatedLinksForm form={form} screenMode={screenMode} />
          {
            screenMode === SCREEN_MODE.EDIT ?
              <LavVersion screenMode={screenMode} data={state?.detail?.versionHistories} form={form} /> : <></>
          }
        </Space>
      </Spin>
      {/* </Scrollbars> */}
    </Form>
  </Spin>
}
export default MockupScreenFormPage
