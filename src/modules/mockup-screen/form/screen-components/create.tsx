import AppState from '@/store/types'
import { PlusOutlined } from '@ant-design/icons'
import {
    AutoComplete,
    Button, Checkbox, Col, Form, Input, Modal, Row, Select, Typography
} from 'antd'
import { createRef, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { COMP_TYPE, ROW_STATUS, SCREEN_MODE, STATUS } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import { getListId, Regex } from '../../../../helper/share/type'
import { getListObjectPropertiesRequest } from '../../action'
import { MockupScreenState } from '../../type'
import useModalConfirmationConfig from './../../../../helper/hooks/useModalConfirmationConfig';

const { Option } = Select
const { Text } = Typography
const { confirm } = Modal
const CreateScreenComponent = (props) => {
    const create_state = useSelector<AppState | null>(
        (s) => s?.MockupScreen
    ) as MockupScreenState
    const dispatch = useDispatch()
    const [visible, setVisible] = useState(false)
    const [createOther, setCreateOther] = useState(false)
    const [form] = Form.useForm()
    const [listRefProperty, setListRefProperty] = useState<any[]>([])
    const getCkeditorData: any = createRef()
    const [modalAction, setModalAction] = useState<any[]>([])
    const [selectCompType, setSelectCompType] = useState<string>('')
    const modalConfirmConfig = useModalConfirmationConfig()

    useEffect(() => {
        setListRefProperty(create_state.listObjectProperties || [])
    }, [create_state.listObjectProperties, create_state.listFunctionsId])

    useEffect(() => {
        // case edit
        if (visible && props.type === SCREEN_MODE.EDIT) {
            const editData = props.editData
            // case edit new screen component
            if (editData.status !== ROW_STATUS.CREATE) {
                if (editData.objectScreenComponent) {
                    // case pick object
                    dispatch(getListObjectPropertiesRequest(editData.objectScreenComponent?.id ? editData.objectScreenComponent?.id : editData.objectScreenComponent));
                    setSelectCompType(editData.componentType)
                    form.setFieldsValue({
                        component: editData.component,
                        componentType: editData.componentType
                            ? editData.componentType
                            : undefined,
                        objectScreenComponent: editData.objectScreenComponent
                            ? editData.objectScreenComponent.id
                                ? editData.objectScreenComponent.id
                                : editData.objectScreenComponent
                            : undefined,
                        sourceObjectProperties: editData.sourceObjectProperties
                            ? editData.sourceObjectProperties[0]
                                ? editData.sourceObjectProperties[0].id
                                : editData.sourceObjectProperties
                            : undefined,
                        screen: editData.screen
                            ? editData.screen.id
                                ? editData.screen.id
                                : editData.screen
                            : undefined,
                        useCase: editData.useCase
                            ? editData.useCase.id
                                ? editData.useCase.id
                                : editData.useCase
                            : undefined,
                        editable: editData.editable,
                        mandatory: editData.mandatory,
                        defaultValue: editData.defaultValue,
                    })
                } else {
                    // case don't pick object
                    form.setFieldsValue({
                        component: editData.component,
                        componentType: editData.componentType
                            ? editData.componentType
                            : undefined,
                        editable: editData.editable,
                        mandatory: editData.mandatory,
                        defaultValue: editData.defaultValue,
                    })
                    setSelectCompType(editData.componentType)
                }
            } else {
                // case edit new screen component get from server
                if (editData.objectScreenComponent) dispatch(getListObjectPropertiesRequest(editData.objectScreenComponent?.id ? editData.objectScreenComponent?.id : editData.objectScreenComponent));
                setSelectCompType(editData.componentType)
                form.setFieldsValue({
                    component: editData.component,
                    componentType: editData.componentType,
                    objectScreenComponent: editData.objectScreenComponent
                        ? editData.objectScreenComponent
                        : undefined,
                    sourceObjectProperties: editData.sourceObjectProperties
                        ? editData.sourceObjectProperties
                        : undefined,
                    editable: editData.editable,
                    mandatory: editData.mandatory,
                    defaultValue: editData.defaultValue,
                })
            }
            setModalAction([
                <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
                    {intl.formatMessage({ id: `common.action.cancel` })}
                </Button>,
                <Button key="5" onClick={form.submit}>
                    {intl.formatMessage({ id: 'common.action.update' })}
                </Button>,
            ])
        } else if (visible && props.type == SCREEN_MODE.CREATE) {
            // case add new screen des
            setListRefProperty(listRefProperty ? listRefProperty : [])
            setModalAction([
                <Button key="4" style={{ float: 'left' }} onClick={handleCancel}>
                    {intl.formatMessage({ id: `common.action.cancel` })}
                </Button>,
                <Checkbox key="3" checked={createOther} onChange={changeCreateOther}>
                    {intl.formatMessage({ id: 'createobject.checkbox.create-another' })}
                </Checkbox>,
                <Button type='primary' className='success-btn' key="5" onClick={form.submit}>
                    {intl.formatMessage({ id: 'createscreen.button.add' })}
                </Button>,
            ])
        }
    }, [visible, createOther, create_state.listObjects])

    useEffect(() => {
        if (visible && props.type == SCREEN_MODE.CREATE) {
            setSelectCompType('');
            form.resetFields()
        }
    }, [visible])

    const showModal = () => {
        setVisible(true)
    }

    const handleCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                form.resetFields()
                setVisible(false)
                setCreateOther(false)
            },
            onCancel() { },
        })
    }

    const getSourceObject = (id) => {
        const obj = create_state.listObjects?.find((e) => e.id === id);
        return obj || null
    }

    const getSourceObjectProperty = (id) => {
        const obj = create_state.listObjectProperties?.find((e) => e.id === id);
        return obj || null
    }
    const getSourceUseCase = (id) => {
        const obj = create_state.listFunctionsId?.find((e) => e.id === id);
        return obj || null
    }
    const onFinish = (values: any) => {
        let data = ''

        if (getCkeditorData?.current?.props?.data) {
            data = getCkeditorData.current?.props?.data
        }
        // 1 get list object
        const regex = Regex.GET_LIST_OBJECT_FROM_CK
        const regexGetId = Regex.GET_LIST_OBJECT_ID
        const foundObject = data.match(regex)
        const listObjectId = getListId(regexGetId, foundObject)
        // 2 get list usecase
        const regexUseCase = Regex.GET_LIST_USE_CASE_FROM_CK
        const regexGetIdUseCase = Regex.GET_LIST_USE_CASE_ID
        const foundUsecase = data.match(regexUseCase)
        const listUseCaseId = getListId(regexGetIdUseCase, foundUsecase)

        // 3 get list actor
        const regexActor = Regex.GET_LIST_ACTOR_FROM_CK
        const regexGetIdActor = Regex.GET_LIST_ACTOR_ID
        const foundActor = data.match(regexActor)
        const listActorId = getListId(regexGetIdActor, foundActor)

        // 4 get list screen
        const regexScreen = Regex.GET_LIST_SCREEN_FROM_CK
        const regexGetIdScreem = Regex.GET_LIST_SCREEN_ID
        const foundScreen = data.match(regexScreen)
        const listScreenId = getListId(regexGetIdScreem, foundScreen)

        // 5 get list workflow
        const regexWorkFlow = Regex.GET_LIST_WORK_FLOW_FROM_CK
        const regexGetIdWorkFlow = Regex.GET_LIST_WORK_FLOW_ID
        const foundWorkFlow = data.match(regexWorkFlow)
        const listWorkFlowId = getListId(regexGetIdWorkFlow, foundWorkFlow)

        // 6 get list stateTrnasition
        const regexStatetransition = Regex.GET_LIST_STATE_TRANSITION_FROM_CK
        const regexGetIdStatetransition = Regex.GET_LIST_STATE_TRANSITION_ID
        const foundStatetransition = data.match(regexStatetransition)
        const listStatetransitionId = getListId(
            regexGetIdStatetransition,
            foundStatetransition
        )

        // 7 get list mess
        const regexMessage = Regex.GET_LIST_MESS_FROM_CK
        const regexGetIdMessage = Regex.GET_LIST_MESS_ID
        const foundMessage = data.match(regexMessage)
        const listMessageId = getListId(regexGetIdMessage, foundMessage)

        // 8 get list email
        const regexMail = Regex.GET_LIST_EMAIL_FROM_CK
        const regexGetIdMail = Regex.GET_LIST_EMAIL_ID
        const foundMail = data.match(regexMail)
        const listMailId = getListId(regexGetIdMail, foundMail)

        //9 get list other Requi
        const regexOtherReq = Regex.GET_LIST_OTHER_REQUIREMENT_FROM_CK
        const regexGetIDOtherReq = Regex.GET_LIST_OTHER_REQUIREMENT_ID
        const foundOtherReq = data.match(regexOtherReq)
        const listOtherReq = getListId(regexGetIDOtherReq, foundOtherReq)

        const sendDataToServer = {
            listObject: listObjectId,
            listUseCase: listUseCaseId,
            listActor: listActorId,
            listScreen: listScreenId,
            listWorkFlow: listWorkFlowId,
            listStateTransition: listStatetransitionId,
            listMess: listMessageId,
            listEmail: listMailId,
            listOtherReq: listOtherReq,
        }

        if (props.type === SCREEN_MODE.CREATE) {
            if (values.componentType !== COMP_TYPE.TABLE) {
                const column = {
                    ...values,
                    editable: values.editable || false,
                    mandatory: values.mandatory || false,
                    description: `<div>
                       ${getCkeditorData?.current?.props?.data}${values?.objectScreenComponent ? '-Source Object: ' + getSourceObject(values.objectScreenComponent)?.name : ''}  <br/> ${values.sourceObjectProperties ? '-Source Object Property: ' + getSourceObjectProperty(values.sourceObjectProperties)?.name : ''}
                    </div>`,
                    id: null,
                    status: ROW_STATUS.CREATE,
                    object: getSourceObject(values.objectScreenComponent),
                    objectProperty: getSourceObjectProperty(values.sourceObjectProperties),
                    useCase: getSourceUseCase(values.useCase),
                    ...sendDataToServer,
                }
                props.addComponent(column)
                form.resetFields()
            } else {
                const column = {
                    ...values,
                    description: `<div>
                       ${getCkeditorData?.current?.props?.data}${values?.objectScreenComponent ? '-Source Object: ' + getSourceObject(values.objectScreenComponent)?.name : ''}<br/>${values.sourceObjectProperties ? '-Source Object Property: ' + getSourceObjectProperty(values.sourceObjectProperties)?.name : ''}
                    </div>`,
                    id: null,
                    status: ROW_STATUS.CREATE,
                    object: getSourceObject(values.objectScreenComponent),
                    objectProperty: getSourceObjectProperty(values.sourceObjectProperties),
                    useCase: getSourceUseCase(values.useCase),
                    ...sendDataToServer,
                }
                props.addComponent(column)
            }
            // setCreateOther(false)
            form.resetFields()
            if (createOther === false) {
                setVisible(false)
            }
        } else if (props.type === SCREEN_MODE.EDIT) {
            const column = {
                ...values,
                description: getCkeditorData?.current?.props?.data,
                status:
                    props.editData.status !== ROW_STATUS.CREATE
                        ? ROW_STATUS.UPDATE
                        : props.editData.status,
                object: getSourceObject(values.objectScreenComponent),
                objectProperty: getSourceObjectProperty(values.sourceObjectProperties),
                useCase: getSourceUseCase(values.useCase),
                ...sendDataToServer,
            }
            props.editDataHandle(column, props.index)
            setVisible(false)
            form.resetFields()
        }

        if (createOther) {
            setSelectCompType('');
            form.resetFields()
            setListRefProperty([])
        }
    }

    const onFinishFailed = (errorInfo: any) => { }

    const changeSelectCompType = (e) => {
        setSelectCompType(e)
    }

    const changeObject = (e) => {
        form.setFieldsValue({
            [`sourceObjectProperties`]: undefined,
        })
        setListRefProperty([])
        if (e) {
            dispatch(getListObjectPropertiesRequest(e));
        }
    }

    const changeCreateOther = (e) => {
        setCreateOther(e.target.checked)
    }

    return (
        <>
            {props.type === SCREEN_MODE.CREATE && (
                <Button icon={<PlusOutlined />} type="link" onClick={showModal}>
                    {intl.formatMessage({ id: 'createscreen.button.new-component' })}
                </Button>
            )}
            {props.type === SCREEN_MODE.EDIT && (
                <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={showModal}></Button>
            )}
            <Form
                form={form}
                id={Date.now().toString()}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
                scrollToFirstError={{ block: 'center' }}
            >
                {visible && (
                    <Modal
                        width={700}
                        onCancel={handleCancel}
                        title={intl.formatMessage({ id: props.type === SCREEN_MODE.CREATE ? 'view-screen-list.modal.title-add-screen-component' : 'view-screen-list.modal.title-update-screen-component' })}
                        visible={visible}
                        footer={modalAction}
                        maskClosable={false}

                    >
                        <Row gutter={[16, 4]}>
                            <Col span={5}>
                                <Text>
                                    {intl.formatMessage({ id: 'createscreen.label-modal.component' })}
                                </Text>
                                <Text type="danger">
                                    {intl.formatMessage({
                                        id: `common.mandatory.*`,
                                    })}
                                </Text>
                            </Col>
                            <Col span={7}>
                                <Form.Item
                                    name="component"
                                    validateTrigger="onBlur"
                                    rules={[
                                        {
                                            required: true,
                                            message: intl.formatMessage({ id: 'IEM_1' }),
                                        },
                                        { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                        {
                                            validator: async (rule, value) => {
                                                if (value && value.trim().length === 0) {
                                                    throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                                }
                                                if (value && value.length > 0) {
                                                    if (props.type === SCREEN_MODE.EDIT) {
                                                        const dupplicate = props.currentData?.findIndex(
                                                            (item: any) =>
                                                                item.component
                                                                    .toLowerCase()
                                                                    .replace(/\s+/g, ' ')
                                                                    .trim() ===
                                                                value.toLowerCase().replace(/\s+/g, ' ').trim()
                                                        )
                                                        if (
                                                            props.editData &&
                                                            value
                                                                .toLowerCase()
                                                                .replace(/\s+/g, ' ')
                                                                .trim() !==
                                                            props.editData.component
                                                                .toLowerCase()
                                                                .replace(/\s+/g, ' ')
                                                                .trim() &&
                                                            dupplicate !== -1
                                                        ) {
                                                            throw new Error(
                                                                `${intl.formatMessage(
                                                                    { id: 'EMSG_7' },
                                                                    {
                                                                        Artefact: `${intl.formatMessage({
                                                                            id: 'common.artefact.component',
                                                                        })}`,
                                                                    }
                                                                )}`
                                                            )
                                                        }
                                                    } else if (props.type === SCREEN_MODE.CREATE) {
                                                        const dupplicate = props.currentData?.findIndex(
                                                            (item: any) =>
                                                                item.component
                                                                    .toLowerCase()
                                                                    .replace(/\s+/g, ' ')
                                                                    .trim() ===
                                                                value.toLowerCase().replace(/\s+/g, ' ').trim()
                                                        )

                                                        if (dupplicate !== -1) {
                                                            throw new Error(
                                                                `${intl.formatMessage(
                                                                    { id: 'EMSG_7' },
                                                                    {
                                                                        Artefact: `${intl.formatMessage({
                                                                            id: 'common.artefact.component',
                                                                        })}`,
                                                                    }
                                                                )}`
                                                            )
                                                        }
                                                    }
                                                }
                                            },
                                        },
                                    ]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </Col>

                            <Col span={5}>
                                <Text>
                                    {intl.formatMessage({
                                        id: 'createscreen.label-modal.compType',
                                    })}
                                </Text>
                            </Col>
                            <Col span={7}>
                                <Form.Item name="componentType" rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                    {/* <Select
                                        filterOption={(input, option: any) =>
                                            option.children
                                                .toLowerCase()
                                                .indexOf(input.toLowerCase()) >= 0
                                        }
                                        showSearch
                                        allowClear
                                        onChange={changeSelectCompType}
                                    >
                                        {props.compType?.map((item: any) => (
                                            <Option key={item.id} value={item.id}>
                                                {item.name}
                                            </Option>
                                        ))}
                                    </Select> */}

                                    <AutoComplete
                                        maxLength={255}
                                        options={props.compType}
                                        defaultValue={props.data?.name}
                                        onSelect={(value, option) => {
                                            setSelectCompType(value)
                                        }}
                                        onChange={(value) => {
                                            setSelectCompType(value)
                                        }}
                                    />
                                </Form.Item>
                            </Col>

                            {selectCompType !== 'Table' && (<><Col span={5}>
                                <Text>
                                    {intl.formatMessage({
                                        id: 'createscreen.label-modal.object',
                                    })}
                                </Text>
                            </Col>
                                <Col span={7}>
                                    <Form.Item name="objectScreenComponent">
                                        <Select
                                            filterOption={(input, option: any) =>
                                                option.children
                                                    .toLowerCase()
                                                    .indexOf(input.toLowerCase()) >= 0
                                            }
                                            showSearch
                                            allowClear
                                            onChange={changeObject}
                                        >
                                            {props?.objects?.map(
                                                (item: any) =>
                                                    item.status !== STATUS.DELETE &&
                                                    item.status !== STATUS.CANCELLED && (
                                                        <Option key={item.id} value={item.id}>
                                                            {item.name}
                                                        </Option>
                                                    )
                                            )}
                                        </Select>
                                    </Form.Item>
                                </Col>
                                {' '}
                                <Col span={5}>
                                    <Text>
                                        {intl.formatMessage({
                                            id: 'createscreen.label-modal.object-property',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={7}>
                                    <Form.Item name="sourceObjectProperties" rules={[
                                        {
                                            validator: async (rule, value) => {
                                                const sourceObj = form.getFieldValue('objectScreenComponent');
                                                if (sourceObj != undefined && !value) {
                                                    throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                                }
                                            }
                                        }

                                    ]}>
                                        <Select showSearch allowClear>
                                            {listRefProperty?.map((item: any) => (
                                                <Option key={item.id} value={item.id}>
                                                    {item.name}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span={5}>
                                    <Text>
                                        {intl.formatMessage({
                                            id: 'createscreen.label-modal.editable',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={1}>
                                    <Form.Item
                                        style={{ marginBottom: '0px' }}
                                        valuePropName="checked"
                                        name="editable"
                                    >
                                        <Checkbox key="1"></Checkbox>
                                    </Form.Item>
                                </Col>
                                <Col span={6}></Col>
                                <Col span={5}>
                                    <Text>
                                        {intl.formatMessage({
                                            id: 'createscreen.label-modal.mandatory',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={1}>
                                    <Form.Item
                                        style={{ marginBottom: '0px' }}
                                        valuePropName="checked"
                                        name="mandatory"
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Checkbox key="2"></Checkbox>
                                    </Form.Item>
                                </Col>
                                <Col span={6}></Col>
                                <Col span={5}>
                                    <Text>
                                        {intl.formatMessage({
                                            id: 'createscreen.label-modal.default',
                                        })}
                                    </Text>
                                </Col>
                                <Col span={7}>
                                    <Form.Item name="defaultValue" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                        <Input maxLength={255} />
                                    </Form.Item>
                                </Col>
                            </>
                            )}

                            <Col span={24}>
                                <Text>
                                    {intl.formatMessage({
                                        id: 'createscreen.label-modal.description',
                                    })}
                                </Text>
                            </Col>
                            <Col span={24}>
                                <Form.Item name="ckeditor">
                                    <CkeditorMention
                                        ref={getCkeditorData}
                                        data={props?.editData?.description}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Modal>
                )}
            </Form>
        </>
    )
}

export default CreateScreenComponent
