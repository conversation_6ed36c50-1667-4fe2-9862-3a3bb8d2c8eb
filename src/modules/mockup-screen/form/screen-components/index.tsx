import AppState from '@/store/types'
import { CheckOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Button, Checkbox, Form, Input, Modal, Row, Select, Space, Spin, Table,
  Typography
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import React, { useEffect, useImperativeHandle, useState } from 'react'
import ReactDragListView from 'react-drag-listview'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../../config/locale.config'
import { COMP_TYPE_LIST, DEFAULT_DATA_SCREEN_COMPONENT, ROW_STATUS, SCREEN_MODE, STATUS } from '../../../../constants'
import CkeditorMention from '../../../../helper/component/ckeditor-mention'
import CustomSvgIcons from '../../../../helper/component/custom-icons'
import CustomAutocomplete from '../../../../helper/component/customAutocomplete/autocomplete'
import FormGroup from '../../../../helper/component/form-group'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import { ShowMessgeAdditionalSubmit } from '../../../../helper/share'
import TriggerComment from '../../../../modules/_shared/comment/trigger-comment'
import { getListSelectProperties } from '../../action'
import { MockupScreenState } from '../../type'
const { confirm } = Modal
const { Text } = Typography
const { Option } = Select

const ScreenComponents = React.forwardRef((props: any, ref: any) => {
  const [dataS, setDataSource] = useState<any>([])
  const [listObjectSelected, setListObjectSelected] = useState([])
  const [dataSelect, setDataSelect] = useState<any>([])
  const [dataSelected, setDataSelected] = useState<any>([])
  const [warning, setWarning] = useState<boolean>(false)
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false)
  const [oldObjectLength, setOldObjectLength] = useState<number>(0);
  const modalConfirmConfig = useModalConfirmationConfig()
  const dispatch = useDispatch()
  const state = useSelector<AppState | null>(
    (s) => s?.MockupScreen
  ) as MockupScreenState


  useEffect(() => {
    if (state?.createSuccess || state?.updateSuccess) {
      setDataSource([])
    }
  }, [state?.createSuccess, state?.updateSuccess])

  useEffect(() => {
    if (state.detail?.screenComponents && state.detail?.screenComponents.length > 0) {
      let x: any[] = state.detail.screenComponents
      let currentSC = Object.assign([], state.detail?.screenComponents);
      currentSC.sort((a: any, b: any) => { return a.order - b.order })
      currentSC.map((e: any) => {
        return {
          ...e,
          editting: false,
        }
      })
      setDataSource(transformDataSource(currentSC))
    }
  }, [state.detail?.screenComponents])

  useEffect(() => {
    let listDropdown = state?.listSelectObjectProperties?.map((e) => {
      return {
        ...e,
        component: e.name,
      }
    })
    let arr: any = listDropdown?.filter((item) => {
      return dataS?.findIndex((e) => e.component === item.name) === -1;
    });


    // currentData
    setDataSelect([
      ...arr,
      ...dataSelect
    ])
  }, [state.listSelectObjectProperties])

  useEffect(() => {
    if (props.isCreate) {
      setDataSource([])
    }
  }, [props.isCreate])

  useImperativeHandle(
    ref,
    () => ({
      getTableState: () => {
        return dataS
      },
    }),
    [dataS]
  )

  const deleteRow = (rowIndex) => {
    confirm({
      ...modalConfirmConfig,
      content: `${intl.formatMessage({ id: 'CFD_1' })}`,
      onOk() {
        let currenSource: any = Object.assign([], dataS)
        if (currenSource.length == 1) {
          currenSource = []
        } else {
          currenSource.splice(rowIndex, 1)
        }
        setDataSource(transformDataSource(currenSource))
      },
      onCancel() { },
    })
  }

  const tableType = COMP_TYPE_LIST[20].value;

  const transformDataSource = (data) => {
    let newData: any = [];
    let newIndex = 1;
    data.forEach((e: any, idx) => {
      newData.push({
        ...e,
        rowId: idx,
        rowNumber: newIndex,
        order: idx + 1,
      })
      if (e.componentType != tableType) {
        newIndex++;
      }
    })
    return newData
  }

  const changeObject = (e) => {
    setListObjectSelected(e)
    if (e.length == 0) {
      setDataSelect([])
      setOldObjectLength(0)
      return
    }


    if (e.length >= oldObjectLength) {
      setWarning(false)
      dispatch(getListSelectProperties(e[e?.length - 1]));
      setOldObjectLength(e.length)
      return
    }

    if (e.length < oldObjectLength) {
      const newDataS = dataSelect.filter(data => e.includes(data.object.id));
      setDataSelect(newDataS);
      setOldObjectLength(e.length)
      return
    }
  }

  const setEditting = (order, item) => {
    if (item?.editting && (item?.component === "" || item?.componentType === "")) {
      ShowMessgeAdditionalSubmit('EMSG_34')
      return;
    }

    if (item?.editting) {
      let valueArr = dataS.map((item) => { return item?.component });
      let isDuplicate = valueArr.some((item, idx) => {
        return valueArr.indexOf(item) != idx
      });
      if (isDuplicate) {
        ShowMessgeAdditionalSubmit('EMSG_7', 'common.artefact.component');
        return;
      }
    }
    const list = dataS.map((record, index) => {
      let nxtRecord = record;
      if (order === index) {
        nxtRecord = {
          ...nxtRecord,
          editting: !nxtRecord.editting,
          
        };
      }
      return nxtRecord;
    })
    setDataSource(list)
  }
  const updateRecordScComponent = (order, partialRecord) => {
    const list = dataS.map(record => {
      let nxtRecord = record;
      if (record.order === order) {
        nxtRecord = { ...nxtRecord, ...partialRecord };
      }
      return nxtRecord;
    })
    setDataSource(list)
  };

  const handleChangeSCProperty = (order, prop, value: any) => {
    updateRecordScComponent(order, { [prop]: value });
  };

  const handleChangeScComponent = (order, prop) => ({ target }) => {
    updateRecordScComponent(order, { [prop]: target?.value });
  };
  const columns: ColumnsType<any> = [
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'createscreen.column.order' })}
        </Text>
      ),
      dataIndex: 'order',
      align: 'center',
      width: '3%',
      render: (text: number, item, index) => {
        return {
          children: (item.componentType === tableType ? <></> : (item.rowNumber)),
          props: {
            colSpan: 1,
          }
        }
      }
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'createscreen.column.component' })}
        </Text>
      ),
      dataIndex: 'component',
      key: 'component',
      width: '9.5%',
      render: (text, item) => {
        return {
          children: item?.editting ? 
          <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(item?.id)}>
            <Input maxLength={255} value={text} onChange={handleChangeScComponent(item?.order, 'component')}></Input> 
          </TriggerComment>
          
          : (item.componentType === tableType ? 
              <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(item?.id)}>
                <div className="tableDangerous" dangerouslySetInnerHTML={{
                  __html: `<div>${item.component}</div>${item?.description}`
                }}>
                </div>
              </TriggerComment> 
            : <TriggerComment screenMode={SCREEN_MODE.EDIT} field={JSON.stringify(item?.id)}>
                <Text>{text}</Text>
              </TriggerComment>),
          props: {
            colSpan: item.componentType === tableType ? (item?.editting ? 1 : 6) : 1,
          }
        }
      },
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'createscreen.column.component-type',
          })}
        </Text>
      ),
      dataIndex: 'componentType',
      key: 'componentType',
      width: '20%',
      render: (text, item) => {
        return {
            children: item?.editting ? <CustomAutocomplete
            order={item?.order} 
            maxLength={255}
            style={{ width: '100%' }}
            text={text}
            handleChangeSCProperty={handleChangeSCProperty}
          /> : <Text>{text}</Text>,
          props: {
            colSpan: item.componentType === tableType ? (item?.editting ? 1 : 0) : 1,
          }
        }
      },
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'createscreen.column.editable' })}
        </Text>
      ),
      dataIndex: 'editable',
      key: 'editable',
      align: 'center',
      width: '5%',
      render: (editable: boolean, item) => {

        return {
          children: item?.editting ? <Checkbox disabled={item.componentType === tableType} checked={editable} onChange={(e) => {
            handleChangeSCProperty(item?.order, 'editable', e?.target?.checked)
          }}></Checkbox> : (editable ? (
            <Checkbox checked={true} disabled />
          ) : (
            <Checkbox checked={false} disabled />
          )),
          props: {
            colSpan: item.componentType === tableType ? (item?.editting ? 1 : 0) : 1,
          }
        }
      }
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'createscreen.column.mandatory' })}
        </Text>
      ),
      dataIndex: 'mandatory',
      key: 'mandatory',
      align: 'center',
      width: '5%',
      render: (mandatory: boolean, item: any) => {
        return {
          children: item?.editting ? <Checkbox disabled={item.componentType === tableType} checked={mandatory} onChange={(e) => {
            handleChangeSCProperty(item?.order, 'mandatory', e?.target?.checked)
          }}></Checkbox> : (mandatory ? (
            <Checkbox checked={true} disabled />
          ) : (
            <Checkbox checked={false} disabled />
          )),
          props: {
            colSpan: item.componentType === tableType ? (item?.editting ? 1 : 0) : 1,
          }
        }
      },
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'createscreen.column.default-value',
          })}
        </Text>
      ),
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      width: '10%',
      render: (value, item) => {
        return {
          children: item?.editting ? <Input value={value} disabled={item.componentType === tableType} maxLength={255} width='100%' onChange={handleChangeScComponent(item?.order, 'defaultValue')}></Input> : value,
          props: {
            colSpan: item.componentType === tableType ? (item?.editting ? 1 : 0) : 1,
          }
        }
      }
    },
    {
      title: (
        <Text strong>
          {intl.formatMessage({
            id: 'createscreen.column.description',
          })}
        </Text>
      ),
      dataIndex: 'description',
      key: 'description',
      render: (text, item) => {
        return {
          children: item?.editting ?
            <CkeditorMention
              saveDataPre={(e) => {
                handleChangeSCProperty(item?.order, 'description', e)
              }}
              data={text}
            /> : <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: text }}></div>,
          props: {
            colSpan: item.componentType === tableType ? (item?.editting ? 1 : 0) : 1,
          }
        }
      },
    },

    {
      title: (
        <Text strong>
          {intl.formatMessage({ id: 'createscreen.column.action' })}
        </Text>
      ),
      width: '5%',
      align: 'center',
      dataIndex: 'order',
      key: 'action',
      className: 'rq-action',
      render: (text, item, index) => {
        return <div style={{ display: 'flex' }}>
          {
            item?.editting ? <Button icon={<CheckOutlined name="EditCustomIcon" />} type="link" onClick={() => setEditting(index, item)}></Button>
              : <Button icon={<CustomSvgIcons name="EditCustomIcon" />} type="link" onClick={() => setEditting(index, item)}></Button>
          }
          <Button type="text" icon={<CustomSvgIcons name="DeleteCustomIcon" />} onClick={() => deleteRow(item?.rowId)} />
        </div>
      },
    },
  ]

  const setNewScreenComponent = (data) => {
    let cloneData: any = [...dataS]
    cloneData = [
      ...cloneData,
      ...data,
    ]
    setDataSource(transformDataSource(cloneData))
  }

  const dragProps = {
    onDragEnd(fromIndex, toIndex) {
      const data = [...dataS]
      const item = data.splice(fromIndex, 1)[0]
      data.splice(toIndex, 0, item)
      setDataSource(transformDataSource(data))
    },
    handleSelector: 'tr',
    ignoreSelector: 'tr.ant-table-expanded-row',
    nodeSelector: 'tr.ant-table-row',
    enableScroll: true,
    scrollSpeed: 4,
  }

  const columnsSelect = [
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.object-name' }),
      dataIndex: 'name',
      width: '13%',
      render: (text, record) => {
        return record?.object?.name
      }
    },
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.object-property' }),
      dataIndex: 'name',
      width: '13%',
    },
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.unique' }),
      dataIndex: 'unique',
      width: '5%',
      render: (text: boolean) => {
        if (text === true) {
          return <CheckOutlined />
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.mandatory' }),
      dataIndex: 'mandatory',
      width: '5%',
      render: (text: boolean) => {
        if (text === true) {
          return <CheckOutlined />
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.max-length' }),
      dataIndex: 'maxLength',
      width: '10%',
    },
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.meaning' }),
      dataIndex: 'description',
      width: '27%',
    },
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.source-object' }),
      dataIndex: 'sourceObject',
      width: '15%',
      render: (text, item) => {
        return item.sourceObject?.name
      }
    },
    {
      title: intl.formatMessage({ id: 'objectSpecification.column.source-object-property' }),
      dataIndex: 'refObjectProperty',
      width: '20%',
      render: (text, item) => {
        return item.refObjectProperty?.name
      }
    },
  ]

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows) => {
      if (selectedRowKeys?.length !== 0) {
        setWarning(false)
        let list = Object.assign([], selectedRows)
        list = list.map((e) => {
          return {
            ...e,
            editable: false,
            component: e.name,
            componentType: "Single line of text",
            defaultValue: '',
            status: ROW_STATUS.CREATE,
            description: e.description,
            objectScreenComponent: e?.sourceObject?.id,
            sourceObjectProperties: e?.refObjectProperty?.id,
          }
        })
        setDataSelected(list)
      } else {
        setDataSelected([])
        setWarning(true)
      }
    },
    getCheckboxProps: (record) => ({
    }),
  };

  const SelectObjProperties = () => {
    if (dataSelected?.length === 0) {
      setWarning(true)
    } else {
      let newData = dataSelected?.map((e) => {
        delete e?.id
        return {
          ...e,
          description: `<div>
                   <p>${e.description}</p> ${e.maxLength ? '<br /> -Max Length: ' + e.maxLength : ''} ${e.unique ? '<br /> -Unique: Yes' : ''} 
                    ${e?.refObjectProperty ? '<br/> -Source Object: ' + e?.sourceObject?.name : ''} ${e?.refObjectProperty ? '<br/> -Source Object Property: ' + e?.refObjectProperty?.name : ''}
                </div>`,
          editting: false,
        }
      })
      setWarning(false)
      setNewScreenComponent(newData)
      setIsModalVisible(false)
      setListObjectSelected([])
      setDataSelect([])
      setDataSelected([])
    }
  }

  const handleaddScComponent = () => {
    setDataSource(transformDataSource([...dataS, { order: dataS.length + 1, ...DEFAULT_DATA_SCREEN_COMPONENT }]));
  }
  const handleCancel = () => {
    setDataSelect([])
    setDataSelected([])
    setListObjectSelected([])
    setIsModalVisible(false)
  }
  return (
    <>
      <FormGroup inline className="rq-fg-comment scrollButtonSC" label={
        <TriggerComment screenMode={props?.screenMode} field="screen-description">
          {intl.formatMessage({ id: 'createscreen.label.screen-description' })}
        </TriggerComment>}>
        <Form.Item name="updatetablescreen">
          <Row justify='end'>
            <Space>
              <Button type="primary" onClick={() => setIsModalVisible(true)} icon={<PlusOutlined />} >
                {
                  intl.formatMessage({
                    id: 'createscreen.button.select-objproperties',
                  })
                }
              </Button>
              <Button type="primary" onClick={handleaddScComponent} icon={<PlusOutlined />} >
                {
                  intl.formatMessage({
                    id: 'createscreen.button.new-component',
                  })
                }
              </Button>
            </Space>
          </Row>
        </Form.Item>
      </FormGroup>
      <ReactDragListView {...dragProps}>
        <Table
          bordered={true}
          columns={columns}
          pagination={false}
          dataSource={dataS}
          rowKey='rowId'
        />
      </ReactDragListView>
      <Modal
        width={1500}
        onCancel={handleCancel}
        title={intl.formatMessage({ id: 'view-screen-list.modal.title-select-object-properties' })}
        visible={isModalVisible}
        footer={[]}
        maskClosable={false}

      >
        <Spin spinning={state?.isLoading}>
          <div>
            <Select
              style={{ width: '30%' }}
              filterOption={(input, option: any) =>
                option.children
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
              value={listObjectSelected}
              showSearch
              allowClear
              mode="multiple"
              onChange={changeObject}
            >
              {state?.listObjects?.map(
                (item: any) =>
                  item.status !== STATUS.DELETE &&
                  item.status !== STATUS.CANCELLED && (
                    <Option key={item.id} value={item.id}>
                      {item.name}
                    </Option>
                  )
              )}
            </Select>
          </div>

          <div style={{ marginTop: '10px' }}>
            <Table
              locale={{ emptyText: 'NO DATA' }}
              rowSelection={{
                type: 'checkbox',
                ...rowSelection,
              }}
              bordered
              dataSource={dataSelect}
              columns={columnsSelect}
              rowKey="id"
              pagination={false}
            />
          </div>
        </Spin>

        {warning ? <Typography.Text type='danger' style={{ margin: '10px 0' }}>You have to select at least one property</Typography.Text> : <></>}

        <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
          <Button className='success-btn' onClick={() => SelectObjProperties()}>{intl.formatMessage({ id: 'common.action.add-to-table' })}</Button>
        </div>
      </Modal>
    </>
  )
})

export default ScreenComponents
