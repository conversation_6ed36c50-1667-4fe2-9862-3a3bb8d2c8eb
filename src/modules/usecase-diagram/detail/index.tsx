import intl from '../../../config/locale.config'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { API_URLS, APP_ROLES, APP_ROUTES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import { extractProjectCode, hasRole } from '../../../helper/share'
import AppState from '../../../store/types'
import { deleteFailed, getDetailFailed, getDetailRequest } from '../action'
import { UsecaseDiagramState } from '../type'
import LavLeftControl from './../../_shared/left-menu'
import RightControl from './content'
import UseCaseDiagramFormPage from '../form/form'
import HistoryScreen from '../../../modules/history'
import AppCommonService from '../../../services/app.service'
import UseCaseDiagramVersionDetails from './history/details'

const UsecaseDiagramDetail = (props) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const state = useSelector<AppState | null>((s) => s?.UseCaseDiagram) as UsecaseDiagramState;
  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [selectedHistoryRowKeys, setHistorySelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRowVersion, setSelectedRowVersion] = useState<any>(null)
  const [selectedVersionData, setSelectedVersionData] = useState<any>(null)
  const [historyLoading, setHistoryLoading] = useState(false)

  useEffect(() => {
    return () => {
      dispatch(getDetailFailed(null))
    }
  }, [])

  useEffect(() => {
    if (props?.match?.params?.useCaseDiagramID) {
      dispatch(getDetailRequest(props.match.params.useCaseDiagramID))   
      setScreenMode(SCREEN_MODE.VIEW)
    }
  }, [props])

  useEffect(() => {
    if(selectedRowVersion){
      setHistoryLoading(true);
      AppCommonService.getData(API_URLS.USE_CASE_DIAGRAMS + '/version/' + props.match.params.useCaseDiagramID +  '/' + selectedRowVersion).then((e) => {        
        setSelectedVersionData(e);        
        setHistoryLoading(false);
      }).catch(err => {
        console.log(err);  
        setHistoryLoading(false);
      })
    }
  },[selectedRowVersion])

  useEffect(() => {
    if (state.deleteSuccess) {
      dispatch(deleteFailed(null));
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DIAGRAM}`)
    }
  }, [state.deleteSuccess])

  const handleReloadData = () => {
    setReload(true);
    dispatch(getDetailRequest(props.match.params.useCaseDiagramID))
  }

  const [reload, setReload] = useState(false);
  const [isCreate, setIsCreate] = useState(false)

  const handleCreate = (items) => {
    if (isCreate) {
      setIsCreate(false)
      history.push(`${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DIAGRAM_DETAIL}` + items[0].id)
    }
  }

  return (
    <Row className='antRowHeight'>
      {
        screenMode === SCREEN_MODE.VIEW || screenMode === SCREEN_MODE.HISTORY || screenMode === SCREEN_MODE.VERSION || screenMode === SCREEN_MODE.COMPARE ?
        <>
          <Col span={5}>
            <LavLeftControl
              activeId={props.match.params.useCaseDiagramID}
              apiUrl={API_URLS.REFERENCES_USECASE_DIAGRAM}
              route={APP_ROUTES.USECASE_DIAGRAM_DETAIL}
              artefactType={REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM}
              title='ucd.column.list'
              reload={reload}
              reloadSuccess={() => setReload(false)}
              handleCreate={handleCreate}
            >
              {
                hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA) ? <Button ghost={true}
                  type='primary'
                  className='lav-btn-create'
                  icon={<PlusOutlined />}
                  onClick={() => setScreenMode(SCREEN_MODE.CREATE)} >{intl.formatMessage({ id: 'ucd.button.create-ucd' })}
                </Button> : <></>
              }
            </LavLeftControl>
          </Col>
        </>
        : <></>
      }
      {
        screenMode === SCREEN_MODE.VIEW ?
          <>            
            <Col span={19}>
              <RightControl onChange={handleReloadData} isLoading={state?.isLoading} data={state?.selectedData} useCaseDiagramID={props.match.params.useCaseDiagramID} setScreenMode={setScreenMode} isModalShow={state?.isModalShow} />
            </Col>
          </> : <></>
      }
      {
        screenMode === SCREEN_MODE.CREATE ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <UseCaseDiagramFormPage screenMode={SCREEN_MODE.CREATE} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => setScreenMode(SCREEN_MODE.VIEW)} />
          </Col> : <></>
      }
      {
        screenMode === SCREEN_MODE.EDIT ?
          <Col span={24} style={{ padding: '20px 10px 0 10px' }}>
            <UseCaseDiagramFormPage screenMode={SCREEN_MODE.EDIT} onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)} onFinish={() => {
              handleReloadData()
              setScreenMode(SCREEN_MODE.VIEW)
            }} useCaseDiagramID={props.match.params.useCaseDiagramID} />
          </Col> : <></>
      } 
      {
        screenMode === SCREEN_MODE.HISTORY ?
          <>
            <Col span={19}>
              <HistoryScreen artefact_type = "common.artefact.usecase-diagram"
                            apiURL = {API_URLS.HISTORY}
                            artefactType = {REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM}
                            onFinish={handleReloadData} pageTitle={state?.selectedData?.code + " - " + state?.selectedData?.name}
               setHistorySelectedRowKeys = {setHistorySelectedRowKeys} screenMode={SCREEN_MODE.HISTORY} 
               setSelectedRowVersion = {setSelectedRowVersion} setScreenMode={setScreenMode} 
               onDismiss={() => {
                handleReloadData()
                setScreenMode(SCREEN_MODE.VIEW)
                }} data={state?.selectedData} />
            </Col> 
          </>: <></>
      }
      {
        screenMode === SCREEN_MODE.VERSION ?
          <>
            <Col span={19}>
              <UseCaseDiagramVersionDetails useCaseDiagramID={props.match.params.useCaseDiagramID} setSelectedRowVersion = {setSelectedRowVersion} isModalShow={state?.isModalShow} onChange={handleReloadData} isLoading={historyLoading} setScreenMode={setScreenMode} onDismiss={() => setScreenMode(SCREEN_MODE.HISTORY)} data={selectedVersionData} />
            </Col> 
          </>: <></>
      }
    </Row>
  )
}

export default UsecaseDiagramDetail
