import AppState from '@/store/types'
import {
    But<PERSON>,
    Card, Checkbox, Col, Form, Input, Modal, Row, Space, Spin, Typography
} from 'antd'
import debounce from 'lodash.debounce'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { APP_ROLES, ARTEFACT_COMMENT, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE, STATUS, WINDOW_CONFIRM_MESS } from '../../../constants'
import AssignTaskComponent from '../../../helper/component/assign-task'
import CkeditorMention from '../../../helper/component/ckeditor-mention'
import FormGroup from '../../../helper/component/form-group'
import LavAttachmentUpload from '../../../helper/component/lav-attachment-upload'
import LavPageHeader from '../../../helper/component/lav-breadcumb'
import LavEffortEstimationForm from '../../../helper/component/lav-efffort-estimation/form'
import LavImpact from '../../../helper/component/lav-impact'
import LavRelatedLinksForm from '../../../helper/component/lav-related-links/form'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import useWindowDimensions from '../../../helper/hooks/useWindowDimensions'
import { currentUserName, getReferencesFromEditor, hasRole, renderStatusBadge, ShowMessgeAdditionalSubmit } from '../../../helper/share'
import { initComment, initCommentScreen } from '../../../modules/_shared/comment/action'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import { CommentState } from '../../../modules/_shared/comment/type'
import { createRequest, getDetailRequest, resetState, updateRequest } from '../action'
import { UsecaseDiagramState } from '../type'
import useBeforeUnload from '../../../helper/hooks/useBeforeUnload'
const { Text } = Typography
const { confirm } = Modal

interface UseCaseDiagramFormModalProps {
    useCaseDiagramID?: number
    screenMode?: SCREEN_MODE.EDIT | SCREEN_MODE.CREATE
    onFinish?: () => void | null
    onDismiss: () => void | null
}

const UseCaseDiagramFormPage = ({ useCaseDiagramID, screenMode, onFinish, onDismiss }: UseCaseDiagramFormModalProps) => {
    const dispatch = useDispatch();
    const [form] = Form.useForm()
    const state = useSelector<AppState | null>((s) => s?.UseCaseDiagram) as UsecaseDiagramState
    const [isDraft, setIsDraft] = useState<any>(null);
    const [isCreateMore, setIsCreateMore] = useState(false);
    const { height: windowHeight } = useWindowDimensions()
    const [impacts, setImpacts] = useState<any>(false)
    const [isSubmitForm, setIsSubmitForm] = useState<boolean>(false)
    const modalConfirmConfig = useModalConfirmationConfig()
    const [attachment, setAttachment] = useState(null) as any
    const attachmentRef = useRef<any>()
    const getCkeditorDataDes: any = createRef()
    // Destroy
    useEffect(() => {
        form.setFieldsValue({
            assignee: currentUserName()
        })
        return () => {
            dispatch(resetState(null));
            resetForm();
            form.resetFields(['createMore']);
            setAttachment(null)
        }
    }, [])

    useEffect(() => {
        if (useCaseDiagramID && screenMode === SCREEN_MODE.EDIT) {
            dispatch(getDetailRequest(useCaseDiagramID))
        }
        document.title = intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'ucd.title-create' : 'ucd.title-update' });
    }, [screenMode, useCaseDiagramID])

    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }

    useEffect(() => {
        if (useCaseDiagramID && screenMode === SCREEN_MODE.EDIT && state.detail?.id) {
            const storage = isJsonString(state.detail?.storage);
            const jira = isJsonString(state.detail?.jira);
            const confluence = isJsonString(state.detail?.confluence);

            form.setFieldsValue({
                code: state.detail.code,
                usecasediagramName: state.detail.name,
                description: state.detail.description,
                req: state.detail.reqElicitation,
                documentation: state.detail.documentation,
                storageLinkText: storage ? storage?.textToDisplay : storage,
                storageWebLink: storage ? storage?.address : storage,
                jiraLinkText: jira ? jira?.textToDisplay : jira,
                jiraWebLink: jira ? jira?.address : jira,
                confluenceLinkText: confluence ? confluence?.textToDisplay : confluence,
                confluenceWebLink: confluence ? confluence?.address : confluence,
            })
            setAttachment(state.detail?.diagram)
        }
    }, [state.detail])

    useEffect(() => {
        if (state.createSuccess || state.updateSuccess) {
            if (isCreateMore) {
                resetForm();
                form.setFieldsValue({
                    assignee: currentUserName(),
                    dueDate: moment(new Date()),
                })
            } else {
                if (onFinish) {
                    onFinish();
                }
                onDismiss();
            }
            setIsDraft(null);
            setIsCreateMore(false);
        }
    }, [state.createSuccess, state.updateSuccess])

    useBeforeUnload()

    const onChange = (e) => {
        setImpacts(JSON.stringify(e))
    }
    const onSubmit = debounce(async (values: any, st?: string) => {
        let mentionReferences = getReferencesFromEditor(getCkeditorDataDes.current?.props?.data);
        const requestData: any = {
            id: useCaseDiagramID || null,
            name: values.usecasediagramName,
            version: values.version,
            diagram: attachment?.id,
            description: getCkeditorDataDes.current?.props?.data,
            storage: JSON.stringify({
                textToDisplay: values.storageLinkText,
                address: values.storageWebLink,
            }),
            jira: JSON.stringify({
                textToDisplay: values.jiraLinkText,
                address: values.jiraWebLink,
            }),
            confluence: JSON.stringify({
                textToDisplay: values.confluenceLinkText,
                address: values.confluenceWebLink,
            }),
            reqElicitation: values.req,
            documentation: values.documentation,
            status: isDraft ? ((screenMode === SCREEN_MODE.CREATE || state.detail?.status === STATUS.APPROVE || state.detail?.status === STATUS.REJECT_CUSTOMER || state.detail?.status === STATUS.REJECT || state.detail?.status === STATUS.DRAFT) ? STATUS.DRAFT : state.detail?.status) : (values.reviewer === currentUserName() ? STATUS.ENDORSE : STATUS.SUBMITTED),
            author: ((state?.detail?.status === STATUS.REJECT || state?.detail?.status === STATUS.REJECT_CUSTOMER || values.assignee !== currentUserName()) && !isDraft) ? currentUserName() : values.assignee,
            reviewer: values.reviewer || '',
            customer: values.customer || '',
            dueDate: values.dueDate ? values.dueDate?.toDate() : null,
            completeDate: values.completeDate ? values.completeDate?.toDate() : null,
            impacts: impacts,
            mentionReferences: mentionReferences ? JSON.stringify(mentionReferences) : null,
        }
        setIsCreateMore(values.createMore);
        if (requestData.status === STATUS.SUBMITTED || requestData.status === STATUS.ENDORSE) {
            if (!attachment?.id) {
                attachmentRef.current.scrollIntoView('img')
                ShowMessgeAdditionalSubmit('EMSG_13', 'common.artefact.usecase-diagram')
                return
            }
        }
        if (isDraft) {
            requestData.messageAction = screenMode === SCREEN_MODE.CREATE ? MESSAGE_TYPES.CREATE : MESSAGE_TYPES.UPDATE;
            dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
        } else {
            confirm({
                ...modalConfirmConfig,
                content: intl.formatMessage(
                    { id: isDraft ? 'CFD_6_1' : 'CFD_6' },
                    { Artefact: intl.formatMessage({ id: 'common.artefact.usecase-diagram' }) }
                ),
                onOk() {
                    requestData.messageAction = requestData?.status === STATUS.SUBMITTED ? MESSAGE_TYPES.SUBMIT : MESSAGE_TYPES.ENDORSE;
                    dispatch(screenMode === SCREEN_MODE.CREATE ? createRequest(requestData) : updateRequest(requestData));
                },
                onCancel() {

                },
            })
        }
    }, 500)

    const onFinishFailed = (errorInfo: any) => { }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss();
            },
            onCancel() { },
        })
    }

    const resetForm = () => {
        setIsCreateMore(false);
        setAttachment(null)
        setIsDraft(null);
        form.resetFields([
            'usecasediagramName',
            'version',
            'img',
            'description',
            'req',
            'documentation',
            'storageLinkText',
            'storageWebLink',
            'jiraLinkText',
            'jiraWebLink',
            'confluenceLinkText',
            'confluenceWebLink',
            'reviewer',
            'customer',
            'dueDate',
            'completeDate'
        ])
        form.setFieldsValue({
            assignee: currentUserName()
        })

    }
    //#region COMMENT INIT

    const commentState = useSelector<AppState | null>(
        (s) => s?.Comment
    ) as CommentState;

    useEffect(() => {
        if (!state.detail?.id || commentState.isLoading) {
            return;
        }

        const fields: { field, title }[] = [
            { field: 'version', title: intl.formatMessage({ id: 'common.form.version' }), },
            { field: 'diagram', title: intl.formatMessage({ id: 'ucd.column.ucd' }), },
            { field: 'description', title: intl.formatMessage({ id: 'ucd.column.ucd-description' }), },
            { field: 'assignee', title: intl.formatMessage({ id: 'common.assign-task.assignee' }), },
            { field: 'reviewer', title: intl.formatMessage({ id: 'common.assign-task.reviewer' }), },
            { field: 'customer', title: intl.formatMessage({ id: 'assigned_task.label.customer' }), },
            { field: 'due-date', title: intl.formatMessage({ id: 'common.assign-task.due_date' }), },
            { field: 'complete-date', title: intl.formatMessage({ id: 'common.assign-task.complete_date' }), },
            { field: 'req-elicitation', title: intl.formatMessage({ id: 'createscreen.label.req' }), },
            { field: 'documentation', title: intl.formatMessage({ id: 'view-screen-list.label.documentation' }), },
            { field: 'storage', title: intl.formatMessage({ id: 'related-links.storage' }), },
            { field: 'jira', title: intl.formatMessage({ id: 'related-links.jira' }), },
            { field: 'confluence', title: intl.formatMessage({ id: 'related-links.confluence' }), },
        ];
        dispatch(initComment({ projectId: state.detail.projectId, itemId: state.detail.id, fields }));

        const payload = {
            projectId: state.detail.projectId,
            itemId: state.detail.id,
            artefact: ARTEFACT_COMMENT.USE_CASE_DIAGRAM,
            fields: fields.map(o => o.field)
        };
        dispatch(initCommentScreen(payload));
    }, [state.detail])

    //#region COMMENT INIT
    return <Spin spinning={state?.isLoading}>
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header'>
                <LavPageHeader
                    showBreadcumb
                    title={intl.formatMessage({ id: screenMode == SCREEN_MODE.CREATE ? 'ucd.title-create' : 'ucd.title-update' })}
                >
                    <Space size="small">
                        {screenMode == SCREEN_MODE.CREATE ?
                            (<Form.Item
                                style={{ marginBottom: '0px' }}
                                valuePropName="checked"
                                name="createMore"
                                wrapperCol={{ span: 24 }}
                            >
                                <Checkbox disabled={state.isLoading}>
                                    {intl.formatMessage({
                                        id: 'createobject.checkbox.create-another',
                                    })}
                                </Checkbox>
                            </Form.Item>) : <></>
                        }

                        <Button onClick={debounce(confirmCancel, 500)}>
                            {intl.formatMessage({ id: 'common.action.close' })}
                        </Button>

                        {screenMode == SCREEN_MODE.CREATE || state.detail?.status == STATUS.DRAFT || state.detail?.status == STATUS.REJECT || state.detail?.status == STATUS.REJECT_CUSTOMER || (state.detail?.status == STATUS.APPROVE && (hasRole(APP_ROLES.BA) || currentUserName() === state?.detail?.customer)) ?
                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button type="primary" ghost htmlType="submit" onClick={() => {
                                    setIsDraft(false)
                                    setIsSubmitForm(true)
                                }}>
                                    {intl.formatMessage({ id: 'common.action.submit' })}
                                </Button>
                            </Form.Item> : <></>
                        }
                        {
                            <Form.Item style={{ marginBottom: '0px' }}>
                                <Button
                                    onClick={() => {
                                        setIsDraft(true)
                                        setIsSubmitForm(true)
                                    }}
                                    className="success-btn"
                                    htmlType="submit"
                                >
                                    {intl.formatMessage({ id: screenMode === SCREEN_MODE.CREATE ? 'common.action.save-as-draft' : 'common.action.save' })}
                                </Button>
                            </Form.Item>
                        }
                    </Space>
                </LavPageHeader>
            </div >

            <Row align="middle">
                {/* <Col span={2}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={14} controlSpan={10} label={
                                <TriggerComment screenMode={screenMode} field='actor'>
                                    {intl.formatMessage({ id: 'createobject.place-holder.version' })}
                                </TriggerComment>}>
                                <Form.Item
                                    name="version"
                                    rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}
                                >
                                    <Input maxLength={255} />
                                </Form.Item>
                            </FormGroup>
                        </Col> */}
                {screenMode === SCREEN_MODE.EDIT ?
                    <Col span={5}>
                        <div className='status-container'>
                            <div>
                                {intl.formatMessage({ id: 'common.field.status' })}
                            </div>
                            <div>
                                {renderStatusBadge(state.detail?.status)}
                            </div>
                        </div>
                    </Col> : <></>
                }
            </Row>
            <Space direction="vertical" size="middle" style={{ padding: '0 10px 2px 0' }}>
                <Card className='rq-form-block' title={intl.formatMessage({ id: 'ucd.column.ucd-infor' })}>
                    {
                        screenMode === SCREEN_MODE.EDIT ?

                            <FormGroup inline labelSpan={3} controlSpan={2} label={intl.formatMessage({ id: 'common.label.code' })}>
                                <Form.Item name="code" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                                    <Input disabled maxLength={255} />
                                </Form.Item>
                            </FormGroup> : <></>
                    }
                    {/* ucd.column.ucd */}

                    <FormGroup inline labelSpan={3} controlSpan={21} required label={intl.formatMessage({ id: 'common.label.name' })}>
                        <Form.Item
                            name="usecasediagramName"
                            rules={[
                                {
                                    required: true,
                                    message: intl.formatMessage({ id: 'IEM_1' }),
                                },
                                { max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) },
                                {
                                    validator: async (rule, value) => {
                                        if (value && value.trim().length === 0) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_1' }))
                                        }
                                    },
                                },
                            ]}
                        >
                            <Input
                                placeholder={`${intl.formatMessage({
                                    id: `ucd.place-holder.use-case-diagram-name`,
                                })}${intl.formatMessage({
                                    id: `common.mandatory.*`,
                                })}`}
                                maxLength={255}
                            />
                        </Form.Item>
                    </FormGroup>

                    <div ref={attachmentRef}>
                        <FormGroup label={
                            <TriggerComment screenMode={screenMode} field="diagram">
                                {intl.formatMessage({ id: 'ucd.column.ucd' })}
                            </TriggerComment>
                        }>
                            <Form.Item name="img">
                                <LavAttachmentUpload artefactType={REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM} attachment={attachment} isCommon={false} name="file" supportPDF onChange={setAttachment} />
                            </Form.Item>
                        </FormGroup>
                    </div>
                    <FormGroup inline controlSpan={21} labelSpan={3} label={
                        <TriggerComment screenMode={screenMode} field="description">
                            {intl.formatMessage({ id: 'ucd.column.ucd-description' })}
                        </TriggerComment>
                    }>
                        <Form.Item
                            name="description"
                            labelAlign="left"
                            wrapperCol={{ span: 24 }}
                        >
                            <CkeditorMention
                                ref={getCkeditorDataDes}
                                data={screenMode === SCREEN_MODE.CREATE ? '' : state.detail?.description}
                            />
                        </Form.Item>
                        {/* <TextAreaBullet
                        reload={false}
                        reloadAfterBack={false}
                        label=""
                        name="description"
                        labelAlign="left"
                        rules={[]}
                    ></TextAreaBullet> */}
                    </FormGroup>
                </Card>
                <AssignTaskComponent form={form} data={screenMode == SCREEN_MODE.EDIT ? state.detail : null} isSubmit={isDraft == false} screenMode={screenMode} />
                {screenMode === SCREEN_MODE.EDIT && <LavImpact dataDetail={state?.detail} artefactType={REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM} onChange={onChange} isSubmitForm={isSubmitForm} />}

                <LavEffortEstimationForm
                    screenMode={screenMode}
                    hasDevelopment={state?.detail?.hasOwnProperty('development')}
                    hasImplementation={state?.detail?.hasOwnProperty('implementation')}
                />

                <LavRelatedLinksForm form={form} screenMode={screenMode} />
            </Space>
        </Form >
    </Spin>
}
export default UseCaseDiagramFormPage
