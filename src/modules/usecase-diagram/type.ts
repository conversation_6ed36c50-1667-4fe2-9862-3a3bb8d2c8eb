
export interface UsecaseDiagramState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: UsecaseDiagramDetail | null,
  selectedData?: UsecaseDiagramDetail | null,
  isModalShow?: boolean,
}
export interface UsecaseDiagramDetail {
  id?: number | null,
  code: string,
  name: string,
  diagram: number | null,
  description: string,
  storage: string
  jira: string
  confluence: string
  reqElicitation: string,
  documentation: string,
  status: number,
  author: string,
  reviewer: string,
  dueDate: string,
  customer: string
  completeDate: string,
  projectId: number | null,
  impacts: string,

}

export const defaultState = {
  detail: {
    id: null,
    code: '',
    name: '',
    version: '',
    diagram: null,
    description: '',
    storage: '',
    jira: '',
    confluence: '',
    reqElicitation: '',
    documentation: '',
    status: 0,
    author: '',
    reviewer: '',
    dueDate: '',
    customer: '',
    completeDate: '',
    projectId: null,
    impacts: '',

  },
  selectedData: null,
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  isLoadingList: false,
  listData: []
}

export enum ActionEnum {
  RESET_STATE = '@@MODULES/USECASE_DIAGRAM/RESET_STATE',

  CREATE_REQUEST = '@@MODULES/USECASE_DIAGRAM/CREATE_REQUEST',
  CREATE_SUCCESS = '@@MODULES/USECASE_DIAGRAM/CREATE_SUCCESS',
  CREATE_FAILED = '@@MODULES/USECASE_DIAGRAM/CREATE_FAILED',

  UPDATE_REQUEST = '@@MODULES/USECASE_DIAGRAM/UPDATE_REQUEST',
  UPDATE_SUCCESS = '@@MODULES/USECASE_DIAGRAM/UPDATE_SUCCESS',
  UPDATE_FAILED = '@@MODULES/USECASE_DIAGRAM/UPDATE_FAILED',

  GET_DETAIL_REQUEST = '@@MODULES/USECASE_DIAGRAM/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = '@@MODULES/USECASE_DIAGRAM/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = '@@MODULES/USECASE_DIAGRAM/GET_DETAIL_FAILED',

  GET_LIST_REQUEST = '@@MODULES/USECASE_DIAGRAM/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = '@@MODULES/USECASE_DIAGRAM/GET_LIST_SUCCESS',
  GET_LIST_FAILED = '@@MODULES/USECASE_DIAGRAM/GET_LIST_FAILED',

  DELETE_REQUEST = '@@MODULES/USECASE_DIAGRAM/DELETE_REQUEST',
  DELETE_SUCCESS = '@@MODULES/USECASE_DIAGRAM/DELETE_SUCCESS',
  DELETE_FAILED = '@@MODULES/USECASE_DIAGRAM/DELETE_FAILED',

  SET_MODAL_VISIBLE = '@@MODULES/USECASE_DIAGRAM/SET_MODAL_VISIBLE',
}
