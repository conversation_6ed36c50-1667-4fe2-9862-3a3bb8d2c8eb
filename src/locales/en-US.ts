import request from './en-US/request'
import viewobject from './en-US/viewObject'
import actor from './en-US/actor'
import common from './en-US/commonlocale'
import msg from './en-US/message'
import viewobjectSpecification from './en-US/viewObjectSpecification'
import createObject from './en-US/createObject'
import updateObject from './en-US/updateObject'
import viewScreenList from './en-US/viewScreenList'
import viewUseCaseDetail from './en-US/viewUseCaseDetail'
import viewScreenDetails from './en-US/viewScreenDetails'
import viewFunction from './en-US/view-function'
import createFunction from './en-US/create-function'
import createScreen from './en-US/createScreen'
import workFlow from './en-US/viewWorkFlow'
import viewStateTransition from './en-US/view-state-transition'
import createStateTransition from './en-US/create-state-transition'
import mess from './en-US/mess'
import mail from './en-US/email'
import viewDataMigration from './en-US/view-data-migration'
import viewNonFunctional from './en-US/view-non-functional'
import createNonFunctional from './en-US/create-non-functional'
import userRequirement from './en-US/user-requirement'
import viewObjectRelationship from './en-US/view-object-relationship'
import meeting from './en-US/meeting'
import viewBusinessRule from './en-US/view-business-rule'
import viewUsecaseDiagram from './en-US/view-usecase-diagram'
import reference_document from './en-US/reference_document'
import viewOtherRequirement from './en-US/view-other-requirement'
import generateSrs from './en-US/generate-srs'
import generateWbs from './en-US/generate-wbs'
import dashboard from './en-US/dashboard'
import projectManagement from './en-US/project-management'
import common_committee from './en-US/common_committee'
import common_usercase from './en-US/common_usercase'
import commonComponent from './en-US/common-component'
import commonObject from './en-US/common-object'
import commonScreen from './en-US/common-screen'
import menu from './en-US/menu'
import myAssignedTask from './en-US/my_assigned_task'
import validate_srs from './en-US/validate-srs'
import select_common_component from './en-US/select-common-component'
import myPendingReviewTask from './en-US/my-pending-review-task'
import recommend_common_component from './en-US/recommend-common-component'
import related_links from './en-US/related-links'
import qualityReport from './en-US/quality-report'
import common_nonFunctionalRequirement from './en-US/common_non-functional-requirement'
import commonmessage from './en-US/common-message'
import effortEstimation from './en-US/effort_estimation'
import epicManagement from './en-US/epic-management'
import sprint_management from './en-US/sprint_management'
import user_story from './en-US/user-story'
import recommended from './en-US/recommenedcommonrequirement'
import glossary from './en-US/glossary'
import viewVersionHistory from './en-US/viewVersionHistory'
import ai from './en-US/ai'
export default {
  ...viewVersionHistory,
  ...glossary,
  ...recommended,
  ...commonmessage,
  ...projectManagement,
  ...request,
  ...viewobject,
  ...actor,
  ...msg,
  ...common,
  ...viewobjectSpecification,
  ...createObject,
  ...updateObject,
  ...viewScreenList,
  ...viewUseCaseDetail,
  ...viewFunction,
  ...createFunction,
  ...createScreen,
  ...viewScreenDetails,
  ...workFlow,
  ...viewStateTransition,
  ...createStateTransition,
  ...mess,
  ...mail,
  ...viewDataMigration,
  ...viewNonFunctional,
  ...createNonFunctional,
  ...userRequirement,
  ...viewObjectRelationship,
  ...meeting,
  ...viewBusinessRule,
  ...viewUsecaseDiagram,
  ...reference_document,
  ...viewOtherRequirement,
  ...generateSrs,
  ...generateWbs,
  ...dashboard,
  ...commonObject,
  ...common_committee,
  ...commonComponent,
  ...commonScreen,
  ...common_usercase,
  ...common_nonFunctionalRequirement,
  ...menu,
  ...myAssignedTask,
  ...myPendingReviewTask,
  ...validate_srs,
  ...select_common_component,
  ...recommend_common_component,
  ...related_links,
  ...qualityReport,
  ...effortEstimation,
  ...epicManagement,
  ...sprint_management,
  ...user_story,
  ...ai
}
