export default {
    'commonscreen.page_title': 'Common Screen',
    'commonscreen.place-holder.cm-screen': 'Common Screen',
    'common_screen.header.title': 'Screen List',
    'common_screen.button.create-screen': 'Create Screen',

    'common_screen.column.screen-code': 'Code',
    'common_screen.column.screen': 'Screen',
    'common_screen.column.description': 'Description',
    'common_screen.column.object': 'Object',
    'common_screen.column.status': 'Status',
    'common_screen.column.update-date': 'Update Date',
    'common_screen.column.update-by': 'Updated By',
    'common_screen.modal.delete_title': 'Delete Screen',
    'common_screen.modal.error_title': 'Error Screen',
    'common_screen.modal.title': 'Delete Screen',
    'common_screen.label.requirement': 'User Requirement',
    'common_screen.label.state-transition': 'State Transition',
    'common_screen.label.effort.': 'Effort Estimation (hour)',
    'common_screen.label.req.': 'Req. Elicitation',
    'common_screen.label.documentation': 'Documentation',
    'common_screen.label.implementation': 'Implementation',
    'common_screen.label.related-links': 'Related Links',
    'common_screen.label.storage': 'Storage',
    'common_screen.label.jira': 'JIRA',
    'common_screen.label.confluence': 'Confluence',
    'common_screen.label.auditTrail': 'Audit Trail',
    'common_screen.label.created-by': 'Created By',
    'common_screen.label.created-date': 'Created Date',
    'common_screen.label.update-by': 'Updated By',
    'common_screen.label.update-date': 'Updated Date',
    'common_screen.label.actor': 'Actor',
    'common_screen.label.object': 'Object',
    'common_screen.label.req': 'Req.',
    'common_screen.label.screen': 'Screen',
    'common_screen.label.use-case': 'Use Case',
    'common_screen.label.submitted-by': 'Submitted By',
    'common_screen.label.submitted-date': 'Submitted Date',
    'common_screen.modal.title-add-screen-component': 'Add Screen Component',
    'common_screen.modal.title-update-screen-component': 'Update Screen Component',
    'common_screen.label.source-screen': 'Source Screen',
    'common_screen.label.email-template': 'Email Template',
    'common_screen.label.message': 'Message',
    'common_screen.label.other-requirement': 'Other Requirment',
    'common_screen.label.target-screen': 'Target Screen',
    'common_screen.label.user-requirement': 'User Requirement',
    'common_screen.default-value.screen-component': 'Retrieve [Object Property] from [Object].',
    'common-screen-details.label.screen': 'Screen',
    'common-screen-details.button.create-screen': 'Create Screen',
    'common-screen-details.label.screen-detail': 'Screen Detail',
  
    'common-screen-details.header.title': 'Screen List',
    'common-screen-details.legend.screen-info': 'Screen Information',
    'common-screen-details.legend.reference': 'Reference',
    'common-screen-details.legend.effort-estimation-hour':
      'Effort Estimation (hour)',
    'common-screen-details.legend.related-links': 'Related Links',
    'common-screen-details.legend.audit-trail': 'Audit Trail',
  
    'common-screen-details.column.order': 'No',
    'common-screen-details.column.component': 'Component',
    'common-screen-details.column.compType': 'Type',
    'common-screen-details.column.editable': 'Editable',
    'common-screen-details.column.mandatory': 'Mandatory',
    'common-screen-details.column.default-value': 'Default Value',
    'common-screen-details.column.description': 'Description',
  
    'common-screen-details.screen-info.description': 'Description',
    'common-screen-details.screen-info.access': 'Access',
    'common-screen-details.screen-info.mockup-screen': 'Mockup Screen',
    'common-screen-details.screen-info.screen-description': 'Screen Description',
  
    'common-screen-details.description-source-object': 'Source Object',
    'common-screen-details.description-source-object-property':
      'Source Object Property',
  
    'common-screen-details.description-target-screen': 'Target Screen',
    'common-screen-details.description-target-uc': 'Target UC',
}

