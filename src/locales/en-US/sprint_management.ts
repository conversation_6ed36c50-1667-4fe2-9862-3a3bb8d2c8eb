export default {
    'createsprint.place-holder.sprint-name':'Sprint Name',
    'sprint-management.title':'Sprint List',
    'sprint-management.code':'Code',
    'sprint-management.name':'Sprint Name',
    'sprint-management.goals':'Sprint Goals',
    'sprint-management.scope':'Sprint Scope',
    'sprint-management.project':'Project',
    'sprint-management.duration':'Duration',
    'sprint-management.start-date':'Start Date',
    'sprint-management.end-date':'End Date',
    'sprint-management.story':'User Story included',
    'sprint-management.update':'Update',
    'create-sprint.sprint-name':'Create Sprint',
    'update-sprint.sprint-name':'Update Sprint',
    'sprint-management.delete':'Delete',
    'sprint-management.cancel':'Cancel',
    'sprint-management.save':'Save',
    'sprint-management.close':'Close',
    'sprint-management.create':'Create',
    'view-sprint-details.sprint-info':'Sprint Information',
    'createsprint.title':'Create Sprint Management',
    'updatesprint.title':'Update Sprint Management',
}