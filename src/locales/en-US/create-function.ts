export default {
  'function.header.create-function': 'Create Function',
  'function.form.function-no': 'Function No:',
  'function.form.function': 'Function',
  'function.form.uc': 'Use Case',
  'function.form.objects': 'Objects',
  'function.form.user-requirement': 'User Requirement',
  'function.form.other-requirement': 'Other Requirement',
  'function.form.actor': 'Actor',
  'function.form.screen': 'Screen',
  'function.form.type': 'Type',
  'function.form.status': 'Status',
  'function.form.trigger': 'Trigger',
  'function.form.description': 'Description',
  'function.form.pre-condition': 'Pre-condition',
  'function.form.pre-condition-type': 'Pre-condition Type',
  'function.form.new-pre-condition': 'Add Pre-condition',
  'function.form.user-permission': 'User Permission',
  'function.form.security': 'Security',

  'function.form.post-condition': 'Post Condition',
  'function.form.activity-flow': 'Activity Flow',
  'function.form.business-rule': 'Business Rule',
  'function.form.messages': 'Messages',
  'function.form.email-templates': 'Email Templates',
  'function.form.common-business-rule': 'Common Business Rule',
  'function.form.new-business-rule': 'New Business Rule',
  'function.form.step': 'Step',
  'function.form.br-rule': 'Business Rule',
  'function.form.create': 'Create',
  'function.form.cancel': 'Cancel',
  'function.form.create-another': 'Create another',
  'function.form.status-draft': 'Draft',
  'function.form.status-stable': 'Stable',
  'function.form.type-crud': 'CRUD',
  'function.form.type-workflow': 'Workflow',
  'function.form.type-others': 'Others',
  'function.form.drag-file': 'Click or drag file to this area to upload',
  'function.form.screen-displaying-rules': 'Screen Displaying Rules',
  'function.form.confirmation-rules': 'Confirmation Rules',
  'function.form.validating-rules': ' Validating Rules',
  'function.form.creating-rules': 'Creating Rules',
  'function.form.deleting-rules': 'Deleting Rules',
  'function.form.updating-rules': 'Updating Rules',
  'function.form.sending-email-notification-rules':
    'Sending Email Notification Rules',
  'function.form.sending-message-notification-rules':
    'Sending Message Notification Rules',
  'function.form.logging-in-rules': 'Logging in Rules',
  'function.form.logging-out-rules': 'Logging out Rules',

  'function.usecase-information': 'Use Case Information',
  'function.reference': 'Reference',
  'function.requirement': 'Requirement',
  'function.link-screen': 'Link Screen',
  'function.link-requirement': 'Link Requirement',
  'function.effort-estimation-hours': 'Effort Estimation (hours)',
  'function.implementation': 'Implementation',
  'function.req-elicitation': 'Req. Elicitation',
  'function.documentation': 'Documentation',
  'function.storage': 'Storage',
  'function.jira': 'JIRA',
  'function.confluence': 'Confluence',
  'function.related-links': 'Related Links',

  'function.link-source-object': 'Link Source Object',
  'function.link-actor': 'Link Actor',
  'function.create-another': 'Create another',
  'function.save-draft': 'Save as draft',
  'function.submit': 'Submit',
  'function.update': 'Update',
  'function.place-holder.use-case-name': 'Use Case Name',
  'function.place-holder.use-case-name-short': 'Name',
  'function.place-holder.use-case-name-label' : 'Use Case Name',
  'function.place-holder-version': 'Version',
  'function.place-usecase-code': 'Use Case Code',
  'function.title-update': 'Update Use Case Specification',
  'function.title-create': 'Create Use Case Specification',

}
