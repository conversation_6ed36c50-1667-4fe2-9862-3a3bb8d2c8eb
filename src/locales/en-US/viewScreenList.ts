export default {
  'view-screen-list.header.title': 'Screen List',
  'view-screen-list.button.create-screen': 'Create Screen',
  'view-screen-list.button.export-screen': 'Export Screen',

  'view-screen-list.column.screen-code': 'Code',
  'view-screen-list.column.screen': 'Screen',
  'view-screen-list.column.description': 'Description',
  'view-screen-list.column.object': 'Object',
  'view-screen-list.column.status': 'Status',
  'view-screen-list.column.update-date': 'Update Date',
  'view-screen-list.column.update-by': 'Updated By',
  'view-screen-list.modal.delete_title': 'Delete Screen',
  'view-screen-list.modal.error_title': 'Error Screen',
  'view-screen-list.modal.title': 'Delete Screen',
  'view-screen-list.label.requirement': 'User Requirement',
  'view-screen-list.label.state-transition': 'State Transition',
  'view-screen-list.label.effort.': 'Effort Estimation (hour)',
  'view-screen-list.label.req.': 'Req. Elicitation',
  'view-screen-list.label.documentation': 'Documentation',
  'view-screen-list.label.implementation': 'Implementation',
  'view-screen-list.label.related-links': 'Related Links',
  'view-screen-list.label.storage': 'Storage',
  'view-screen-list.label.jira': 'JIRA',
  'view-screen-list.label.confluence': 'Confluence',
  'view-screen-list.label.auditTrail': 'Audit Trail',
  'view-screen-list.label.created-by': 'Created By',
  'view-screen-list.label.created-date': 'Created Date',
  'view-screen-list.label.update-by': 'Updated By',
  'view-screen-list.label.update-date': 'Updated Date',
  'view-screen-list.label.actor': 'Actor',
  'view-screen-list.label.object': 'Object',
  'view-screen-list.label.req': 'Req.',
  'view-screen-list.label.screen': 'Screen',
  'view-screen-list.label.use-case': 'Use Case',
  'view-screen-list.label.submitted-by': 'Submitted By',
  'view-screen-list.label.submitted-date': 'Submitted Date',
  'view-screen-list.modal.title-add-screen-component': 'Add Screen Component',
  'view-screen-list.modal.title-select-object-properties': 'Select Object Properties',
  'view-screen-list.modal.title-update-screen-component': 'Update Screen Component',
  'view-screen-list.label.source-screen': 'Source Screen',
  'view-screen-list.label.email-template': 'Email Template',
  'view-screen-list.label.message': 'Message',
  'view-screen-list.label.other-requirement': 'Other Requirment',
  'view-screen-list.label.target-screen': 'Target Screen',
  'view-screen-list.label.user-requirement': 'User Requirement',
  'view-screen-list.default-value.screen-component' : 'Retrieve [Object Property] from [Object].'
}
