export default {
  // ENUM
  'common.enum.draft': 'Draft',
  'common.enum.cancelled': 'Cancelled',
  'common.enum.recommended': 'Recommended',

  'common.enum.blank': 'Blank',
  'common.enum.blank-value': '(blank)',
  'common.enum.submitted': 'Submitted',
  'common.enum.checkIn': 'Checked-in',
  'common.enum.approved': 'Approved',
  'common.enum.cm-rejected': 'Rejected',

  'common.enum.rejected': 'Rejected By Reviewer',
  'common.enum.rejected-customer': 'Rejected By Customer',
  'common.enum.deleted': 'Deleted',
  'common.enum.removed': 'Removed',
  'common.enum.endorsed': 'Endorsed',
  'common.enum.reject': 'Rejected',

  // ACTIONS
  'common.action.delete': 'Delete',
  'common.action.create': 'Create',
  'common.action.close': 'Close',
  'common.action.search': 'Search',
  'common.action.endorse': 'Endorse',
  'common.action.assign': 'Assign',
  'common.action.reset': 'Reset',
  'common.action.edit': 'Edit',
  'common.action.apply': 'Apply Filter',
  'common.action.ok': 'OK',
  'common.action.reference': 'Reference',
  'common.action.reference-to': 'Reference To',
  'common.action.reference-from': 'Reference From',
  'common.action.import': 'Import',
  'common.action.export': 'Export',
  'common.action.back': 'Back',
  'common.action.next': 'Next',
  'common.action.cancel': 'Cancel',
  'common.action.re-open': 'Re-open',
  'common.action.generate': 'Generate',
  'common.action.update': 'Update',
  'common.action.reject': 'Reject',
  'common.action.approve': 'Approve',
  'common.action.remove': 'Remove',
  'common.action.save': 'Save',
  'common.action.submit': 'Submit',
  'common.action.recommend': 'Recommend',
  'common.action.select': 'Select',
  'common.action.save-as-draft': 'Save as draft',
  'common.action.create-another': 'Create another',
  'common.action.add': 'Add',
  'common.action.code': 'Code',
  'common.action.add-to-table': 'Add to table',
  'common.field.status': 'Status',
  // ARTEFACTS
  'common.artefact.object': 'Object',
  'common.artefact.objects': 'Object List',
  'common.artefact.permission': 'Permission Matrix',
  'common.artefact.project': 'Project',
  'common.artefact.screen': 'Mockups Screen',
  'common.artefact.screens_item': 'Screen',
  'common.artefact.actor': 'Actor List',
  'common.artefact.actor-short': 'Actor',
  'common.artefact.actor_item': 'Actor',
  'common.artefact.function': 'Use Case',
  'common.artefact.use-case': 'Use Case Specification',
  'common.artefact.use-case-specifications': 'Use Case Specifications',
  'common.artefact.workflow': 'Workflow',
  'common.artefact.message': 'Message',
  'common.artefact.glossary': 'Glossary',
  'common.artefact.change-request': 'Change Request',
  'common.artefact.user-requirement': 'User Requirement',
  'common.artefact.user-requirements': 'User Requirements',
  'common.artefact.reference-document': 'Reference Document',
  'common.artefact.my-assigned-task': 'My Assigned Task',
  'common.artefact.discussion-item': 'Discussion Item',
  'common.artefact.meeting-minustes': 'Meeting Minutes',
  'common.artefact.component': 'component',
  'common.artefact.property': 'property',
  'common.artefact.state-transition': 'State Transition',
  'common.artefact.email': 'Email Template',
  'common.artefact.common-email': 'Common Email Template',
  'common.artefact.data-migration': 'Data Migration',
  'common.artefact.non-functional': 'Non-Functional Requirement',
  'common.artefact.common-non-functional': 'Common Non-Functional Requirement',
  'common.artefact.object-relationship': 'Object Relationship Diagram',
  'common.artefact.business-rule': 'Common Business Rule',
  'common.artefact.usecase-diagram': 'Use Case Diagram',
  'common.artefact.other-requirement': 'Other Requirement',
  'common.artefact.other-requirements': 'Other Requirements',
  'common.artefact.recommend-common-component': 'Recommend Common Component',
  'common.artefact.sprint-management':'Sprint',
  'common.artefact.common-component': 'Component',
  'common.artefact.common-committee': 'Common Committee',
  'common.artefact.common-usecase': 'Use Case',
  'common.artefact.common-workflow': 'Workflow',
  'common.artefact.common-screen': 'Screen',
  'common.artefact.common-common-screen': 'Common Screen',
  'common.artefact.common-object': 'Object',
  'common.artefact.common-common-object': 'Common Object',
  'common.artefact.common-common-usecase': 'Common Use Case',
  'common.artefact.common-common-workflow': 'Common Workflow',

  'common.artefact.epic': 'Epic',
  'common.artefact.sprint':'Sprint',
  'common.artefact.sprint-details':'Sprint Details',
  'common.artefact.user-story-requirements': 'User Story',
  'common.artefact.recommended-common-requiremnt': 'Recommended Common Requirement',

  

  // MESSAGES
  'common.message.delete-failure': 'Delete data failure',
  'common.message.create-failure': 'Create data failure',
  'common.message.get-detail-failure': 'Get data failure',
  'common.message.update-failure': 'Update data failure',
  'common.message.cancel-failure': 'Cancel data failure',
  'common.message.import-validate-success': `Validate file success`,
  'common.message.import-success': `Import data success`,
  'common.message.export-success': `Your request to generate SRS is in-progress. An email with the download URL will be sent to you once done.`,
  'common.message.validate-failure': 'Validate file failure',
  'common.message.request-fail': `Request Fail`,
  'common.message.create-success': `A new {Artefact} is created successfully`,
  'common.message.update-success': `The {Artefact} is updated successfully`,
  'common.message.restore-success': `The {Artefact} is restored successfully`,
  'common.message.submit-success': `The {Artefact} is submitted successfully`,
  'common.message.delete-success': 'The {Artefact} is deleted successfully',
  'common.message.approve-success': `The {Artefact} is approved successfully`,
  'common.message.reject-success': `The {Artefact} is rejected successfully`,
  'common.message.reopen-success': `The {Artefact} is re-opened successfully`,
  'common.message.cancel-success': `The {Artefact} is cancelled successfully`,
  'common.message.remove-success': `The {Artefact} is removed successfully`,
  'common.message.endorse-success': `The {Artefact} is endorsed successfully`,
  'common.message.request-success': 'The {Artefact} is request successfully',
  'common.message.submitted-cr-success': `The Change Request is submitted successfully.`,
  "common.message.error": 'Error Message',
  "common.message.warning": 'Warning Message',
  'common.message.error-description': 'An Error Occurred Please Try Again Later.',
  "common.message.require-select": 'You need to select at least one item in the list before proceeding',
  "common.message.sync-data": "Data Synchronization is in-progress. Please wait a few minutes.",

  // TABLE
  'common.table.header.code': 'Code',
  'common.table.no-data': 'No Data',
  'common.table.pagination.of': 'of',
  'common.table.pagination.items': 'items',
  'common.table.column.#': '#',
  'common.table.column.code': 'Code',
  'common.table.column.status': 'Status',
  'common.table.column.action': 'Action',
  
  // MODAL
  'common.dialog.confirm': 'Confirmation',
  'common.dialog.error': 'Error',
  'common.dialog.success': 'Success Dialog',
  'common.dialog.warning': 'Warning Dialog',
  'common.dialog.attach-file': 'Attach Screenshot',
  'common.dialog.screen-shot': 'Take a screenshot',
  'common.dialog.paste': 'Paste the image',
  'common.dialog.file-name': 'File name',
  'common.dialog.input-screen-shot': 'PrtScn',
  'common.dialog.input-paste': 'Ctrl + V',
  'common.dialog.default-file-name': 'Screenshot-1',


  // ASSIGN TASK
  'common.assign-task.title': 'Assign Task Information',
  'common.assign-task.assignee': 'Author',
  'common.assign-task.customer': 'Customer',
  'common.assign-task.reviewer': 'Reviewer',
  'common.assign-task.due_date': 'Due Date',
  'common.assign-task.complete_date': 'Complete Date',

  'common.name.last-day': 'Within Past',
  'common.name.next-day': 'Within Next',
  'common.name.today': 'Today',
  'common.committee.reviewer': 'Reviewer',
  'common.committee.ba-member': 'BA Member',
  'common.committee.system-admin': 'System Admin',
  'common.committee.active': 'Active',
  'common.committee.inactive': 'Inactive',

  'common.breadcrumb.common': 'Common Requirement',
  'common.text.search': 'Search',
  'common.header.permission-matrix': 'Permission Matrix',
  'common.title.dot': '•',
  'common.mandatory.*': ' *',
  'common.form.version': 'Version',
  'common.na': 'N/A',
  'common.audit-trail': 'Audit Trail',
  'common.placeholder-select': 'Select...',
  'common.label.version': 'Version',
  'common.label.upload-label': 'Drag and drop file here or',
  'common.label.upload-label-browse-file': 'Browse for file',
  'common.label.ctrlv-label-file': 'User can also copy and paste diagram here directly without exporting to file',
  'common.reference-to': 'Reference To',
  'common.reference-from': 'Reference From',
  'common.status.on-going': 'On-Going',
  'common.status.tentative': 'Tentative',

  // OTHER
  'Error': 'Error',
  'Fetch data fail': 'Fetch data fail',
  'Create data success': 'Create data success',
  'Create data failure': 'Create data failure',
  'Update data success': 'Update data success',
  'Update data fail': 'Update data fail',
  'Update data failure': 'Update data failure',
  'Delete data success': 'Delete data success',
  'Delete data fail': 'Delete data fail',
  'Delete data failure': 'Delete data failure',
  'Cancel data failure': 'Cancel data failure',
  'Cancel data success': 'Cancel data success',
  'Validate file success': `Validate file success`,
  'Import data success': `Import data success`,
  'Validate file failure': 'Validate file failure',
  'Request Fail': `Request Fail`,
  'common.label.impact' : 'Impact',


  'common.label.yes' : 'Yes',
  'common.label.no' : 'No',
  //USER STORY 
  'common.user-story.critical': 'Critical',
  'common.user-story.high': 'High',
  'common.user-story.medium': 'Medium',
  'common.user-story.slow': 'Low',

  'common.label.name' : 'Name',
  'common.label.product' : 'Product',
  'common.label.reference' : 'Reference',
  'common.label.code' : 'Code',

  'common.label.referencefrom' : 'Reference From:',
  'common.label.referenceto' : 'Reference To:',

  'common.usercase.tooltip-br' : 'You can drag and drop to re-order business rules. BR code will be auto-generated as you save the Use Case',


  'common.title.version_card' : 'Version History',
  'common.label.change-description' : 'Change Description',
  'common.column.date' : 'Date',
  'common.column.version' : 'Version',
  'common.column.updated-by' : 'Updated By',
  'common.column.change-description' : 'Change Description',

  'common.status.approve': 'Approved',
  'common.status.cancelled': 'Cancelled',
  'common.status.draft': 'Draft',
  'common.status.endorsed': 'Endorsed',
  'common.status.rejected-by-reviewer': 'Rejected by Reviewer',
  'common.status.rejected-by-customer': 'Rejected by Customer',
  'common.status.submitted': 'Submitted',

  'common.place-holder.link-text': 'Display Text',
  'common.place-holder.web-link': 'URL',
}
