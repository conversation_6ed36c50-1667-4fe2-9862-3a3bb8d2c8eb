export default {
  'user-requirement.header.title': 'User Requirement List',
  'user-requirement.header.title-detail': 'USR List',
  'user-requirement.column.user-requirement-code': 'Code',
  'user-requirement.column.user-requirement': 'User Requirement',
  'user-requirement.column.type': 'Type',
  'user-requirement.column.source': 'Source',
  'user-requirement.column.dueDate': 'Due Date',
  'user-requirement.column.priority': 'Priority',
  'user-requirement.column.isCovered': 'Covered USR',
  'user-requirement.column.product': 'Product',

  'user-requirement.column.scope': 'Scope',
  'user-requirement.column.sender': 'Sender',
  'user-requirement.column.send-date': 'Send Date',
  'user-requirement.column.status': 'Status',
  'user-requirement.column.action': 'Action',
  'user-requirement.column.usrdetails': 'USR Details',
  'user-requirement.column.req-eliciation': 'Request Eliciation',
  'user-requirement.column.documentation': 'Documentation',
  'user-requirement.column.development': 'Development',
  'user-requirement.column.error-type': 'Error Type',
  'user-requirement.column.row-number': 'Row Number',

  'user-requirement.button.create-user-requirement': 'Create USR',
  'user-requirement.button.import-user-requirement': 'Import USR',
  'user-requirement.create-modal-title.import': 'Import',
  'user-requirement.label.user-requirement-details': 'User Requirement Details',
  'user-requirement.label.change-reason':'Change Reason',
  'user-requirement.label.product':'Product',
  'user-requirement.label.please-specify-a-source': 'Please Specify A Source',
  'user-requirement.label.select-a-meeting-minutes': 'Select A Meeting Minutes',
  'user-requirement.label.select-a-reference-document':
    'Select A Reference Document',
  'user-requirement.label.select-a-reference-cbr': 'Select A Reference Common Business Rule',
  'user-requirement.column.source-type': 'Source Type',
  'user-requirement.label.user-requirement-infomation':
    'User Requirement Information',
  'user-requirement.label.details': 'User Requirement Details',
  'user-requirement.button.add-user-requirement': 'Add User Requirement',
  'user-requirement.reference.common-bussiness-rule': 'Common Business Rule',
  'user-requirement.reference.meeting': 'Meeting Minutes',
  'user-requirement.reference.cbr': 'Common Business Rule',
  'user-requirement.place-holder': 'USER REQUIREMENT',
  'user-requirement.button.create-ur': 'Create UR ',
  'user-requirement.button.export-ur': 'Export UR ',

  'user-requirement.place-holder.reject-ur': 'Add reason of this rejection...',
  // 'user-requirement.pagination.of': 'of',
  // 'user-requirement.pagination.items': 'items',
  // 'user-requirement.action.delete-user-requirement': 'Delete user-requirement',

  // 'create-user-requirement.title.create-user-requirementage':
  //   'Create user-requirementage',
  // 'create-user-requirement.card.user-requirementage-infomation':
  //   'user-requirementage Information',
  // 'create-user-requirement.label.category': 'Category',
  // 'create-user-requirement.label.user-requirementage': 'user-requirementage',
  // 'create-user-requirement.label.code': 'MSG Code',
  // 'create-user-requirement.breadcrumb.user-requirement-detail':
  //   'user-requirement Detail',

  // 'create-user-requirement.label.create-user-requirement':
  //   'Create user-requirementage',
  // 'update-user-requirement.label.update-user-requirement':
  //   'Update user-requirementage',

  'user-requirement.title-update': 'Update User Requirement',
  'user-requirement.title-create': 'Create User Requirement',
  'user-requirement.label.name' : 'User Requirement Name',
  'user-requirement.place-holder.name' : 'User Requirement Name',

  'user-requirement.piority-low': 'Low',
  'user-requirement.piority-medium': 'Medium',
  'user-requirement.piority-high': 'High',
  'user-requirement.piority-urgent': 'Urgent',
}
