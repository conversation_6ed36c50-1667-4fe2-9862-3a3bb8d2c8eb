export default {
    'project.breadcrumb': 'Projects',
    'project.action.new': 'Create Project',
    'project.table.title': 'Project List',
    'project.info.group': 'Group',
    'project.info.project-manager': 'Project Manager',
    'project.info.project-status': 'Project Status',
    'project.info.customer': 'Customer',
    'project.info.rank': 'Rank',
    'project.info.start-date': 'Start Date',
    'project.info.end-date': 'End Date',    
    'project.info.project-contract-type': 'Project Contract Type',
    'project.info.industry': 'Industry',
    'project.info.project-category': 'Project Category',
    'project.info.project-scope': 'Project Scope',
    'project.info.requirement-methodology': 'Requirement Methodology',
    'project.info.project-description': 'Project Description',
    'project.pagination.of': 'of',
    'project.pagination.items': 'items',
    'project.action.add-new': 'Add new',
    'project.detail.breadcrumb': 'Project Details',
    'project.detail.project-list': 'Project List',
    'project.detail.back': 'Back to project list',
    'project.detail.tabs.information': 'Project Information',
    'project.detail.tabs.members': 'Project Members',
    'project.detail.tabs.products': 'Products',
    'project.detail.tabs.requirement-document': 'Requirement Document',
    'project.detail.tabs.requirement-dashboard': 'Requirement Dashboard',
    'project.detail.tabs.project-config': 'Project Configuration',

    'project.detail.tabs.members.add-a-new-member': 'ADD NEW A MEMBER',
    'project.table.project-code': 'Project Code',
    'project.table.group': 'Group',
    'project.table.project-management': 'Project Manager',
    'project.table.project-status': 'Project Status',
    'project.table.customer': 'Customer',
    'project.table.rank': 'Rank',
    'project.table.start-date': 'Start Date',
    'project.table.end-date': 'End Date',
    'project.table.project-contract-type': 'Project Contract Type',
    'project.table.industry': 'Industry',
    'project.table.project-catagory': 'Project Category',
    'project.table.action': 'Action',
    'project.label.confirm': 'Confirm',
    'project.message.confirm': 'Are you sure to change role of member?',
    'project.message.warning': 'You can not to delete all roles of member',
    'project.title.confirm': 'CONFIRM',
    'project.title.warning': 'WARNING',
    'project.config.generate-srs': 'Generate SRS',
    'project.config.export-comments': 'Generate SRS',
    'project.config.default-paging':'Default Paging',
    'project.config.project-overview':'Project Overview',
    'project.config.abbreviations': 'Abbreviations',
    'project.config.confluence': 'Confluence',
    'project.config.project': 'Project',
    'project.config.product': 'Product',
    'project.config.confluence-space-key': 'Confluence Space Key',
    'project.config.destination-page': 'Destination Page',
    'project.config.one-file': '1 File',
    'project.config.two-files': '2 Files (Separate Screen Description)',

    'common_create_project.page_title' : 'Create project',
    'common_update_project.page_title' : 'Update project'
}