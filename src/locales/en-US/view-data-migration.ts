export default {
  'data.header.title': 'Data Migration List',
  'data.column.code': 'Data Migration Code',
  'data.column.data-migration': 'Data Migration',

  'data.column.status': 'Status',
  'data.column.status-draft': 'Draft',
  'data.column.status-submitted': 'Submitted',
  'data.column.status-deleted': 'Deleted',
  'data.column.status-cancelled': 'Cancelled',

  'data.column.updated-by': 'Updated By',
  'data.column.updated-date': 'Updated Date',
  'data.column.action': 'Action',
  'data.button.create-uc': 'Create UC ',
  'data.pagination.of': 'of',
  'data.pagination.items': 'items',
  'data.create.data': 'Create Data Migration',
  'data.update.data': 'Update Data Migration',
  'data.update': 'Update',

  'data.detail.workflow': 'Workflow',
  'data.data-detail': 'Data Migration Detail',

  'data.info': 'Data Migration Information',
  'data.rule': 'Data Migration Rule',
  'data.reference': 'Reference',
  'data.object': 'Object',
  'data.user-requirement': 'User Requirement',
  'data.effort-estimation': 'Effort Estimation (hour)',
  'data.req-elicitation': 'Req. Elicitation',
  'data.documentation': 'Documentation',
  'data.storage': 'Storage',
  'data.jira': 'Jira',
  'data.confluence': 'Confluence',
  'data.related-links': 'Related Links',
  'data.submit': 'Submit',
  'data.cancel': 'Cancel',
  'data.save-as-draft': 'Save as draft',
  'data.create-another': 'Create another',
  'data.place-holder' : 'DATA MIGRATION',
  'create-data-migration.artefact.datamigration': 'Data Migration',
}
