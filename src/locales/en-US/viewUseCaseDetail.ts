export default {
  'view-use-case-details.column.brname': 'BR Name',
  'view-use-case-details.column.step': 'Step',
  'view-use-case-details.column.brCode': 'BR Code',
  'view-use-case-details.column.description': 'Description',

  'view-use-case-details.label.actor': 'Actor',
  'view-use-case-details.label.storage': 'Storage',
  'view-use-case-details.label.jira': 'Jira',
  'view-use-case-details.label.confluence': 'Confluence',
  'view-use-case-details.label.description': 'Description',
  'view-use-case-details.label.product': 'Product',
  'view-use-case-details.label.trigger': 'Trigger',
  'view-use-case-details.label.pre-condition': 'Pre-condition',
  'view-use-case-details.label.post-condition': 'Post-condition',
  'view-use-case-details.label.activity-flow': 'Activity Flow',
  'view-use-case-details.label.business-rule': 'Business Rule',
  'view-use-case-details.label.req-elicitation': 'Req.Elicitaion',
  'view-use-case-details.label.documentation': 'Documentation',
  'view-use-case-details.label.implementation': 'Implementation',
  'view-use-case-details.label.object': 'Object',
  'view-use-case-details.label.message': 'Message',
  'view-use-case-details.label.email-template': 'Email Template',
  'view-use-case-details.label.cbr': 'Common Business Rule',
  'view-use-case-details.label.user-requirement': 'User Requirement',
  'view-use-case-details.label.other-requirement': 'Other Requirement',
  'view-use-case-details.label.workflow': 'Workflow',
  'view-use-case-details.label.statetransition': 'State Transition',
  'view-use-case-details.label.use-case': 'Use Case',

  'view-use-case-details.label.screen': 'Screen',
  'view-use-case-details.label.ur': 'User Requirement',
  'view-use-case-details.label.mm': 'Meeting Minutes',
}
