export default {
  'object.header.title': 'Object List',
  'object.column.object-code': 'Code',
  'object.column.object-name': 'Object',
  'object.column.description': 'Description',
  'object.column.active-properties': 'Properties',
  'object.column.status': 'Status',
  'object.column.created-by': 'Created By',
  'object.column.update-by': 'Updated By',
  'object.column.action': 'Action',
  'object.button.create-object': 'Create Object',
  'object.button.export-object': 'Export Object',
  'object.pagination.of': 'of',
  'object.pagination.items': 'items',
  'object.action.delete-object': 'Delete Object',
  'object.a.header': 'Baemin - Purchase Order Management',
  'object.breadcrumb.object-list': ' Object List',
  'object.breadcrumb.object-detail': 'Object Detail',
  'object.column.update-date': 'Updated Date',
}
