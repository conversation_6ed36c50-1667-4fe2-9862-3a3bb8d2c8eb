export default {
    'common_usecase.page_title': 'Use case List',
    'common_usecase.column.usecase': 'Use Case',
    'common_usecase.column.description': 'Description',
    'common_usecase.column.object': 'Object',
    'common_usecase.column.screen': 'Screen',
    'common_usecase.column.status': 'Status',
    'common_usecase.action.create': 'Create UC',
    'common_usecase.card.usecase_information': 'Use Case Information',
    'common_usecase.usecase_information.code': 'Use Case Code',
    'common_usecase.usecase_information.version': 'Version',
    'common_usecase.usecase_information.actor': 'Actor',
    'common_usecase.usecase_information.description': 'Description',
    'common_usecase.usecase_information.trigger': 'Trigger',
    'common_usecase.usecase_information.preCondition': 'Pre-condition',
    'common_usecase.usecase_information.add_preCondition': 'Add pre-condition',
    'common_usecase.usecase_information.postCondition': 'Post-condition',
    'common_usecase.usecase_information.activity_flow': 'Activity Flow',
    'common_usecase.usecase_information.business_rule': 'Business Rule',
    'common_usecase.usecase_information.add_business_rule': 'Add Business Rule',
    'common_usecase.usecase_information.step': 'Step',
    'common_usecase.usecase_information.br_code': 'BR code',
    'common_usecase.usecase_information.reference': 'Reference',
    'common_usecase.reference_to.object': 'Object',
    'common_usecase.reference_from.screen': 'Screen',
    'common_usecase.card.audit_trail': 'Audit trail',
    'commonusecase.page_title' : 'Common Use Case',
    'common_create_usecase.page_title' : 'Create Common Use Case',
    'common_update_usecase.page_title' : 'Update Common Use Case',
  }
  