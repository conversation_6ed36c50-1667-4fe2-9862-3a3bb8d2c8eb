export default {
  'view-workflow.header.workflow-list': 'Workflow List',
  'view-workflow.header.workflow-detail': 'Workflow Detail',
  'view-workflow.column.workflow-code': 'Code',
  'view-workflow.column.workflow': 'Workflow',
  'view-workflow.column.status': 'Status',
  'view-workflow.column.update-by': 'Update By',
  'view-workflow.column.update-date': 'Update Date',
  'view-workflow.column.action': 'Action',
  'view-workflow.place-holder.wf': 'WORKFLOW',
  'view-workflow.legend.workflow-info': 'Workflow Information',
  'create-workflow.card.workflow-information': 'Workflow Information',
  'create-workflow.label.workflow-diagram': 'Workflow Diagram',
  'create-workflow.label.workflow-explanation': 'Workflow Explanation',
  'create-workflow.artefact.workflow': 'Workflow',
  'create-workflow.button.create-workflow': 'Create Workflow',
  'create-workflow.label.workflow-description': 'Workflow Description',
  'create-workflow.label.workflow-code': 'Workflow Code',

  'create-workflow.label.object': 'Object',
  'create-workflow.label.use-case': 'Use Case',
  'create-workflow.place-holder.wf': 'Workflow Name',
  'create-workflow.place-holder.name-label' : 'Workflow Name',

  'create-workflow.title-update': 'Update Workflow',
  'create-workflow.title-create': 'Create Workflow',
}
