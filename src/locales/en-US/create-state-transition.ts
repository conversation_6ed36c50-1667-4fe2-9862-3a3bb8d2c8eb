export default {
  'state.create-state-transition': 'Create State Transition',
  'state.version': 'Version',
  'state.state-transition-info': 'State Transition Information',
  'state.state-transition': 'State Transition',
  'state.state-transition-diagram': 'State Transition Diagram',
  'state.state-transition-description': 'State Transition Description',
  'state.reference': 'Reference',
  'state.function': 'Function',
  'state.scope': 'Scope',
  'state.object': 'Object',
  'state.user-requirement': 'User Requirement',
  'state.workflow': 'Workflow',
  'state.effort-estimation': 'Effort Estimation (hour)',
  'state.req-elicitation': 'Req. Elicitation',
  'state.documentation': 'Documentation',
  'state.implementation': 'Implementation',
  'state.storage': 'Storage',
  'state.jira': 'Jira',
  'state.confluence': 'Confluence',
  'state.related-links': 'Related Links',
  'state.submit': 'Submit',
  'state.cancel': 'Cancel',
  'state.save-as-draft': 'Save as draft',
  'state.create-another': 'Create another',

  'state.title-update': 'Update State Transition',
  'state.title-create': 'Create State Transition',
}
