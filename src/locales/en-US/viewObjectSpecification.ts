export default {
  'objectSpecification.header.title': 'Object Specification',
  'objectSpecification.column.order': '#',
  'objectSpecification.column.object-property': 'Object Property',
  'objectSpecification.column.object-name': 'Object Name',
  'objectSpecification.column.field': 'field',
  'objectSpecification.column.unique': 'Unique',
  'objectSpecification.column.mandatory': 'Mandatory',
  'objectSpecification.column.max-length': 'Max Length',
  'objectSpecification.column.meaning': 'Meaning',
  'objectSpecification.column.source-object': 'Source Object',
  'objectSpecification.column.source-object-property': 'Source Object Property',
  'objectSpecification.column.action': 'Action',

  'objectSpecification.view.status': 'Status',
  'objectSpecification.view.status-draf': 'Draf',
  'objectSpecification.view.status-stable': 'Stable',
  'objectSpecification.view.status-reviewed': 'Reviewed',
  'objectSpecification.view.status-signed-off': 'Signed-off',
  'objectSpecification.view.status-cancelled': 'Cancelled',

  'objectSpecification.title.actions': 'Actions',
  'objectSpecification.title.description': 'Description',
  'objectSpecification.title.source-object': 'Source Object',
  'objectSpecification.title.target-object': 'Target Object',
  'objectSpecification.title.screen': 'Screen',

  'objectSpecification.audit-trail.title': 'Audit Trail',
  'objectSpecification.audit-trail.created-by': 'Created By',
  'objectSpecification.audit-trail.created-date': 'Created Date',
  'objectSpecification.audit-trail.updated-by': 'Updated By',
  'objectSpecification.audit-trail.updated-date': 'Updated Date',
  'objectSpecification.audit-trail.submited-by': 'Submitted By',
  'objectSpecification.audit-trail.submited-date': 'Submitted Date',
  'objectSpecification.button.update-object': 'Update Object',
}
