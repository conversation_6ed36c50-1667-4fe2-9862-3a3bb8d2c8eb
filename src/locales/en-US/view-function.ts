export default {
  'function.header.title': 'Use Case List',
  'function.column.code': 'UC Code',
  'function.column.function-name': 'Use Case',
  'function.column.function-detail': 'Use Case Detail',
  'function.column.description': 'Description',

  'function.column.reviewer': 'Reviewer',
  'function.column.author': 'Author',
  'function.column.customer': 'Customer',

  'function.column.object-name': 'Object',
  'function.column.screen': 'Screen',
  'function.column.req-eliciation': 'Request Eliciation',
  'function.column.documentation': 'Documentation',
  'function.column.implementation': 'Implementation',
  'function.column.type-crud': 'CRUD',
  'function.column.type-others': 'Others',
  'function.column.type-workflow': 'Workflow',
  'function.column.status': 'Status',
  'function.column.status-draft': 'Draft',
  'function.column.status-submitted': 'Submitted',
  'function.column.status-cancelled': 'Cancelled',
  'function.column.created-by': 'Created By',
  'function.column.update-by': 'Updated By',
  'function.column.update-date': 'Updated Date',
  'function.column.action': 'Action',
  'function.button.create-uc': 'Create UC ',
  'function.button.import-function': 'Import UC ',
  'function.button.export-function': 'Export UC ',
  'function.pagination.of': 'of',
  'function.pagination.items': 'items',
  'function.place-holder-brType' : 'Rule Type',
  'function.place-holder-brDescription' : 'Rule Description',
  'function.default.description':'This Use Case allow user to {Use Case}',
}
