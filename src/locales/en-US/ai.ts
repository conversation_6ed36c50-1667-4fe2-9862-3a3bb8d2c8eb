const aiLocale = {
  'ai.agent.name': 'Orca AI',
  'ai.send-token.description': 'Token that uses the AI agent to generate responses.',
  'ai.receive-token.description': 'Token that receives the AI agent\'s response.',
  'ai.welcome-message.title': 'Welcome to Orca AI Assistant',
  'ai.welcome-message.description': 'I\'m here to help you analyze requirements, generate documentation, and improve your business analysis workflow.',
  'ai.save-artefact.title': 'Save output',
  'ai.save-artefact.parsing': 'Data is being parsed, please wait...',
  'ai.save-artefact.no-items': 'No valid artefacts found in the canvas. Please ensure you have created actors, objects, use cases, screens, or workflows before saving.',
  'ai.save-artefact.description': 'Below are the artefacts generated by AI that you can save to the system. Please review carefully before saving.',
  'ai.save-artefact.note.1': 'This list only includes valid items from the list in the canvas screen.',
  'ai.save-artefact.note.2': 'If you need to make changes, please go back to the canvas screen to edit, or save and then edit each artefact individually in their respective screens.',

  'ai.save-artefact.status-column': 'Saving Status',
  /**
   * Artefact section titles for the save artefact dialog.
   */
  'ai.save-artefact.actors.title': 'Actors',
  'ai.save-artefact.actors.name-column': 'Actor Name',
  'ai.save-artefact.actors.description-column': 'Description',

  'ai.save-artefact.objects.title': 'Objects',
  'ai.save-artefact.objects.name-column': 'Object Name',
  'ai.save-artefact.objects.description-column': 'Description',

  'ai.save-artefact.useCases.title': 'Use Cases',
  'ai.save-artefact.useCases.name-column': 'Use Case Name',
  'ai.save-artefact.useCases.description-column': 'Description',

  'ai.save-artefact.workflows.title': 'Workflows',
  'ai.save-artefact.workflows.name-column': 'Workflow Name',
  'ai.save-artefact.workflows.description-column': 'Description',

  'ai.save-artefact.screens.title': 'Screens',
  'ai.save-artefact.screens.name-column': 'Screen Name',
  'ai.save-artefact.screens.description-column': 'Description',

  'ai.save-artefact.userRequirements.title': 'User Requirements',
  'ai.save-artefact.userRequirements.name-column': 'Requirement Name',
  'ai.save-artefact.userRequirements.type-column': 'Type',
  'ai.save-artefact.userRequirements.priority-column': 'Priority',
  'ai.save-artefact.userRequirements.description-column': 'Description',
}

export default aiLocale
