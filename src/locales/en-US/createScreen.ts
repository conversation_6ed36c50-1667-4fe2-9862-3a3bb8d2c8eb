export default {
  'createscreen.header.title': 'Screen Information',
  'createscreen.input-placehold.screen-name': 'Screen Name',
  'createscreen.input-placehold.version': 'Version',
  'createscreen.card-title.screen-information': 'Screen Information',
  'createscreen.label.object': 'Object',
  'createscreen.label.description': 'Description',
  'createscreen.label.access': 'Access',
  'createscreen.label.mockup': 'Mockup Screen',
  'createscreen.label.screen-description': 'Screen Description',
  'createscreen.label.screen-name': 'Screen Name',
  'createscreen.label.title': 'Create Common Screen',
  'updatescreen.label.title': 'Update Common Screen',

  'createscreen.column.order': 'No',
  'createscreen.column.component-type': 'Comp.Type',
  'createscreen.column.component': 'Component',
  'createscreen.column.editable': 'Editable',
  'createscreen.column.mandatory': 'Mandatory',
  'createscreen.column.default-value': 'Default Value',
  'createscreen.column.description': 'Description',
  'createscreen.column.action': 'Action',
  'createscreen.button.new-component': 'New Component',
  'createscreen.button.select-objproperties': 'Add component from Object',

  'createscreen.card-title.reference': 'Reference',
  'createscreen.label.actor': 'Actor',
  'createscreen.label.usecase': 'Use Case',
  'createscreen.label.messages': 'Messages',
  'createscreen.label.email-template': 'Email Templates',
  'createscreen.label.user-requirement': 'User Requirement',
  'createscreen.label.other-requirement': 'Other Requirements',
  'createscreen.label.requirement': 'Requirement',
  'createscreen.button.add': 'Add to table',
  'createscreen.card-title.effort': 'Effort Estimation (hour)',
  'createscreen.label.req': 'Req. Elicitation',
  'createscreen.label.documentation': 'Documentation',
  'createscreen.label.implementation': 'Implementation',

  'createscreen.card-title.related-links': 'Related Links',
  'createscreen.label.Storage': 'Storage',
  'createscreen.label.JIRA': 'JIRA',
  'createscreen.label.confluence': 'Confluence',

  'createscreen.label-modal.component': 'Component',
  'createscreen.label-modal.compType': 'Comp. Type',
  'createscreen.label-modal.type': 'Type',
  'createscreen.label-modal.object': 'Source Object',
  'createscreen.label-modal.object-property': 'Source Object Property',
  'createscreen.label-modal.screen': 'Target Screen',
  'createscreen.label-modal.use-case': 'Target Use Case',
  'createscreen.label-modal.editable': 'Editable',
  'createscreen.label-modal.mandatory': 'Mandatory',
  'createscreen.label-modal.default': 'Default',
  'createscreen.label-modal.description': 'Description',
  'createscreen.label-modal.screen-code': 'Screen Code',
  'createscreen.label.development': 'Development',

  'createscreen.page_create_title': 'Create Mockup Screen',
  'createscreen.page_update_title': 'Update Mockup Screen',
  'createscreen.page_export_title': 'Export Mockup Screen',
}
