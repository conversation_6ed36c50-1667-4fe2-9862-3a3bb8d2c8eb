export default {
  CDF_0: 'Are you sure you want to delete this comment?',
  CDF_1: 'Are you sure you want to delete this reply?',
  CDF_7: 'Are you sure you want to delete this {artefact_type}?',
  CFD_7: 'Are you sure you want to delete this {artefact_type}?',
  CFD_1: 'Are you sure you want to delete this item?',
  IEM_1: 'This field must not left blank.',
  IEM_2: 'Complete Date must be equal or greater than Due Date.',
  IEM_3: 'Duplicated common component name submitted from this project. Please choose a different name.',
  IEM_3_1: 'Complete Date must be equal or greater than Current Date.',
  IEM_3_2: 'Due Date must be equal or greater than Current Date.',
  IEM_4: 'Version must be in correct format as "n.n.n" with n is an integer number from 1 to 99. Example: 0.1.15',
  IEM_5: 'Please input a valid version greater than or equal to the current version.',
  warning_max_length_255: 'This field must be <= 255 characters.',
  CFD_3: 'Are you sure you want to discard the changes?',
  CFD_4: 'Are you sure you want to delete this {Artefact} ?',
  CFD_6: 'Are you sure you want to submit this {Artefact}?',
  CFD_5: 'Are you sure you want to discard the changes?', // is cfd_1
  MT_C: 'Are you sure you want to generate permission matrix?',
  MT_ES: 'Update success',
  CFD_6_1: 'Are you sure you want to create this {Artefact}?',
  CFD_6_2: 'Are you sure you want to update this {Artefact}?',
  CFD_6_3: 'Are you sure you want to approve this {Artefact}?',
  CFD_6_4: 'Are you sure you want to cancel this {Artefact}?',
  CFD_6_5: 'Are you sure you want to reject this {Artefact}?',
  CFD_6_6: 'Are you sure you want to endorse this {Artefact}?',
  CFD_6_7: 'Are you sure you want to remove this {Artefact}?',
  CFD_6_8: 'Are you sure you want to submit this Change Request?',
  CFD_6_9: 'This {Artefact} is linked to other items. Are you sure you want to cancel this {Artefact}?',
  CFD_11: 'Are you sure you want to generate the SRS?',
  CFD_13: ' Are you sure you want to generate the quality report?',
  ESMG_99: 'Please select other values. Selected artefacts not found or removed:',
  EMSG_UPLOAD_FAILURE: 'Only images are allowed (.pdf, .jpeg, .jpg, .png). Please select other file.',
  EMSG_UPDATE_CONCURRENCY: 'The {Artefact} has been updated by another user. Please reload your page',
  REQUIRED_PERMISSION: 'You need permission to perform this action',
  CANNOT_ACTION: 'You cannot perform this action',
  SCD_1: 'A new {Artefact} is created successfully',
  SCD_9: 'The common requirement is recommended successfully',
  CFD_8: 'Have you completely removed any customer related information yet?',
  SCD_10: 'The {Artefact} is exported successfully',
  EMSG_1: 'You must specify a value for {artefact_type}.',
  EMSG_1_1: 'This {field} has been used in other {Artefact}.',
  EMSG_3: 'Cannot delete this item. It is being referred in other items.',
  EMSG_4: 'This {Artefact} {field} has already existed in the system.',
  EMSG_5: 'You cannot recommend an empty common requirement. Please add artefact before submitting again.',
  EMSG_6: 'The order of this {Artefact} has already existed in the project',
  EMSG_7: 'The {Artefact} has already existed in the project',
  EMSG_8: 'This {Artefact} is referred from other items and cannot be deleted. Please remove all items in Reference From section first.',
  EMSG_9: 'This {artefact_type} has been signed-off. You cannot edit this {artefact_type}.',
  EMSG_10: 'Uploading file must follow Default Template.',
  EMSG_11: 'Uploading file must not exceed 5 MB.',
  EMSG_11_1: 'Uploading file must not exceed 15 MB.',
  EMSG_12: 'Please add property for this {Artefact}.',
  EMSG_13: 'Please add attached file for this {Artefact}.',
  EMSG_14: 'Please add Use Case which this Screen is referred to.',
  EMSG_15: 'Please add Component for this Screen.',
  EMSG_16: 'Please add all information for these Business Rule',
  EMSG_17: 'Please add Actor for this Use Case',
  EMSG_18: 'Please add Trigger for this Use Case',
  EMSG_33: 'Please add Description for this Use Case',
  EMSG_19: 'Please add Object which this Use Case is referred to',
  EMSG_20: 'Please add Pre-Condition for this Use Case',
  EMSG_21: 'Please close all related comments before proceeding Approval.',
  EMSG_22: 'Please add Description for this User Requirement.',
  EMSG_23: 'Please add comment for this {Artefact} before rejecting.',
  EMSG_24: 'Cannot cancel this item. It is being referred in other items.',
  EMSG_25: 'Cannot remove this item. It is being referred in other items.',
  EMSG_26: 'There is no data to export.',
  EMSG_27: 'You have to input Reason for Rejection.',
  EMSG_MM: 'Cannot delete this item: These User Requirements are being used in other artefacts: ',
  EMSG_31: 'Please add Pre-condition for this Use Case',
  EMSG_31A: 'Please add Pre-condition Type',
  EMSG_31B: 'Please add Pre-condition Description',
  EMSG_32: 'Your project has too many pending recommended common requirements. Please contact the "Common Requirement Committee" to speed up the review process before you can submit it again.',
  EMSG_34: 'Please add Component Name or Comp.Type of Screen Component',
  EMSG_35: 'You’re not allowed to proceed approval with the current user role. Please contact project PM for details.',
  EMSG_36: 'Please add Object Property Name or Meaning of Object Property',
  EMSG_36A: 'Please add Source Object Property of Object Property',
  EMSG_37: 'The following item is currently being edited.',
  MSG0100: "Please resolve all customer's comments and close all internal comments before proceeding Approval",
  MSG0101: 'Please resolve all related comments before Submitting',
  EMSG_38: 'Please add Due Date to approve this {Artefact}',
  EMSG_39: 'Cannot re-open {Artefact}. Please check this {Artefact} again.',
  EMSG_40: 'Cannot cancel {Artefact}. Please check this {Artefact} again.',
  EMSG_41: 'Please enter a positive number to step.',
  EMSG_42: 'Data Synchronization is in-progress. Please wait a few minutes.'
}