export default {
  'non.functional.list': 'Non-Functional Requirement List',
  'non.functional.nfrlist': 'NFR List',

  'non.functional.create': 'Create Non-Functional Requirement',
  'non.functional.createnfr': 'Create NFR',
  'non.functional.update': 'Update Non-Functional Requirement',

  'non.functional': 'Non-Functional',
  'non.functional.req': 'Non-Functional Requirement',
  'non.functional.info': 'Non-Functional Requirement Information',
  'non.functional.version':'Version',
  'nfr.column.code': 'NFR Code',
  'nfr.column.category': 'NFR Category',
  'nfr.column.sub-category': 'NFR Subcategory',
  'nfr.column.type': 'NFR Type',
  'nfr.column.varia': 'Variables/Criteria',
  'nfr.column.remark': 'Remarks',

  'nfr.column.status': 'Status',
  'nfr.status.draft': 'Draft',
  'nfr.status.submitted': 'Submitted',
  'nfr.status.cancelled': 'Cancelled',

  'nfr.column.updated-by': 'Updated by',
  'nfr.column.updated-date': 'Updated Date',
  'nfr.column.action': 'Action',
  'non.functional.detail': 'Non-Functional Requirement Detail',

  'non.functional.message-warning' : 'No available Subcategory for this type of Category',

  'epic.column.name': 'Name',
  'epic.column.description' : 'Description'
}
