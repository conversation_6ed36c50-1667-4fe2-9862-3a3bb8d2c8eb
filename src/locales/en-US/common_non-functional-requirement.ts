export default {
    'common.nfr.list': 'Non-Functional Requirement List',
    'common.nfr.nfrlist': 'NFR List',
  
    'common.nfr.create': 'Create Non-Functional Requirement',
    'common.nfr.createnfr': 'Create NFR',
    'common.nfr.update': 'Update Non-Functional Requirement',
  
    'common.nfr': 'Non-Functional',
    'common.nfr.req': 'Non-Functional Requirement',
    'common.nfr.info': 'Non-Functional Requirement Information',
    'common.nfr.version':'Version',
    'common.nfr.column.code': 'NFR Code',
    'common.nfr.column.category': 'NFR Category',
    'common.nfr.column.sub-category': 'NFR Subcategory',
    'common.nfr.column.type': 'NFR Type',
    'common.nfr.column.varia': 'Variables/Criteria',
    'common.nfr.column.remark': 'Remarks',
  
    'common.nfr.column.status': 'Status',
    'common.nfr.status.draft': 'Draft',
    'common.nfr.status.submitted': 'Submitted',
    'common.nfr.status.cancelled': 'Cancelled',
  
    'common.nfr.column.updated-by': 'Updated by',
    'common.nfr.column.updated-date': 'Updated Date',
    'common.nfr.column.action': 'Action',
    'common.nfr.detail': 'Non-Functional Requirement Detail',
  
    'common.non.functional.message-warning' : 'No available Subcategory for this type of Category'
  }
  