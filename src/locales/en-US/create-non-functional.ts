export default {
  'non.functional.list': 'Non-Functional Requirement List',
  'non.functional.create': 'Create Non-Functional Requirement',

  'nfr.form.code': 'NFR Code',
  'nfr.form.nfr-code': 'Non-Functional Requirement Code',
  'nfr.form.category': 'NFR Category',
  'nfr.form.type': 'NFR Type',
  'nfr.form.status': 'Status',
  'nfr.form.updated-by': 'Updated by',
  'nfr.form.updated-date': 'Updated Date',
  'nfr.form.action': 'action',
  'nfr.form.version': 'version',
  'non.functional.info': 'Non-Functional Requirement Information',
  'nfr.form.variables-criteria': 'Variables/Criteria',
  'nfr.form.remarks': 'Remarks',
  'nfr.form.effort-estimation': 'Effort Estimation (hour)',
  'nfr.form.req-elicitation': 'Req. Elicitation',
  'nfr.form.documentation': 'Documentation',
  'nfr.form.implementation': 'Implementation',
  'nfr.form.related-links': 'Related Links',
  'nfr.form.storage': 'Storage',
  'nfr.form.jira': 'Jira',
  'nfr.form.confluence': 'Confluence',
  'nfr.form.create-another': 'Create another',
  'nfr.form.submit': 'Submit',
  'nfr.form.cancel': 'Cancel',
  'nfr.form.save-as-draft': 'Save as draft',

  'nfr.category.performance-requirements': 'Performance Requirement',
  'nfr.category.safety-requirements': 'Safety Requirements',
  'nfr.category.security-requirements': 'Security Requirements',
  'nfr.category.software-quality-attributes': 'Software Quality Attributes',

  'nfr.subcategory.usability': 'Usability',
  'nfr.subcategory.reliability': 'Reliability',
  'nfr.subcategory.functional-suitability': 'Functional Suitability',
  'nfr.subcategory.compliance': 'Compliance',
  'nfr.subcategory.constraint': 'Constraint',

  'nfr.type.response-time': 'Response Time',
  'nfr.type.workload': 'Workload',
  'nfr.type.Scalability': 'Scalability',
  'nfr.type.Platform': 'Platform',
  'nfr.type.Authenticity': 'Authenticity',
  'nfr.type.Privacy': 'Privacy',
  'nfr.type.Authentication': 'Authentication',
  'nfr.type.Accessibility': 'Accessibility',
  'nfr.type.Internationalisation': 'Internationalisation',
  'nfr.type.Ease-of-Use': 'Ease of Use',
  'nfr.type.Availability': 'Availability',
  'nfr.type.Backup': 'Backup',
  'nfr.type.Accuracy': 'Accuracy',
  'nfr.type.Completeness': 'Completeness',
  'nfr.type.Regulatory-Compliance': 'Regulatory Compliance',
  'nfr.type.Price': 'Price',
  'nfr.type.Timeline': 'Timeline',

  'nfr.varia.Measurement-Point': 'Measurement Point',
  'nfr.varia.Statistic-Type': 'Statistic Type',
  'nfr.varia.Measurement-Period': 'Measurement Period',
  'nfr.varia.Platform': 'Platform',
  'nfr.varia.Error-Rate': 'Error Rate',
  'nfr.varia.Workload percentage at peak time':
    'Workload percentage at peak time',
  'nfr.varia.Workload percentage at off-peak time':
    'Workload percentage at off-peak time',
  'nfr.varia.Ease of Scalability': 'Ease of Scalability',
  'nfr.varia.Hardware': 'Hardware',
  'nfr.varia.Software': 'Software',
  'nfr.varia.External System Integration': 'External System Integration',
  'nfr.varia.Mode of authentication': 'Mode of authentication',
  'nfr.varia.Password and message encryption':
    'Password and message encryption',
  'nfr.varia.Success rate in authentication': 'Success rate in authentication',
  'nfr.varia.Resistance to known attacks': 'Resistance to known attacks',
  'nfr.varia.Probability/time/resources to detect an attack':
    'Probability/time/resources to detect an attack',
  'nfr.varia.Percentage of useful services still available during an attack':
    'Percentage of useful services still available during an attack',
  'nfr.varia.Percentage of successful attacks':
    'Percentage of successful attacks',
  'nfr.varia.Lifespan of a password, of a session':
    'Lifespan of a password, of a session',
  'nfr.varia.Encryption level': 'Encryption level',
  'nfr.varia.Color-blind users': 'Color-blind users',
  'nfr.varia.Voice-over for hearing impaired users':
    'Voice-over for hearing impaired users',
  'nfr.varia.Time zone adaptation': 'Time zone adaptation',
  'nfr.varia.Support multiple currencies': 'Support multiple currencies',
  'nfr.varia.Support multiple languages': 'Support multiple languages',
  'nfr.varia.Maximum number of clicks to complete any operation':
    'Maximum number of clicks to complete any operation',
  'nfr.varia.Learnability': 'Learnability',
  'nfr.varia.Efficiency': 'Efficiency',
  'nfr.varia.Memorability': 'Memorability',
  'nfr.varia.Error Avoidance': 'Error Avoidance',
  'nfr.varia.User Satisfaction': 'User Satisfaction',
  'nfr.varia.Percentage of time available': 'Percentage of time available',
  'nfr.varia.Backup frequency': 'Backup frequency',
  'nfr.varia.Accuracy Rate': 'Accuracy Rate',
  'nfr.varia.Completeness Percentage': 'Completeness Percentage',
  'nfr.varia.Data Protection': 'Data Protection',
  'nfr.varia.Target price for the solution': 'Target price for the solution',
  'nfr.varia.Limit for extensions': 'Limit for extensions',
  'nfr.varia.Subscription allowance': 'Subscription allowance',
  'nfr.varia.Release duration': 'Release duration',
}
