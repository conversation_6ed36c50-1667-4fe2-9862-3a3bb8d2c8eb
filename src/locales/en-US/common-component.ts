export default {
  'common_component.page_title': 'Common Component',
  'common_component.column.name': 'COMPONENT NAME',
  'common_component.column.name-short': 'Name',
  'common_component.column.component_name': 'Name',
  'common_component.column.description': 'Description',
  'common_component.column.properties': 'Component Properties',
  'common_component.column.object': 'Object',
  'common_component.column.use_case': 'Use Case',
  'common_component.column.screen': 'Screen',
  'common_component.column.status': 'Status',
  'common_component.column.warning_message': 'Warning Message',
  'common_component.action.create': 'Create Component',
  'common_component.action.add': 'Add',
  'common_component.action.leave_request': 'Leave Request',
  'common_component.card.general_information': 'General Information',
  'common_component.scope.all_projects': 'All Projects',
  'common_component.scope.current_customer': 'Current Customer',
  'common_component.card.component_detail': 'Component Detail',
  'common_component.card.objects': 'Objects',
  'common_component.card.function': 'Use Case',
  'common_component.card.screens': 'Screens',
  'common_component.card.others': 'Others',
  'common_component.card.message': 'Message',
  'common_component.card.email-template': 'Email Template',
  'common_component.card.user_profile_detail': 'User Profile Detail',
  'common_component.card.missing_mentioned': 'Missing Mentioned',
  'common_component.card.missing_referenced': 'Missing Referenced',
  'common_component.card.nfr': 'Non-Functional Requirement',
}
