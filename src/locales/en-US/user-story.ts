export default {
  'user-story.header.title': 'User Story List',
  'user-story.header.title-short': 'User Story',
  'user-story.header.title-detail': 'USR List',
  'user-story.column.user-requirement-code': 'Code',
  'user-story.column.user-summary': 'Summary',
  'user-story.column.epic': 'Epic',
  'user-story.column.sprint': 'Sprint',
  'user-story.column.assignee': 'Assignee',
  'user-story.column.project': 'Project',
  'user-story.column.component': 'Component',
  'user-story.column.uscode': 'US Code',
  'user-story.column.label': 'Label',
  'user-story.column.summary': 'Summary',
  'user-story.column.estimation': 'Estimation',
  'user-story.column.reviewer': 'Reviewer',
  'user-story.column.story-point': 'Story point',
  'user-story.column.due-date': 'Due date',
  'user-story.column.priority': 'Priority',
  'user-story.column.description': 'Description',
  'user-story.column.acceptance-criteria': 'Acceptance Criteria',
  'user-story.column.product' : 'Product',
  'user-story.column.objects' : 'Objects',
  'user-story.column.useCase' : 'Use Case',
  'user-story.column.screens' : 'Screens',
  'user-story.error-data' : 'Error Data',
  'user-story.qualified' : 'Qualified',
  


  'user-story.column.sender': 'Sender',
  'user-story.column.send-date': 'Send Date',
  'user-story.column.status': 'Status',
  'user-story.column.action': 'Action',
  'user-story.column.usrdetails': 'USR Details',
  'user-story.column.req-eliciation': 'Request Eliciation',
  'user-story.column.documentation': 'Documentation',
  'user-story.column.development': 'Development',
  'user-story.column.error-type': 'Error Type',
  'user-story.column.row-number': 'Row Number',


  'user-story.title-create': 'Create User Story',
  'user-story.title-update': 'Update User Story',

  'user-story.button.create-user-requirement': 'Create US',
  'user-story.button.import-user-story': 'Import US',
  'user-story.button.export-user-story': 'Export US',
  'user-story.file-name-export': 'User Story',
  'user-story.artefact': 'User Story Management',
  'user-story.button-cancel-user-story': 'Cancel',
  'user-story.column.user-story': 'User Story',


  'user-story.create-modal-title.import': 'Import',
  'user-story.create-modal-title.export': 'Export',
  'user-story.label.user-requirement-details': 'User Requirement Details',
  'user-story.label.please-specify-a-source': 'Please Specify A Source',
  'user-story.label.select-a-meeting-minutes': 'Select A Meeting Minutes',
  'user-story.label.select-a-reference-document':
    'Select A Reference Document',
  'user-story.label.select-a-reference-cbr': 'Select A Reference Common Business Rule',
  'user-story.column.source-type': 'Source Type',
  'user-story.label.user-requirement-infomation':
    'User Requirement Information',
  'user-story.label.details': 'User Requirement Details',
  'user-story.button.add-user-requirement': 'Add User Requirement',
  'user-story.reference.common-bussiness-rule': 'Common Business Rule',
  'user-story.reference.meeting': 'Meeting Minutes',
  'user-story.reference.cbr': 'Common Business Rule',
  'user-story.place-holder': 'USER REQUIREMENT',
  'user-story.button.create-us': 'Create US ',

  // 'user-story.pagination.of': 'of',
  // 'user-story.pagination.items': 'items',
  // 'user-story.action.delete-user-requirement': 'Delete user-requirement',

  // 'create-user-story.title.create-user-requirementage':
  //   'Create user-requirementage',
  // 'create-user-story.card.user-requirementage-infomation':
  //   'user-requirementage Information',
  // 'create-user-story.label.category': 'Category',
  // 'create-user-story.label.user-requirementage': 'user-requirementage',
  // 'create-user-story.label.code': 'MSG Code',
  // 'create-user-story.breadcrumb.user-requirement-detail':
  //   'user-requirement Detail',

  // 'create-user-story.label.create-user-requirement':
  //   'Create user-requirementage',
  // 'update-user-story.label.update-user-requirement':
  //   'Update user-requirementage',
}
