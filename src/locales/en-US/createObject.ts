export default {
  'createobject.header.title': 'Create Object',
  'updateobject.header.title': 'Update Object',
  'createobject.column.order': '#',
  'createobject.column.object-property': 'Object Property',
  'createobject.column.unique': 'Unique',
  'createobject.column.mandatory': 'Mandatory',
  'createobject.column.max-length': 'Max Length',
  'createobject.column.meaning': 'Meaning',
  'createobject.column.source-object': 'Source Object',
  'createobject.column.source-object-property': 'Source Object Property',
  'createobject.column.action': 'Action',
  'createobject.select.status-draf': 'Draf',
  'createobject.select.status-stable': 'Stable',
  'createobject.label.object-no': 'Object No',
  'createobject.label.object-name': 'Object Name',
  'createobject.label.object-code': 'Object Code',
  'createobject.label.description': 'Description',
  'createobject.label.user-requirement': 'User Requirement',
  'createobject.label.product': 'Product',
  'createobject.label.property': 'Property',
  'createobject.label.status': 'Status',
  'createobject.label.actor': 'Actor',
  'createobject.button.create': 'Create',
  'createobject.button.cancel-by': 'Cancel',
  'createobject.checkbox.create-another': 'Create another',
  'createobject.checkbox.add-to-table': 'Add to table',
  'createobject.button.add-new': 'Add New',
  'createobject.place-holder.object-name': 'Object Name',
  'createobject.card-title.object-infomation': 'Object Information',
  'createobject.card-title.related-links': 'Related links',
  'createobject.label.storage': 'Storage',
  'createobject.label.jira': 'Jira',
  'createobject.label.confluence': 'Confluence',
  'createobject.label.none': 'None',
  'createobject.create-modal-title.add-property': 'Add Property',
  'createobject.create-modal-title.update-property': 'Update Property',
  'createobject.card-title.reference': 'Reference',
  'createobject.label.source-object': 'Source Object',
  'createobject.label.target-object': 'Target Object',
  'createobject.label.use-case': 'Use Case',
  'createobject.label.screen': 'Screen',
  'createobject.label.workflow': 'Workflow',
  'createobject.label.state-transition': 'State Transition',
  'createobject.label.data-migration': 'Data Migration',
  'createobject.place-holder.version': 'Version',

  'createobject.page_create_title': 'Create Object',
  'createobject.page_update_title': 'Update Object',
}
