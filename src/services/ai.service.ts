import { apiCall } from '@/helper/api/aloApi'
import { API_URLS } from '@/constants'
import { extractProjectCode } from '@/helper/share'

import {
  Agent,
  AgentPromptUpdate,
  ArtefactParseResult,
  Conversation,
  ConversationCreateRequest,
  FileCreateRequest,
  FileCreateResponse,
  IFIle,
  ITokenUsageRequest,
  ITokenUsageResponse,
  Message,
  MessageEvent,
  ParseRequest,
  SendMessageRequest,
} from '@/modules/_shared/ai'
import { fetchEventSource } from '@microsoft/fetch-event-source'

export interface AIApiResponse<T = any> {
  data: T
  metadata: {
    total: number
    offset: number
    limit: number
  }
  error: string
  message?: string
}

class _AIService {
  /**
   * Get available agents
   */
  async getListAgents(): Promise<Agent[]> {
    try {
      const response = await apiCall('GET', API_URLS.AI_AGENTS).then(
        ({ data }) => data
      )
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch agents: ${error.message}`)
    }
  }

  /**
   * Get project-specific agents
   */
  async getListProjectAgents(projectCode: string): Promise<Agent[]> {
    try {
      const response = await apiCall(
        'GET',
        API_URLS.AI_PROJECT_AGENTS(projectCode)
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch project agents: ${error.message}`)
    }
  }

  /**
   * Get project-specific agent details
   */
  async getProjectAgentDetails(
    projectCode: string,
    agentCode: string
  ): Promise<Agent> {
    try {
      const response = await apiCall(
        'GET',
        API_URLS.AI_PROJECT_AGENT_DETAIL(projectCode, agentCode)
      ).then(({ data }) => data)
      
      console.log('Raw API response for agent details:', response)
      
      // Check if response has nested data property
      return response.data || response
    } catch (error: any) {
      console.error('API Error in getProjectAgentDetails:', error)
      throw new Error(`Failed to fetch project agent details: ${error.message}`)
    }
  }

  async getAgentDetails(agentCode: string): Promise<Agent> {
    try {
      const response = await apiCall(
        'GET',
        `${API_URLS.AI_AGENTS}/${agentCode}`
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch agents: ${error.message}`)
    }
  }

  /**
   * Update project-specific agent instructions
   */
  async updateProjectAgentInstructions(
    projectCode: string,
    agentCode: string,
    request: { systemPrompt: string; additionalPrompt?: string }
  ): Promise<Agent> {
    try {
      // Prepare the complete payload as per the API requirement
      const payload = {
        systemPrompt: request.systemPrompt,
        additionalPrompt: request.additionalPrompt || '',
        modelId: 'gpt-4.1', // Default model
        project: projectCode,
        status: 0,
      }

      const response = await apiCall(
        'PATCH',
        API_URLS.AI_PROJECT_AGENT_DETAIL(projectCode, agentCode),
        payload
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(
        `Failed to update project agent instructions: ${error.message}`
      )
    }
  }

  async updateAgentInstruction(
    agentCode,
    request: AgentPromptUpdate
  ): Promise<Agent> {
    try {
      const response = await apiCall(
        'PATCH',
        API_URLS.AGENT_CONFIG(agentCode),
        request
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to update agent: ${error.message}`)
    }
  }

  async toggleAgentStatus(agentCode: string): Promise<Agent> {
    try {
      const response = await apiCall(
        'PATCH',
        API_URLS.AGENT_TOGGLE(agentCode),
        {}
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to toggle agent status: ${error.message}`)
    }
  }

  /**
   * List all conversations
   */
  async listConversations(
    projectId = extractProjectCode(),
    limit: number = 50,
    offset: number = 0
  ): Promise<AIApiResponse<Conversation[]>> {
    try {
      const params = { projectId, limit, offset }
      return await apiCall('GET', API_URLS.AI_CONVERSATIONS, params).then(
        ({ data }) => data
      )
    } catch (error: any) {
      throw new Error(`Failed to fetch conversations: ${error.message}`)
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(
    request: ConversationCreateRequest
  ): Promise<Conversation> {
    try {
      const response = await apiCall(
        'POST',
        API_URLS.AI_CONVERSATIONS,
        request
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to create conversation: ${error.message}`)
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<AIApiResponse> {
    try {
      const response = await apiCall(
        'DELETE',
        API_URLS.AI_DELETE_CONVERSATION(conversationId)
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to delete conversation: ${error.message}`)
    }
  }

  /**
   * List messages in a conversation
   */
  async listMessages(
    conversationId: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<AIApiResponse<Message[]>> {
    try {
      const params = { limit, offset }
      const response = await apiCall(
        'GET',
        API_URLS.AI_CONVERSATION_MESSAGES(conversationId),
        params
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch messages: ${error.message}`)
    }
  }

  /**
   * Send a message to the AI assistant
   */
  async sendMessage({
    conversationId,
    ...request
  }: SendMessageRequest): Promise<AIApiResponse> {
    try {
      const response = await apiCall(
        'POST',
        API_URLS.AI_CONVERSATION_MESSAGES(conversationId),
        request
      ).then(({ data }) => data)

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to send message: ${error.message}`)
    }
  }

  /**
   * Update message content
   */
  async updateMessageContent(
    conversationId: string,
    messageId: string,
    content: string
  ): Promise<Message> {
    try {
      const response = await apiCall(
        'PATCH',
        `${API_URLS.AI_CONVERSATION_MESSAGES(
          conversationId
        )}/${messageId}/content`,
        { content }
      ).then(({ data }) => data)

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to update message content: ${error.message}`)
    }
  }

  /**
   * Send a streaming message to the AI assistant using Server-Sent Events
   * The POST /v1/conversations/:id/messages endpoint returns an event stream directly
   */
  async sendStreamingMessage(
    request: SendMessageRequest,
    onChunk: (event: MessageEvent) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<void> {
    try {
      // Create EventSource directly to the messages endpoint
      const url = API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId)

      // We need to make a streaming request to the same endpoint
      await this.createStreamingRequest(
        url,
        request,
        onChunk,
        onComplete,
        onError
      )
    } catch (error: any) {
      onError(new Error(`Streaming failed: ${error.message}`))
    }
  }

  /**
   * Create a streaming request using fetch with ReadableStream
   */
  private async createStreamingRequest(
    url: string,
    messageRequest: SendMessageRequest,
    onChunk: (event: MessageEvent) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<AbortController> {
    const controller = new AbortController()
    try {
      // Get auth token and project code for headers
      const accessToken = localStorage.getItem('accessToken')
      const projectCode = extractProjectCode()

      await fetchEventSource(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ProjectCode: projectCode || '',
          Authentication: `Bearer ${accessToken}`,
          Authorization: `Bearer ${accessToken}`,
          Accept: 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(messageRequest),
        signal: controller.signal,
        onmessage: (event) => {
          onChunk(JSON.parse(event.data) as MessageEvent)
        },
        onclose: onComplete,
        onerror: (e) => {
          throw e
        },
        openWhenHidden: true, // Keep connection open even if hidden
      })
    } catch (error: any) {
      console.error(error)
      onError(error)
    }
    return controller
  }

  public async parseContentToArtefacts(
    request: ParseRequest
  ): Promise<ArtefactParseResult> {
    try {
      const response = await apiCall(
        'POST',
        API_URLS.AI_TOOLS_ARTEFACT_PARSER,
        request
      ).then(({ data }) => data)

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to parse content: ${error.message}`)
    }
  }

  /**
   * File Upload Methods
   */

  /**
   * Create file metadata and get presigned URL for upload
   */
  async createFile(request: FileCreateRequest): Promise<FileCreateResponse> {
    try {
      const response = await apiCall('POST', API_URLS.AI_FILES, request).then(
        ({ data }) => data
      )

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to create file: ${error.message}`)
    }
  }

  /**
   * Upload file content to presigned URL
   */
  async uploadFileContent(
    presignedUrl: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    try {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        // Track upload progress
        if (onProgress) {
          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const progress = Math.round((event.loaded / event.total) * 100)
              onProgress(progress)
            }
          })
        }

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve()
          } else {
            reject(new Error(`Upload failed with status: ${xhr.status}`))
          }
        })

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed'))
        })

        xhr.open('PUT', presignedUrl)
        xhr.setRequestHeader('Content-Type', file.type)
        xhr.setRequestHeader('x-ms-blob-type', 'BlockBlob')
        xhr.send(file)
      })
    } catch (error: any) {
      throw new Error(`Failed to upload file: ${error.message}`)
    }
  }

  /**
   * Commit file upload to mark it as completed
   */
  async commitFileUpload(fileId: string): Promise<void> {
    try {
      await apiCall('POST', API_URLS.AI_FILE_COMMIT(fileId)).then(
        ({ data }) => data
      )
    } catch (error: any) {
      throw new Error(`Failed to commit file upload: ${error.message}`)
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(fileId: string): Promise<IFIle> {
    try {
      const response = await apiCall(
        'GET',
        API_URLS.AI_FILE_DETAIL(fileId)
      ).then(({ data }) => data)

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to get file metadata: ${error.message}`)
    }
  }

  /**
   * Get file view/download URL
   */
  async getFileViewUrl(fileId: string): Promise<string> {
    try {
      return await apiCall(
        'GET',
        `${API_URLS.AI_FILE_VIEW(fileId)}?redirect=False`
      ).then(({ data }) => data.data)
    } catch (error: any) {
      throw new Error(`Failed to get file view URL: ${error.message}`)
    }
  }

  /**
   * Complete file upload workflow: create -> upload -> commit
   */
  async uploadFile(
    file: File,
    callback?: {
      onFileCreated?: (file: IFIle) => void
      onFileUploadProgress?: (file: IFIle, progress: number) => void
    }
  ): Promise<IFIle> {
    try {
      // Step 1: Create file metadata and get presigned URL
      const createRequest: FileCreateRequest = {
        name: file.name,
        contentType: file.type,
        size: file.size,
      }

      const { presignedUrl, ..._file } = await this.createFile(createRequest)
      if (callback?.onFileCreated) {
        callback.onFileCreated(_file)
      }

      // Step 2: Upload file content to presigned URL
      await this.uploadFileContent(presignedUrl, file, (progress) =>
        callback?.onFileUploadProgress?.(_file, progress)
      )

      // Step 3: Commit the upload
      await this.commitFileUpload(_file.id)

      return _file
    } catch (error: any) {
      throw new Error(`Failed to upload file: ${error.message}`)
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      await apiCall('DELETE', API_URLS.AI_FILE_DELETE(fileId)).then(
        ({ data }) => data
      )
    } catch (error: any) {
      throw new Error(`Failed to delete file: ${error.message}`)
    }
  }

  async getTokenUsage(
    request: ITokenUsageRequest
  ): Promise<ITokenUsageResponse> {
    try {
      const response = await apiCall(
        'POST',
        API_URLS.AI_STATISTICS_TOKEN_USAGE,
        request
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to get token usage: ${error.message}`)
    }
  }
}

const AIService = new _AIService()
export default AIService
