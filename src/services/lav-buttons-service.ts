import { apiCall } from "../helper/api/aloApi";

const ButtonService = {
    async rejectUr(url, reason) {
        const targetUrl = `${url}/reject`
        const result = await apiCall('PUT', targetUrl, reason)
        return result
    },
    async reject(url) {
        const targetUrl = `${url}/reject`
        const result = await apiCall('PUT', targetUrl)
        return result
    },
    async rejectCustomer(url) {
        const targetUrl = `${url}/customerreject`
        const result = await apiCall('PUT', targetUrl)
        return result
    },
    async cancel(url) {
        const targetUrl = `${url}/cancel`;
        const result = await apiCall('PUT', targetUrl);
        return result;
    },
    async approve(url) {
        const targetUrl = `${url}/approve`;
        const result = await apiCall('PUT', targetUrl);
        return result;
    },
    async endorse(url) {
        const targetUrl = `${url}/endorse`
        const result = await api<PERSON>all('PUT', targetUrl)
        return result
    },
    async reOpen(url) {
        const targetUrl = `${url}/reopen`
        const result = await apiCall('PUT', targetUrl)
        return result
    },
    async remove(url) {
        const targetUrl = `${url}/remove`
        const result = await apiCall('PUT', targetUrl)
        return result
    }
}
export default ButtonService