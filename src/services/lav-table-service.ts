import { apiCall } from "../helper/api/aloApi";

const TableService = {
    async getData(url, pageIndex, pageSize, filters, sorter) {
        let take = pageSize;
        let skip = (pageIndex - 1) * take;
        const targetUrl = `${url}?Take=${take}&Skip=${skip}` + `${filters}${sorter}`;
        const result = await apiCall('GET', targetUrl);
        return result;
    },
    async deleteItem(url, id) {
        const targetUrl = `${url}/${id}`;
        const result = await apiCall('DELETE', targetUrl);
        return result;
    },
    async export(url) {
        const targetUrl = `${url}`;
        const result = await apiCall('GET', targetUrl, undefined, undefined, true);
        return result;
    },
}
export default TableService
