@card_header_color_1: #f6faff;

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.buttonCreate {
  display: flex !important;
  justify-content: flex-end;
}

.ck.ck-balloon-panel {
  z-index: 9999 !important;
}

.ant-collapse-header {
  background-color: @card_header_color_1 !important;
}
.cancelStatus {
  color: red;
}
.customerCanCelColor {
  color: red !important;
}
.field-required::after {
  content: " *";
  color: red;
}

.description {
  white-space: break-spaces;
  word-break: initial !important ;
}

.ant-form-item-explain-error {
  word-break: initial !important;
}

// .ant-col {
//   color: rgba(0, 0, 0, 0.85);
//   font-weight: 600;
// }

.input-full-width {
  width: 100% !important;
}

.devider {
  width: 1px;
  height: 10px;
  margin: -3px 0;
  background:black;
}

.impact-row {
  width: 100%;
  &:hover {
    background-color: #e6f7ff;
  }
}