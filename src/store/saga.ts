import { all } from '@redux-saga/core/effects'
import ActorSaga from '../modules/actor/saga'
import AdminWorkerAgentSaga from '../modules/admin-worker-agent/saga'
import AdminSupervisorAgentSaga from '../modules/admin-supervisor-agent/saga'
import BusinessRuleSaga from '../modules/business-rule/saga'
import CommonCommitteeSaga from '../modules/common/committee/saga'
import CommonComponentSaga from '../modules/common/component/saga'
import commonObjectSaga from '../modules/common/object/saga'
import CommonScreenSaga from '../modules/common/screen/saga'
import CommonFunctionSaga from '../modules/common/usecase/saga'
import DashboardSaga from '../modules/dashboard/statistic/saga'
import DataMigrationSaga from '../modules/data-migration/saga'
import EmailTemplateSaga from '../modules/email-templates/saga'
import generatesrsSaga from '../modules/generate-srs/saga'
import generatewbsSaga from '../modules/generate-wbs/saga'
import MeetingMinuteSaga from '../modules/meeting-minutes/saga'
import MessagesSaga from '../modules/messages/saga'
import MockupScreenSaga from '../modules/mockup-screen/saga'
import NonFunctionalRequirementSaga from '../modules/non-functional-requirement/saga'
import ObjectRelationshipDiagramSaga from '../modules/object-relationship-diagram/saga'
import ObjectsSaga from '../modules/objects/saga'
import OtherRequirementSaga from '../modules/other-requirement/saga'
import PermissionMatrixSaga from '../modules/permission-matrix/saga'
import ProjectSaga from '../modules/project-management/saga'
import ProjectWorkerAgentSaga from '../modules/project-worker-agent/saga'
import ReferenceDocumentSaga from '../modules/reference-document/saga'
import StateTransitionSaga from '../modules/state-transition/saga'
import UseCaseDiagramSaga from '../modules/usecase-diagram/saga'
import FunctionSaga from '../modules/usecase/saga'
import UserRequirementSaga from '../modules/user-requirement/saga'
import WorkFlowSaga from '../modules/workflow/saga'
import RecommendCommonComponentSaga from '../modules/recommendedcommonrequirement/saga'
import commentSaga from '../modules/_shared/comment/saga'
import CommonNonFunctionalRequirementSaga from '../modules/common/non-functional-requirement/saga'
import CommonBusinessRuleSaga from '../modules/common/business-rule/saga'
import CommonEmailTemplateSaga from '../modules/common/email-templates/saga'
import CommonMessagesSaga from '../modules/common/messages/saga'
import EpicManagementSaga from '../modules/epic-management/saga'
import SprintSaga from '../modules/sprint-management/saga'
import UserStorySaga from '../modules/user-story-management/saga'
import GlossarySaga from '../modules/glossary/saga'
import CommonWorkFlowSaga from '../modules/common/workflow/saga'
import aiAssistantSaga from '../modules/_shared/ai/saga'

export default function* saga(): Generator {
  yield all([
    ObjectsSaga(),
    ActorSaga(),
    AdminWorkerAgentSaga(),
    AdminSupervisorAgentSaga(),
    MockupScreenSaga(),
    PermissionMatrixSaga(),
    FunctionSaga(),
    WorkFlowSaga(),
    StateTransitionSaga(),
    MessagesSaga(),
    EmailTemplateSaga(),
    DataMigrationSaga(),
    NonFunctionalRequirementSaga(),
    UserRequirementSaga(),
    ObjectRelationshipDiagramSaga(),
    MeetingMinuteSaga(),
    BusinessRuleSaga(),
    UseCaseDiagramSaga(),
    ReferenceDocumentSaga(),
    CommonCommitteeSaga(),
    OtherRequirementSaga(),
    ProjectWorkerAgentSaga(),
    generatesrsSaga(),
    generatewbsSaga(),
    DashboardSaga(),
    ProjectSaga(),
    SprintSaga(),
    CommonComponentSaga(),
    commonObjectSaga(),
    CommonScreenSaga(),
    CommonFunctionSaga(),
    RecommendCommonComponentSaga(),
    commentSaga(),
    CommonNonFunctionalRequirementSaga(),
    CommonBusinessRuleSaga(),
    CommonEmailTemplateSaga(),
    CommonMessagesSaga(),
    EpicManagementSaga(),
    UserStorySaga(),
    GlossarySaga(),
    CommonWorkFlowSaga(),
    aiAssistantSaga(),
  ])
}
