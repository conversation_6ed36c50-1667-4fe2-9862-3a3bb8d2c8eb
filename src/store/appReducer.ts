
import LavImpactReducer, { LavImpactState } from '../helper/component/lav-impact/reducer'
import ActorReducer, { ActorState } from '../modules/actor/reducer'
import AdminWorkerAgentReducer from '../modules/admin-worker-agent/reducer'
import { defaultState as AdminWorkerAgentDefaultState } from '../modules/admin-worker-agent/type'
import AdminSupervisorAgentReducer from '../modules/admin-supervisor-agent/reducer'
import { defaultState as AdminSupervisorAgentDefaultState } from '../modules/admin-supervisor-agent/type'
import BusinessRuleReducer, { BusinessRuleState } from '../modules/business-rule/reducer'
import CommonBusinessRuleReducer, { CommonBusinessRuleState } from '../modules/common/business-rule/reducer'
import CommonCommitteeReducer, { CommonCommitteeState } from '../modules/common/committee/reducer'
import CommonComponentReducer, { CommonComponentState } from '../modules/common/component/reducer'
import commonObjectReducer, {
  CommonObjectInitState
} from '../modules/common/object/reducer'
import CommonScreenReducer, { CommonScreenState } from '../modules/common/screen/reducer'
import CommonFunctionReducer, { CommonFunctionState } from '../modules/common/usecase/reducer'
import DashboardReducer, { DashboardState } from '../modules/dashboard/statistic/reducer'
import DataMigrationReducer, { DataMigrationState } from '../modules/data-migration/reducer'
import EmailTemplateReducer, { EmailTemplateState } from '../modules/email-templates/reducer'
import generateSrsReducer, { GenerateSrsInitState } from '../modules/generate-srs/reducer'
import generateWbsReducer, { GenerateWbsInitState } from '../modules/generate-wbs/reducer'
import MeetingMinuteReducer, { MeetingMinuteState } from '../modules/meeting-minutes/reducer'
import MessagesReducer, { MessagesState } from '../modules/messages/reducer'
import MockupScreenReducer, { MockupScreenState } from '../modules/mockup-screen/reducer'
import NonFunctionalRequirementReducer, { NonFunctionalRequirementState } from '../modules/non-functional-requirement/reducer'
import ObjectRelationshipDiagramReducer, { ObjectRelationshipDiagramState } from '../modules/object-relationship-diagram/reducer'
import ObjectsReducer, { ObjectsState } from '../modules/objects/reducer'
import OtherRequirementReducer, { OtherRequirementState } from '../modules/other-requirement/reducer'
import PermissionMatrixReducer, { PermissionMatrixState } from '../modules/permission-matrix/reducer'
import ProjectReducer, { ProjectState } from '../modules/project-management/reducer'
import ProjectWorkerAgentReducer from '../modules/project-worker-agent/reducer'
import { defaultState as ProjectWorkerAgentDefaultState } from '../modules/project-worker-agent/type'
import RecommendCommonComponentReducer, { RecommendCommonComponentState } from '../modules/recommendedcommonrequirement/reducer'
import ReferenceDocumentReducer, { ReferenceDocumentState } from '../modules/reference-document/reducer'
import StateTransitionReducer, { StateTransitionState } from '../modules/state-transition/reducer'
import UsecaseDiagramReducer, { UsecaseDiagramState } from '../modules/usecase-diagram/reducer'
import FunctionReducer, {
  FunctionState
} from '../modules/usecase/reducer'

import paginationReducer, {
  PaginationState
} from '../helper/component/lav-table/reducer'
import CommonEmailReducer, { CommonEmailTemplateState } from '../modules/common/email-templates/reducer'
import CommonMessageReducer, { CommonMessagesState } from '../modules/common/messages/reducer'
import NonfunctionalReducer, { CommonNonFunctionalRequirementState } from '../modules/common/non-functional-requirement/reducer'
import CommonWorkFlowReducer, { CommonWorkFlowState } from '../modules/common/workflow/reducer'
import EpicReducer, { EpicManagementState } from '../modules/epic-management/reducer'
import glossaryReducer, { GlossaryState } from '../modules/glossary/reducer'
import SprintReducer, { SprintManagementState } from '../modules/sprint-management/reducer'
import UserRequirementReducer, { UserRequirementState } from '../modules/user-requirement/reducer'
import UserStoryReducer, { UserStoryManagementState } from '../modules/user-story-management/reducer'
import WorkFlowReducer, {
  WorkFlowState
} from '../modules/workflow/reducer'
import CommentReducer, { CommentState } from '../modules/_shared/comment/reducer'
import { aiAssistantReducer, AIState } from '../modules/_shared/ai/reducer'
import AppState from './types'
import paginationHistoryReducer, { PaginationHistoryState } from '../helper/component/lav-history-table/reducer'
export const AppInitState: AppState = {
  Objects: ObjectsState,
  Actor: ActorState,
  AdminWorkerAgent: AdminWorkerAgentDefaultState,
  AdminSupervisorAgent: AdminSupervisorAgentDefaultState,
  MockupScreen: MockupScreenState,
  PermissionMatrix: PermissionMatrixState,
  Function: FunctionState,
  WorkFlow: WorkFlowState,
  StateTransition: StateTransitionState,
  Messages: MessagesState,
  EmailTemplate: EmailTemplateState,
  DataMigration: DataMigrationState,
  NonFunctionalRequirement: NonFunctionalRequirementState,
  UserRequirement: UserRequirementState,
  ObjectRelationship: ObjectRelationshipDiagramState,
  MeetingMinute: MeetingMinuteState,
  BusinessRule: BusinessRuleState,
  UseCaseDiagram: UsecaseDiagramState,
  ReferenceDocument: ReferenceDocumentState,
  CommonCommittee: CommonCommitteeState,
  OtherRequirement: OtherRequirementState,
  ProjectWorkerAgent: ProjectWorkerAgentDefaultState,
  generateSrs: GenerateSrsInitState,
  generateWbs: GenerateWbsInitState,
  Dashboard: DashboardState,
  Project: ProjectState,
  CommonComponent: CommonComponentState,
  commonObject: CommonObjectInitState,
  CommonScreen: CommonScreenState,
  CommonFunction: CommonFunctionState,
  RecommendCommonComponent: RecommendCommonComponentState,
  Comment: CommentState,
  CommonNonFunctionalRequirement: CommonNonFunctionalRequirementState,
  CommonEmailTemplate: CommonEmailTemplateState,
  CommonMessage: CommonMessagesState,
  CommonBusinessRule: CommonBusinessRuleState,
  Epic: EpicManagementState,
  Sprints:SprintManagementState,
  UserStory: UserStoryManagementState,
  Pagination: PaginationState,
  PaginationHistory: PaginationHistoryState,
  Glossary: GlossaryState,
  LavImpact: LavImpactState,
  CommonWorkFlow: CommonWorkFlowState,
  aiAssistant: AIState,
}

const AppReducer = {
  Objects: ObjectsReducer,
  Actor: ActorReducer,
  AdminWorkerAgent: AdminWorkerAgentReducer,
  AdminSupervisorAgent: AdminSupervisorAgentReducer,
  MockupScreen: MockupScreenReducer,
  PermissionMatrix: PermissionMatrixReducer,
  Function: FunctionReducer,
  WorkFlow: WorkFlowReducer,
  StateTransition: StateTransitionReducer,
  Messages: MessagesReducer,
  EmailTemplate: EmailTemplateReducer,
  DataMigration: DataMigrationReducer,
  NonFunctionalRequirement: NonFunctionalRequirementReducer,
  UserRequirement: UserRequirementReducer,
  ObjectRelationship: ObjectRelationshipDiagramReducer,
  MeetingMinute: MeetingMinuteReducer,
  BusinessRule: BusinessRuleReducer,
  UseCaseDiagram: UsecaseDiagramReducer,
  ReferenceDocument: ReferenceDocumentReducer,
  CommonCommittee: CommonCommitteeReducer,
  OtherRequirement: OtherRequirementReducer,
  ProjectWorkerAgent: ProjectWorkerAgentReducer,
  generateSrs: generateSrsReducer,
  generateWbs: generateWbsReducer,
  Dashboard: DashboardReducer,
  Project: ProjectReducer,
  CommonComponent: CommonComponentReducer,
  commonObject: commonObjectReducer,
  CommonScreen: CommonScreenReducer,
  CommonFunction: CommonFunctionReducer,
  RecommendCommonComponent: RecommendCommonComponentReducer,
  Comment: CommentReducer,
  CommonNonFunctionalRequirement: NonfunctionalReducer,
  CommonEmailTemplate: CommonEmailReducer,
  CommonMessage: CommonMessageReducer,
  CommonBusinessRule: CommonBusinessRuleReducer,
  Epic: EpicReducer,
  Sprints:SprintReducer,
  UserStory: UserStoryReducer,
  Pagination: paginationReducer,
  PaginationHistory: paginationHistoryReducer,
  Glossary: glossaryReducer,
  LavImpact: LavImpactReducer,
  CommonWorkFlow: CommonWorkFlowReducer,
  aiAssistant: aiAssistantReducer,
}

export default AppReducer
