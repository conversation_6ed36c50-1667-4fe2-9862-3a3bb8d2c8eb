@primary-bg-color: #eeeeee;
@sidebar-bg-color: #f4f5f7;
@secondary-bg-color: rgba(255, 255, 255, 0.7);
@bg-color-3: #f6faff;
@bg-color-4: #e8e8e8;
@disabled-color: #515560;
@text-color: #42526e;

@navbar-height: 45px;
@sider-width: 280px;
@sider-collapsed-width: 56px;
@menu-heading-text-color: #344563;
@menu-item-text-color: #42526e;

// Custom CSS
p {
  word-break: initial !important;
}

td,
tr,
th,
table,
thead,
tbody {
  border-color: #e8e8e8 !important;
}

// Custom ant css
.no-after {
  &::after {
    display: none;
  }
}

.ant-table-cell.ant-table-column-has-sorters::before {
  width: 0 !important;
}



.ant-btn {
  box-shadow: none;
  transition: all ease-in-out 0.3s;

  &:hover {
    opacity: 0.75;
  }
}


.breadcrumb-link-btn {
  padding: 0;
}

.full-width {
  width: 100%;
}

// common class

.bg-color-3 {
  background-color: @bg-color-3;
}

.success-btn {
  background-color: @success-color !important;
  color: white !important;
  border: none !important;
}

.info-btn {
  background-color: #00a3bf;
  color: white;
  border: none;

  &:hover,
  &:focus {
    background-color: #00a3bf;
    color: white;
    border: none;
  }
}

.cancel-btn {
  background-color: #e1e4e9;
  color: #081833;
  border: none;

  &:hover,
  &:focus {
    background-color: #e1e4e9;
    color: #081833;
    border: none;
  }
}

.p-1rem {
  padding: 1rem;
}

.p-2rem {
  padding: 2rem;
}

.p-20px {
  padding: 20px;

  //  .ant-space-item:first-child {
  //   position: sticky;
  //   top: 0;
  //   background-color: #fff;
  //   z-index: 11;
  // }
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.sprint {
  margin: 4px 0px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.ant-card {
  color: #172b4d;
}

.title-card {
  .ant-card {
    margin-bottom: 10px;
  }
}

.rq-references .ant-card:last-child {
  margin-top: 20px;
}

.record-detail-right-control {
  &-container {
    border-left: 0.5rem solid @primary-bg-color;
  }
}

.detail-menu {
  &-item {
    min-width: 200px;
    height: 4.8rem !important;
    line-height: 1.4rem !important;
    margin: 0 !important;
    border-bottom: 1px solid @bg-color-4;
    padding: 0px 1rem !important;
    border-left: 4px solid transparent !important;

    &.ant-menu-item-selected {
      border-left: 4px solid @primary-color !important;
    }

    &.ant-menu-item-selected::after {
      display: none;
    }
  }
}


.tableDangerous{
  p {
    margin-bottom: 0px !important;
  }
}

.ant-table-thead > tr > th {
  vertical-align: top !important;
  white-space: nowrap;
}

.ant-table-thead > tr > th,
.ant-table-tbody > tr > td,
.ant-table tfoot > tr > th,
.ant-table tfoot > tr > td {
  padding: 5px 10px;
  > p {
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dashboard-page {
  padding: 0 16px;
}

.table-permission-matrix table tr td,
.table-permission-matrix table tr th {
  border: 1px #f0f0f0 solid;
  vertical-align: top;
}

.table-permission-matrix table tr th {
  padding: 16px;
  background-color: #fafafa;
  font-weight: bold;
  text-align: left;
}

.table-permission-matrix table tr td {
  padding: 10px 16px;
  border-collapse: collapse;
}

.tableDangerous {
  table > thead > tr > th {
    font-weight: 700;
    background: hsla(0, 0%, 0%, 5%);
    min-width: 2em;
    padding: 0.4em;
    border: 1px solid #bfbfbf;
  }

  table > tbody > tr > td {
    min-width: 2em;
    padding: 0.4em;
    border: 1px solid #bfbfbf;
    // text-align: center;
  }

  table > tbody > tr > th {
    min-width: 2em;
    padding: 0.4em;
    border: 1px solid #bfbfbf;
    // text-align: center;
  }
}

.ant-table {
  .ant-table-tbody {
    td {
      vertical-align: top;
      // padding-top: 10px;
      // padding-bottom: 10px;
      .ant-btn {
        height: 24px;
      }
    }
  }
}




.dashboard-page .ant-table-thead > tr > th {
  font-weight: bold;
}

.dashboard-page .ant-table-thead > tr > th,
.dashboard-page .ant-table-tbody > tr > td {
  padding-top: 10px;
  padding-bottom: 10px;
}

.dashboard-page .ant-table-thead > tr > th {
  background-color: rgba(0, 0, 0, 0.05);
}

.use-case-br .ant-table-thead tr th,
.use-case-br .ant-table-tbody tr td {
  text-align: center;
}

.table-permission-matrix table tr th:not(:first-child) {
  text-align: center;
}

.table-permission-matrix table tr td:not(:first-child) {
  text-align: center;
}

.tableDangerous > .table table > thead > tr > th {
  font-weight: 700;
  background: hsla(0, 0%, 0%, 5%);
  min-width: 2em;
  padding: 0.4em;
  border: 1px solid #bfbfbf;
}

.tableDangerous > .table table > tbody > tr > td {
  min-width: 2em;
  padding: 0.4em;
  border: 1px solid #bfbfbf;
  // text-align: center;
}



.use-case-br .ant-table-thead tr th:last-child,
.use-case-br .ant-table-tbody tr td:last-child {
  text-align: left;
}

.dashboard-page .ant-table-tbody > tr > td a {
  text-decoration: underline;
}

.ant-table-container table > thead > tr:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.dashboard-page .ant-table-tbody > tr:nth-child(2n + 1) {
  background-color: #fafafa;
}

.table-permission-matrix {
  max-width: 100%;
  overflow: auto;
}
.table-permission-matrix table{
  width: 100%;
  color: #081833;
}


.tableDangerous {
  word-break: initial !important;
  color: #172b4d;

  img {
    max-width: 100%;
  }

  table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    height: 100%;
    border: 1px double #b3b3b3;
  }

  
}

// .table-permission-matrix{
//   table {
//     width: 100%;
//     color: #081833;

//     tr {
//       td,
//       th {
//         border: 1px #f0f0f0 solid;
//         vertical-align: top;
//       }

//       th {
//         padding: 16px;
//         background-color: #fafafa;
//         font-weight: bold;
//         text-align: left;

//         &:not(:first-child) {
//           text-align: center;
//         }
//       }

//       td {
//         padding: 10px 16px;
//         border-collapse: collapse;

//         &:not(:first-child) {
//           text-align: center;
//         }
//       }
//     }
//   }
// }

.ant-collapse-content > .ant-collapse-content-box,
.ant-card-body {
  padding: 10px;
}

.rq-form-block-p0 {
  // border: 0 !important;

  .ant-card-body {
    padding: 0 !important;
    min-height: 235px;
  }

  .ant-table-thead {
    display: none !important;
  }
}



.tableDangerous > .table {
  table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    height: 100%;
    border: 1px double #b3b3b3;
  }
}

// .rq-form-block-p0 {
//   .ant-table-thead > tr,
//   .ant-table-tbody > tr {
//     th,
//     td {
//       &:first-child {
//         div {
//           white-space: nowrap;
//           text-overflow: ellipsis;
//           overflow: hidden;
//           max-width: 100%;
//         }
//       }

//       &:last-child {
//         max-width: 36px !important;
//         width: 36px !important;

//         .ant-btn-link {
//           width: 16px;
//         }
//       }
//     }
//   }
// }


// .tableDangerous > .table {
//   table > thead > tr > th {
//     font-weight: 700;
//     background: hsla(0, 0%, 0%, 5%);
//     min-width: 2em;
//     padding: 0.4em;
//     border: 1px solid #bfbfbf;
//   }

//   table > tbody > tr > td {
//     min-width: 2em;
//     padding: 0.4em;
//     border: 1px solid #bfbfbf;
//     // text-align: center;
//   }
// }



.rq-form-block-p0 .ant-table-thead > tr th:first-child div,
.rq-form-block-p0 .ant-table-tbody > tr th:first-child div,
.rq-form-block-p0 .ant-table-thead > tr td:first-child div,
.rq-form-block-p0 .ant-table-tbody > tr td:first-child div {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
}

.rq-form-block-p0 .ant-table-thead > tr th:last-child,
.rq-form-block-p0 .ant-table-tbody > tr th:last-child,
.rq-form-block-p0 .ant-table-thead > tr td:last-child,
.rq-form-block-p0 .ant-table-tbody > tr td:last-child {
  max-width: 36px !important;
  width: 36px !important;
}

.rq-form-block-p0 .ant-table-thead > tr th:last-child .ant-btn-link,
.rq-form-block-p0 .ant-table-tbody > tr th:last-child .ant-btn-link,
.rq-form-block-p0 .ant-table-thead > tr td:last-child .ant-btn-link,
.rq-form-block-p0 .ant-table-tbody > tr td:last-child .ant-btn-link {
  width: 16px;
}

.ant-space.ant-space-vertical {
  width: 100%;
}

.modal-create-name-input-field {
  font-size: 24px !important;
  border: none;
  padding-left: 0px;
  border-bottom: 2px solid @primary-color;
  font-weight: bold;
  width: 100%;

  &:focus {
    box-shadow: none;
  }
}

.breakWorldTextArea,
.ant-typography,
.ant-col {
  word-break: initial !important;
}



.ant-table.ant-table-bordered > .ant-table-container {
  border-color: #e8e8e8 !important;
}

.ant-card-bordered {
  border-color: #e8e8e8 !important;
}

.ck.ck-content.ck-editor__editable.ck-rounded-corners.ck-editor__editable_inline.ck-blurred {
  p {
    margin-bottom: 0px !important;
  }
}

.rq-breadcrumb {
  .breadcrumb-link-btn {
    height: 24px;
    color: #2979ff !important;
  }
}

.rq-page-heading {
  padding-bottom: 10px;
  border-bottom: 2px solid #ebecf0;
  position: sticky;
  top: 0;
  z-index: 11;
  background-color: white;
}

.title-page-heading {
  display: flex;

  h3 {
    margin-right: 50px;
  }
}

.rq-page-title {
  color: #172b4d !important;
  margin-bottom: 0 !important;
  font-weight: 600 !important;
}

.rq-panel-heading {
  margin-bottom: 10px;

  .rq-panel-title {
    color: #172b4d !important;
    margin-bottom: 10px !important;
    font-weight: 600 !important;
  }
}



th.rq-action,
td.rq-action {
  max-width: 80px !important;
}

td.rq-action {
  text-align: left;
  white-space: nowrap;
  gap: 8px;
}

.rq-sub-table-action {
  display: flex;
  align-items: center;
}

.rq-modal-header {
  border-bottom: 2px solid #ebecf0;
  margin-bottom: 10px;
  position: sticky;
  top: 0;
  z-index: 99;
  background-color: white;
}

.ant-modal-body {
  padding: 16px;
}

.ant-form-item-explain.ant-form-item-explain-error {
  font-size: 12px;
}

.ant-form-item {
  margin-bottom: 16px;

  &.ant-form-item-has-error {
    margin-bottom: 20px;
  }
}

.rq-modal-header {
  .ant-form-item {
    margin-bottom: 0;

    &.ant-form-item-has-error {
      margin-bottom: 0;
    }
  }
}

.ant-form-item-explain,
.ant-form-item-extra {
  min-height: 20px;
}

.rq-modal-fill {
  .ant-modal-body {
    padding: 0;
  }

  .rq-modal-inner {
    padding: 24px;
  }

  .rq-modal-footer {
    text-align: right;
    padding: 16px 24px;
    background: transparent;
    border-top: 1px solid rgb(240, 240, 240);
    border-radius: 0px 0px 2px 2px;
  }
}

.rq-comment-field {
  // display: inline-block;
  .ant-typography {
    line-height: 1.5715;
  }
}

.rq-modal-header-filled {
  background-color: rgb(41, 121, 255);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgb(255, 255, 255);

  h4 {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 22px;
    margin: 0;
    color: rgb(255, 255, 255);
  }

  span {
    font-size: 24px;
  }

  b {
    span {
      font-size: 16px !important;
    }

    .ant-typography {
      color: #172b4d !important;
    }

    .ant-badge-status-dot {
      background-color: #172b4d !important;
    }
  }

  
}

.ant-card-head-title {
  padding: 5px 0;
}

.rq-tab {
  h4 {
    margin-bottom: 0 !important;
    opacity: 0.6;
    padding-top: 10px;
  }
}

.ant-card-head {
  min-height: 0;
  padding: 0 15px;
  background-color: #f6faff;

  .ant-card-head-title {
    padding: 5px 0;

    h1,
    h2,
    h3,
    h4,
    h5 {
      color: #081833;
      font-weight: 600;
      font-size: 14px;
    }
  }
}

.rq-card {
  .ant-card-head {
    padding: 0;
    min-height: 42px;

    .ant-card-head-title {
      padding: 5px 15px;
    }
  }

  .ant-card-body {
    padding: 15px;
  }
}

.common-component-modal {
  top: 20px !important;
}

.rq-form-block {
  .ant-card-head {
    padding-left: 16px;
    padding-right: 16px;
    min-height: auto;
    background-color: rgba(9, 30, 66, 0.08);

    .ant-card-head-title {
      color: #081833;
      font-weight: 600;
      font-size: 14px;
      padding: 5px 0;
    }
  }
}

.rq-form-group {
  .ant-row {
    width: 100%;
  }

  .rq-form-label {
    color: #6b778c;
    font-weight: 500;
    margin-bottom: 6px;
    padding-top: 4px;
    width: 100%;

    .rq-required {
      color: red;
    }
  }

  .rq-form-note {
    font-style: italic;
    color: #6b778c;
    margin-bottom: 6px;
    margin-top: -3px;
    font-size: 13px;
  }
}





.ant-col .ant-col-4 {
  padding: 1px 8px;
}

.ant-typography.ant-typography-secondary {
  color: #6b778c;
  line-height: 28px;
}



.description {
  white-space: break-spaces;
  color: #172b4d;
}





.rq-code-column {
  white-space: nowrap;
}

.title-card .ant-card-head {
  font-size: 14px;
}

.list-comment {
  &-items {
    min-height: 10rem;
    overflow-x: scroll;
  }

  &-items:not(:first-child) {
    margin-top: 15px;
  }
}

.comment-items-name {
  color: #2979ff;

  &:hover {
    cursor: pointer;
    opacity: 0.7;
  }
}

.comment-items-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .comment-items-btn {
    .ant-btn:empty {
      display: none;
    }

    .ant-btn-icon-only {
      width: 22px;
      height: 22px;
      border: 0px solid transparent;
    }
  }
}

.comment-items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.commemt-items-name {
  display: flex;
  align-items: center;

  .author {
    color: #2979ff;
  }
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title-card {
  display: flex;
  align-items: stretch;
  gap: 30px !important;

  .title-card-content {
    display: flex;
    gap: 20px;
  }

  .ant-card-head-wrapper {
    text-align: center;
  }

}

.rq-audit-trail .ant-collapse-header {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
  display: flex;
  align-items: center;

  .ant-collapse-arrow {
    margin-right: 8px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-bottom: 0;
    color: #081833;
    font-weight: 600;
    font-size: 14px;
  }
}

.rq-warning-srs .ant-collapse-header {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  display: flex;
  align-items: center;

  .ant-collapse-arrow {
    margin-right: 8px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-bottom: 0;
    color: #081833;
    font-weight: 600;
    font-size: 14px;
  }
}

.rq-ms-0 {
  .ant-menu-title-content {
    margin-left: 0 !important;
  }
}

.rq-modal-validate {
  .ant-modal-header {
    background-color: #fff;

    .ant-modal-title {
      // text-align: center!important;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 600;
      font-size: 20px;
      line-height: 1.4;
    }
  }
}

.rq-tab {
  position: relative;
  transition: all ease-in-out 0.3s;
  margin-bottom: 0 !important;
  padding-top: 10px;

  &::before {
    content: '';
    width: 0;
    height: 1px;
    background-color: #2979ff;
    left: 0;
    bottom: -11px;
    position: absolute;
    transition: all ease-in-out 0.3s;
  }

  &.rq-tab-active {
    opacity: 1;

    &::before {
      width: 100%;
    }
  }
}

.my-assigned-task {
  .ant-pagination {
    margin-top: 0;
  }
}

.assing-date {
  .ant-select {
    width: 100px !important;
  }

  .ant-radio-wrapper {
    width: 106px !important;
  }
}

.rd-filter-label {
  width: 110px;
}

.rd-filter-control {
  width: 130px !important;
}

.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.btn-warning {
  color: #f27228 !important;
  border-color: #f27228 !important;
}

.ant-upload-list {
  margin-top: 10px;
}



.rq-comment-trigger {
  display: none;
  margin-left: 4px;
  cursor: pointer;

  &.d-inline {
    display: inline;
  }
}

.rq-comment-field {
  // display: inline-block;
  &:hover {
    .rq-comment-trigger {
      display: inline;
    }
  }

  &.active {
    .rq-comment-field-label {
      border-bottom: 2px dashed #ff9900;
    }
  }
}

.source {
  .rq-comment-field {
    display: inline-block;
  }
}

.rq-comment-wrapper {
  font-size: 13px;

  .edit-menu {
    margin: 0 24px 0 0;

    .ant-btn:empty {
      display: none;
    }

    .ant-btn-icon-only {
      width: 22px;
      height: 22px;
      border: 0px solid transparent;
    }
  }

  .ant-modal-close-x {
    width: 16px;
    height: 16px;
    margin: 8px 16px 0 0;
    line-height: normal;
  }

  .ant-modal-header {
    background-color: #fff;
    padding: 5px 16px;

    .ant-modal-title {
      color: #000000d9;
    }
  }

  .ant-modal-body {
    padding: 12px 16px;
  }
}

.rq-comment-title {
  .center-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // width: calc(100% - 165px);
    // padding: 0 20px 0 0;
  }

  .right-title {
    align-items: center;
    align-content: center;
    display: flex;
  }

  .up-icon,
  .reload,
  .down-icon {
    cursor: pointer;
    padding: 0 2px;

    &.disabled {
      cursor: default;
      color: #00000040;

      &:hover {
        color: #00000040;
      }
    }

    &:hover {
      color: #2979ff;
    }
  }
}

.rq-comment-body {
  min-height: 100px;

  .rq-add-comment {
    width: 100%;

    .ant-space-item {
      &:first-child {
        flex: 1;
      }
    }

    .rq-comment-action {
      height: 24px;
      padding: 0 10px;
    }
  }

  .rq-comment-items {
    overflow-y: auto;

    .rq-comment-item {
      position: relative;

      &:not(:last-child) {
        margin-bottom: 12px;
      }

      &.editing {
        margin-bottom: 0;
      }
    }
  }

  .rq-reply-item {
    margin: 0 0 0 10px;
    padding: 10px 5px 0 10px;
    border-left: 1px solid #e8e8e8;
    position: relative;

    &.active {
      background-color: #f5f5f5;
      border-left: 2px solid;
    }

    &:last-child {
      padding-bottom: 10px;
    }

    .rq-edit-reply,
    .rq-remove-reply {
      padding: 0 4px 0 0;
      font-size: 13px;
      display: none;
      position: absolute;
      top: 10px;
    }

    .rq-edit-reply {
      right: 22px;
    }

    .rq-remove-reply {
      right: 0;
    }

    .created {
      color: #616770;
      font-size: 12px;
    }

    .author {
      margin: 0 0 0 3px;
      color: #2979ff;
    }

    .category {
      font-weight: bold;
    }

    .comment-body,
    .reply-body {
      white-space: pre-wrap;
    }

    .comment-status {
      margin: 0 0 0 8px;
    }

    .ant-form-item {
      margin-bottom: 5px;
    }

    &.can-edit {
      &:hover {
        .created {
          display: none;
        }

        .rq-edit-reply,
        .rq-remove-reply {
          display: block;
        }
      }
    }

    &.can-delete {
      &:hover {
        .created {
          display: none;
        }

        .rq-edit-reply,
        .rq-remove-reply {
          display: block;
        }
      }
    }
  }

  
}

.rq-comment-menu {
  &.ant-menu-vertical {
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    // box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;

    > .ant-menu-item {
      height: auto;
      line-height: 2;
      padding: 0 12px;

      &:not(:last-child) {
        margin-bottom: 4px;
      }
    }

    .ant-menu-item-active {
      background-color: #f5f5f5;
    }
  }
}

.ant-row.ant-form-item.height-default .ck-content {
  min-height: 150px;
}

.rq-fg-comment.rq-form-group-required {
  .rq-required {
    display: none;
  }

  .rq-comment-field-label {
    display: inline-block;
    padding-right: 12px;
    position: relative;

    &::before {
      content: '*';
      color: red;
      right: 1px;
      top: 1px;
      position: absolute;
    }
  }
}

.ck-editor__editable {
  min-height: 50px;
}

.selected-preview {
  font-weight: 500;
  color: #344563 !important;
}



.dashboard-page .dashboard-page-title {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 24px;
  padding-top: 20px;
  margin-bottom: 10px;
}

.dashboard-page .ant-card.ant-card-bordered {
  border-color: #91d5ff;
}

.dashboard-page .ant-card .ant-card-head {
  background: #e6f7ff;
}

.dashboard-page .ant-card .ant-card-head .ant-card-head-title {
  font-weight: bold;
}

.dashboard-page .ant-card-body {
  position: relative;
  padding-top: 60px;
}

.dashboard-page .dashboard-chart-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dashboard-page .dashboard-chart {
  margin-bottom: 24px;
  height: 360px;
}

.dashboard-page .dashboard-filter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 16px;
  position: absolute;
  top: 15px;
  right: 15px;
}

.dashboard-page .dashboard-filter > span {
  margin-right: 10px;
  font-weight: bold;
}

.dashboard-page .dashboard-filter .ant-select {
  width: 200px;
}

.rd-des .rq-form-label {
  display: flex;
  gap: 2px;
}

.dashboard-page .dashboard-table {
}

.dashboard-page .dashboard-table h4 {
  margin-bottom: 10px;
  font-weight: bold;
}



.ck-list__item {
  max-width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.field-selectable {
  padding: 10px 16px;
  transition: all ease-in-out 0.3s;

  &.d-none {
    display: none !important;
  }

  &:not(:last-child) {
    border-bottom: 1px rgba(0, 0, 0, 0.05) solid;
  }

  &:hover,
  &.selected {
    background-color: #f7f7f7;
    cursor: pointer;
  }

  &.selected {
    font-weight: 500;
  }
}

.rq-tree {
  // height: 300px;
  max-height: 310px;
  max-width: 100%;
  padding-bottom: 50px;
  overflow: auto;
  display: flex;
  flex-direction: column;

  .rq-tree-item {
    display: inline-flex;
    position: relative;
    height: 75px;

    .rq-tree-item-selectable {
      width: 300px;
      min-width: 300px;
      height: 75px;
      background-color: #fff;
      display: inline-flex;
      align-items: center;
      padding: 10px 15px;
      border: 1px #ddd solid;
      position: relative;
      transition: all ease-in-out 0.3s;

      &.selected {
        background-color: #f8f9fa;
        background-color: #e9edf1;

        &::after {
          display: block;
        }
      }

      &:hover {
        background-color: #f8f9fa;
      }

      &::after {
        content: '';
        width: 31px;
        border-top: 1px #666 solid;
        top: 50%;
        transform: translateY(-50%);
        right: -31px;
        position: absolute;
        display: none;
      }

      .rq-tree-item-checkbox {
        margin-bottom: 0;
        cursor: pointer;
        display: flex;

        .form-check-input {
          margin-right: 10px;
          margin-top: 4px;
        }

        .rq-tree-item-code {
          font-size: 13px;
          color: #666;
          font-weight: bold;
        }
      }

      .rq-tree-item-badge {
        position: absolute;
        top: 5px;
        right: 5px;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        background-color: #fee;
      }

      .rq-tree-item-toggle {
        border: 0;
        position: absolute;
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
        background-color: #f8f9fa;
        border-radius: 4px;
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        font-weight: bold;
        transition: all ease-in-out 0.3s;
        font-size: 13px;
        outline: 0 !important;

        &:disabled {
          opacity: 0.3;
        }

        &:not(:disabled):hover {
          background-color: #e3e3e3 !important;
        }
      }

      .rq-tree-item-name {
        color: #2979ff;

        &:hover {
          cursor: pointer;
        }
      }

      .rq-tree-item-delete {
        border: 0;
        position: absolute;
        bottom: -5px;
        right: 5px;
        transform: translateY(-50%);
        background-color: #fff;
        border-radius: 4px;
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        font-weight: bold;
        transition: all ease-in-out 0.3s;
        font-size: 13px;
        color: red;

        outline: none !important;
      }

      .re-tree-item-connect {
        display: none;
        outline: none !important;
      }
    }

    &:not(:last-child) > .rq-tree-item-selectable {
      border-bottom-color: transparent;
    }

    &-to {
      background-color: #add8e6 !important;
    }

    &-from {
      background-color: #fee1ae !important;
    }
    &-disable {
      background-color: #d3d3d3 !important;
    }
    

    .rq-tree-children {
      display: flex;
      flex-direction: column;
      z-index: 2;
      margin-left: 80px;
      position: relative;

      &::before {
        width: 1px;
        height: calc(100% - 37px - 37px + 1px);
        background-color: #666;
        content: '';
        position: absolute;
        left: -50px;
        top: 50%;
        transform: translateY(-50%);
      }

      .rq-tree-item {
        .rq-tree-item-selectable {

          .re-tree-item-connect {
            position: absolute;
            padding: 0;
            border: 0;
            left: -28px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            background-color: #fff;
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            z-index: 3;
            transition: all ease-in-out 0.3s;
            font-size: 13px;

            &.act-add {
              color: #00c853;
            }

            &.act-break {
              color: red;
              opacity: 0;
              transition: all ease-in-out 0.3s;

              &:hover {
                opacity: 1;
              }
            }
          }

          &.broken {
            &::before {
              border-top: 1px #999 dashed;
            }
          }

          &::before {
            content: '';
            width: 50px;
            border-top: 1px #666 solid;
            top: 50%;
            transform: translateY(-50%);
            left: -50px;
            position: absolute;
            display: block;
          }

          &:hover {
            .re-tree-item-connect {
              &.act-break {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
}




.ant-table-container table > thead > tr:first-child th:first-child {
  border-top-left-radius: 0;
}

.ant-table-container table > thead > tr:first-child th:last-child {
  border-top-right-radius: 0;
}

.rq-ellipsis {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.rq-project-info {
  .ant-row {
    align-items: center;
  }
}

.ant-image-mask:hover {
  background: none;
}

.tooltips-container {
  display: flex;
  align-items: center;
  justify-content: start;

  .tooltips-icon {
    margin-left: 15px;
  }
}

.status-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.ck-editor__editable {
  min-height: 50px !important;
}

.ant-table-pagination.ant-pagination {
  margin: 16px 0 12px 0;
}

.ant-picker-custom {
  .ant-picker-suffix {
    display: none;
  }

  .ant-picker-clear {
    display: none;
  }
}

.assign-select {
  .ant-select-clear {
    display: none;
  }
}

.card-comment-container {
  margin: 10px 0;
}

.scrollButtonUC {
  position: sticky;
  top: 58px;
  background: #fff;
  z-index: 10;
  padding: 10px 0;
}

.scrollButtonSC {
  position: sticky;
  top: 37px;
  background: #fff;
  z-index: 10;
  padding: 10px 0;
}

.card-position {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 9;
}

.ant-tooltip {
  max-width: initial !important;
}

.white-pre {
  white-space: pre;
  word-wrap: break-word;
}
