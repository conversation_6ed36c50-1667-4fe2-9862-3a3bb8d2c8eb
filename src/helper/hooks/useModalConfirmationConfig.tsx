import intl from '../../config/locale.config'
import { Typography } from 'antd'

const { Text, Title } = Typography

export default function useModalConfirmationConfig() {
  return {
    centered: true,
    icon: null,
    title: (
      <Title level={4}>
        <Text>{`${intl.formatMessage({ id: 'common.dialog.confirm' })}`}</Text>
      </Title>
    ),
    okText: `${intl.formatMessage({ id: 'common.action.ok' })}`,
    cancelText: `${intl.formatMessage({ id: 'common.action.cancel' })}`,
  }
}
