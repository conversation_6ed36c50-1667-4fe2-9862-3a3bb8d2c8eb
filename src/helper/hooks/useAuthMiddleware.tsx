import { loginRequest } from "../../authConfig";
import { useMsal } from "@azure/msal-react";
import { parseJwt } from "../share";



export const useAuthMiddleware = () => {
  let accessToken: string  = localStorage.getItem('accessToken') || ''
  const { instance, accounts } = useMsal();
  const request = {
    ...loginRequest,
    account: accounts[0],
  }

  const handleCheckTokenExpire = async () => {
    const timeStamp = parseJwt(accessToken)['exp'] || 0
    const isExpired = (new Date().getTime() / 1000) - timeStamp >= 0 

    if(isExpired) {
      const response = await instance.acquireTokenSilent(request)
      localStorage.setItem('accessToken', response.accessToken)
    } else {
      return;
    }
  }

  return {
    handleCheckTokenExpire
  }
}