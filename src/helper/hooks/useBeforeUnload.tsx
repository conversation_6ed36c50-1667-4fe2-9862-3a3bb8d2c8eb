
import { useHistory } from 'react-router-dom';
import { WINDOW_CONFIRM_MESS } from '../../constants';
import { useEffect, useRef, useState } from 'react';

const useBeforeUnload = () => {
    const currentDomain = window.location.hostname;
    const history = useHistory();
    let block = useRef(true);
    let ignoreClick = useRef(false);

    useEffect(() => {
      block.current = true;
      ignoreClick.current = false;
      const unblock = history.block((location, action) => {
        if (block.current) {
          const confirm = window.confirm(getConfirmMessage());
          if (!confirm) {
            ignoreClick.current = true
            return false;
          } else {
            ignoreClick.current = true
          }
        } else {
          block.current = true;
        }
      });
      
      return () => {
        unblock();
      };
    }, [history]);
   
    useEffect(() => {
      const unloadCallback = (event) => {
          event.preventDefault();
          event.returnValue = "";
          return "";
      };

      window.addEventListener("beforeunload", unloadCallback);

      
      const handleClick = (event) => {
          if (event.target.tagName === 'A' &&
             !ignoreClick.current &&
              event.target.getAttribute('target') !== '_blank' &&
              currentDomain === new URL(event.target.href, window.location.origin).hostname) {
            event.preventDefault();
            const confirmLeave = window.confirm(getConfirmMessage());
            if (confirmLeave) {
              block.current = false;
                window.location.href = event.target.href;
            }
          } else {
            ignoreClick.current = false
          }
      };
      document.addEventListener('click', handleClick);

      return () => {
          window.removeEventListener("beforeunload", unloadCallback);
          document.removeEventListener('click', handleClick);
      };
  }, []);
    
    const getConfirmMessage = () => {
        const lang = navigator.language;
        switch (lang) {
            case WINDOW_CONFIRM_MESS.KEY_VI:
                return WINDOW_CONFIRM_MESS.MESS_VI;
            default:
                return WINDOW_CONFIRM_MESS.MESS_DEFAULT;
        }
    };
};

export default useBeforeUnload;