import intl from '../../config/locale.config'
import { Typography } from 'antd'
import { useState } from 'react'
import { recordStatus } from '../share/type'

export default function useFilterStatus(selectedFilter: any) {
  return {
    filteredValue: selectedFilter,
    // defaultFilteredValue: [0, 1, 2, 3],
    onFilter: (value, record, dataIndex) => {
      return record.status === value
    },
  }
}
