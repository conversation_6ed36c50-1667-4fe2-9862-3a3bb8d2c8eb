import { FilterOutlined, SearchOutlined } from '@ant-design/icons'
import {
  Badge, Button, Checkbox, Col, DatePicker, Form, Input, notification, Row, Select, Space, Typography
} from 'antd'
import moment from 'moment'
import { Scrollbars } from 'react-custom-scrollbars'
import intl from '../../config/locale.config'
import {
  COMMITTEE_STATUS,
  COM_ARTEFACT_TYPE_ID,
  DATE_FORMAT,
  MESSAGE_FUNCTION,
  MESSAGE_TYPE,
  MESSAGE_TYPES,
  PROJECT_PREFIX,
  REQ_ARTEFACT_TYPE_ID,
  SEARCH_TYPE,
  STATUS,
  STATUS_COMMON,
  AGENT_STATUS
} from '../../constants'
import { recordStatus } from '../../helper/share/type'

const { Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select
const reqNoti = notification

export const renderStatusBadge = (status, isCR?) => {
  let color = ''
  let textCode = 'common.enum.draft'
  const colors = {
    card_header_color_1: '#f6faff',
    record_submited_color: '#00C853',
    record_draft_color: '#515560',
    record_cancelled_color: '#faad14',
    record_check_in: '#515560',
    record_delete: '#FF0000',
    record_approved_color: '#414ac6',
  }

  switch (status) {
    case STATUS.APPROVE: {
      color = colors.record_approved_color
      textCode = 'common.enum.approved'
      break
    }

    case STATUS.CANCELLED: {
      color = colors.record_cancelled_color
      textCode = 'common.enum.cancelled'
      break
    }

    case STATUS.CHECK_IN: {
      color = colors.record_check_in
      textCode = 'common.enum.checkIn'
      break
    }

    case STATUS.DELETE: {
      color = colors.record_delete
      textCode = 'common.enum.deleted'
      break
    }

    case STATUS.DRAFT: {
      color = colors.record_draft_color
      textCode = 'common.enum.draft'
      break
    }

    case STATUS.ENDORSE: {
      color = colors.record_draft_color
      textCode = 'common.enum.endorsed'
      break
    }

    case STATUS.REJECT: {
      color = colors.record_draft_color
      textCode = isCR ? 'common.enum.reject' : 'common.enum.rejected'
      break
    }

    case STATUS.SUBMITTED: {
      color = colors.record_submited_color
      textCode = 'common.enum.submitted'
      break
    }
    case STATUS.REJECT_CUSTOMER: {
      color = colors.record_draft_color
      textCode = 'common.enum.rejected-customer'
      break
    }
    case STATUS.REJECTED: {
      color = colors.record_draft_color
      textCode = 'common.enum.cm-rejected'
      break
    }
  }

  return (
    <Badge
      className="record-status-badge"
      text={
        <Text style={{ color: color }}>
          {intl.formatMessage({ id: textCode })}{' '}
        </Text>
      }
      color={color}
    />
  )
}

export const renderAgentStatusBadge = (status) => {
  let color = ''
  let textCode = 'common.enum.draft'
  const colors = {
    record_submited_color: '#00C853',
    record_draft_color: '#515560',
    record_approved_color: '#414ac6',
  }

  switch (status) {
    case AGENT_STATUS.APPROVE: {
      color = colors.record_approved_color
      textCode = 'common.enum.approved'
      break
    }

    case AGENT_STATUS.DRAFT: {
      color = colors.record_draft_color
      textCode = 'common.enum.draft'
      break
    }

    case AGENT_STATUS.SUBMITTED: {
      color = colors.record_submited_color
      textCode = 'common.enum.submitted'
      break
    }

    default: {
      // Default to Draft for null/undefined status
      color = colors.record_draft_color
      textCode = 'common.enum.draft'
      break
    }
  }

  return (
    <Badge
      className="record-status-badge"
      text={
        <Text style={{ color: color }}>
          {intl.formatMessage({ id: textCode })}{' '}
        </Text>
      }
      color={color}
    />
  )
}

export const renderCommitteeStatusBadge = (status) => {
  const stt = COMMITTEE_STATUS.find((e) => e.value == status)
  return (
    <Badge
      className="record-status-badge"
      text={<Text style={{ color: stt?.color }}>{stt?.label}</Text>}
      color={stt?.color}
    />
  )
}
export const renderCRStatusBadge = (status) => {
  let color = ''
  let textCode = 'common.enum.draft'
  const colors = {
    card_header_color_1: '#f6faff',
    record_submited_color: '#00C853',
    record_draft_color: '#515560',
    record_cancelled_color: '#faad14',
    record_check_in: '#515560',
    record_delete: '#FF0000',
    record_approved_color: '#414ac6',
  }

  switch (status) {
    case STATUS.APPROVE: {
      color = colors.record_approved_color
      textCode = 'common.enum.approved'
      break
    }

    case STATUS.CANCELLED: {
      color = colors.record_cancelled_color
      textCode = 'common.enum.cancelled'
      break
    }

    case STATUS.CHECK_IN: {
      color = colors.record_check_in
      textCode = 'common.enum.checkIn'
      break
    }

    case STATUS.DELETE: {
      color = colors.record_delete
      textCode = 'common.enum.deleted'
      break
    }

    case STATUS.DRAFT: {
      color = colors.record_draft_color
      textCode = 'common.enum.draft'
      break
    }

    case STATUS.ENDORSE: {
      color = colors.record_draft_color
      textCode = 'common.enum.endorsed'
      break
    }

    case STATUS.REJECT: {
      color = colors.record_draft_color
      textCode = 'common.enum.reject'
      break
    }

    case STATUS.SUBMITTED: {
      color = colors.record_submited_color
      textCode = 'common.enum.submitted'
      break
    }
  }

  return (
    <Badge
      className="record-status-badge"
      text={
        <Text style={{ color: color }}>
          {intl.formatMessage({ id: textCode })}{' '}
        </Text>
      }
      color={color}
    />
  )
}
export const renderCommonStatusBadge = (status) => {
  let color = ''
  let textCode = 'common.enum.draft'
  const colors = {
    card_header_color_1: '#f6faff',
    record_approved_color: '#414ac6',
    record_submited_color: '#00C853',
    record_draft_color: '#515560',
    record_cancelled_color: '#faad14',
    record_check_in: '#515560',
    record_delete: '#FF0000',
  }

  switch (status) {
    case STATUS_COMMON.DRAFT: {
      color = colors.record_draft_color
      textCode = 'common.enum.draft'
      break
    }

    case STATUS_COMMON.SUBMITTED: {
      color = colors.record_submited_color
      textCode = 'common.enum.submitted'
      break
    }

    case STATUS_COMMON.APPROVED: {
      color = colors.record_approved_color
      textCode = 'common.enum.approved'
      break
    }
    case STATUS_COMMON.REJECTED: {
      color = colors.record_delete
      textCode = 'common.enum.cm-rejected'
      break
    }
    case STATUS_COMMON.REMOVED: {
      color = colors.record_delete
      textCode = 'common.enum.removed'
      break
    }
    case STATUS_COMMON.DELETED: {
      color = colors.record_delete
      textCode = 'common.enum.deleted'
      break
    }
    case STATUS_COMMON.RECOMMEND: {
      color = colors.record_cancelled_color
      textCode = 'common.enum.recommended'
      break
    }
  }

  return (
    <Badge
      className="record-status-badge"
      text={
        <Text style={{ color: color }}>
          {intl.formatMessage({ id: textCode })}{' '}
        </Text>
      }
      color={color}
    />
  )
}

export const findPageOfRecord = (data, recordId) => {
  const filterData = data?.filter((item) => item.status !== STATUS.DELETE)
  const findIndexOfEditRecord = filterData.findIndex(
    (item) => item.id === recordId
  )
  return findIndexOfEditRecord
}

const handleSearch = (selectedKeys: string[], confirm) => {
  confirm()
}
const handleReset = (clearFilters) => {
  clearFilters()
}

export const getColumnFilterDueDateProps = (form2) => {
  return {
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => {
      const handleDateChange = (e) => {
        const fromDate = moment(e[0]._d).format("DD/MM/YYYY")
        const toDate = moment(e[1]._d).format("DD/MM/YYYY")
        setSelectedKeys(e ? [fromDate, toDate] : []);
      }

      const handleReset = () => {
        clearFilters();
      }
      return (
        <Row style={{ padding: 8, width: 300 }}>
          <Col span={24}>
            <Form scrollToFirstError={{ block: 'center' }} form={form2} autoComplete="off" >
              <Form.Item name="date">
                <RangePicker onChange={handleDateChange} />
              </Form.Item>
            </Form>
          </Col>

          <Col style={{ paddingTop: '16px' }} span={24}>
            <Space size="small">
              <Button disabled={!selectedKeys || selectedKeys.length <= 0} type="link" className='px-0' onClick={handleReset} size="small">{intl.formatMessage({ id: 'common.action.reset' })}</Button>
              <Button type="primary" onClick={() => confirm()} size="small" >{intl.formatMessage({ id: 'common.action.apply' })}</Button>
            </Space>
          </Col>
        </Row>
      )
    },
    filterIcon: (filtered) => <FilterOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
  }
}
export const getColumnDropdownFilterProps = (options, filterKey = '', isDropdown = false, single = false, width: any = false) => {
  return {
    filterType: SEARCH_TYPE.MULTIPLE_CHOICES,
    filterKey: filterKey,
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => {
      return <Row style={{ padding: 8, width: width ? width : 230 }}>
        <Col span={24}>
          {
            isDropdown ? <>
              {single ? <Select
                className='full-width'
                showSearch
                optionFilterProp="children"
                onChange={(e) => {
                  setSelectedKeys(e ? [e] : [])
                }}
                filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}>
                {
                  options?.map((e, idx) => {
                    return <Option key={idx} value={e.id}>{e.name}</Option>
                  })
                }
              </Select> : <Select
                mode="multiple"
                className='full-width'
                showSearch
                optionFilterProp="children"
                value={selectedKeys}
                onChange={(e) => {
                  setSelectedKeys(e || [])
                }}
                filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}>
                {
                  options?.map((e, idx) => {
                    return <Option key={idx} value={e.value}>{e.text}</Option>
                  })
                }
              </Select>
              }
            </> : <Scrollbars autoHide autoHeight>
              <Checkbox.Group
                onChange={(e) => { setSelectedKeys(e || []) }}
                value={selectedKeys}>
                <Row gutter={[16, 4]}>
                  {
                    options?.map((e, idx) => {
                      return <Col key={idx} span={24}>
                        <Checkbox id={`chk_${idx}`} value={e.value}>{e.text}</Checkbox>
                      </Col>
                    })
                  }
                </Row>
              </Checkbox.Group>
            </Scrollbars>
          }
        </Col>
        <Col style={{ paddingTop: '16px' }} span={24}>
          <Space size="small">
            <Button disabled={!selectedKeys || selectedKeys.length <= 0} type="link" style={{ paddingLeft: 0, paddingRight: 0 }} onClick={() => clearFilters()} size="small">{intl.formatMessage({ id: 'common.action.reset' })}</Button>
            <Button type="primary" onClick={() => confirm()} size="small" >Apply Filter</Button>
          </Space>
        </Col>
      </Row>
    },
    filterIcon: (filtered) => <FilterOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
  }
}

export const getColumnSearchProps = (
  dataIndex: string,
  type: number,
  listFilter?: any,
  listDropdown?: any,
  isCovered?: boolean
) => ({
  filterDropdown: ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
  }) => {
    return (
      <Row
        style={{
          padding: 8,
          width: type === SEARCH_TYPE.CHECK_BOX ? '12vw' : '15vw',
        }}
      >
        {type === SEARCH_TYPE.DATE && (
          <Col span={24}>
            <RangePicker
              format={DATE_FORMAT}
              value={
                selectedKeys && selectedKeys.length > 0 ?
                  [selectedKeys[0][0] ? moment(new Date(selectedKeys[0][0]), DATE_FORMAT) :
                    null, selectedKeys[0][1] ? moment(new Date(selectedKeys[0][1]), DATE_FORMAT) :
                    null] : null
              }
              onChange={(e: any) => {
                setSelectedKeys(e ? [e] : [])
              }
              }
            ></RangePicker>
          </Col>
        )}
        {type === SEARCH_TYPE.TEXT && (
          <Col span={24}>
            <Input
              placeholder={`${intl.formatMessage({ id: 'common.text.search' })} ${dataIndex}`}
              value={selectedKeys[0]}
              onChange={(e) =>
                setSelectedKeys(e.target.value ? [e.target.value] : [])
              }
              onPressEnter={() =>
                handleSearch(selectedKeys, confirm)
              }
              style={{ marginBottom: 8, display: 'block' }}
            />
          </Col>
        )}
        {type === SEARCH_TYPE.CHECK_BOX && (
          <Col span={24}>
            <Checkbox.Group
              onChange={(e) => setSelectedKeys(e ? [e] : [])}
            // defaultValue={[null as any, 0, 1, 2, 3]}
            >
              <Row gutter={[16, 4]}>
                {recordStatus.map((item) => (
                  <Col key={item.value} span={24}>
                    <Checkbox value={item.value}>{item.text}</Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Col>
        )}

        {type === SEARCH_TYPE.DROPDOWN_LIST && (
          <Col span={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(e) => setSelectedKeys(e ? [e] : [])}
              allowClear
              showSearch
            >
              {listDropdown?.map((item) => (
                <Option key={item.id} value={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Col>)}

        {type === SEARCH_TYPE.SINGLE_CHOICE && (
          <Col span={24}>
            <Select
              style={{ width: '100%' }}
              value={selectedKeys[0]}
              onChange={(e) => {
                setSelectedKeys(e ? [e] : (isCovered ? [false] : []))
              }}
              allowClear
            >
              {listFilter?.map((item) => (
                <Option key={item.id} value={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Col>
        )}

        <Col
          style={{
            paddingTop:
              type === SEARCH_TYPE.CHECK_BOX ||
                type === SEARCH_TYPE.SINGLE_CHOICE ||
                type === SEARCH_TYPE.DATE
                ? '16px'
                : '0px',
          }}
          span={24}
        >
          <Space size="small">
            <Button
              disabled={!selectedKeys || selectedKeys.length <= 0}
              type="link"
              style={{ paddingLeft: 0, paddingRight: 0 }}
              onClick={() => handleReset(clearFilters)}
              size="small"
            >
              {intl.formatMessage({ id: 'common.action.reset' })}
            </Button>

            <Button
              type="primary"
              onClick={() => handleSearch(selectedKeys, confirm)}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              {intl.formatMessage({ id: 'common.action.search' })}
            </Button>
          </Space>
        </Col>
      </Row>
    )
  },
  filterIcon: (filtered) =>
    type === SEARCH_TYPE.CHECK_BOX ? (
      <FilterOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ) : (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
  render: (text) => <p>{text}</p>,
})

export const extractProjectCode = () => {
  try {
    const str = window.location.href
    const allHash = str.split('/')
    var startIndex = allHash.indexOf(PROJECT_PREFIX.replaceAll('/', ''))
    if (startIndex != -1 && allHash && allHash[startIndex + 1]) {
      return decodeURI(allHash[startIndex + 1])
    }
    return null
  } catch (error) {
    return null
  }
}

export const hasRole = (role, prjCode = null) => {
  const projectCode = prjCode ? prjCode : extractProjectCode()
  const userRoles = currentUserRoles() || []
  if (projectCode) {
    const project: any = userRoles.find(
      (x: any) => x.projectCode.toLowerCase() === decodeURI(projectCode).toLowerCase()
    )
    return project && project?.roles ? project.roles?.map(e => e.toLowerCase())?.includes(role.toLowerCase()) : false
  }
  return false
}

export const hasCommonRole = (role) => {
  const currentUserStored = localStorage.getItem('currentUserProjects') || ''
  let rs = false;
  try {
    rs = JSON.parse(currentUserStored).role == role
  } catch (error) { }
  return rs
}

export const getProjectMethodology = (projectCode) => {
  const projects = currentUserRoles() || []
  const project = projects.find((x) => x.projectCode === projectCode)
  return project ? project.methodology : ''
}

export const getProjectName = (projectCode) => {
  const projects = currentUserRoles() || []
  const project = projects.find((x) => x.projectCode === projectCode)
  return project ? project.projectName : ''
}

export const currentUserRoles = () => {
  const currentUserStored = localStorage.getItem('currentUserProjects') || ''
  let projects: any[] = []
  try {
    projects = JSON.parse(currentUserStored).projects
  } catch (error) { }
  return projects
}

export const currentUserName = () => {
  const currentUserInfor = localStorage.getItem('currentUserProjects') || ''
  let userName = ''
  try {
    userName = JSON.parse(currentUserInfor).userName
  } catch (error) { }
  return userName
}

export const getArtefactTypeName = (artefact_type: string) => {
  const artefact_type_value = intl.formatMessage({ id: artefact_type })
  try {
    return artefact_type_value.indexOf(' List') != -1
      ? artefact_type_value.replace(' List', '')
      : artefact_type_value
  } catch (e) {
    return artefact_type_value
  }
}

export const ReplaceArtefactName = (message: string) => {
  try {
    return message.indexOf(' List') != -1
      ? message.replace(' List', '')
      : message
  } catch (e) {
    return message
  }
}

export const ShowAppMessage = (error, messageType: any = null, artefact = '', field: any = null) => {
  const typeM = error ? MESSAGE_TYPE.ERROR : MESSAGE_TYPE.SUCCESS;
  const artefact_name = artefact ? getArtefactTypeName(artefact) : '';
  if (error) {
    let contentMessage: any = intl.formatMessage({ id: MESSAGE_FUNCTION.ERROR });
    if (error?.response?.data || typeof error === "string") {
      if (field && intl.formatMessage({ id: error?.response?.data || error }).indexOf('field') != -1) {
        const field_name = intl.formatMessage({ id: field });
        contentMessage = intl.formatMessage({ id: error?.response?.data || error}, { Artefact: artefact_name, field: field_name });
      } else {
        contentMessage = intl.formatMessage({ id: error?.response?.data || error}, { Artefact: artefact_name })
      }
    } else {
      contentMessage = intl.formatMessage({ id: messageType })
    }
    return reqNoti[typeM]({
      description: contentMessage,
      message: intl.formatMessage({ id: 'common.message.error' }),
      placement: 'bottomRight',
    })
  }
  let messageF = '';


  switch (messageType) {
    case MESSAGE_TYPES.CREATE: {
      messageF = MESSAGE_FUNCTION.CREATE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.UPDATE: {
      messageF = MESSAGE_FUNCTION.UPDATE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.RESTORE: {
      messageF = MESSAGE_FUNCTION.RESTORE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.SUBMIT: {
      messageF = MESSAGE_FUNCTION.SUBMIT_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.APPROVE: {
      messageF = MESSAGE_FUNCTION.APPROVE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.REJECT: {
      messageF = MESSAGE_FUNCTION.REJECT_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.ENDORSE: {
      messageF = MESSAGE_FUNCTION.ENDORSE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.CANCEL: {
      messageF = MESSAGE_FUNCTION.CANCEL_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.REMOVE: {
      messageF = MESSAGE_FUNCTION.REMOVE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.DELETE: {
      messageF = MESSAGE_FUNCTION.DELETE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.IMPORT_VALIDATE: {
      messageF = MESSAGE_FUNCTION.IMPORT_VALIDATE_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.IMPORT: {
      messageF = MESSAGE_FUNCTION.IMPORT_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.EXPORT: {
      messageF = MESSAGE_FUNCTION.EXPORT_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.SUBMIT_CR: {
      messageF = MESSAGE_FUNCTION.SUBMITTED_CR_SUCCESS;
      break;
    }
    case MESSAGE_TYPES.REOPEN: {
      messageF = MESSAGE_FUNCTION.REOPEN_SUCCESS;
      break;
    }
    default: messageF = messageType;
    // code block
  }
  let msg = intl.formatMessage({ id: messageF }, { Artefact: artefact_name });
  // Show other message
  return reqNoti[typeM]({
    description: messageType === (MESSAGE_TYPES.SUBMIT_CR || MESSAGE_TYPES.EXPORT) ? intl.formatMessage({ id: messageF }) : msg,
    message: intl.formatMessage({ id: typeM === MESSAGE_TYPE.SUCCESS ? 'common.dialog.success' : typeM === MESSAGE_TYPE.ERROR ? 'common.message.error' : 'common.dialog.warning' }),
    placement: 'bottomRight',
  })
}

const _toast = (type: 'success' | 'error' | 'info' | 'warning', message: string) => {
  notification[type]({
    placement: 'bottomRight',
    message
  })
}
export const toast = {
  success: (message: string) => _toast('success', message),
  error: (error: any) => _toast('error', error["message"] ?? error),
  info: (message: string) => _toast('info', message),
  warning: (message: string) => _toast('warning', message),
}

export const ShowWarningMessge = (messId?, artefact?) => {
  reqNoti['warning']({
    description: intl.formatMessage({ id: messId }, artefact ? { Artefact: intl.formatMessage({ id: artefact }) } : {}),
    message: intl.formatMessage({ id: 'Warning' }),
    placement: 'bottomRight',
  })
}
export const ShowMessgeAdditionalSubmit = (messId, artefact?) => {
  reqNoti['error']({
    description: intl.formatMessage({ id: messId }, artefact ? { Artefact: intl.formatMessage({ id: artefact }) } : {}),
    message: intl.formatMessage({ id: 'common.message.error' }),
    placement: 'bottomRight',
  })
}
export const isImageType = (fileType) => {
  return fileType && fileType.indexOf('image') != -1;
}

export const isPDFType = (fileType) => {
  return fileType && fileType.indexOf('pdf') != -1;
}

export const arrayHaveData = (data) => {
  return data && Array.isArray(data) && data.length > 0;
}

function extract([beg, end]) {
  const matcher = new RegExp(`${beg}(.*?)${end}`, 'gm');
  const normalise = (str) => str.slice(beg.length, end.length * -1);
  return function (str) {
    return str.match(matcher)?.map(normalise);
  }
}
export const getReferencesFromEditor = (data, isCommon = false) => {
  let rs: any = null;
  let actors: any[] = [];
  let businessRules: any[] = [];
  let dataMigrations: any[] = [];
  let emailTemplates: any[] = [];
  let meetingMinutes: any[] = [];
  let messages: any[] = [];
  let nonFunctionalRequirements: any[] = [];
  let objects: any[] = [];
  let objectRelationshipDiagrams: any[] = [];
  let otherRequirements: any[] = [];
  let referenceDocuments: any[] = [];
  let screens: any[] = [];
  let stateTransitions: any[] = [];
  let usecases: any[] = [];
  let usecaseDiagrams: any[] = [];
  let userRequirements: any[] = [];
  let workflows: any[] = [];
  let permissionMatrices: any[] = [];
  let userStories: any[] = [];
  let epics: any[] = [];
  let sprints: any[] = [];
  let term: any[] = [];
  try {
    const stringExtractor = extract(['<a ', '</a>']);
    const referencesArr = stringExtractor(data);
    const filter = referencesArr.filter((e) => e.includes("class") == true)
    const mentions = filter.join()
    const extractor = extract(['#_', '_#']);
    const afterHandle = extractor(mentions)
    afterHandle.forEach(r => {
      const ref = r.split('_');
      if (ref && ref.length == 2) {
        const artefactType = Number(ref[0]);
        const refId = Number(ref[1]);
        if (isCommon) {
          switch (artefactType) {
            case COM_ARTEFACT_TYPE_ID.OBJECT:
              objects.push(refId);
              break
            case COM_ARTEFACT_TYPE_ID.SCREEN:
              screens.push(refId);
              break
            case COM_ARTEFACT_TYPE_ID.USECASE:
              usecases.push(refId);
              break
            case COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE:
              businessRules.push(refId);
              break
            case COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT:
              nonFunctionalRequirements.push(refId);
              break
            case COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE:
              emailTemplates.push(refId);
              break
            case COM_ARTEFACT_TYPE_ID.MESSAGE:
              messages.push(refId);
              break
              case COM_ARTEFACT_TYPE_ID.WORKFLOW:
              workflows.push(refId);
              break
          }
          rs = {
            objectIds: objects,
            screenIds: screens,
            usecaseIds: usecases,
            commonBusinessRuleIds: businessRules,
            nonFunctionalRequirementIds: nonFunctionalRequirements,
            emailTemplateIds: emailTemplates,
            messageIds: messages,
            workflowIds: workflows
          }
        } else {
          switch (artefactType) {
            case REQ_ARTEFACT_TYPE_ID.ACTOR:
              actors.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE:
              businessRules.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION:
              dataMigrations.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE:
              emailTemplates.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE:
              meetingMinutes.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.MESSAGE:
              messages.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT:
              nonFunctionalRequirements.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.OBJECT:
              objects.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM:
              objectRelationshipDiagrams.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT:
              otherRequirements.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT:
              referenceDocuments.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.SCREEN:
              screens.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION:
              stateTransitions.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.USECASE:
              usecases.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM:
              usecaseDiagrams.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT:
              userRequirements.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.WORKFLOW:
              workflows.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX:
              permissionMatrices.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.USER_STORY:
              userStories.push(refId);
              break

            case REQ_ARTEFACT_TYPE_ID.SPRINT:
              sprints.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.EPIC:
              epics.push(refId);
              break
            case REQ_ARTEFACT_TYPE_ID.GLOSSARY:
              term.push(refId);
              break
          }
          rs = {
            actorIds: actors,
            commonBusinessRuleIds: businessRules,
            dataMigrationIds: dataMigrations,
            emailTemplateIds: emailTemplates,
            meetingMinuteIds: meetingMinutes,
            messageIds: messages,
            nonFunctionalRequirementIds: nonFunctionalRequirements,
            objectIds: objects,
            objectRelationshipDiagramIds: objectRelationshipDiagrams,
            otherRequirementIds: otherRequirements,
            referenceDocumentIds: referenceDocuments,
            screenIds: screens,
            stateTransitionIds: stateTransitions,
            usecaseIds: usecases,
            usecaseDiagramIds: usecaseDiagrams,
            userRequirementIds: userRequirements,
            workflowIds: workflows,
            permissionIds: permissionMatrices,
            userStoryIds: userStories,
            epicIds: epics,
            sprintIds: sprints,
            terms: term
          }
        }
      }
    })
  } catch (err) {
    console.log(err)
  }
  return rs
}

export const concatMentionReferences = (value1, value2) => {
  if (!value1 && !value2) return null
  if (!value1) return value2;
  if (!value2) return value1;
  try {
    let rs = Object.assign({}, value1);
    Object.keys(rs).forEach(function (key) {
      rs[key] = value1[key].concat(value2[key])
    });
    return rs;
  } catch (error) {
    return null;
  }
}


export const commonPrefixType = (type, isCommon) => {
  let prefix = '';
  if (isCommon) {
    switch (type) {
      case COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT: {
        prefix = 'NFR';
        break;
      }
      case COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE: {
        prefix = 'CBR';
        break;
      }
      case COM_ARTEFACT_TYPE_ID.MESSAGE: {
        prefix = 'MSG';
        break;
      }
      case COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE: {
        prefix = 'ET';
        break;
      }
      case COM_ARTEFACT_TYPE_ID.OBJECT: {
        prefix = 'CO';
        break;
      }
      case COM_ARTEFACT_TYPE_ID.USECASE: {
        prefix = 'CU';
        break;
      }
      case COM_ARTEFACT_TYPE_ID.SCREEN: {
        prefix = 'CSC';
        break;
      }
      case COM_ARTEFACT_TYPE_ID.WORKFLOW: {
        prefix = 'CW';
        break;
      }
    }
  } else {
    switch (type) {
      case REQ_ARTEFACT_TYPE_ID.ACTOR: {
        prefix = 'AC';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE: {
        prefix = 'CBR';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION: {
        prefix = 'DM';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE: {
        prefix = 'ET';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE: {
        prefix = 'MM';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.MESSAGE: {
        prefix = 'MSG';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT: {
        prefix = 'NFR';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.OBJECT: {
        prefix = 'OBJ';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM: {
        prefix = 'ORD';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT: {
        prefix = 'OR';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT: {
        prefix = 'RD';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.SCREEN: {
        prefix = 'SC';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION: {
        prefix = 'ST';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.USECASE: {
        prefix = 'UC';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM: {
        prefix = 'UCD';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT: {
        prefix = 'USR';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.WORKFLOW: {
        prefix = 'WF';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX: {
        prefix = 'PM';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.USER_STORY: {
        prefix = 'US';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.EPIC: {
        prefix = 'EP';
        break;
      }
      case REQ_ARTEFACT_TYPE_ID.SPRINT: {
        prefix = 'SP';
        break;
      }
    }
  }
  return prefix;
}

export function parseJwt (token) {
  var base64Url = token.split('.')[1];
  var base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  var jsonPayload = decodeURIComponent(window.atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
  }).join(''));

  return JSON.parse(jsonPayload);
}
