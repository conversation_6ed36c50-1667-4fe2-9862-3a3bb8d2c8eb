import intl from '../../config/locale.config'
export enum STATUSCODE {
  OK = 200,
}

// GET_LIST_OBJECT_FROM_CK dùng để lấy ra chuỗi string có chứa ccnobject1dtatobject
// GET_LIST_OBJECT_ID để lấy ra cái số 1 ( ở đây là object id) tương tự với các con khác
export const Regex = {
  GET_LIST_OBJECT_FROM_CK: /(?:ccnobject)(.*?)(?:dtatobject)/g,
  GET_LIST_OBJECT_ID: /(?:ccnobject)(.*?)(?:dtatobject)/i,

  GET_LIST_USE_CASE_FROM_CK: /(?:ccnusecase)(.*?)(?:dtausecase)/g,
  GET_LIST_USE_CASE_ID: /(?:ccnusecase)(.*?)(?:dtausecase)/i,

  GET_LIST_ACTOR_FROM_CK: /(?:ccnactor)(.*?)(?:dtaactor)/g,
  GET_LIST_ACTOR_ID: /(?:ccnactor)(.*?)(?:dtaactor)/i,

  GET_LIST_SCREEN_FROM_CK: /(?:ccnscreen)(.*?)(?:dtascreen)/g,
  GET_LIST_SCREEN_ID: /(?:ccnscreen)(.*?)(?:dtascreen)/i,

  GET_LIST_WORK_FLOW_FROM_CK: /(?:ccnworkflow)(.*?)(?:dtaworkflow)/g,
  GET_LIST_WORK_FLOW_ID: /(?:ccnworkflow)(.*?)(?:dtaworkflow)/i,

  GET_LIST_STATE_TRANSITION_FROM_CK:
    /(?:ccnstatetransition)(.*?)(?:dtastatetransition)/g,
  GET_LIST_STATE_TRANSITION_ID:
    /(?:ccnstatetransition)(.*?)(?:dtastatetransition)/i,

  GET_LIST_MESS_FROM_CK: /(?:ccnmess)(.*?)(?:dtamess)/g,
  GET_LIST_MESS_ID: /(?:ccnmess)(.*?)(?:dtamess)/i,

  GET_LIST_EMAIL_FROM_CK: /(?:ccnemail)(.*?)(?:dtaemail)/g,
  GET_LIST_EMAIL_ID: /(?:ccnemail)(.*?)(?:dtaemail)/i,

  GET_LIST_BUSINESS_RULE_FROM_CK:
    /(?:ccnbusinessrule)(.*?)(?:dtabusinessrule)/g,
  GET_LIST_BUSINESS_RULE_ID: /(?:ccnbusinessrule)(.*?)(?:dtabusinessrule)/i,

  GET_LIST_USER_REQUIREMENT_FROM_CK:
    /(?:ccnuserrequirement)(.*?)(?:dtauserrequirement)/g,
  GET_LIST_USER_REQUIREMENT_ID:
    /(?:ccnuserrequirement)(.*?)(?:dtauserrequirement)/i,

  GET_LIST_MEETING_MINUTES_FROM_CK:
    /(?:ccnmeetingminutes)(.*?)(?:dtameetingminutes)/g,
  GET_LIST_MEETING_MINUTES_ID:
    /(?:ccnmeetingminutes)(.*?)(?:dtameetingminutes)/i,

  GET_LIST_OTHER_REQUIREMENT_FROM_CK:
    /(?:ccnotherrequirement)(.*?)(?:dtaotherrequirement)/g,
  GET_LIST_OTHER_REQUIREMENT_ID:
    /(?:ccnotherrequirement)(.*?)(?:dtaotherrequirement)/i,

  // dùng để lấy freetext
  CcRegex: 'ccn@dtat',
  req_suffix_obj: 'ccnobject',
  req_prefix_obj: 'dtatobject',

  req_suffix_use_case: 'ccnusecase',
  req_prefix_use_case: 'dtausecase',

  req_suffix_actor: 'ccnactor',
  req_prefix_actor: 'dtaactor',

  req_suffix_screen: 'ccnscreen',
  req_prefix_screen: 'dtascreen',

  req_suffix_work_flow: 'ccnworkflow',
  req_prefix_work_flow: 'dtaworkflow',

  req_suffix_state_transition: 'ccnstatetransition',
  req_prefix_state_transition: 'dtastatetransition',

  req_suffix_mess: 'ccnmess',
  req_prefix_mess: 'dtamess',

  req_suffix_email: 'ccnemail',
  req_prefix_email: 'dtaemail',

  req_suffix_business_rule: 'ccnbusinessrule',
  req_prefix_business_rule: 'dtabusinessrule',

  req_suffix_user_requirement: 'ccnuserrequirement',
  req_prefix_user_requirement: 'dtauserrequirement',

  req_suffix_meeting_minutes: 'ccnmeetingminutes',
  req_prefix_meeting_minutes: 'dtameetingminutes',

  req_suffix_other_requirement: 'ccnotherrequirement',
  req_prefix_other_requirement: 'dtaotherrequirement',

  req_suffix_non_functional_requirement: 'ccnnonfunctionalrequirement',
  req_prefix_non_functional_requirement: 'dtanonfunctionalrequirement',

  req_suffix_data_migration: 'ccndatamigration',
  req_prefix_data_migration: 'dtadatamigration',

  req_suffix_object_relationship_diagram: 'ccnobjectrelationshipdiagram',
  req_prefix_object_relationship_diagram: 'dtaobjectrelationshipdiagram',
  
  req_suffix_reference_document: 'ccnreferencedocument',
  req_prefix_reference_document: 'dtareferencedocument',
  
  req_suffix_usecase_diagram: 'ccnusecasediagram',
  req_prefix_usecase_diagram: 'dtausecasediagram',
  
  req_suffix_permission_matrix: 'ccnpermissionmatrix',
  req_prefix_permission_matrix: 'dtapermissionmatrix',

  
  req_suffix_user_story: 'ccnuserstory',
  req_prefix_user_story: 'dtauserstory',

  req_suffix_epic: 'ccnepic',
  req_prefix_epic: 'dtaepic',

  req_suffix_sprint: 'ccnsprint',
  req_prefix_sprint: 'dtasprint',

  req_suffix_cm_object: 'ccncmobject',
  req_prefix_cm_object: 'dtacmobject',

  req_suffix_cm_screen: 'ccncmscreen',
  req_prefix_cm_screen: 'dtacmscreen',

  req_suffix_cm_usecase: 'ccncmusecase',
  req_prefix_cm_usecase: 'dtacmusecase',

  req_suffix_cm_cbr: 'ccncmcommonbusinessrule',
  req_prefix_cm_cbr: 'dtacmcommonbusinessrule',

  req_suffix_cm_nfr: 'ccncmnonfunctionalrequirement',
  req_prefix_cm_nfr: 'dtacmnonfunctionalrequirement',

  req_suffix_cm_email_template: 'dtacmemailtemplate',
  req_prefix_cm_email_template: 'dtacmemailtemplate',

  req_suffix_cm_message: 'ccncmmessage',
  req_prefix_cm_message: 'dtacmmessage',

  req_suffix_cm_workflow: 'ccncworkflow',
  req_prefix_cm_workflow: 'dtacworkflow',

  req_suffix_glossary: 'ccnglossary',
  req_prefix_glossary: 'dtaglossary',
}

export const getListId = (regex, listData) => {
  if (listData && listData.length > 0) {
    const listObjectId: any = []
    listData.map((item: any) => {
      const id: any[] = item.match(regex)
      if (id && id.length > 1) {
        if (/^\d+$/.test(id[1])) {
          listObjectId.push(parseInt(id[1]))
        }
      }
    })
    return listObjectId
  } else {
    return []
  }
}
export const filterPast = [
  {
    text:'1 day',
    value: 2
  },
  {
    text:'2 days',
    value: 3
  },
  {
    text:'3 days',
    value: 4
  },
  {
    text:'1 week',
    value: 5
  },
  {
    text:'2 weeks',
    value: 6
  },
  {
    text: "More than 2 weeks",
    value: 7
  }
]
export const filterNext = [
  {
    text:'1 day',
    value: 8
  },
  {
    text:'2 days',
    value: 9
  },
  {
    text:'3 days',
    value: 10
  },
  {
    text:'1 week',
    value: 11
  },
  {
    text:'2 weeks',
    value: 12
  },
  {
    text: "More than 2 weeks",
    value:13
  }
]
export const recordStatus = [
  {
    text: `${intl.formatMessage({ id: 'common.enum.draft' })}`,
    value: 0,
  },
  {
    text: `${intl.formatMessage({ id: 'common.enum.submitted' })}`,
    value: 1,
  },
  {
    text: `${intl.formatMessage({ id: 'common.enum.cancelled' })}`,
    value: 2,
  },
  {
    text: `${intl.formatMessage({ id: 'common.enum.checkIn' })}`,
    value: 3,
  },
  // {
  //   text: `${intl.formatMessage({ id: 'common.enum.blank' })}`,
  //   value: null,
  // },
]

export const defaultStatus = [0, 1, 2, 3]

export const pageSize = 10

export const filterStatus = {
  filters: recordStatus,
  defaultFilteredValue: [0, 1, 2],
  // filteredValue: [0, 1],
  onFilter: (value, record) => record.status === value,
}

export interface Pagination {
  current: number
  pageSize?: number
  total?: number
}

export interface SelectItem {
  id: number
  name: string
}
