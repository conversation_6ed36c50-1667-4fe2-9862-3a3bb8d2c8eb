import { createReducer } from '@reduxjs/toolkit'
import { resetState, setCommon, setPagination, setUrl } from './action';
import { defaultState, PaginationState } from './type';
const initState: PaginationState = defaultState
const reducer = createReducer(initState, (builder) => {
  return (
    builder
      .addCase(resetState, (state, action?) => {
        Object.assign(state, defaultState);
      })
      .addCase(setPagination, (state, action?) => {
        if(action.payload.artefactType == undefined) {
        } else {
          state.artefactType = action.payload.artefactType
        }
        if (action.payload.paging) {
          state.paginationProps = action.payload.paging
        }
        if(action.payload.isCommon == undefined) {
        } else {
          state.isCommon = action.payload.isCommon
        }
      })
      .addCase(setUrl, (state, action?) => {
        state.paginationUrl.apiUrl = action.payload.apiUrl
        state.paginationUrl.pageIndex = action.payload.pageIndex
        state.paginationUrl.pageSize = action.payload.pageSize
        state.paginationUrl.paramFilter = action.payload.paramFilter
        state.paginationUrl.paramSorter = action.payload.paramSorter
      })
      .addCase(setCommon, (state, action?) => {
        state.isCommon = action.payload
      })
  )
})

export default reducer
export { initState as PaginationState }