interface Sorter {
    column: any
    columnKey: string
    field: string
    order: string
}
interface Pagination {
    pagination: any
    filters: any[]
    sorter: Sorter
    extra: any
}
export interface PaginationState {
    isCommon: boolean
    artefactType: number
    paginationProps?: Pagination
    paginationUrl?: any
}
export const defaultState: PaginationState = {
    isCommon: false,
    artefactType: -1,
    paginationProps: {
        extra: '',
        filters: [],
        pagination: '',
        sorter: {
            column: '',
            columnKey: '',
            field: '',
            order: '',
        }
    },
    paginationUrl: {
        apiUrl: '',
        paramSorter: '',
        pageIndex: 1,
        pageSize: 20,
        paramFilter: ''
    }
}
export enum ActionEnum {
    RESET_STATE = '@@MODULES/LAV_TABLE/RESET_STATE',
    SET_PAGINATION = '@@MODULES/LAV_TABLE/SET_PAGINATION',
    SET_URL = '@@MODULES/LAV_TABLE/SET_URL',
    SET_COMMON = '@@MODULES/LAV_TABLE/SET_COMMON',
}