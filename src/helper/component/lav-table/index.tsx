/* eslint-disable eqeqeq */
/* eslint-disable no-loop-func */
import AppState from '@/store/types'
import { Col, Modal, notification, Row, Space, Spin, Table } from 'antd'
import { saveAs } from 'file-saver'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import {
  BUTTON_TYPE, DEFAULT_PAGE_SIZE, MESSAGE_TYPE, MESSAGE_TYPES, REQ_ARTEFACT_TYPE_ID, SEARCH_TYPE
} from '../../../constants'
import { extractProjectCode, ShowAppMessage } from '../../../helper/share'
import TableService from '../../../services/lav-table-service'
import DeleteButton from '../commonButton/DeleteButton'
import StatisticRight from '../lav-assigned-task-statistic'
import LavPageHeader from '../lav-breadcumb'
import CommentContent from '../right-control-comment'
import { setPagination, setUrl } from './action'
import { PaginationState } from './type'

const { Column } = Table

interface LavTableProps {
  title: string
  apiUrl?: string
  columns: any[]
  rowKey?: string
  showDelete?: boolean
  showUpdate?: boolean
  updateComponent?: React.FC<any>
  createComponent?: React.FC<any>
  deleteComponent?: React.FC<any>
  importComponent?: React.FC<any>
  exportComponent?: React.FC<any>
  commentComponent?: React.FC<any>
  artefact_type: string
  showBreadcumb?: boolean
  artefactType?: number
  isCommon?: boolean
  [props: string]: any
  showHeader?: boolean
  isReload?: boolean
  isHasAction?: boolean
  reloadSuccess?: any
  onDataChange?: any
  handleFilterStatus?: any
  handleFilterDate?: any
  getData?: any
  isAssignTask?: boolean
  committee?: boolean
  hasComment?: boolean
}

const LavTable = ({
  showBreadcumb = true,
  title,
  artefact_type,
  apiUrl,
  columns,
  rowKey = 'rowId',
  createComponent,
  showUpdate = true,
  updateComponent,
  commentComponent,
  showDelete = true,
  deleteComponent,
  importComponent,
  exportComponent,
  showHeader = true,
  isReload = false,
  isHasAction = true,
  reloadSuccess,
  isCommon = false,
  onDataChange,
  handleFilterStatus,
  handleFilterDate,
  artefactType,
  isAssignTask = false,
  committee = false,
  hasComment = false,
  ...rest
}: LavTableProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const [lavColumns, setLavColumns] = useState<any[]>([])
  const [currentFilters, setCurrentFilters] = useState<any>({})
  const [currentSorter, setCurrentSorter] = useState<any>([])
  const [listComment, setListComment] = useState([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [loadedSession, setLoadedSession] = useState(false)
  const [currentPageIndex, setCurrentPageIndex] = useState(1)
  const currentUserProjects = localStorage.getItem('currentUserProjects') || ''
  const currentUserProjectsParse: any = JSON.parse(currentUserProjects)
  const currentProj = currentUserProjectsParse?.projects?.filter((e) => e.projectCode === extractProjectCode())
  let defaultPaging: any
  if (currentProj) {
    defaultPaging = currentProj[0]?.defaultPaging
  }
  const [currentPageSize, setCurrentPageSize] = useState(defaultPaging ? defaultPaging : DEFAULT_PAGE_SIZE)
  const [dataSource, setDataSource] = useState<any>([])
  const [statictis, setStatictis] = useState<any>({})
  const [totalItems, setTotalItems] = useState(0)
  const paginationState = useSelector<AppState | null>((s) => s?.Pagination) as PaginationState;

  const dispatch = useDispatch()
  useEffect(() => {
    if (isReload) {
      loadData(currentPageIndex, currentPageSize, currentFilters, currentSorter, null);
    }
  }, [isReload])

  useEffect(() => {
    if (paginationState?.artefactType === -1) {
    } else {
      localStorage.setItem('paginationState', JSON.stringify(paginationState))
      if (loadedSession) {
        loadData(
          paginationState.paginationProps?.pagination?.current,
          paginationState.paginationProps?.pagination?.pageSize,
          paginationState.paginationProps?.filters,
          [paginationState.paginationProps?.sorter],
          null
        )
        setLoadedSession(false)
      }
    }
  }, [paginationState, loadedSession])

  useEffect(() => {
    if (columns) {
      configTable(true)
    }
  }, [columns])

  const configTable = (isLoadData) => {
    let tempColumns = columns || []
    let defaultSort: any = []
    let defaultFilters: any = null
    const state = localStorage.getItem('paginationState') || ''
    let pState: any
    if (state != '') {
      pState = JSON.parse(state)
    }
    if (((paginationState.paginationProps?.pagination !== '' || pState?.paginationProps?.pagination !== '') && artefactType === pState?.artefactType && isCommon === pState?.isCommon) && !pState) {
      setLavColumns(tempColumns)
    } else {
      if (pState?.paginationProps?.sorter && pState?.artefactType === artefactType) {
        defaultSort.push({ field: pState?.paginationProps?.sorter.field, order: pState?.paginationProps?.sorter.order })
        setCurrentSorter(defaultSort)
        defaultFilters = pState?.paginationProps?.filters
        setCurrentSorter(defaultFilters)
        tempColumns.forEach((element, index) => {
          if (element.dataIndex === 'code') {
            element.className = 'rq-code-column'
          }
          // Default sorter
          if (pState?.paginationProps?.sorter.field === element.dataIndex) {
            element.sortOrder = pState?.paginationProps?.sorter.order
          } else {
            delete element.sortOrder
          }
          // Default filter
          if (pState?.paginationProps?.filters) {
            if (pState?.paginationProps?.filters[index]) {
              element.defaultFilteredValue = pState?.paginationProps?.filters[index]
            }
            if (element.dataIndex === 'isCovered' && String(pState?.paginationProps?.filters[index]) === 'false') {
              element.defaultFilteredValue = false
            }
          }
        })

      } else {
        tempColumns.forEach((element, index) => {
          if (element.dataIndex === 'code') {
            element.className = 'rq-code-column'
          }
          // Default sorter
          if (element.sortOrder) {
            defaultSort.push({ field: element.dataIndex, order: element.sortOrder })
            setCurrentSorter(defaultSort)
          }
          // Default filter
          if (element.defaultFilteredValue && element.defaultFilteredValue.length > 0) {
            let obj: any = {};
            obj[index] = element.defaultFilteredValue
            defaultFilters = obj;
            setCurrentFilters(obj);
          }
        })
      }

      if (committee) {
        defaultSort.push({ field: 'account', order: 'ascend' })
        setCurrentSorter(defaultSort)
      }
      let paging = {
        extra: '',
        filters: defaultFilters,
        pagination: {
          current: currentPageIndex,
          pageSize: currentPageSize,
        },
        sorter: defaultSort[0]
      }
      setLoadedSession(true)
      dispatch(setPagination({ artefactType, isCommon, paging }))
      setLavColumns(tempColumns)
    }
  }

  const loadData = (pageIndex, pageSize, filters, sorter, extra) => {
    let paramFilter = ''
    let paramSorter = ''
    if (sorter && sorter.length) {
      sorter.forEach(element => {
        if (element?.field) {
          let sortDir =
            element.order == 'ascend'
              ? 'asc'
              : element.order == 'descend'
                ? 'desc'
                : ''
          paramSorter += `&SortField=${element.field}&SortDir=${sortDir}`
        }
      });
    }
    var keys = filters ? Object.keys(filters) : null
    if (keys && keys.length > 0) {
      for (var i = 0; i < keys.length; i++) {
        const filterVal = filters[keys[i]]
        // Filter Status
        const columnKey = lavColumns[keys[i]]?.dataIndex || columns[keys[i]]?.dataIndex
        const columnFilterType = lavColumns[keys[i]]?.filterType || columns[keys[i]]?.filterType
        const columnFilterKey = lavColumns[keys[i]]?.filterKey || columns[keys[i]]?.filterKey
        if (columnFilterType === SEARCH_TYPE.MULTIPLE_CHOICES && filterVal) {
          filterVal.forEach((element) => {
            paramFilter += `&${columnFilterKey ? columnFilterKey : columnKey}=${element}`
          })
        } else {
          if (filterVal && filterVal[0] || i === 5 && filterVal && String(filterVal[0] === 'false')) {
            const currentFilterValue = filterVal[0]
            // Date
            if (Array.isArray(currentFilterValue)) {
              const fromDate = currentFilterValue[0] ? moment(currentFilterValue[0]).format("YYYY-MM-DD") : null
              const toDate = currentFilterValue[1] ? moment(currentFilterValue[1]).format("YYYY-MM-DD") : null

              if (fromDate) {
                paramFilter += `&FromDate=${fromDate}`
              }
              if (toDate) {
                paramFilter += `&ToDate=${toDate}`
              }
            } else {
              // String
              paramFilter += `&${columnKey}=${currentFilterValue}`
            }
          }
        }
      }
    }
    setIsLoading(true)
    dispatch(setUrl({ apiUrl, pageIndex, pageSize, paramFilter, paramSorter }))
    TableService
      .getData(apiUrl, pageIndex, pageSize, paramFilter, paramSorter)
      .then((res) => {

        if (isAssignTask) {
          let response = res.data.data ? res.data.data : res.data
          response.assignedTasks ? setDataSource(response.assignedTasks.data?.map((e, index) => {
            return { ...e, rowId: index }
          })) : setDataSource(response.pendingReviewTasks.data?.map((e, index) => {
            return { ...e, rowId: index }
          }))

          setStatictis(response?.statistic)
          let comment: any = []
          response?.assignedTasks?.data?.map((e: any) => {
            if (e?.comments) {
              e.comments.map((ele: any) => {
                const commentEle: any = {
                  ...ele,
                  editting: false,
                  repling: false,
                }
                comment.push(commentEle)
              })
            }
          })
          setListComment(comment)
          response.assignedTasks ? setTotalItems(response?.assignedTasks.total) : setTotalItems(response?.pendingReviewTasks.total)
          setCurrentPageIndex(pageIndex)
          setCurrentPageSize(pageSize)
          setCurrentFilters(filters)
          setCurrentSorter(sorter)
          setIsLoading(false)
          if (onDataChange) {
            onDataChange(res.data)
          }
          if (isReload) {
            reloadSuccess()
          }
        } else {
          let response = res.data.data ? res.data.data : res.data
          setDataSource(response.map((e, index) => {
            return { ...e, rowId: index }
          }))
          let comment: any = []
          response.map((e: any) => {
            if (e?.comments) {
              e.comments.map((ele: any) => {
                const commentEle: any = {
                  ...ele,
                  editting: false,
                  repling: false,
                }
                comment.push(commentEle)
              })
            }
          })
          setListComment(comment)
          setTotalItems(res.data.total)
          setCurrentPageIndex(pageIndex)
          setCurrentPageSize(pageSize)
          setCurrentFilters(filters)
          setCurrentSorter(sorter)
          setIsLoading(false)
          if (onDataChange) {
            onDataChange(res.data)
          }
          if (isReload) {
            reloadSuccess()
          }
        }
      })
      .catch((e) => {
        setDataSource([])
        setTotalItems(0)
        setCurrentPageIndex(1)
        setCurrentPageSize(DEFAULT_PAGE_SIZE)
        setCurrentFilters(null)
        setCurrentSorter(null)
        setIsLoading(false)
        if (onDataChange) {
          onDataChange(null)
        }
        if (isReload) {
          reloadSuccess()
        }
      })
  }
  const handleDelete = (id) => {
    TableService
      .deleteItem(apiUrl, id)
      .then((res) => {
        handleDataChange()
        ShowAppMessage(null, MESSAGE_TYPES.DELETE, artefact_type);
      })
      .catch((err) => {
        ShowAppMessage(err, null, artefact_type);
      })
  }

  const handleDataChange = () => {
    loadData(
      1,
      currentPageSize,
      currentFilters,
      currentSorter,
      null
    )
  }

  const setEditting = () => {

  }

  const handleShowComment = () => {
    setIsModalVisible(true)
  }

  const handleExport = (fileName) => {
    const state = localStorage.getItem('paginationState') || ''
    let pState: any
    if (state != '') {
      pState = JSON.parse(state)
    }

    let paramFilter = ''
    let paramSorter = ''
    if (pState.paginationProps?.sorter) {
      if (Array.isArray(pState.paginationProps?.sorter) && pState.paginationProps?.sorter.length) {
        pState.paginationProps?.sorter.forEach(element => {
          if (element?.field) {
            let sortDir =
              element.order == 'ascend'
                ? 'asc'
                : element.order == 'descend'
                  ? 'desc'
                  : ''
            paramSorter += `&SortField=${element.field}&SortDir=${sortDir}`
          }
        });
      } else {
        paramSorter += `&SortField=${pState.paginationProps?.sorter.field}&SortDir=${pState.paginationProps?.sorter?.sortDir}`
      }
    }
    var keys = pState.paginationProps?.filters ? Object.keys(pState.paginationProps?.filters) : null
    if (keys && keys.length > 0) {
      for (var i = 0; i < keys.length; i++) {
        const filterVal = pState.paginationProps?.filters[keys[i]]
        // Filter Status
        const columnKey = lavColumns[keys[i]]?.dataIndex || columns[keys[i]]?.dataIndex
        const columnFilterType = lavColumns[keys[i]]?.filterType || columns[keys[i]]?.filterType
        const columnFilterKey = lavColumns[keys[i]]?.filterKey || columns[keys[i]]?.filterKey
        if (columnFilterType === SEARCH_TYPE.MULTIPLE_CHOICES && filterVal) {
          filterVal.forEach((element) => {
            paramFilter += `&${columnFilterKey ? columnFilterKey : columnKey}=${element}`
          })
        } else {
          if (filterVal && filterVal[0] || i === 5 && filterVal && String(filterVal[0] === 'false')) {
            const currentFilterValue = filterVal[0]
            // Date
            if (Array.isArray(currentFilterValue)) {
              const fromDate = currentFilterValue[0] ? moment(currentFilterValue[0]).format("YYYY-MM-DD") : null
              const toDate = currentFilterValue[1] ? moment(currentFilterValue[1]).format("YYYY-MM-DD") : null

              if (fromDate) {
                paramFilter += `&FromDate=${fromDate}`
              }
              if (toDate) {
                paramFilter += `&ToDate=${toDate}`
              }
            } else {
              // String
              paramFilter += `&${columnKey}=${currentFilterValue}`
            }
          }
        }
      }
    }

    const pageSize: number = pState.paginationProps?.pagination.pageSize
    const skip: number = (pState.paginationProps?.pagination.pageSize) * (pState.paginationProps?.pagination.current - 1)
    const reqNoti = notification
    TableService
      .export(`${apiUrl}/Export?take=${pageSize}&skip=${skip}${paramFilter}${paramSorter}`)
      .then((res: any) => {
        var blob = new Blob([res.data])
        let fName = `${intl.formatMessage({ id: fileName })}_${moment().format(
          'DDMMYYYYHHMMSS'
        )}.xlsx`
        saveAs(blob, fName)
      })
      .then(() => reqNoti['success']({
        description: intl.formatMessage({ id: 'SCD_10' }, { Artefact: intl.formatMessage({ id: artefact_type }) }),
        message: intl.formatMessage({ id: 'common.dialog.success' }),
        placement: 'bottomRight',
      }))
      .catch((e) => {
        try {
          let responseMess: any = e.response.data
          const uint8Array: any = new Uint8Array(responseMess)
          const messId = String.fromCharCode.apply(null, uint8Array)
          reqNoti['error']({
            description: intl.formatMessage({ id: messId }),
            message: intl.formatMessage({ id: 'common.message.error' }),
            placement: 'bottomRight'
          })
        } catch (err) {
          ShowAppMessage(MESSAGE_TYPE.ERROR)
        }
      })
  }

  const handleTableChange = (pagination, filters, sorter, extra) => {
    // Update Columns after sort
    let columnsUpdated = Object.assign(lavColumns)
    if (extra && extra.action == 'sort') {
      columnsUpdated.forEach((element, idx) => {
        if (element.dataIndex === sorter.field) {
          element.sortOrder = sorter.order
        } else {
          if (!element.sorter?.multiple) {
            element.sortOrder = null
          }
        }
      })
      setLavColumns(columnsUpdated)
    } else {
      if (extra && extra.action == 'filter') {
        columnsUpdated.forEach((element, idx) => {
          if (!filters || !filters[idx]) {
            element.defaultFilteredValue = null;
          }
          if (filters && filters[idx]) {
            element.defaultFilteredValue = filters[idx]
          }
          if (filters[idx] && Object.keys(filters[idx]).length > 0 && element.title == "Due Date") {
            element.defaultFilteredValue = [[filters[idx][0][0], filters[idx][0][1]]]
          }
        })
        setLavColumns(columnsUpdated)
      }
    }
    let defaultSort: any = []
    if (committee) {
      defaultSort.push({ field: 'account', order: 'ascend' })
      setCurrentSorter(defaultSort)
    }
    const paging = { pagination, filters, sorter, extra }
    dispatch(setPagination({ artefactType, paging }))
    loadData(pagination.current, pagination.pageSize, filters, defaultSort.length != 0 ? defaultSort : [sorter], extra)
  }

  return (
    <Spin spinning={isLoading}>
      {showHeader ? (
        <LavPageHeader
          showBreadcumb={showBreadcumb}
          title={intl.formatMessage({ id: title })}
        >
          <Space size="small">
            {createComponent ? (
              React.createElement(createComponent, {
                handleDataChange: handleDataChange,
              })
            ) : (
              <></>
            )}
            {importComponent ? (
              React.createElement(importComponent, {
                handleDataChange: handleDataChange,
              })
            ) : (
              <></>
            )}
            {exportComponent ? (
              React.createElement(exportComponent, {
                handleExport: handleExport,
              })
            ) : (
              <></>
            )}

            {listComment.length > 0 && commentComponent ? (
              React.createElement(commentComponent, {
                handleShowComment: handleShowComment,
              })
            ) : (
              <></>
            )}
          </Space>
        </LavPageHeader>
      ) : (
        <></>
      )}
      <Row gutter={[10, 10]}>
        {
          isAssignTask && artefactType !== REQ_ARTEFACT_TYPE_ID.MY_PENDING_REVIEW_TASK ?
            <Row justify='space-between' style={{ width: '100%' }}>
              <Col span={20} className="card-position">
                <StatisticRight data={statictis} onFilterStatus={handleFilterStatus} onFilterDate={handleFilterDate} />
              </Col>
              <Col span={2} style={{display: 'flex', justifyContent: 'flex-end'}}>{
                listComment.length > 0 && commentComponent ? (
                  React.createElement(commentComponent, {
                    handleShowComment: handleShowComment,
                  })
                ) : (
                  <></>
                )}
              </Col>
            </Row>
            : <></>
        }


        <Col span={24}>
          {
            !isLoading && lavColumns ? <Table
              locale={{
                emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                filterReset: 'Reset',
                filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                filterConfirm: 'Apply Filter',
                selectAll: 'All',
                selectionAll: 'Select All',
              }}
              className="lav-table"
              bordered
              dataSource={dataSource}
              // columns={lavColumns}
              rowKey={rowKey}
              onChange={handleTableChange}
              pagination={{
                current: currentPageIndex,
                pageSize: currentPageSize,
                total: totalItems,
                size: 'small',
                showLessItems: true,
                showSizeChanger: true,
                position: ['topRight'],
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} ${intl.formatMessage({
                    id: 'common.table.pagination.of',
                  })} ${total} ${intl.formatMessage({
                    id: 'common.table.pagination.items',
                  })}`,
              }}
              loading={isLoading}
              scroll={{ x: 650 }}
              {...rest}
            >
              {lavColumns?.map((col, idx) => {
                return (
                  <Column
                    title={col.title}
                    width={col.width}
                    dataIndex={col.dataIndex}
                    key={idx}
                    sorter={col.sorter}
                    sortOrder={col.sortOrder ? col.sortOrder : null}
                    filtered={col.filtered}
                    filters={col.filters ? col.filters : undefined}
                    filterDropdown={col.filterDropdown ? col.filterDropdown : null}
                    filteredValue={col.filteredValue ? col.filteredValue : col.defaultFilteredValue}
                    defaultFilteredValue={
                      col.defaultFilteredValue ? col.defaultFilteredValue : null
                    }
                    onFilter={col.onFilter}
                    filterIcon={col.filterIcon ? col.filterIcon : null}
                    render={col.render ? col.render : (text) => text}
                  />
                )
              })}
              {(((showUpdate && updateComponent) || (showDelete && deleteComponent)) && isHasAction) ? (
                <Column
                  title={intl.formatMessage({ id: 'common.table.column.action' })}
                  dataIndex={rowKey}
                  className="rq-action"
                  width="80px"
                  render={(text, record: any, order) => (
                    <>
                      {showUpdate && updateComponent ? (
                        React.createElement(updateComponent, {
                          order: order,
                          record: record,
                          handleDataChange: handleDataChange,
                          setEditting: setEditting,
                        })
                      ) : (
                        <></>
                      )}
                      {showDelete && deleteComponent ? (
                        React.createElement(deleteComponent, {
                          record: record,
                          children: (
                            <DeleteButton
                              type={BUTTON_TYPE.ICON}
                              content={intl.formatMessage(
                                { id: 'CFD_7' },
                                { artefact_type: intl.formatMessage({ id: artefact_type }) }
                              )}
                              okCB={() => handleDelete(record?.id)}
                              confirmButton={intl.formatMessage({ id: 'common.action.ok' })}
                            />
                          ),
                        })
                      ) : (
                        <></>
                      )}
                    </>
                  )}
                />
              ) : (
                <></>
              )}
            </Table> : <></>
          }
        </Col>

        <Modal
          width={1000}
          title={'Comments'}
          visible={isModalVisible}
          footer={null}
          // okButtonProps={<></>}
          onCancel={() => setIsModalVisible(false)}
          maskClosable={false}>
          <CommentContent comments={listComment} onChange={() => configTable(true)} />
        </Modal>

      </Row>
    </Spin>
  )
}

export default LavTable
