import intl from '../../../config/locale.config'
import { Button } from 'antd'
import { ExportOutlined } from '@ant-design/icons'

interface ExportButtonProps {
    fileName: string,
    onExport: (fileName: string) => void | null,
    title: string
}

const ExportButton = ({ fileName, onExport, title }: ExportButtonProps) => {

    const handleExport = () => {
        onExport(fileName);
    }


    return (
        <Button ghost={true} type='primary' onClick={handleExport}>
            <ExportOutlined />
            {intl.formatMessage({ id: title })}
        </Button>
    )
}

export default ExportButton