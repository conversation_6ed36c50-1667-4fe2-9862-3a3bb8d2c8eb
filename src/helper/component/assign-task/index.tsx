
import { Card, Col, DatePicker, Form, Row, Select } from 'antd'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import intl from '../../../config/locale.config'
import { APP_ROLES, DATE_FORMAT, SCREEN_MODE, STATUS } from '../../../constants'
import { currentUserName, hasRole } from '../../../helper/share'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import AppCommonService from '../../../services/app.service'
import FormGroup from '../form-group'

interface AssigneeTaskProps {
    data: any,
    form: any,
    isDifferent?: boolean
    isSubmit?: boolean
    isEndorse?: boolean
    screenMode?: SCREEN_MODE
}

const   AssignTaskComponent = ({ form, data, isDifferent = false, isSubmit = false, screenMode = SCREEN_MODE.EDIT, isEndorse = false }: AssigneeTaskProps) => {
    const [members, setMembers] = useState<any[]>([]);
    const [memberReviewer, setMemberReviewers] = useState<any[]>([]);
    const [customers, setCustomers] = useState<any[]>([]);
    const [reviewer, setReviewer] = useState<string>("")
    const { Option } = Select
    const [isFormSubmit, setIsFormSubmit] = useState(false);
    useEffect(() => {
        setIsFormSubmit(isSubmit);
    }, [isSubmit])

    useEffect(() => {
        setIsFormSubmit(false);
        if (data?.id && data?.status === STATUS.SUBMITTED || isSubmit) {
            setIsFormSubmit(true);
        }
    }, [isSubmit, data])

    useEffect(() => {
        setReviewer(data?.reviewer)
        form.setFieldsValue({
            reviewer: '',
            dueDate: moment(new Date())
        })
        AppCommonService.getMembers().then(res => setMembers(res.data)).catch((err) => setMembers([]));
        AppCommonService.getMembers().then(res => setMemberReviewers(res.data)).catch((err) => setMemberReviewers([]));
        AppCommonService.getCustomers().then(res => setCustomers(res.data)).catch((err) => setCustomers([]));
    }, [])

    useEffect(() => {
        form.setFieldsValue({ reviewer: '' })
        if (hasRole(APP_ROLES.BA)) {
            form.setFieldsValue({
                assignee: currentUserName(),
            })
        }
        if (data?.id) {
            form.setFieldsValue({
                assignee: data?.author || '',
                reviewer: data?.reviewer || '',
                customer: data?.customer || '',
                // lavdate
                dueDate: data?.dueDate ? moment(new Date(data.dueDate)) : '',
                completeDate: data?.completeDate ? moment(new Date(data.completeDate)) : '',
            })
        }
    }, [data])


    const handleDueDateChange = (e) => {
        if (form.getFieldValue('dueDate') && form.getFieldValue('completeDate') && form.getFieldValue('dueDate')?.startOf('day') > form.getFieldValue('completeDate')?.startOf('day')) {
            form.resetFields(['completeDate']);
        }
    }

    // const disabledDate = (current) => {
    //     return current && current < moment().startOf('day');
    // }

    const disabledCompleteDate = (current) => {
        return current && form.getFieldValue('dueDate') && current < form.getFieldValue('dueDate')?.startOf('day');
    }

    const Content = () => {
        return <>
            <Row >
                <Col span={8}>
                    <FormGroup required={isSubmit} inline className="rq-fg-comment" label={
                        <TriggerComment screenMode={screenMode} field="assignee">
                            {intl.formatMessage({ id: 'common.assign-task.assignee' })}
                        </TriggerComment>} labelSpan={9} controlSpan={10}>
                        <Form.Item name='assignee' rules={[{ required: isSubmit, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                            <Select
                                filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                showSearch
                                className='full-width'>
                                {
                                    members.map(member => (
                                        <Option key={member.userName} value={member.userName}>{member.fullName}</Option>
                                    ))
                                }
                            </Select>
                        </Form.Item>
                    </FormGroup>
                </Col>
                <Col span={8}>
                    <div style={{ paddingLeft: 10 }}>
                        <FormGroup className="rq-fg-comment" inline label={
                            <TriggerComment screenMode={screenMode} field="reviewer">
                                {intl.formatMessage({ id: 'common.assign-task.reviewer' })}
                            </TriggerComment>} labelSpan={10} controlSpan={10}>
                            <Form.Item name='reviewer' rules={[{ required: isFormSubmit, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                <Select
                                    className='full-width'
                                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                    showSearch
                                    onChange={(e: string) => {
                                        setReviewer(e)
                                    }}
                                    allowClear>
                                    {
                                        memberReviewer.map(member => (
                                            <Option key={member.userName} value={member.userName}>{member.fullName}</Option>
                                        ))
                                    }
                                </Select>
                            </Form.Item>
                        </FormGroup>
                    </div>
                </Col>
                <Col span={8}>
                    <div style={{ paddingLeft: 10 }}>
                        <FormGroup className="rq-fg-comment" inline labelSpan={7} controlSpan={10} label={
                            <TriggerComment screenMode={screenMode} field="customer">
                                {intl.formatMessage({ id: 'common.assign-task.customer' })}
                            </TriggerComment>}>
                            <Form.Item name='customer' rules={[{ required: isFormSubmit && reviewer?.toLocaleLowerCase() === currentUserName().toLocaleLowerCase(), message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                                <Select
                                    className='full-width'
                                    filterOption={(input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                    showSearch
                                    allowClear>
                                    {
                                        customers.map(member => (
                                            <Option key={member.userName} value={member.userName}>{member.fullName}</Option>
                                        ))
                                    }
                                </Select>
                            </Form.Item>
                        </FormGroup>
                    </div>
                </Col>
                <Col span={8}>
                    <FormGroup required={isSubmit} className="rq-fg-comment"  inline label={
                        <TriggerComment screenMode={screenMode} field="due-date">
                            {intl.formatMessage({ id: 'common.assign-task.due_date' })}
                        </TriggerComment>} labelSpan={9} controlSpan={10} >
                        <Form.Item name='dueDate' rules={[
                            { required: isSubmit, message: intl.formatMessage({ id: 'IEM_1' }) },
                            // {
                            //     validator: async (rule, value) => {
                            //         if (isSubmit && value && value.startOf('day') < moment().startOf('day')) {
                            //             throw new Error(intl.formatMessage({ id: 'IEM_3_2' }))
                            //         }
                            //     }
                            // }
                        ]}>
                            <DatePicker onChange={handleDueDateChange} className='full-width' format={DATE_FORMAT} />
                        </Form.Item>
                    </FormGroup>
                </Col>
                <Col span={8}>
                    <div style={{ paddingLeft: 10 }}>
                        <FormGroup required={isSubmit} className="rq-fg-comment"  inline label={
                            <TriggerComment screenMode={screenMode} field="complete-date">
                                {intl.formatMessage({ id: 'common.assign-task.complete_date' })}
                            </TriggerComment>} labelSpan={10} controlSpan={10}>
                            <Form.Item name='completeDate' rules={[
                                { required: isSubmit, message: intl.formatMessage({ id: 'IEM_1' }) },
                                // {
                                //     validator: async (rule, value) => {
                                //         if (isSubmit && value && form.getFieldValue('dueDate') && value.startOf('day') < form.getFieldValue('dueDate').startOf('day')) {
                                //             throw new Error(intl.formatMessage({ id: 'IEM_2' }))
                                //         } else {
                                //             if (isSubmit && value && value.startOf('day') < moment().startOf('day')) {
                                //                 throw new Error(intl.formatMessage({ id: 'IEM_3_1' }))
                                //             }
                                //         }
                                //     },
                                // }
                            ]}>
                                <DatePicker className='full-width' format={DATE_FORMAT} disabledDate={disabledCompleteDate} />
                            </Form.Item>
                        </FormGroup>
                    </div>
                </Col>
            </Row>
        </>
    }

    return isDifferent ? <Content /> : <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'assigned_task.title.card' })}>
        <Content />
    </Card>
}


export default AssignTaskComponent