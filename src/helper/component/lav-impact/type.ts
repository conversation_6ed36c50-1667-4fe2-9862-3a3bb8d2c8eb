
interface LAV_DATASOURCE {
    id?: number
    artefactType?: number
    unqId?: string
    children?: LAV_DATASOURCE[]
    code: string,
    name: string,
    selected: boolean,
    checked: boolean,
    broken: boolean,
    disable: boolean,
    level: number,
    numberOfChildren: number,
}

export interface LavImpactState {
    data: LAV_DATASOURCE[]
}

export const defaultState: LavImpactState = {
    data: [],
}
export enum ActionEnum {
    RESET_STATE = '@@MODULES/LAV_IMPACT/RESET_STATE',
    SET_DATAROOT = '@@MODULES/LAV_IMPACT/SET_DATAROOT',
}