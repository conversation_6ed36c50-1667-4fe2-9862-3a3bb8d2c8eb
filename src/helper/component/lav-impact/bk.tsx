import { CloseOutlined, DeleteOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons'
import { <PERSON>Complete, Button, Card, Space, Tooltip, Typography } from 'antd'
import { useEffect, useState } from 'react'
import intl from '../../../config/locale.config'

const { Title } = Typography

const LavImpact = ({ dataDetail, artefactType, onChange, editable = false }) => {
    const [treeData, setTreeData] = useState<any>([]);

    const [inputSearch, setInputSearch] = useState('');
    const [selectedInput, setSelectedInput] = useState<any>(null);
    const allOptions = [
        { value: 1, label: 'UC0001 - Use case 001', code: 'UC0001', name: 'Use case 0001', numberOfChildren: 2 },
        { value: 2, label: 'SC0002 - Screen 002', code: 'SC0002', name: 'Screen 0002', numberOfChildren: 1 },
        { value: 3, label: 'OB0003 - Object 003', code: 'OB0003', name: 'Object 0003', numberOfChildren: 3 }
    ]
    const MOCK_DATA = [
        {
            code: dataDetail.code,
            children: [
                { value: 97, label: 'SC0993 - Screen 0993', code: 'SC0993', name: 'Screen 0993', numberOfChildren: 2 },
                { value: 98, label: 'SC0994 - Screen 0994', code: 'SC0994', name: 'Screen 0994', numberOfChildren: 1 },
                { value: 99, label: 'UC0995 - Use case 0995', code: 'UC0995', name: 'Use case 0995', numberOfChildren: 0 }
            ]
        },
        {
            code: 'UC0001',
            children: [
                { value: 4, label: 'SC0003 - Screen 0003', code: 'SC0003', name: 'Screen 0003', numberOfChildren: 1 },
                { value: 5, label: 'SC0004 - Screen 0004', code: 'SC0004', name: 'Screen 0004', numberOfChildren: 0 }
            ]
        },
        {
            code: 'SC0002',
            children: [
                { value: 7, label: 'OB0006 - Object 0006', code: 'OB0006', name: 'Object 0006', numberOfChildren: 0 }
            ]
        },
        {
            code: 'SC0003',
            children: [
                { value: 6, label: 'OB0005 - Object 0005', code: 'OB0005', name: 'Object 0005', numberOfChildren: 0 }
            ]
        },
        {
            code: 'SC0993',
            children: [
                { value: 100, label: 'OB0007 - Object 0007', code: 'OB0007', name: 'Object 0007', numberOfChildren: 0 },
                { value: 101, label: 'OB0008 - Object 0008', code: 'OB0008', name: 'Object 0008', numberOfChildren: 1 }
            ]
        },
        {
            code: 'SC0994',
            children: [
                { value: 102, label: 'OB0009 - Object 0009', code: 'OB0009', name: 'Object 0009', numberOfChildren: 0 },
            ]
        },
        {
            code: 'OB0008',
            children: [
                { value: 103, label: 'SC0009 - Screen 0009', code: 'SC0009', name: 'Screen 0009', numberOfChildren: 0 },
            ]
        },
        {
            code: 'OB0003',
            children: [
                { value: 8, label: 'UC0003 - Use case 0002', code: 'UC0003', name: 'Use case 0003', numberOfChildren: 2 },
                { value: 9, label: 'UC0004 - Use case 0003', code: 'UC0004', name: 'Use case 0004', numberOfChildren: 0 },
                { value: 10, label: 'UC0005 - Use case 0004', code: 'UC0005', name: 'Use case 0005', numberOfChildren: 0 }
            ]
        },
        {
            code: 'UC0003',
            children: [
                { value: 11, label: 'SC1003 - Screen 1003', code: 'SC1003', name: 'Screen 1003', numberOfChildren: 0 },
                { value: 12, label: 'SC1004 - Screen 1004', code: 'SC1004', name: 'Screen 1004', numberOfChildren: 0 },
            ]
        }
    ]

    const [options, setOptions] = useState<any>(allOptions);

    const onSearch = (searchText: string) => {
        setInputSearch(searchText);
        setOptions(allOptions.filter(e => e.label.toLocaleLowerCase().includes(searchText.toLocaleLowerCase())));
    };

    const handleAdd = () => {
        if (!options || options.length <= 0) {
            return
        }
        const newData = [...treeData, { ...selectedInput, root: true }];
        setTreeData(newData);
        setSelectedInput(null);
        setInputSearch('');
        setNewOptions(newData);
    }

    const onSelect = (onSelect: string, option: any) => {
        setInputSearch(option.label);
        setSelectedInput(option);
    };

    const handleRemove = (index) => {
        let newData = Object.assign([], treeData);
        if (newData.length == 1) {
            newData = []
        } else {
            newData.splice(index, 1);
        }
        setTreeData(newData);
        setNewOptions(newData)
    }

    const setNewOptions = (tData) => {
        let newOptions = allOptions.filter(e => {
            let exists = tData.findIndex(x => x.code === e.code);
            return exists == -1;
        })
        setOptions(newOptions);
    }

    useEffect(() => {
        const rootData = [
            {
                id: dataDetail?.id,
                code: dataDetail?.code,
                name: dataDetail?.name ? dataDetail.name : dataDetail?.title ? dataDetail.title : dataDetail?.subject,
                selected: false,
                checked: false,
                broken: false,
                children: [],
                numberOfChildren: 2,
                root: true,
                artefactType: artefactType,
            }
        ]
        if (dataDetail.impacts) {
            // have impact section
            //setTreeData(JSON.parse(dataDetail.impacts))
            setTreeData(rootData)
        } else {
            if (dataDetail?.id) {
                setTreeData(rootData)
            }
        }
        if (editable) {
            setTreeData([]);
        }
    }, [dataDetail])

    useEffect(() => {
        onChange(treeData)
    }, [treeData])

    const setSelectedItem = (code, data) => {
        if (data.length > 0) {
            data.forEach((e: any) => {
                if (e.code === code) {
                    e.selected = e.selected == true ? false : true;
                    // const url = `${API_URLS.REFERENCE_IMPACT}?artefactType=${e.artefactType}&artefactId=${dataDetail?.id}`
                    // AppCommonService.getImpact(url).then((res) => {
                    //     const newData = res.data?.map((x) => {
                    //         return {
                    //             ...x,
                    //             selected: false,
                    //             checked: false,
                    //             broken: false,
                    //             children: [],
                    //             numberOfChildren: 2
                    //         }
                    //     })
                    //     e.children = newData
                    // })
                    let childrenMock = MOCK_DATA.find(e => e.code === code);
                    e.children = childrenMock?.children || [];
                    // e.children = ResChildren(code)
                } else {
                    if (e.children && e.children.length > 0) {
                        setSelectedItem(code, e.children)
                    }
                }
            })
        }
    }

    const setBrokenItem = (code, data) => {
        if (data.length > 0) {
            data.forEach((e: any) => {
                if (e.code === code) {
                    e.broken = !e.broken
                } else {
                    if (e.children && e.children.length > 0) {
                        setBrokenItem(code, e.children)
                    }
                }
            })
        }
    }
    const setCheckedItem = (code, data, event) => {
        if (data.length > 0) {
            data.forEach((e: any) => {
                if (e.code === code) {
                    e.checked = event.target.checked
                } else {
                    if (e.children && e.children.length > 0) {
                        setCheckedItem(code, e.children, event)
                    }
                }
            })
        }
    }


    const reverseIt = (obj) => {
        if (obj.children) {
            obj.selected = obj.children.reduce((a, c) => obj.selected || reverseIt(c), !1)
        }
        return obj.selected;
    }

    const handleCollapse = (obj, code) => {
        let updated = false;
        obj.children?.forEach(e => {
            if (e.code === code) {
                updated = true;
            } else {
                handleCollapse({ children: e.children }, code);
            }
        })
        if (updated) {
            obj.children?.forEach(e => {
                if (e.code !== code) {
                    e.selected = false;
                }
            })
        }
    }

    const updateChildren = (selectedItem) => {
        let newData = Object.assign([], treeData);
        setSelectedItem(selectedItem.code, newData);
        let dataSource = { children: newData }
        reverseIt(dataSource)
        if (selectedItem.selected) {
            handleCollapse(dataSource, selectedItem.code);
        }
        setTreeData(dataSource.children);
    }

    const handleToggleBreak = (e) => {
        let newData = Object.assign([], treeData);
        setBrokenItem(e.code, treeData)
        let dataSource = { children: newData }
        setTreeData(dataSource.children);
    }
    const handleToggleChecked = (event, e) => {
        let newData = Object.assign([], treeData);
        setCheckedItem(e.code, treeData, event)
        let dataSource = { children: newData }
        setTreeData(dataSource.children);
    }
    const ItemsComponent = ({ data }: any) => {
        if (data.length <= 0) {
            return <></>;
        } else {
            return <>
                {
                    data?.map((e, idx) => {
                        return <div key={idx} className='rq-tree-item'>
                            <div className={`rq-tree-item-selectable ${e.root ? 'root-el' : ''} ${e.selected ? 'selected' : ''} ${e.broken ? 'broken' : ''}`}>
                                <div className="rq-tree-item-checkbox form-check">
                                    <Tooltip title='Verified/Un-Verify'>
                                        <input className="form-check-input" type="checkbox" checked={e.checked} id={`chk_${e.code}`} onChange={(event) => handleToggleChecked(event, e)} />
                                    </Tooltip>
                                    <label className="form-check-label" htmlFor={`chk_${e.code}`}>
                                        <div className="rq-tree-item-code">{e.code}</div>
                                        <div className="rq-tree-item-name">{e.name}</div>
                                    </label>
                                </div>
                                <div className="rq-tree-item-badge">
                                    <Tooltip title="Total Reference From Item">
                                        <span>{e.numberOfChildren}</span>
                                    </Tooltip>
                                </div>
                                <button type='button' className={`re-tree-item-connect ${e.broken ? 'act-add' : 'act-break'}`} onClick={() => handleToggleBreak(e)}>
                                    {e.broken ? <PlusOutlined /> : <CloseOutlined />}
                                </button>
                                {
                                    e.numberOfChildren ? <button type="button" className="rq-tree-item-toggle" onClick={() => updateChildren(e)}>{e.selected ? <MinusOutlined /> : <PlusOutlined />}</button> : <></>
                                }
                                {
                                    (e.root && editable) ? <button className="rq-tree-item-delete" onClick={() => handleRemove(idx)} >
                                        <DeleteOutlined />
                                    </button> : <></>
                                }
                            </div>
                            {
                                (e.selected && e.children && e.children.length > 0) ? <div className="rq-tree-children" style={{ height: `${75 * e.children.length}px` }}>
                                    <ItemsComponent data={e.children} />
                                </div> : <></>
                            }
                        </div>
                    })
                }
            </>
        }
    }

    return <Card title={<Title level={5}>{intl.formatMessage({ id: 'common.label.impact' })}</Title>}>
        <Space direction='vertical' size="large">
            {editable ? <Space>
                <AutoComplete
                    options={options}
                    style={{ width: 400 }}
                    onSelect={onSelect}
                    onSearch={onSearch}
                    value={inputSearch}
                    notFoundContent={inputSearch ? 'No data' : ''}
                    size='middle'
                />
                <Button onClick={handleAdd} type='ghost' size='middle' icon={<PlusOutlined />}>{intl.formatMessage({ id: 'recommend_common_component.action.add' })}</Button>
            </Space> : <></>
            }
            <div className="rq-tree" id="TreeView">
                {treeData && treeData.length > 0 ? <ItemsComponent data={treeData} /> : <>No impact</>}
            </div>
        </Space>
    </Card >

}

export default LavImpact