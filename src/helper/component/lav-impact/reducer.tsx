import { createReducer } from '@reduxjs/toolkit';
import { resetState, setDataRoot } from './action';
import { defaultState, LavImpactState } from './type';
const initState: LavImpactState = defaultState
const reducer = createReducer(initState, (builder) => {
    return (
        builder
            .addCase(resetState, (state, action?) => {
                Object.assign(state, defaultState);
            })
            .addCase(setDataRoot, (state, action?) => {
                if (action.payload.action === "ADD") {
                    state.data = action.payload.data
                } else {
                    debugger
                }
            })
    )
})

export default reducer
export { initState as LavImpactState };

