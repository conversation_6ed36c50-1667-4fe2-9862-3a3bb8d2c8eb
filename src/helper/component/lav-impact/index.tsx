import AppState from '@/store/types'
import { CloseOutlined, DeleteOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons'
import { <PERSON>Complete, Button, Card, Collapse, Space, Spin, Tooltip, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import { API_URLS, APP_ROUTES, PROJECT_PREFIX, REFERENCE_IMPACT } from '../../../constants'
import { extractProjectCode } from '../../../helper/share'
import AppCommonService from '../../../services/app.service'
import { setDataRoot } from './action'
import { LavImpactState } from './type'

const { Title } = Typography
const { Panel } = Collapse

const getUrl = (artefactType, id) => {
    const domain = `${window.location.origin}/#${PROJECT_PREFIX}${extractProjectCode()}`
    let url = ''
    switch (artefactType) {
        case 1:
            url = `${domain}${APP_ROUTES.ACTOR_DETAIL}${id}`;
            break;
        case 2:
            url = `${domain}${APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL}${id}`;
            break;
        case 3:
            url = `${domain}${APP_ROUTES.DATA_MIGRATION_DETAIL}${id}`;
            break;
        case 4:
            url = `${domain}${APP_ROUTES.MAIL_DETAIL}${id}`;
            break;
        case 5:
            url = `${domain}${APP_ROUTES.MEETING_DETAIL}${id}`;
            break;
        case 6:
            url = `${domain}${APP_ROUTES.MESSAGE_DETAIL}${id}`;
            break;
        case 7:
            url = `${domain}${APP_ROUTES.NONFUNTIONAL_REQ_DETAIL}${id}`;
            break;
        case 8:
            url = `${domain}${APP_ROUTES.OBJECT_DETAIL}${id}`;
            break;
        case 9:
            url = `${domain}${APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL}${id}`;
            break;
        case 10:
            url = `${domain}${APP_ROUTES.OTHER_REQUIREMENT_DETAIL}${id}`;
            break;
        case 11:
            url = `${domain}${APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}${id}`;
            break;
        case 12:
            url = `${domain}${APP_ROUTES.SCREEN_DETAIL}${id}`;
            break;
        case 13:
            url = `${domain}${APP_ROUTES.STATE_TRANSITION_DETAIL}${id}`;
            break;
        case 14:
            url = `${domain}${APP_ROUTES.USECASE_DETAIL}${id}`;
            break;
        case 15:
            url = `${domain}${APP_ROUTES.USECASE_DIAGRAM_DETAIL}${id}`;
            break;
        case 16:
            url = `${domain}${APP_ROUTES.USER_REQUIREMENT_DETAIL}${id}`;
            break;
        case 17:
            url = `${domain}${APP_ROUTES.WORKFLOW_DETAIL}${id}`;
            break;
    }
    return url;
}

const LavImpact = ({ dataDetail, artefactType, onChange, editable = false, isSubmitForm = false, isViewMode = false }) => {
    const [treeData, setTreeData] = useState<any>(null);
    const [inputSearch, setInputSearch] = useState('');
    const [selectedInput, setSelectedInput] = useState<any>(null);
    const [allOptions, setAllOptions] = useState<any>([]);
    const [options, setOptions] = useState<any>(allOptions);
    const dispatch = useDispatch()
    const state = useSelector<AppState | null>((s) => s?.LavImpact) as LavImpactState;

    useEffect(() => {
        if (state.data.length > 0) {

        }
    }, [state.data])

    const defaultRootTree = {
        unqId: 'lav_root',
        children: editable ? [] :
            [
                {
                    unqId: 'lav_root_' + dataDetail?.code + '_0',
                    id: dataDetail?.id,
                    code: dataDetail?.code,
                    name: dataDetail?.name ? dataDetail.name : dataDetail?.title ? dataDetail.title : dataDetail?.subject,
                    selected: false,
                    checked: false,
                    broken: false,
                    disable: false,
                    level: 1,
                    children: [],
                    numberOfChildren: dataDetail?.referenceFrom?.length,
                    root: true,
                    artefactType: artefactType,
                }
            ]
    }

    // Update tree node data
    const updateItem = (data, newValue) => {
        let newData = Object.assign({}, data);
        newData?.children?.map((e, idx) => {
            if (e.unqId === newValue.unqId) {
                e = newValue;
            } else {
                updateItem(e, newValue);
            }
        })
        return newData
    }

    //Flat nested tree data 
    let newFlatData: any = []
    const handleFlatData = (data) => {
        let newData = Object.assign([], data);
        newData?.forEach((e, idx) => {
            if (e.root) {
                newFlatData = newFlatData.concat([e])
            }
            if (e.children?.length > 0 && e.selected === true) {
                newFlatData = newFlatData.concat([...e.children])
                handleFlatData(e.children)
            }
        })
        return newFlatData
    }

    //Count children 

    let numberChildren: number = 0
    const handleCountChildren = (data) => {
        numberChildren += data?.numberOfChildren
        let newData = Object.assign({}, data);
        newData?.children.forEach((e, idx) => {
            if (e.children?.length > 0 && e.selected === true) {
                handleCountChildren(e)
            }
        })

        return numberChildren
    }

    useEffect(() => {
        AppCommonService.getMentions(false).then(res => {
            setAllOptions(res.data?.map(e => {
                return {
                    ...e,
                    value: e.id,
                    label: e.id.slice(1),
                    id: e.name,
                    name: e.id.slice(1)
                }
            }))
        }).catch(err => {
            setAllOptions([])
        })
    }, [])

    useEffect(() => {
        setTreeData(null);
        //Set tree if have exist data
        if (dataDetail?.impacts && dataDetail?.impacts !== 'false' && dataDetail?.impacts !== 'null' && dataDetail?.impacts !== '[]') {
            const currentImpacts = JSON.parse(dataDetail?.impacts);
            const assignCurrentData = JSON.parse(JSON.stringify(currentImpacts))
            const newDataRootState = handleFlatData(currentImpacts.children)
            if (newDataRootState.length > 0) {
                dispatch(setDataRoot({
                    data: newDataRootState,
                    action: "ADD"
                }))

                // const dataBinding = handleDataFirstTime({ ...assignCurrentData, unqId: "lav_root" }, newDataRootState)
                setTreeData(assignCurrentData)
            }

        }
        //Set tree if not have exist data
        if ((dataDetail?.impacts === null || dataDetail?.impacts === 'false' || dataDetail?.impacts === "{}" || dataDetail?.impacts === 'null' || dataDetail?.impacts === '[]') && !isViewMode) {
            setTreeData(defaultRootTree)
        }
    }, [dataDetail])

    //Send tree data when submit form
    useEffect(() => {
        if (isSubmitForm) {
            onChange(treeData)
        }
    }, [isSubmitForm])

    //handle disable data from root 

    const handleDisableDataFromRoot = (data, flatData) => {
        const newData = Object.assign({}, data)
        const newFlatData = Object.assign([], flatData)

        newData.children.forEach((e) => {
            const filterDuplicate = newFlatData.filter((ele) => e.id === ele.id && e.artefactType === ele.artefactType)
            if (filterDuplicate.length > 2 && e.level > Math.min(...filterDuplicate.map((e) => e.level))) {
                e.disable = true
                e.selected = false
            }

            if (e.selected) {
                handleDisableDataFromRoot(e, newFlatData)
            }
        })

        return data
    }

    const handleAbleDataFromRoot = (data, item, flatData) => {
        const newData = Object.assign({}, data)
        newData.children.forEach((e) => {
            const filterDuplicate = flatData.filter((e) => item.id === e.id && item.artefactType === e.artefactType && e.level === item.level + 1)
            if (item.id === e.id && item.artefactType === e.artefactType && e.level === item.level + 1 && filterDuplicate[0].direction === e.direction) {
                e.disable = false
            }

            if (e.selected) {
                handleAbleDataFromRoot(e, item, flatData)
            }
        })

        return data
    }

    const handleSortDataAble = (data, direction) => {
        const newData = Object.assign({}, data)
        newData?.children.forEach((e) => {
            if (e.selected) {
                if (direction) {
                    const newDataFrom = e?.children.filter((e) => e?.direction === 1)
                    const newDataTo = e?.children.filter((e) => e?.direction === 2)

                    const dataSortFromTo = [...newDataFrom, ...newDataTo]
                    const newDataAble = dataSortFromTo.filter((e) => e?.disable === false)
                    const newDataDisable = dataSortFromTo.filter((e) => e?.disable === true)
                    //data after sort able/ disable
                    e.children = [...newDataAble, ...newDataDisable]
                } else {
                    const newDataAble = e?.children.filter((e) => e?.disable === false)
                    const newDataDisable = e?.children.filter((e) => e?.disable === true)
                    e.children = [...newDataAble, ...newDataDisable]
                }

                handleSortDataAble(e, direction)
            }
        })
    }



    const handleDataFirstTime = (data, flatData) => {
        let newData = Object.assign({}, data);
        let newFlatData = Object.assign([], flatData);
        newData?.children && newData?.children?.map((e) => {
            if (e.children?.length !== 0 && e.children) {
                delete e?.description;
                const url = `${API_URLS.REFERENCE_IMPACT}?artefactType=${e.artefactType}&artefactId=${e?.id}`
                AppCommonService.getImpact(url).then((res) => {
                    e.numberOfChildren = res.data?.length || 0
                    res.data.forEach(element => {
                        const exists = e.children.find(x => x.code === element.code && x.direction === element.direction);
                        if (exists) {
                            delete exists?.description;
                            element.checked = exists.checked;
                            element.selected = exists.selected;
                            element.disable = exists.disable;
                            element.broken = exists.broken;
                            element.children = exists.children;
                        }
                    });


                    const newData = res.data?.map((x) => {
                        delete x?.description;
                        const a = newFlatData.filter((ele) => ele.id === x.id && ele.artefactType === x.artefactType && ele.direction === x.direction && ele.level > 1)
                        return {
                            ...x,
                            selected: x.selected || false,
                            checked: x.checked || false,
                            disable: x.disable || false,
                            level: e?.level + 1,
                            broken: x.broken || false,
                            children: x.children || [],
                            numberOfChildren: x.numberOfChildren,
                        }
                    })
                    const newDataAble = newData.filter((e) => e?.disable === false)
                    const newDataDisable = newData.filter((e) => e?.disable === true)
                    e.children = [...newDataAble, ...newDataDisable]
                    // const a = updateItem(treeData, )
                    // updateItem()
                })
                if (e.selected) {
                    handleDataFirstTime(e, [])
                }
            }
        })
        return data
    }

    const onSearch = (searchText: string) => {
        setInputSearch(searchText);
        const newOpts = allOptions.filter((elem) => !treeData?.children?.find(({ code }) => elem?.code === code));
        setOptions(newOpts.filter(e => e.value.toLocaleLowerCase().includes(searchText.toLocaleLowerCase())));
    };

    //handle Disable Data when add more at root
    const handleAdd = () => {
        if (!options || options.length <= 0 || !selectedInput) {
            return
        }

        AppCommonService.getImpactCountCR(selectedInput?.artefactType, selectedInput?.id).then((res) => {
            const newData = {
                ...treeData,
                children: [
                    ...treeData?.children || [],
                    {
                        ...selectedInput,
                        id: parseInt(selectedInput.id),
                        root: true,
                        level: 1,
                        numberOfChildren: res.data,
                        unqId: `lav_root_${selectedInput.code}_${treeData?.children.length}`
                    }
                ]
            };

            const newDataState = [
                {
                    ...selectedInput,
                    id: parseInt(selectedInput.id),
                    root: true,
                    level: 1,
                    numberOfChildren: res.data
                },
                ...state.data
            ]
            handleDisableDataFromRoot(newData, newDataState)
            handleSortDataAble(newData, false)

            const assignCurrentData = JSON.parse(JSON.stringify(newData))
            const newDataRootState = handleFlatData(assignCurrentData.children)

            dispatch(setDataRoot({
                data: newDataRootState,
                action: "ADD"
            }))
            setTreeData(newData);
            setSelectedInput(null);
            setInputSearch('');
            setNewOptions(newData);
        })

    }

    const onSelect = (onSelect: string, option: any) => {
        setInputSearch(option.label);
        setSelectedInput(option);
    };

    const setNewOptions = (tData) => {
        let newOptions = allOptions.filter(e => {
            let exists = tData?.children?.findIndex(x => x.code === e.code);
            return exists == -1;
        })
        setOptions(newOptions);
    }

    const handleDataChange = (e) => {
        setTreeData(e);
    }

    const LavImpactItems = ({ data, editable = false, onChange, isSubmitForm = false }) => {
        const [dataSource, setDataSource] = useState<any>([]);
        const [isLoading, setIsLoading] = useState(false);
        const dispatch = useDispatch()

        useEffect(() => {
            //Set unique for all element
            setDataSource(updateItemUnqId(data));
        }, [data])

        useEffect(() => {
            if (isSubmitForm) {
                onChange(dataSource)
            }
        }, [isSubmitForm])

        const updateItemUnqId = (data) => {
            let newData = Object.assign({}, data);
            newData?.children?.forEach((e, idx) => {
                e.unqId = `${newData.unqId}_${e.code}_${idx}`
            })

            return newData;
        }

        // Update child node
        const updateItemSelected = (data, selected) => {
            let newData = Object.assign({}, data);
            newData?.children?.forEach((e) => {
                e.selected = selected
            })
            return newData;
        }

        // Update parent tree selected
        const updateParentSelected = (obj) => {
            if (obj.children) {
                obj.selected = obj.children.reduce((a, c) => obj.selected || updateParentSelected(c), !1)
            }
            return obj.selected;
        }



        // Update tree node attribute
        const updateItemAttr = (data, unqId, key, value) => {
            let newData = Object.assign({}, data);
            newData?.children?.map((e, idx) => {
                if (e.unqId === unqId) {
                    e[key] = value;
                } else {
                    updateItemAttr(e, unqId, key, value);
                }
            })
            return newData
        }

        // Update tree selected (same level)
        const resetSelectedTree = (data, selectedItem) => {
            let newData = Object.assign({}, data);
            let existsIndex = newData?.children?.findIndex(e => e.unqId === selectedItem.unqId);
            if (existsIndex != -1) {
                newData?.children?.forEach((e, idx) => {
                    if (idx !== existsIndex && selectedItem.selected) {
                        e.selected = false;
                    }
                })
            }
            return newData;
        }


        const updateDataSource = (item, selected) => {
            let newDataSource = updateItemSelected(dataSource, false);
            item.selected = selected;
            updateParentSelected(newDataSource);
            newDataSource = updateItem(newDataSource, item);
            newDataSource = resetSelectedTree(newDataSource, item);
            setDataSource(newDataSource);
        }

        // Handle break impact
        const handleToggleBreak = (item) => {
            let newDataSource = updateItemAttr(dataSource, item.unqId, 'broken', !item.broken);
            setDataSource(newDataSource);
            if (item?.children) {
                item?.children?.map((e) => {
                    if (!e.broken && item.broken) {
                        handleToggleBreak(e)
                    }
                    if (e.broken && item.broken) {
                        e.broken = false
                        handleToggleBreak(e)
                    }
                    if (e.broken && !item.broken) {
                        e.broken = true
                        handleToggleBreak(e)
                    }
                })
            }
        }

        const handleToggleCollapse = (item) => {
            if (!item.selected) {
                const filterLevel = state.data.filter((e) => e.level <= item.level)
                const url = `${API_URLS.REFERENCE_IMPACT}?artefactType=${item.artefactType}&artefactId=${item?.id}`
                setIsLoading(true);
                AppCommonService.getImpact(url).then((res) => {
                    item.numberOfChildren = res.data?.length || 0
                    dispatch(setDataRoot({
                        data: [...filterLevel, ...res.data.map((e) => {
                            return {
                                ...e,
                                checked: false,
                                level: item.level + 1,
                            }
                        })],
                        action: "ADD"
                    }))
                    if (item.children && item.children.length > 0) {
                        res.data.forEach(element => {
                            const exists = item.children.find(x => x.code === element.code);
                            delete element?.description;
                            if (exists) {
                                element.checked = exists.checked;
                                element.broken = exists.broken;
                                element.children = exists.children;
                            }
                        });
                    }

                    const newData = res.data?.map((x) => {
                        let filterDataDuplicate = res.data.filter((e) => e.id == x.id && e.artefactType === x.artefactType)
                        delete x?.description;
                        return {
                            ...x,
                            selected: false,
                            checked: x.checked || false,
                            broken: x.broken || false,
                            level: item.level + 1,
                            children: x.children || [],
                            disable: (filterLevel?.findIndex((e) => e.artefactType === x.artefactType && e.id === x.id) === -1 && filterDataDuplicate?.findIndex((e) => e.direction === x.direction) === 0) ? false : true,
                            numberOfChildren: x.numberOfChildren
                        }
                    })

                    const newDataAble = newData.filter((e) => e?.disable === false)
                    const newDataDisable = newData.filter((e) => e?.disable === true)
                    item.children = [...newDataAble, ...newDataDisable]
                    item.selected = !item.selected;
                    updateDataSource(item, item.selected);
                    setIsLoading(false);
                }).catch(e => {
                    setIsLoading(false);
                })
            } else {
                const numberNeedRemove = handleCountChildren(item)
                let newData: any = []
                state.data.forEach((e, index) => {
                    if (index < (state.data.length - numberNeedRemove)) {
                        newData.push(e)
                    }
                })
                dispatch(setDataRoot({
                    data: newData,
                    action: "ADD"
                }))

                item.selected = !item.selected;
                updateDataSource(item, item.selected);
            }
        }

        const handleToggleChecked = (event, item) => {
            let newDataSource = updateItemAttr(dataSource, item.unqId, 'checked', event.target.checked);
            setDataSource(newDataSource);
        }

        const handleRemove = (itemIndex, isSelected) => {
            let newDataSource = Object.assign({}, dataSource);
            if (newDataSource.children?.length <= 1) {
                newDataSource = []
                dispatch(dispatch(setDataRoot({
                    data: [],
                    action: "ADD"
                })))
            } else {
                let newData: any = Object.assign([], state.data)
                handleAbleDataFromRoot(newDataSource, newDataSource.children[itemIndex], newData)
                const dataSpliced = newDataSource.children.splice(itemIndex, 1);

                handleSortDataAble(newDataSource, true)

                //Get index of sliced data in State Impact
                const indexDataInState = newData?.findIndex((e) => e.id === dataSpliced[0].id && e.artefactType === dataSpliced[0].artefactType)
                //Remove sliced data from State Impact
                newData?.splice(indexDataInState, 1)
                dispatch(dispatch(setDataRoot({
                    data: newData,
                    action: "ADD"
                })))
            }

            setDataSource(newDataSource);
            onChange(newDataSource);
        }


        if (!dataSource?.children || dataSource?.children?.length <= 0) {
            return <></>;
        } else {
            return <>
                {
                    !isLoading ? (
                        <>
                            {
                                dataSource.children.map((e, idx) => {
                                    return <div key={idx} className={`rq-tree-item`}>
                                        <div className={`rq-tree-item-selectable ${e.root ? 'root-el' : ''} ${(e.selected && e.children?.length > 0) ? 'selected' : ''} ${e.broken ? 'broken' : ''} ${e.disable ? 'rq-tree-item-disable' : e?.direction === REFERENCE_IMPACT.TO ? 'rq-tree-item-to' : (e?.direction === REFERENCE_IMPACT.FROM ? 'rq-tree-item-from' : 'xxx')}`}>
                                            <div className="rq-tree-item-checkbox form-check">
                                                {!e?.disable ? <Tooltip title='Verified/Un-Verify'>
                                                    <input className="form-check-input" type="checkbox" disabled={isViewMode || e.disable} checked={e.checked} id={`chk_${e.code}`} onChange={(event) => handleToggleChecked(event, e)} />
                                                </Tooltip> : <div> </div>
                                                }
                                                <label className="form-check-label" >
                                                    <div className="rq-tree-item-code">{`${e?.direction === REFERENCE_IMPACT.TO ? intl.formatMessage({ id: 'common.label.referenceto' }) : (e?.direction === REFERENCE_IMPACT.FROM ? intl.formatMessage({ id: 'common.label.referencefrom' }) : '')} ${e.code || intl.formatMessage({ id: 'common.artefact.permission' })}`}</div>
                                                    <div className="rq-tree-item-name" onClick={() => window.open(getUrl(e?.artefactType, e?.id), '_blank', 'noopener,noreferrer')}>
                                                        <Tooltip title={e?.name} placement="bottom">
                                                            {e?.name?.length > 65 ? e.name.substring(0, 65) + '...' : e.name}
                                                        </Tooltip>
                                                    </div>
                                                </label>
                                            </div>
                                            {!e?.disable ? <div className="rq-tree-item-badge">
                                                <Tooltip title="Total Reference From Item">
                                                    <span>{e.numberOfChildren === -1 ? 0 : e.numberOfChildren}</span>
                                                </Tooltip>
                                            </div> : <></>
                                            }
                                            {
                                                isViewMode || e.disable ? <></> :
                                                    <button disabled={isLoading} type='button' className={`re-tree-item-connect ${isViewMode || e.disable ? '' : (e.broken ? 'act-add' : 'act-break')}`} onClick={() => handleToggleBreak(e)}>
                                                        {(e.broken ? <PlusOutlined /> : <CloseOutlined />)}
                                                    </button>
                                            }
                                            {
                                                (e.numberOfChildren || e.numberOfChildren == 0) && !e.disable ? <button disabled={isLoading} type="button" className="rq-tree-item-toggle" onClick={() => handleToggleCollapse(e)}>{e.selected ? <MinusOutlined /> : <PlusOutlined />}</button> : <></>
                                            }
                                            {
                                                (e.root && editable) ? <button type="button" disabled={isViewMode} className="rq-tree-item-delete" onClick={() => handleRemove(idx, e.selected)} >
                                                    <DeleteOutlined />
                                                </button> : <></>
                                            }
                                        </div>

                                        {
                                            (e.selected && e.children && e.children.length > 0) ? <div className="rq-tree-children" style={{ height: `${75 * e.children.length}px` }}>
                                                <LavImpactItems onChange={onChange} data={e} />
                                            </div> : <></>
                                        }
                                    </div>
                                })
                            }
                        </>
                    ) : (
                        <div style={{ margin: "200px 450px" }}>
                            <Spin spinning={isLoading}></Spin>
                        </div>
                    )
                }
            </>
        }
    }


    return (
        <>
            {isViewMode ? (
                <Collapse bordered={true} className="rq-audit-trail">
                    <Panel header={<Title level={5}>{intl.formatMessage({ id: 'common.label.impact' })}</Title>} key="1">
                        <div className="rq-tree" id="TreeView"
                            style={{ height: '300px' }}
                        >
                            {treeData && treeData?.children?.length > 0 ? <LavImpactItems onChange={handleDataChange} editable={editable} data={treeData} /> : <></>}
                        </div>
                    </Panel>
                </Collapse>
            ) : (
                <Card title={<Title level={5}>{intl.formatMessage({ id: 'common.label.impact' })}</Title>}>
                    <Space direction='vertical' size="large" >
                        {editable ?
                            <Space>
                                <AutoComplete
                                    options={options}
                                    style={{ width: 400 }}
                                    onSelect={onSelect}
                                    onSearch={onSearch}
                                    value={inputSearch}
                                    notFoundContent={inputSearch ? 'No data' : ''}
                                    size='middle'
                                />
                                <Button onClick={handleAdd} type='ghost' size='middle' icon={<PlusOutlined />}>{intl.formatMessage({ id: 'recommend_common_component.action.add' })}</Button>
                            </Space>
                            : <></>
                        }
                        <div className="rq-tree" id="TreeView"
                            style={{ height: '300px' }}
                        >
                            {treeData && treeData?.children?.length > 0 ? <LavImpactItems onChange={handleDataChange} editable={editable} data={treeData} /> : <></>}
                        </div>
                    </Space>
                </Card >
            )}

        </>

    )
}

export default LavImpact
