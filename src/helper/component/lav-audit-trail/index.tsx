import intl from "../../../config/locale.config";
import { Collapse, Typography } from "antd";

const { Panel } = Collapse
const { Title } = Typography

interface LavAuditTrailProps {
    data: any
}
const LavAuditTrail = ({ data }: LavAuditTrailProps) => {
    return <Collapse bordered={true} className="rq-audit-trail">
        <Panel
            className="description"
            header={<Title level={5}>{intl.formatMessage({ id: 'common.audit-trail' })}</Title>}
            key="1"
        >
            {data}
        </Panel>
    </Collapse>
}
export default LavAuditTrail;