import intl from '../../../config/locale.config'
import { Col, Row } from 'antd'
import React from 'react'

interface FormGroupProps {
    className?: string,
    label?: React.ReactNode | string
    inline?: boolean
    required?: boolean
    children: any
    labelSpan?: number
    controlSpan?: number
    isCustomLabel?: boolean
}
const FormGroup = ({
    className,
    inline,
    required,
    label,
    children,
    controlSpan,
    labelSpan = 4,
    isCustomLabel = false
}: FormGroupProps) => {
    return (
        <div className={`rq-form-group ${required ? 'rq-form-group-required' : ''} ${inline ? 'inline' : ''} ${className}`}>
            {
                inline ?
                    <Row>
                        <Col span={labelSpan}>
                            <div className='rq-form-label' style={{ textAlign: isCustomLabel ? 'center' : 'left' }}>
                                <span >{label}</span>
                                {required ? <span className='rq-required'>{intl.formatMessage({ id: `common.mandatory.*` })}</span> : <></>}
                            </div>
                        </Col>
                        <Col span={controlSpan || 20}>
                            <div className='rq-form-control'>{children}</div>
                        </Col>
                    </Row> :
                    <>
                        <div className='rq-form-label'>
                            <span>{label}</span>
                            {required ? <span className='rq-required'>{intl.formatMessage({ id: `common.mandatory.*` })}</span> : <></>}
                        </div>
                        <div className='rq-form-control'>{children}</div>
                    </>
            }
        </div>
    )
}

export default FormGroup
