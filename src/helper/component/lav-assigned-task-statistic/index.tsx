import { Card, Typography } from 'antd'
import moment from 'moment'
import intl from '../../../config/locale.config'
import { STATUS } from '../../../constants'

const { Text } = Typography

export interface StaticsProps {
    data?: any
    onFilterStatus?: any
    onFilterDate?: any
}
const StatisticRight = ({ data, onFilterStatus, onFilterDate }: StaticsProps) => {
    const handleFilterByStatus = (e, status) => {
        e.preventDefault();
        onFilterStatus(status);
    }

    const handleFilterByDate = (e, dateRanger) => {
        e.preventDefault();
        onFilterDate(dateRanger);
    }
    return (
        <div className="title-card">
            <Card title={intl.formatMessage({ id: 'assigned_task.title.due' })}>
                <div className='title-card-content'>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.due_date.late' })}&nbsp;</span>
                        {data?.byDueDate?.late > 0 ? <a href="#" onClick={(e) => handleFilterByDate(e, [null, moment().add(-1, 'days')])}><b>{data?.byDueDate?.late}</b></a> : <Text strong>{data?.byDueDate?.late}</Text>}
                    </p>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.due_date.today' })}&nbsp;</span>
                        {data?.byDueDate?.today > 0 ? <a href="#" onClick={(e) => handleFilterByDate(e, [moment(), moment()])}><b>{data?.byDueDate?.today}</b></a> : <Text strong>{data?.byDueDate?.today}</Text>}
                    </p>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.due_date.future' })}&nbsp;</span>
                        {data?.byDueDate?.future > 0 ? <a href="#" onClick={(e) => handleFilterByDate(e, [moment().add(1, 'days'), null])}><b>{data?.byDueDate?.future}</b></a> : <Text strong>{data?.byDueDate?.future}</Text>}
                    </p>
                </div>
            </Card>

            <Card title={intl.formatMessage({ id: 'assigned_task.title.status' })}>
                <div className='title-card-content'>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.status.draft' })}&nbsp;</span>
                        {data?.byStatus?.draft > 0 ? <a href="#" onClick={(e) => handleFilterByStatus(e, [STATUS.DRAFT])}><b>{data?.byStatus?.draft}</b></a> : <Text strong>{data?.byStatus?.draft}</Text>}
                    </p>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.status.submitted' })}&nbsp;</span>
                        {data?.byStatus?.submitted > 0 ? <a href="#" onClick={(e) => handleFilterByStatus(e, [STATUS.SUBMITTED])}><b>{data?.byStatus?.submitted}</b></a> : <Text strong>{data?.byStatus?.submitted}</Text>}
                    </p>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.status.rejected' })}&nbsp;</span>
                        {data?.byStatus?.rejected > 0 ? <a href="#" onClick={(e) => handleFilterByStatus(e, [STATUS.REJECT, STATUS.REJECT_CUSTOMER])}><b>{data?.byStatus?.rejected}</b></a> : <Text strong>{data?.byStatus?.rejected}</Text>}
                    </p>
                </div>
            </Card>

            <Card title={intl.formatMessage({ id: 'assigned_task.title.performance-index' })}>
                <div className='title-card-content'>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.performance_index.artefact' })}&nbsp;</span>
                        {data?.performanceIndex?.totalEndorsed > 0 ? <Text><b>{data?.performanceIndex?.totalEndorsed}</b></Text> : <Text strong>{data?.byPerformance?.totalEndorsed}</Text>}
                    </p>
                    <p>
                        <span>{intl.formatMessage({ id: 'assigned_task.performance_index.comment' })}&nbsp;</span>
                        <Text strong>{data?.byPerformance?.totalComment}</Text>
                    </p>
                </div>
            </Card>
        </div>
    )
}

export default StatisticRight
