import React, { createRef, FC, useEffect, useRef, useState } from 'react'
import AppState from '@/store/types'
import intl from '../../../config/locale.config'
import { useDispatch } from 'react-redux'
import { Table } from 'antd'
import { useSelector } from 'react-redux'
import { RqTableProps } from './type'

import './styles.less'

const RqTable: FC<RqTableProps> = (props) => {
  const { dataSource, columns, onChangeHandle, pagination, isLoading } = props
  const tablePagination = pagination ?? false

  return (
    <>
      <Table
        // locale={{
        //   emptyText: `${intl.formatMessage({ id: 'common.table.no-data' })}`,
        // }}
        className="custom-table"
        bordered
        dataSource={dataSource}
        columns={columns}
        rowKey="id"
        onChange={onChangeHandle}
        pagination={tablePagination}
        loading={isLoading}
        scroll={{x : 800}}
      />
    </>
  )
}

export default RqTable
