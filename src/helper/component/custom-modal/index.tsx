import React from 'react'
import { CloseCircleFilled } from '@ant-design/icons'
import { Modal, Spin } from 'antd'
import './styles.less'

const CustomModal = (props) => {
  const width = props.size === 'extra' ? '98rem' : props.size === 'large' ? '90rem' : props.size === 'medium' ? '70rem' : props.size === 'small' ? '35rem' : props.size === 'smalls' ? '45rem' : '55rem';

  return (
    <Modal
      closeIcon={
        <CloseCircleFilled style={{ color: 'white', fontSize: '1.5rem' }} />
      }
      {...props}
      wrapClassName="custom-modal-container"
      width={width}
    >
      <Spin spinning={props.isLoading || false}>
        {props.children}
      </Spin>
    </Modal>
  )
}
export default CustomModal
