import { EllipsisOutlined, UserOutlined } from '@ant-design/icons'
import { useAccount, useMsal } from '@azure/msal-react'
import { Badge, Button, Card, Dropdown, Form, Input, Menu, Modal, Row, Select, Space, Spin, Tooltip, Typography } from 'antd'
import moment from 'moment'
import { createRef, useEffect, useRef, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import intl from '../../../config/locale.config'
import { COMMENT_CATEGORY, COMMENT_STATUS, DATETIME_FORMAT, getArtefactTypeURLDetail, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, SCREEN_MODE } from '../../../constants'
import Ckeditor from '../../../helper/component/ckeditor'
import CustomSvgIcons from '../../../helper/component/custom-icons'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { extractProjectCode } from '../../../helper/share'
import { CommentModel } from '../../../modules/_shared/comment/type'
import AppCommonService from '../../../services/app.service'
const { Text } = Typography
const { Option } = Select
const { TextArea } = Input

interface SharedCommentProps {
    field?: string,
    comments: CommentModel[],
    onChange?: any
}
const CommentContent = ({ field, comments, onChange }: SharedCommentProps) => {
    const { accounts } = useMsal();
    const currentUser = useAccount(accounts[0] || {});
    const [isLoading, setIsLoading] = useState(false)
    const colors = {
        record_opened_color: '#515560',
        record_resolved_color: '#00C853',
        record_closed_color: '#515560',
        record_cancelled_color: '#faad14',
    }
    const commentListRef = useRef<any>(null);
    const modalConfirmConfig = useModalConfirmationConfig()

    // STATE
    const [commentItems, setCommentItems] = useState<any[]>([]);


    //#region EDITOR

    const commentRef: any = createRef()
    const replyRef: any = createRef()

    //#endregion EDITOR

    //#region LIFE CYCLE


    useEffect(() => {
        bindColor(comments)
    }, [field, comments])

    const bindColor = (cmts) => {
        cmts.forEach(o => {
            switch (o.status) {
                case COMMENT_STATUS.OPEN:
                    o.color = colors.record_opened_color;
                    break;
                case COMMENT_STATUS.RESOLVED:
                    o.color = colors.record_resolved_color;
                    break;
                case COMMENT_STATUS.CLOSED:
                    o.color = colors.record_closed_color;
                    break;
                case COMMENT_STATUS.CANCELLED:
                    o.color = colors.record_cancelled_color;
                    break;
            }
        });

        setCommentItems(cmts);
    }
    //#endregion LIFE CYCLE

    //#region METHODS

    const handleEditComment = (comment) => {
        const items = Object.assign([], commentItems)
        items.map((e: any) => {
            if (e.id === comment.id) {
                e.editting = true
                e.displayMenu = false
            }
        })
        setCommentItems(items)
    }

    const handleReplyComment = (id) => {
        const items = Object.assign([], commentItems)
        items.map((e: any) => {
            if (e.id === id) {
                e.repling = true
                e.displayMenu = false
            }
        })
        setCommentItems(items)
    }

    const handleEditReply = (co, re) => {
        const items = Object.assign([], commentItems)
        items.map((e: any) => {
            if (e.id === co?.id) {
                const index = e?.replies.findIndex((ele) => ele.id === re.id)
                e.replies[index] = {
                    ...e.replies[index],
                    edittingReply: true
                }
            }
        })
        setCommentItems(items)
    }

    const handleRemoveReply = (replyId, commentId) => {
        setIsLoading(true)
        const items: any = Object.assign([], commentItems)
        AppCommonService.deleteReplyComment(replyId).then((e) => {
            items.forEach((ele) => {
                if (commentId === ele.id) {
                    const index = ele.replies.findIndex((e) => e.id === replyId)
                    if (index !== -1) {
                        ele.replies.splice(index, 1)
                    }
                }
            })
            setCommentItems(items)
            setIsLoading(false)
        })
    }

    const handleCancel = (id) => {
        const items = Object.assign([], commentItems)
        items.map((e: any) => {
            if (e.id === id) {
                e.editting = false
            }
        })
        setCommentItems(items)
    }

    //#endregion METHOD

    //#region COMMENT HANDLER

    const _onRemoveComment = (commentId: number) => {
        Modal.confirm({
            ...modalConfirmConfig,
            title: 'Confirm',
            content: intl.formatMessage({ id: 'CDF_0' }),
            zIndex: 1020,
            okText: modalConfirmConfig.okText,
            okButtonProps: { danger: true },
            onOk() {
                setIsLoading(true)
                const items: any = Object.assign([], commentItems)
                AppCommonService.deleteComment(commentId).then((res) => {
                    const index = items.findIndex((e) => e.id === commentId)
                    if (index !== -1) {
                        items.splice(index, 1)
                    }
                    setCommentItems(items)
                    setIsLoading(false)
                })
            },
        })
    }
    const handleChangeCommentType = (event, id) => {
        commentItems.map((e) => {
            if (e.id === id) {
                e.category = event
            }
        })
        setCommentItems(commentItems)
    }

    const _onResolveComment = (comment: any) => {
        const payload = {
            commentId: comment?.id,
            rowVersion: comment?.rowVersion,
        }
        const items: any = Object.assign([], commentItems)
        setIsLoading(true)
        AppCommonService.resolveComment(payload).then((res) => {
            items.forEach((ele, index) => {
                if (res.data.id === ele.id) {
                    const newData = {
                        ...items[index],
                        canCancel: res.data.canCancel,
                        canClose: res.data.canClose,
                        canDelete: res.data.canDelete,
                        canEdit: res.data.canEdit,
                        canReopen: res.data.canReopen,
                        canReply: res.data.canReply,
                        canResolve: res.data.canResolve,
                        status: res.data.status,
                        rowVersion: res.data.rowVersion,
                        displayMenu: false
                    }
                    items[index] = newData
                }
            })
            bindColor(items)
            setIsLoading(false)
        })
    }

    const _onReopenComment = (comment: any) => {
        const payload = {
            commentId: comment?.id,
            rowVersion: comment?.rowVersion,
        }
        const items: any = Object.assign([], commentItems)
        setIsLoading(true)
        AppCommonService.reopenComment(payload).then((res) => {
            items.forEach((ele, index) => {
                if (res.data.id === ele.id) {
                    const newData = {
                        ...items[index],
                        canCancel: res.data.canCancel,
                        canClose: res.data.canClose,
                        canDelete: res.data.canDelete,
                        canEdit: res.data.canEdit,
                        canReopen: res.data.canReopen,
                        canReply: res.data.canReply,
                        canResolve: res.data.canResolve,
                        status: res.data.status,
                        rowVersion: res.data.rowVersion,
                        displayMenu: false
                    }
                    items[index] = newData
                }
            })
            bindColor(items)
            setIsLoading(false)
        })
    }

    const _onCloseComment = (comment: any) => {
        const payload = {
            commentId: comment?.id,
            rowVersion: comment?.rowVersion,
        }
        const items: any = Object.assign([], commentItems)
        setIsLoading(true)
        AppCommonService.closeComment(payload).then((res) => {
            items.forEach((ele, index) => {
                if (res.data.id === ele.id) {
                    items.splice(index, 1)
                    ele.displayMenu = false
                }
            })
            bindColor(items)
            setIsLoading(false)
        })
    }

    const _onCancelComment = (comment: any) => {
        const payload = {
            commentId: comment?.id,
            rowVersion: comment?.rowVersion,
        }
        const items: any = Object.assign([], commentItems)
        setIsLoading(true)
        AppCommonService.cancelComment(payload).then((res) => {
            items.forEach((ele, index) => {
                if (res.data.id === ele.id) {
                    if (res.data.id === ele.id) {
                        items.splice(index, 1)
                        ele.displayMenu = false
                    }
                }
            })
            bindColor(items)
            setIsLoading(false)
        })
    }

    const onCommentHandle = {
        onDelete: _onRemoveComment,
        onResolve: _onResolveComment,
        onReopen: _onReopenComment,
        onClose: _onCloseComment,
        onCancel: _onCancelComment,
    }
    //#endregion COMMENT HANDLER

    //#region VIEW

    const EditCommentForm = ({ co }) => {
        const [form] = Form.useForm();
        const [messages, setMessages] = useState(co?.commentBody)
        useEffect(() => {
            setMessages(co?.commentBody)
            form.setFieldsValue({
                messages: co?.commentBody,
                category: co?.category
            })
        }, [])

        const commentChange = (event) => {
            setMessages(event)
        }
        const handleSaveComment = (comment) => {
            const payload = {
                id: comment?.id,
                category: form.getFieldValue('category'),
                commentBody: messages,
                rowVersion: comment?.rowVersion
            }
            setIsLoading(true)
            const items: any = Object.assign([], commentItems)
            AppCommonService.updateComment(payload).then((res) => {
                items.forEach((ele, index) => {
                    if (res.data.id === ele.id) {
                        items[index] = res.data
                    }
                })
                bindColor(items)
                setIsLoading(false)
            })

        }
        return <Form name="basic" onFinish={(event) => handleSaveComment(co)} autoComplete="off" form={form} className='rq-add-comment'>
            <Form.Item name="rowVersion" hidden={true} />
            <Form.Item name="category" rules={[
                { required: true, message: 'Please input your category' },
                { validator: async (rule, value) => { if (value && value.trim().length === 0) { throw new Error(intl.formatMessage({ id: 'IEM_1' })) } } }
            ]}>
                <Select style={{ width: '200px', }} placeholder='Category' onChange={(e) => handleChangeCommentType(e, co.id)} size='small'>
                    <Option key={COMMENT_CATEGORY.BUG} value={COMMENT_CATEGORY.BUG}>{COMMENT_CATEGORY.BUG}</Option>
                    <Option key={COMMENT_CATEGORY.QA} value={COMMENT_CATEGORY.QA}>{COMMENT_CATEGORY.QA}</Option>
                    <Option key={COMMENT_CATEGORY.SUGGESTION} value={COMMENT_CATEGORY.SUGGESTION}>{COMMENT_CATEGORY.SUGGESTION}</Option>
                </Select>
            </Form.Item>
            <Form.Item name="message" rules={[{ required: true, message: 'Please input your comment' }]}>
                <Ckeditor ref={commentRef} data={messages} onChange={(e) => commentChange(e)} comment placeholder="Add comment..." />
            </Form.Item>
            <Space direction='horizontal' size='small' align='end'>
                <Button type='primary' htmlType='submit' size='small'>Save</Button>
                <Button type='ghost' onClick={() => handleCancel(co?.id)} size='small'>Cancel</Button>
            </Space>
        </Form>
    }

    const ViewCommentForm = ({ co }) => {
        return <div className='comment-detail'>
            <div className='comment-info'>
                <span className='category'>{co.category}</span>
                <Badge
                    className="comment-status"
                    color={co.color}
                    text={
                        <Text style={{ color: co.color }}>
                            {co.status}
                        </Text>
                    }
                />
            </div>
            <div className='comment-body'>
                <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: co?.commentBody }}></div>
                <div ref={commentListRef} />
            </div>
        </div>
    }

    const NewReplyForm = ({ re, id, mode }) => {
        const [form] = Form.useForm();
        const [messages, setMessages] = useState<any>('')
        useEffect(() => {
            if (mode === SCREEN_MODE.EDIT && re) {
                setMessages(re?.replyBody)
            } else {
                setMessages('')
            }
        }, [])

        const commentChange = (event) => {
            setMessages(event)
        }
        const handleCancelReply = (cmtId, replyId) => {
            if (replyId) {
                const items = Object.assign([], commentItems)
                items.map((e: any) => {
                    if (e.id === id) {
                        const index = e.replies.findIndex((ele) => ele.id === replyId)
                        e.replies[index].edittingReply = false
                    }
                })
                setCommentItems(items)
            } else {
                const items = Object.assign([], commentItems)
                items.map((e: any) => {
                    if (e.id === cmtId) {
                        e.repling = false
                    }
                })
                setCommentItems(items)
            }
        }
        const handleSubmitReply = () => {
            setIsLoading(true)
            const items: any = Object.assign([], commentItems)
            items.map((e: any) => {
                if (e.id === id) {
                    if (mode == SCREEN_MODE.CREATE) {
                        AppCommonService.replyComment({
                            replyBody: messages,
                            commentId: id,
                        }).then((res) => {
                            items.forEach((ele) => {
                                if (res.data.commentId === ele.id) {
                                    ele.replies.push(res.data)
                                }
                            })
                            setCommentItems(items)
                            setIsLoading(false)
                            handleCancelReply(id, null)
                        })
                    } else if (mode == SCREEN_MODE.EDIT) {
                        const payload = {
                            id: re?.id,
                            replyBody: messages,
                            rowVersion: re?.rowVersion,
                            commentId: id,
                        }

                        AppCommonService.updateReplyComment(payload).then((res) => {
                            items.forEach((ele) => {
                                if (res.data.commentId === ele.id) {
                                    const index = ele.replies?.findIndex((e) => e.id === res.data.id)
                                    if (index !== -1) {
                                        ele.replies[index] = res.data
                                        e.replies[index].edittingReply = false
                                    }
                                }
                            })
                            setCommentItems(items)
                            setIsLoading(false)
                        })
                    }
                }
            })
        }





        return <div style={{ paddingTop: 8, borderTop: '1px dashed #eee', padding: '5px 10px 8px', backgroundColor: '#f5f5f5', borderRadius: 6 }}>
            <div style={{ marginBottom: 5 }}>
                <UserOutlined /><span className='author'>{currentUser?.name}</span>
            </div>

            <Form name="basic" autoComplete="off" form={form}>
                <Form.Item name="id" hidden={true} />
                <Form.Item name="rowVersion" hidden={true} />
                <Form.Item name="message" rules={[{ required: true, message: 'Please input your comment' }]}>
                    <Ckeditor ref={replyRef} data={messages} onChange={(e) => commentChange(e)} comment placeholder="Add reply..." />
                    <TextArea style={{ display: 'none' }} rows={3} placeholder='Add reply...' size='small' />
                </Form.Item>
                <Space direction='horizontal' size='small'>
                    <Button type='primary' onClick={(e) => {
                        handleSubmitReply()
                    }} size='small'>{mode == SCREEN_MODE.CREATE ? 'Add' : 'Save'}</Button>
                    <Button type='ghost' onClick={(e) => {
                        handleCancelReply(id, re ? re.id : null)
                    }} size='small'>Cancel</Button>
                </Space>
            </Form>
        </div>
    }

    const EditMenu = ({ comment }) => {
        if (!comment) {
            return <></>;
        }

        return <Menu className='rq-comment-menu'>
            {comment.canEdit ? <Menu.Item key={'Edit' + comment.id} onClick={() => handleEditComment(comment)}>Edit</Menu.Item> : <></>}
            {comment.canReply ? <Menu.Item key={'Reply' + comment.id} onClick={() => handleReplyComment(comment.id)}>Reply</Menu.Item> : <></>}
            {comment.canResolve ? <Menu.Item key={'Resolve' + comment.id} onClick={() => onCommentHandle.onResolve(comment)}>Resolve</Menu.Item> : <></>}
            {comment.canReopen ? <Menu.Item key={'Re-open' + comment.id} onClick={() => onCommentHandle.onReopen(comment)}>Re-open</Menu.Item> : <></>}
            {comment.canClose ? <Menu.Item key={'Close' + comment.id} onClick={() => onCommentHandle.onClose(comment)}>Close</Menu.Item> : <></>}
            {comment.canCancel ? <Menu.Item key={'Cancel' + comment.id} onClick={() => onCommentHandle.onCancel(comment)}>Cancel</Menu.Item> : <></>}
            {comment.canDelete ? <Menu.Item key={'Delete' + comment.id} onClick={() => onCommentHandle.onDelete(comment?.id)}>Delete</Menu.Item> : <></>}
        </Menu>
    }

    //#endregion VIEW

    const handleVisibleMenu = (e, cmt) => {
        const items = Object.assign([], commentItems)
        items.map((ele: any) => {
            if (ele.id === cmt?.id) {
                ele.displayMenu = e
            }
        })
        setCommentItems(items)
    }

    const setSectionComment = (co) => {
        const commentSection = JSON.stringify(co)
        localStorage.setItem('comment', commentSection)
    }

    return <Space className='rq-comment-body' direction='vertical' size='small'>
        <Spin spinning={isLoading}>
            {
                commentItems.length > 0
                    ? <Scrollbars
                        autoHide
                        autoHeight
                        autoHeightMax={800}
                    >
                        <div className='rq-comment-items'>
                            {
                                commentItems.map((co) => {
                                    return <div className='card-comment-container' onClick={() => {
                                    }}>

                                        <Card title={''} className='list-comment-items' key={`${co?.field}_${co?.commentDate}`}>
                                            <div className="comment-items-title">
                                                <a className="comment-items-name" onClick={() => setSectionComment(co)} target="_blank" href={`/#${PROJECT_PREFIX}${extractProjectCode()}${getArtefactTypeURLDetail(co?.artefactType)}${co?.artefactType !== REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX ? co?.itemId : ''}`}>{co?.itemCode} - {co?.itemName}
                                                </a>
                                                <div className="comment-items-header">
                                                    <div className="comment-items-name"><span className='created'>{moment(co.commentDate).format(DATETIME_FORMAT)}</span></div>
                                                    <Dropdown visible={co?.displayMenu ? true : false} overlay={<EditMenu comment={co} />} onVisibleChange={(e) => {
                                                        handleVisibleMenu(e, co)
                                                    }} ><EllipsisOutlined style={{ marginLeft: 10 }} /></Dropdown>
                                                </div>
                                            </div>
                                            <div key={co.id} className={`rq-comment-item ${co.editting ? 'active' : ''}`}>
                                                <div className="comment-items-containter">
                                                    <div className="commemt-items-name">
                                                        <UserOutlined style={{ marginRight: 5 }} /><span className='author'>{co?.createdUserFullName}</span>
                                                    </div>
                                                </div>
                                                {co.editting ? <EditCommentForm co={co} /> : <ViewCommentForm co={co} />}
                                                {
                                                    co.replies.map(re => {
                                                        return re?.edittingReply ?
                                                            <div key={re.id} className='rq-add-reply-section'><NewReplyForm re={re} id={co?.id} mode={SCREEN_MODE.EDIT} /></div>
                                                            : <div key={re.id} className={`rq-reply-item ${re.canEdit ? 'can-edit' : ''} ${re.canDelete ? 'can-delete' : ''} ${re?.editting ? 'active' : ''}`}>
                                                                <Row style={{ marginBottom: 3 }} align='middle' justify='space-between'>
                                                                    <div className='reply-author'><UserOutlined /><span className='author'>{re.createdUserFullName}</span></div>
                                                                    <span className='reply-right'>
                                                                        <span className='created'>{moment(re.replyDate).format(DATETIME_FORMAT)}</span>
                                                                        {
                                                                            re.canEdit
                                                                                ? <Button htmlType='button' className='rq-edit-reply' type='link' size='small' onClick={() => {
                                                                                    handleEditReply(co, re)
                                                                                }}>
                                                                                    <CustomSvgIcons name="EditCustomIcon" />
                                                                                </Button>
                                                                                : <></>
                                                                        }
                                                                        {
                                                                            re.canDelete
                                                                                ? <Button htmlType='button' className='rq-remove-reply' type='link' size='small' onClick={() => {
                                                                                    handleRemoveReply(re.id, co.id)
                                                                                }}>
                                                                                    <CustomSvgIcons name="DeleteCustomIcon" />
                                                                                </Button>
                                                                                : <></>
                                                                        }
                                                                    </span>
                                                                </Row>
                                                                <div className='reply-body'>
                                                                    <div className="tableDangerous" dangerouslySetInnerHTML={{ __html: re?.replyBody }}></div>
                                                                </div>
                                                            </div>
                                                    })
                                                }
                                                {co?.repling ? <div className='rq-add-reply-section'><NewReplyForm re={null} id={co?.id} mode={SCREEN_MODE.CREATE} /></div> : <></>}
                                            </div>
                                        </Card>
                                    </div>
                                })
                            }
                        </div>
                    </Scrollbars>
                    : <></>
            }
        </Spin>
    </Space>
}
export default CommentContent
