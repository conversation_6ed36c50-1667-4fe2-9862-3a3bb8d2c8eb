import AppCommonService from '../../../services/app.service'
import { Button, Col, Modal, Row, Space, Table } from 'antd'
import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { API_URLS, APP_COMMON_ROLES, APP_ROLES, COM_ARTEFACT_TYPE_ID, COM_VALIDATE_ARTEFACT_TYPE, MESSAGE_TYPE, MESSAGE_TYPES, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID, STATUS, STATUS_COMMON, VALIDATE_ARTEFACT_TYPE } from '../../../constants'
import useModalConfirmationConfig from '../../../helper/hooks/useModalConfirmationConfig'
import { currentUserName, extractProjectCode, hasCommonRole, hasRole, ShowAppMessage } from '../../../helper/share'
import ButtonService from '../../../services/lav-buttons-service'
const { confirm } = Modal
interface Props {
    url: string
    reviewer?: string
    artefact_type?: string
    status: any
    changePage: any
    id?: number | string
    artefactType?: number
    // handleReload: any
    customer?: string
    children?: any
    isCommon?: boolean
    isCommonComponent?: boolean
    approved?: boolean
    isHasEndorse?: boolean
    isHasApprove?: boolean
    isHasRemove?: boolean
    isHasCancel?: boolean
    isHasReject?: boolean
    isHasApproveCR?: boolean
    deleteButton?: React.FC<any>
    rejectButton?: React.FC<any>
    approveButton?: React.FC<any>
    cancelButton?: React.FC<any>
}

const getUrlApi = (artefactType, id) => {
    let url = ''
    switch (artefactType) {
        case 1:
            url = `${API_URLS.ACTORS}/${id}`;
            break;
        case 2:
            url = `${API_URLS.BUSINESS_RULES}/${id}`;
            break;
        case 3:
            url = `${API_URLS.DATA_MIGRATIONS}/${id}`;
            break;
        case 4:
            url = `${API_URLS.EMAILS}/${id}`;
            break;
        case 5:
            url = `${API_URLS.MEETING_MINUTES}/${id}`;
            break;
        case 6:
            url = `${API_URLS.MESSAGES}/${id}`;
            break;
        case 7:
            url = `${API_URLS.NON_FUNCTIONS}/${id}`;
            break;
        case 8:
            url = `${API_URLS.OBJECT}/${id}`;
            break;
        case 9:
            url = `${API_URLS.OBJECT_RELATIONSHIP_DIAGRAM}/${id}`;
            break;
        case 10:
            url = `${API_URLS.OTHER_REQS}/${id}`;
            break;
        case 11:
            url = `${API_URLS.REFERENCE_DOCUMENT}/${id}`;
            break;
        case 12:
            url = `${API_URLS.SCREENS}/${id}`;
            break;
        case 13:
            url = `${API_URLS.STATE_TRANSITIONS}/${id}`;
            break;
        case 14:
            url = `${API_URLS.USE_CASE}/${id}`;
            break;
        case 15:
            url = `${API_URLS.USE_CASE_DIAGRAMS}/${id}`;
            break;
        case 16:
            url = `${API_URLS.USER_REQUIREMENTS}/${id}`;
            break;
        case 17:
            url = `${API_URLS.WORKFLOWS}/${id}`;
            break;
    }
    return url;
}


const LavButtons = ({ url, id, artefactType, reviewer, customer, cancelButton, artefact_type, status, changePage, children, deleteButton, rejectButton, approveButton, isCommon = false, isCommonComponent = false, approved = false, isHasEndorse = false, isHasApprove = false, isHasCancel = false, isHasReject = false, isHasRemove = false, isHasApproveCR = false }: Props) => {
    const currentUser = currentUserName()
    const modalConfirmConfig = useModalConfirmationConfig()
    const [data, setData] = useState<any>([])
    const [isApprove, setIsApprove] = useState<boolean>(false)

    const columns = [
        {
            title: intl.formatMessage({ id: 'actor.column.code' }),
            dataIndex: 'code',
            render: (text: string, record: any) => {
                const artefactType = record.artefactType
                let artefactLink;
                let artefactTypeList = isCommon ? COM_VALIDATE_ARTEFACT_TYPE : VALIDATE_ARTEFACT_TYPE
                artefactTypeList.forEach((arterfact) => {
                    if (arterfact.value == artefactType) {
                        artefactLink = arterfact.url
                    }
                })
                const href = isCommon ? `${artefactLink}${record.id}` : `${PROJECT_PREFIX}${extractProjectCode()}${artefactLink}${record.id}`
                return <Link to={href} target="_blank">{text}</Link>
            },
        },
        {
            title: intl.formatMessage({ id: 'actor.column.approve.name' }),
            dataIndex: 'name',
        },
        {
            title: intl.formatMessage({ id: 'actor.column.description' }),
            dataIndex: 'description',
            render: (text) => {
                return <Col className="description" span={24}>
                    {/* {text.startsWith("<p>" ? text.replace(/(<p[^>]+?>|<p>|<\/p>)/img, "") : text)} */}
                    <div
                        className="tableDangerous"
                        dangerouslySetInnerHTML={{ __html: text }}
                    ></div>
                </Col>
            }
        }
    ]

    const endorseHandle = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_6' },
                {
                    Artefact: `${intl.formatMessage({
                        id: artefact_type,
                    })}`,
                }
            )}`,
            onOk() {
                ButtonService
                    .endorse(url)
                    .then((res) => {
                        ShowAppMessage(null, MESSAGE_TYPES.ENDORSE, artefact_type);
                        changePage()
                    })
                    .catch((error) => {
                        ShowAppMessage(error, null, artefact_type);
                    })
            },
            onCancel() { },
        })
    }
    const rejectHandle = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_5' },
                {
                    Artefact: `${intl.formatMessage({
                        id: artefact_type,
                    })}`,
                }
            )}`,
            onOk() {
                ButtonService
                    .reject(url)
                    .then((res) => {
                        ShowAppMessage(null, MESSAGE_TYPES.REJECT, artefact_type);
                        changePage()

                    })
                    .catch((error) => {
                        ShowAppMessage(error, null, artefact_type);
                    })
            },
            onCancel() { },
        })
    }
    const rejectCustomerHandle = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_5' },
                {
                    Artefact: `${intl.formatMessage({
                        id: artefact_type,
                    })}`,
                }
            )}`,
            onOk() {
                ButtonService
                    .rejectCustomer(url)
                    .then((res) => {
                        ShowAppMessage(null, MESSAGE_TYPES.REJECT, artefact_type);
                        changePage()

                    })
                    .catch((error) => {
                        ShowAppMessage(error, null, artefact_type);
                    })
            },
            onCancel() { },
        })
    }
    const cancelHandle = (data = []) => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: data.filter((e : any) => e?.status !== STATUS.CANCELLED).length > 0 ? 'CFD_6_9' : 'CFD_6_4' },
                {
                    Artefact: `${intl.formatMessage({
                        id: artefact_type,
                    })}`,
                }
            )}`,
            onOk() {
                ButtonService
                    .cancel(url)
                    .then((res) => {
                        ShowAppMessage(null, MESSAGE_TYPES.CANCEL, artefact_type);
                        changePage()

                    })
                    .catch((error) => {
                        ShowAppMessage(error, null, artefact_type);
                    })
            },
            onCancel() { },
        })
    }

    const approveHandle = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_3' },
                {
                    Artefact: `${intl.formatMessage({
                        id: artefact_type,
                    })}`,
                }
            )}`,
            onOk() {
                ButtonService
                    .approve(url)
                    .then((res) => {
                        ShowAppMessage(null, MESSAGE_TYPES.APPROVE, artefact_type);
                        changePage()
                    })
                    .catch((error) => {
                        ShowAppMessage(error, null, artefact_type);
                    })
            },
            onCancel() { },
        })
    }

    const removeHandle = () => {
        confirm({
            ...modalConfirmConfig,
            content: `${intl.formatMessage(
                { id: 'CFD_6_7' },
                {
                    Artefact: `${intl.formatMessage({
                        id: artefact_type,
                    })}`,
                }
            )}`,
            onOk() {
                ButtonService
                    .remove(url)
                    .then((res) => {
                        ShowAppMessage(null, MESSAGE_TYPES.REMOVE, artefact_type);
                        changePage()
                    })
                    .catch((error) => {
                        ShowAppMessage(error, null, artefact_type);
                    })
            },
            onCancel() { },
        })
    }

    const handleBeforEndorse = () => {
        if (customer === "") {
            ShowAppMessage(MESSAGE_TYPE.ERROR, 'The Customer must not left blank')
        } else {
            const url = isCommon ? `${API_URLS.COMMON_REFERENCE_TO}?artefactType=${artefactType}&artefactId=${id}` : `${API_URLS.REFERENCE_TO}?artefactType=${artefactType}&artefactId=${id}`
            AppCommonService.getReferenceTo(url).then((res) => {
                if (res.data && res.data.length != 0) {
                    const filter = isCommon ? res.data?.filter((e) => e.status !== STATUS_COMMON.APPROVED && e.status !== null && e.artefactType !== COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE && e.artefactType !== COM_ARTEFACT_TYPE_ID.MESSAGE && e.artefactType !== COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE) : res.data?.filter((e) => e.status !== STATUS.APPROVE && e.status !== null && e.artefactType !== REQ_ARTEFACT_TYPE_ID.MESSAGE && e.artefactType !== REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE && e.artefactType !== REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT && e.artefactType !== REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT && e.artefactType !== REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE && e.artefactType !== REQ_ARTEFACT_TYPE_ID.EPIC && e.artefactType !== REQ_ARTEFACT_TYPE_ID.SPRINT)
                    if (filter.length != 0) {
                        setData(filter)
                        setIsApprove(false)
                    } else {
                        setData([])
                        endorseHandle()
                    }
                } else {
                    setData([])
                    endorseHandle()
                }
            })
        }
    }

    
    const handleBeforApprove = () => {
        if (isCommon && artefactType == COM_ARTEFACT_TYPE_ID.COMPONENT) {
            approveHandle()
        } else {
            const url = isCommon ? `${API_URLS.COMMON_REFERENCE_TO}?artefactType=${artefactType}&artefactId=${id}` : `${API_URLS.REFERENCE_TO}?artefactType=${artefactType}&artefactId=${id}`
            AppCommonService.getReferenceTo(url).then((res) => {
                if (res.data && res.data.length != 0) {
                    const filter = isCommon ? res.data?.filter((e) => e.status !== STATUS_COMMON.APPROVED && e.status !== null && e.artefactType !== COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE && e.artefactType !== COM_ARTEFACT_TYPE_ID.MESSAGE && e.artefactType !== COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE) : res.data?.filter((e) => e.status !== STATUS.APPROVE && e.status !== null && e.artefactType !== REQ_ARTEFACT_TYPE_ID.MESSAGE && e.artefactType !== REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE && e.artefactType !== REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT && e.artefactType !== REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT && e.artefactType !== REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE && e.artefactType !== REQ_ARTEFACT_TYPE_ID.EPIC && e.artefactType !== REQ_ARTEFACT_TYPE_ID.SPRINT)
                    if (filter.length != 0) {
                        setData(filter)
                        setIsApprove(true)
                    } else {
                        setData([])
                        approveHandle()
                    }
                } else {
                    setData([])
                    approveHandle()
                }
            })
        }
    }
    const handleApproveAfterCheck = () => {
        ButtonService
            .approve(url)
            .then((res) => {
                ShowAppMessage(null, MESSAGE_TYPES.APPROVE, artefact_type);
                changePage()
            })
            .catch((error) => {
                ShowAppMessage(error, null, artefact_type);
            })
    }

    const handleEndorseAfterCheck = () => {
        ButtonService
            .endorse(url)
            .then((res) => {
                ShowAppMessage(null, MESSAGE_TYPES.ENDORSE, artefact_type);
                changePage()
            })
            .catch((error) => {
                ShowAppMessage(error, null, artefact_type);
            })
    }

    const handleBeforCancel = () => {
        const url = `${API_URLS.REFERENCE_TO}?artefactType=${artefactType}&artefactId=${id}`
        const urlDetail = getUrlApi(artefactType, id)
        AppCommonService.getData(urlDetail).then((e) => {
            if(e?.status === STATUS.CANCELLED) {
                ShowAppMessage('EMSG_40', null, artefact_type);
            } else {
                AppCommonService.getReferenceTo(url).then((res) => {
                    cancelHandle(res.data)
                })
            }
        })
    }
    const reopenHandle = () => {
        ButtonService
            .reOpen(url)
            .then((res) => {
                ShowAppMessage(null, MESSAGE_TYPES.REOPEN, artefact_type);
                changePage()
            })
            .catch((error) => {
                ShowAppMessage(error, null, artefact_type);
            })
    }
    return (
        <Space size="small">
            {(data.length > 0) && (
                <Modal
                    title={`There are item(s) being linked but not ${!isApprove ? 'endorsed or' : ''} approved yet. Are you sure you want to ${isApprove ? 'approve' : 'endorse'} this ${intl.formatMessage({ id: artefact_type })}?`}
                    visible={data ? true : false}
                    className='rq-comment-wrapper'
                    footer={null}
                    onCancel={() => setData([])}
                    width='55rem'
                >
                    <Table
                        locale={{ emptyText: intl.formatMessage({ id: 'common.table.no-data' }) }}
                        columns={columns}
                        dataSource={data}
                        pagination={false}
                        bordered
                        style={{ marginBottom: '15px' }}
                    />
                    <Row align="middle" justify="center">
                        <Space size='middle'>
                            <Button type='ghost' onClick={() => setData([])}>
                                {intl.formatMessage({ id: 'common.action.cancel' })}
                            </Button>
                            <Button type='ghost' className='success-btn' onClick={() => {
                                setData([])
                                isApprove ? handleApproveAfterCheck() : handleEndorseAfterCheck()
                            }}>
                                {intl.formatMessage({ id: 'common.action.ok' })}
                            </Button>
                        </Space>

                    </Row>
                </Modal>
            )}
            {
                deleteButton ? React.createElement(deleteButton) : <></>
            }
            {
                isHasRemove && (status !== STATUS_COMMON.DELETED && status !== STATUS_COMMON.REMOVED) && isCommon && !isCommonComponent && (<Button onClick={() => removeHandle()} ghost className='btn-warning'>{intl.formatMessage({ id: `common.action.remove` })}</Button>)
            }
            {
                isHasCancel && (status != STATUS.DELETE && status != STATUS.CANCELLED && (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM))) ?
                    <Button className='cancel-btn' onClick={() => handleBeforCancel()}>
                        {intl.formatMessage({ id: `common.action.cancel` })}
                    </Button> : <></>
            }
            {
                (isHasCancel || artefact_type === "common.artefact.change-request") && status == STATUS.CANCELLED && (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA_LEAD)) ?
                    <Button className='cancel-btn' onClick={() => reopenHandle()}>
                        {intl.formatMessage({ id: `common.action.re-open` })}
                    </Button> : <></>
            }

            {
                cancelButton ? React.createElement(cancelButton) : <></>
            }
            {
                isHasReject && ((status == STATUS.SUBMITTED && currentUser.toLocaleLowerCase() === reviewer?.toLocaleLowerCase()) || (hasCommonRole(APP_COMMON_ROLES.REVIEWER) && status != STATUS_COMMON.DELETED && isCommon && status == STATUS_COMMON.SUBMITTED)) ?
                    <Button danger onClick={() => rejectHandle()}>
                        {intl.formatMessage({ id: `common.action.reject` })}
                    </Button> : <></>
            }
            {
                isHasReject && ((status === STATUS.ENDORSE && currentUserName().toLocaleLowerCase() === customer?.toLocaleLowerCase()) || (hasRole(APP_ROLES.CUSTOMER) && artefactType == REQ_ARTEFACT_TYPE_ID.USER_STORY && status == STATUS.ENDORSE)) ?
                    <Button danger onClick={() => rejectCustomerHandle()}>
                        {intl.formatMessage({ id: `common.action.reject` })}
                    </Button> : <></>
            }
            {
                rejectButton ? React.createElement(rejectButton) : <></>
            }
            {
                (
                    approved && isHasApproveCR || (hasRole(APP_ROLES.CUSTOMER) && artefactType == REQ_ARTEFACT_TYPE_ID.USER_STORY && status == STATUS.ENDORSE) ||
                    (isHasApprove && ((status == STATUS.ENDORSE && customer?.toLocaleLowerCase() === currentUserName()?.toLocaleLowerCase()) || (hasCommonRole(APP_COMMON_ROLES.REVIEWER) && status != STATUS_COMMON.DELETED && status == STATUS_COMMON.SUBMITTED && isCommon)))
                ) ?
                    <Button type='primary' className='success-btn' onClick={() => handleBeforApprove()} >
                        {intl.formatMessage({ id: `common.action.approve` })}
                    </Button> : <></>
            }
            {
                approveButton ? React.createElement(approveButton) : <></>
            }
            {
                isHasEndorse && (!isCommon && status == STATUS.SUBMITTED && currentUser.toLocaleLowerCase() === reviewer?.toLocaleLowerCase()) ?
                    <Button type='primary' ghost onClick={() => handleBeforEndorse()}>
                        {intl.formatMessage({ id: `common.action.endorse` })}
                    </Button> : <></>
            }
            {children}


        </Space>
    )
}


export default LavButtons