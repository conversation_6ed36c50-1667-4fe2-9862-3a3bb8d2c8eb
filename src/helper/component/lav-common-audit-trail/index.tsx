import { Collapse, Typography } from "antd";
import moment from "moment";
import intl from "../../../config/locale.config";
import { AUDIT_TRAIL, DATETIME_FORMAT } from "../../../constants";

const { Panel } = Collapse
const { Title } = Typography

interface LavAuditTrailProps {
    data: any
}
const LavCommonAuditTrail = ({ data }: LavAuditTrailProps) => {
    return <Collapse bordered={true} className="rq-audit-trail">
        <Panel
            className="description"
            header={<Title level={5}>{intl.formatMessage({ id: 'common.audit-trail' })}</Title>}
            key="1"
        >
            {
                data ? data.map((item, idx) => {
                    const label = AUDIT_TRAIL.find(e => e.key === item.action)?.label
                    return <div key={idx}>{`${label} By: ${item.actionBy}, ${label} Date: ${moment(item.actionDate).format(DATETIME_FORMAT)}.`}</div>
                }) : <></>
            }
        </Panel>
    </Collapse>
}
export default LavCommonAuditTrail;