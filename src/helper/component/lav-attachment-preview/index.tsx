import { DownloadOutlined } from '@ant-design/icons'
import { Image } from 'antd'
import React, { useEffect, useState } from 'react'
import { isImageType } from '../../share'
import AppCommonService from '../../../services/app.service'

interface LavAttachmentPreviewProps {
    attachment: any
    isCommon: boolean
}
const LavAttachmentPreview = ({ attachment, isCommon }: LavAttachmentPreviewProps) => {
    const [imagePreview, setImagePreview] = useState<any>(null)
    useEffect(() => {
        setImagePreview(null);
        if (attachment?.id) {
            if (isImageType(attachment?.fileType)) {
                AppCommonService.previewImage(attachment.id, isCommon).then((res) => {
                    setImagePreview(res.data);
                })
            }
        }
        if (attachment?.fileContent) {
            if (isImageType(attachment?.fileType)) {
                setImagePreview('data:image/jpeg;base64,' + attachment?.fileContent);
            }
        }
    }, [attachment])

    const handleDownload = (e) => {
        e.preventDefault();
        AppCommonService.downloadFile(attachment.id, isCommon).then((res) => {
            const url = window.URL.createObjectURL(new Blob([res.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', attachment.fileName);
            document.body.appendChild(link);
            link.click();
        })
    }

    return <>
        {
            attachment?.id || attachment?.fileContent ?
                <>
                    {
                        imagePreview ? <Image src={imagePreview} /> :
                            <a href='#' onClick={handleDownload}>
                                <span>{attachment?.fileName}</span>&nbsp;
                                <DownloadOutlined />
                            </a>
                    }
                </> : <></>
        }
    </>
}

export default LavAttachmentPreview
