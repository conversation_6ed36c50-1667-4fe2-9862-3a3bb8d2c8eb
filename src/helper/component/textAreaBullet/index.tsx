import {
  Form, Input
} from 'antd'
import { FormLabelAlign } from 'antd/lib/form/interface'
import React, { useEffect, useState } from 'react'

interface TextAreaBulletProps {
  label: string
  name: string
  labelAlign?: FormLabelAlign
  rules: any
  reload?: any
  reloadAfterBack?: any
  maxLength?: number
}

const { TextArea } = Input
const TextAreaBullet = ({
  label,
  name,
  labelAlign,
  rules,
  reload,
  reloadAfterBack,
  maxLength,
}: TextAreaBulletProps) => {
  const [previousLength, setPreviousLength] = useState(0)
  const handleInput = (event) => {
    const bullet = '\u2022'
    const newLength = event.target.value.length
    const characterCode = event.target.value.substr(-1).charCodeAt(0)

    if (newLength > previousLength) {
      if (characterCode === 10) {
        event.target.value = `${event.target.value}${bullet} `
      } else if (newLength === 1) {
        event.target.value = `${bullet} ${event.target.value}`
      }
    }

    setPreviousLength(newLength)
  }
  useEffect(() => {
    setPreviousLength(0)
  }, [reload, reloadAfterBack])

  return (
    <Form.Item label={label} name={name} labelAlign={labelAlign} rules={rules}>
      <TextArea
        className="breakWorldTextArea"
        onInput={handleInput}
        rows={3}
      />
    </Form.Item>
  )
}

export default TextAreaBullet
