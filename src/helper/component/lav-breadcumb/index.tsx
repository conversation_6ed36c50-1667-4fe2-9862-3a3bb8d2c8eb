import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Row, Typography } from 'antd'
import { extractProjectCode, getProjectName } from '../../../helper/share'
import { Link } from 'react-router-dom'
import { APP_ROUTES ,PROJECT_PREFIX} from '../../../constants'

const { Title } = Typography

interface LavPageHeaderProps {
  title?: string,
  showBreadcumb?: boolean,
  children?: any,
  link?: boolean
  text?:string
}

const LavPageHeader = ({ title, showBreadcumb = true, children,text,link }: LavPageHeaderProps) => {
  const projectCode = extractProjectCode();
  const projectName = getProjectName(projectCode);
  const dashboardUrl = `/PRJ/${projectCode}/dashboard`
  return (
    <div className='rq-page-heading'>
      <Row align="middle" justify="space-between">
        <div>
          {
            showBreadcumb ? <Breadcrumb className='rq-breadcrumb' separator=">">
              <Breadcrumb.Item>
                <Link className="breadcrumb-link-btn" to={dashboardUrl}>{projectCode} - {projectName}</Link>
              </Breadcrumb.Item>
            </Breadcrumb> : <></>
          }
        <div className='title-page-heading'>
        <Title level={3} className='rq-page-title'>{title}</Title>
          {link ? <Link  to={`${PROJECT_PREFIX}${extractProjectCode()}/review-task`}>
            <Title level={3}>{text}</Title>
          </Link> : <></>}
        </div>
        </div>
        {children}
      
      </Row>
    </div>
  )
}

export default LavPageHeader
