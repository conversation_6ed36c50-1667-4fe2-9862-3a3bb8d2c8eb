import {
  DeleteOutlined
} from '@ant-design/icons'
import {
  <PERSON>Complete,
  Card, Col, Form, Row
} from 'antd'
import React, { useEffect } from 'react'
import intl from '../../../config/locale.config'
import { PreConditionType } from '../../../modules/usecase/type'
import CkeditorMention from '../ckeditor-mention'
import FormGroup from '../form-group'

const PreCondition = React.forwardRef((props: any, ref: any) => {
  useEffect(() => {
    // let numberType = parseInt(props.data?.type)
    // let type: any

    // if(isNaN(numberType)) {
    //   debugger
    //   if(props.data?.type && props.data?.type.trim() === "") {
    //     type = undefined
    //   }else {
    //     type = props.data?.type ? props.data?.type : intl.formatMessage({ id: 'function.form.user-permission', })
    //   }
    // }else {
    //   numberType === 0 ? type = intl.formatMessage({ id: 'function.form.user-permission', }) : type = intl.formatMessage({ id: 'function.form.security' })
    // }

    props.form?.setFieldsValue({
    [`type-${props.data?.keyId}`]: props.data?.type,
    [`condition-${props.data?.keyId}`]: props.data?.description,
  })
  }, [props.data])

const options: any = [
  { type: PreConditionType.userPermission, value: 'User Permission' },
  { type: PreConditionType.security, value: 'Security' },
]

const setDataCkMention2 = (dataCK) => {
  props.setUpdateItems(() => {
    const newData = {
      ...props.data,
      description: dataCK,
    }
    const newAllItems = [
      ...props.updateItems
    ]
    newAllItems[props.index] = newData
    return newAllItems
  })
}

return (
  <Card style={{ width: '100%', marginTop: 15 }}>
    <Row>
      <Col span={22}>
        <FormGroup required inline label={intl.formatMessage({ id: 'function.form.pre-condition-type', })} labelSpan={4} controlSpan={20}>
          <Form.Item name={`type-${props.data?.keyId}`} rules={[
            { required: true, message: intl.formatMessage({ id: 'IEM_1' }) },
            { validator: async (rule, value) => { if (value && value.trim().length === 0) { throw new Error(intl.formatMessage({ id: 'IEM_1' })) } } }
          ]}>
            <AutoComplete
              maxLength={255}
              options={options}
              // defaultValue={props.data?.type}
              onSelect={(value, option) => {
                props.setUpdateItems(() => {
                  const newData = {
                    ...props.data,
                    type: option.value,
                    value: option.value
                  }
                  const newAllItems = [
                    ...props.updateItems
                  ]
                  newAllItems[props.index] = newData
                  return newAllItems
                })
              }}
              onChange={(value) => {
                props.setUpdateItems(() => {
                  const newData = {
                    ...props.data,
                    type: value,
                    value: value
                  }
                  const newAllItems = [
                    ...props.updateItems
                  ]
                  newAllItems[props.index] = newData
                  return newAllItems
                })
              }}
            />
          </Form.Item>
        </FormGroup>
      </Col>
      <Col span={2} push={1}>
        <DeleteOutlined onClick={() => props.remove(props.index)} />
      </Col>
    </Row>

    <Row>
      <Col span={24}>
        <Form.Item name={`condition-${props.data?.keyId}`} rules={[{
          validator: async (rule, value) => {
            if (!props.data?.description || props.businessRule?.description === "") {
              throw new Error(intl.formatMessage({ id: 'IEM_1' }))
            }
          }
        }]}>
          <CkeditorMention
            ref={ref}
            data={props.data.description || ''}
            saveDataPre={setDataCkMention2}
            isCommon={props.isCommon}
          />
        </Form.Item>
      </Col>
    </Row>
  </Card>
)
})

export default PreCondition
