import { brOptions } from '../../../modules/usecase/type'
import React, { useEffect, useState } from 'react'
import { AutoComplete } from 'antd'
import intl from '../../../config/locale.config'

const CustomAutoCompleteBR = (props) => {
    const [listSelect, setListSelect] = useState(brOptions)
    useEffect(() => {
        const list = brOptions.filter((e) => e?.value?.toLocaleLowerCase().includes(props?.defaultValue.toLocaleLowerCase()))
        setListSelect(list)
    }, [])
    return (
        <AutoComplete
            style={{ width: '100%' }}
            maxLength={255}
            options={listSelect}
            defaultValue={props?.defaultValue}
            onSelect={(value, option) => {
                props.handleChangeNameBr(props?.order, option?.value, option?.type)
            }}
            onSearch={(value) => {
                const list = brOptions.filter((e) => e?.value?.toLocaleLowerCase().includes(value.toLocaleLowerCase()))
                setListSelect(list)
            }}
            onChange={(value) => {
                props?.handleChangeNameBr(props?.order, value, null)
            }}
            placeholder={intl.formatMessage({ id: 'function.place-holder-brType' })}
        />
    )
}

export default CustomAutoCompleteBR