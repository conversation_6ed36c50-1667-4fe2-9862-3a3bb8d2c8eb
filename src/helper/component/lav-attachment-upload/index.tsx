import { DeleteOutlined, FileImageOutlined, LoadingOutlined } from '@ant-design/icons';
import { Image, Input, message, Modal, Row, Space, Upload } from 'antd';
import { useEffect, useState } from 'react';
import intl from '../../../config/locale.config';
import { API_URLS, IMAGE_MAX_SIZE } from '../../../constants';
import { extractProjectCode, isImageType, isPDFType } from '../../share';
import AppCommonService from '../../../services/app.service';
import FormGroup from '../form-group';
const { Dragger } = Upload;

const LavAttachmentUpload = ({ artefactType, name, attachment, onChange, supportPDF = false, isCommon, supportAllType = false }) => {
  const [loading, setLoading] = useState(false)
  const [attachmentFile, setAttachmentFile] = useState<any>(null)
  const accessToken: any = localStorage.getItem('accessToken')
  const projectCode = extractProjectCode();
  const baseUrl = process.env.REACT_APP_API_BACKEND || '';
  const uploadUrl = isCommon ? `${API_URLS.COMMON_ATTACHMENTS}?artefactType=${artefactType}` : `${baseUrl}projects/${projectCode}/Attachments?artefactType=${artefactType}`;
  const [filePaste, setFilePaste] = useState<any>(null)
  const [requestUploadFile, setRequestUploadFile] = useState<any>(null)
  const [fileName, setFileName] = useState<any>(null)

  const handlePaste = (e) => {
    let items = e.clipboardData.items;
    for (let item of items) {
      if (item.kind === 'file') {
        setLoading(true);
        var pasteFile = item.getAsFile();
        // setRequestUploadFile(pasteFile)
        // getBase64(pasteFile, (imageUrl) => {
        //   setFilePaste({
        //     ...pasteFile,
        //     imagePreview: imageUrl
        //   })
        //   setLoading(false)
        // })
        handleUpload(pasteFile);

      }
    }
  }

  const handleUpload = async (pasteImg) => {
    let formData = new FormData();
    formData.append('file', pasteImg);
    formData.append('fileName', fileName ? fileName : intl.formatMessage({ id: 'common.dialog.default-file-name' }));
    AppCommonService.uploadFile(isCommon ? `${API_URLS.COMMON_ATTACHMENTS}?artefactType=${artefactType}` : `${baseUrl}Attachments?artefactType=${artefactType}`, formData).then((res: any) => {
      onChange(res.data)
      getBase64(pasteImg, (imageUrl) => {
        setAttachmentFile({
          ...res.data,
          imagePreview: imageUrl
        })
        setLoading(false)
      })
    }).catch(err => {
      setLoading(false)
    });
  }

  useEffect(() => {
    window.addEventListener('paste', handlePaste)
    return () => {
      window.removeEventListener('paste', handlePaste);
      setAttachmentFile(null)
    }
  }, [])

  useEffect(() => {
    if (attachment && attachment?.id) {
      if (isImageType(attachment?.fileType)) {
        AppCommonService.previewImage(attachment.id, isCommon).then((res) => {
          setAttachmentFile({
            ...attachment,
            imagePreview: res.data
          })
        })
      } else {
        setAttachmentFile(attachment);
      }
    } else {
      setAttachmentFile(null);
    }
  }, [attachment])

  const getBase64 = (img, callback) => {
    const reader = new FileReader()
    reader.addEventListener('load', () => callback(reader.result))
    reader.readAsDataURL(img)
  }

  const beforeUpload = async (file) => {
    const isValidFileType = supportAllType || (supportPDF ? (isImageType(file.type) || isPDFType(file.type)) : isImageType(file.type))
    if (!isValidFileType) {
      message.error(`${intl.formatMessage({ id: 'EMSG_UPLOAD_FAILURE' })}`)
      return false;
    }

    const isValidFileSize = file.size < IMAGE_MAX_SIZE
    if (isValidFileType && !isValidFileSize) {
      message.error(`Uploading file must not exceed 5MB.`)
      return false;
    }
    return isValidFileType && isValidFileSize
  }

  const handleChange = (info) => {
    if (info.file.status === 'uploading') {
      setLoading(false)
      return
    }
    if (info.file.status === 'done') {
      onChange(info.file.response)
      if (isImageType(info.file.response.fileType)) {
        getBase64(info.file.originFileObj, (imageUrl) => {
          setAttachmentFile({
            ...info.file.response,
            imagePreview: imageUrl
          })
          setLoading(false)
        })
      } else {
        setAttachmentFile(info.file.response)
        setLoading(false)
      }
    }
  }

  const handleRemove = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setAttachmentFile(null);
    onChange(null)
  }

  return (
    <>
      {/* {(filePaste && requestUploadFile) ? (
        <Modal title={intl.formatMessage({ id: 'common.dialog.attach-file' })} visible={filePaste ? true : false} onOk={() => {
          handleUpload(requestUploadFile);
          setFilePaste(null)
          setRequestUploadFile(null)
        }} onCancel={() => setFilePaste(null)}>
          <Space direction='vertical' size={'middle'}>
            <FormGroup inline label={intl.formatMessage({ id: 'common.dialog.screen-shot' })} labelSpan={6} controlSpan={4}>
              <Input disabled={true} defaultValue={intl.formatMessage({ id: 'common.dialog.input-screen-shot' })} />
            </FormGroup>
            <FormGroup inline label={intl.formatMessage({ id: 'common.dialog.paste' })} labelSpan={6} controlSpan={4}>
              <Input disabled={true} defaultValue={intl.formatMessage({ id: 'common.dialog.input-paste' })} />
            </FormGroup>
            <div style={{ border: '1px solid black', margin: '10px 0' }}>
              <Image preview={false} src={filePaste?.imagePreview} alt="avatar" style={{ maxWidth: '70%', margin: 'auto' }} />
            </div>
            <FormGroup inline label={intl.formatMessage({ id: 'common.dialog.file-name' })} labelSpan={4} controlSpan={9}>
              <Input defaultValue={intl.formatMessage({ id: 'common.dialog.default-file-name' })} onChange={(e) => setFileName(e.target.value)}/>
            </FormGroup>
          </Space>

        </Modal>
      ) : <></>} */}
      <Dragger
        name={name}
        listType="picture-card"
        className="avatar-uploader"
        headers={{
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${accessToken}`,
          Authentication: `Bearer ${accessToken}`,
        }}
        showUploadList={false}
        action={uploadUrl}
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        <div style={{ paddingTop: '50px' }}>
          <p className="ant-upload-drag-icon">
            {loading ? <LoadingOutlined /> : <FileImageOutlined />}
          </p>
          <p className="ant-upload-text">
            {intl.formatMessage({ id: 'common.label.upload-label' })}&nbsp;
            <span style={{ cursor: 'pointer', color: '#2979FF', textDecoration: 'underline' }}>
              {intl.formatMessage({ id: 'common.label.upload-label-browse-file' })}.
            </span>
            &nbsp;{intl.formatMessage({id: 'common.label.ctrlv-label-file'})}
          </p>
          {
            attachmentFile?.id ? <>
              {
                isImageType(attachmentFile?.fileType) ? <>
                  <Image preview={false} src={attachmentFile?.imagePreview} alt="avatar" style={{ maxWidth: '100%' }} />
                </> : <>
                  <div style={{ paddingTop: 30 }}>
                    <span>{attachmentFile?.fileName}</span>&nbsp;&nbsp;
                    <a href='#' style={{ color: 'red' }} onClick={handleRemove}><DeleteOutlined /></a>
                  </div>
                </>
              }
            </> : <></>
          }
        </div>
      </Dragger>
    </>
  )
}

export default LavAttachmentUpload
