import React, { cloneElement, FC, useRef, useState } from 'react'
import { Button } from 'antd'
import { ActionType, EditableProTable } from '@ant-design/pro-table'
import { EditTableProps } from './type'
import { CloseOutlined, CheckOutlined } from '@ant-design/icons'
const EditTable: FC<EditTableProps> = (param: EditTableProps) => {
  const { dataSource, columns, onSave, formInstance, paginationConfig } = param
  const actionRef = useRef<ActionType>()
  const onSaveHandle = async (key, record, origin) => {
    onSave(record)
  }
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([])
  const editTable = (
    <EditableProTable
      actionRef={actionRef}
      columns={columns}
      rowKey="id"
      value={dataSource}
      request={async () => ({
        data: dataSource,
        success: true,
      })}
      pagination={{
        pageSize: paginationConfig.pageSize,
        total: paginationConfig.total,
        current: paginationConfig.index,
      }}
      recordCreatorProps={false}
      toolBarRender={() => {
        return [
          <Button
            type="primary"
            onClick={() => {
              actionRef.current?.addEditRecord?.({
                id: Date.now(),
              })
            }}
          >
            Add new row
          </Button>,
        ]
      }}
      editable={{
        type: 'multiple',
        editableKeys,
        // form: formInstance,
        actionRender: (record, config, dom) => {
          return [dom.save, dom.cancel]
        },
        // onCancel: async (key, record, origin) => {
     
        //     alert('ab');
        // },
        onSave: onSaveHandle,
        onChange: setEditableRowKeys,
      }}
    />
  )
  return editTable
}
export default EditTable
