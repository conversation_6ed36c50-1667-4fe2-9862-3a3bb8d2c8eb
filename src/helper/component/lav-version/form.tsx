import { Card, Col, Collapse, Form, Input, Row, Table, Typography } from "antd";
import { compareVersions } from 'compare-versions';
import moment from "moment";
import { useState } from "react";
import intl from "../../../config/locale.config";
import { DATE_FORMAT, SCREEN_MODE } from "../../../constants";
import FormGroup from "../form-group";

interface LavVersionProps {
    screenMode?: SCREEN_MODE,
    data: any,
    form?: any
}
const LavVersion = ({ screenMode = SCREEN_MODE.EDIT, data, form }: LavVersionProps) => {


    const columns = [
        {
            title: intl.formatMessage({ id: 'common.column.date' }),
            dataIndex: 'dateCreated',
            width: '12.5%',
            render: (text) => {
                return moment(new Date(text)).format(DATE_FORMAT)
            }
        },
        {
            title: intl.formatMessage({ id: 'common.column.version' }),
            dataIndex: 'version',
            width: '7.5%'
        },
        {
            title: intl.formatMessage({ id: 'common.column.updated-by' }),
            dataIndex: 'createdBy',
            width: '20%'
        },
        {
            title: intl.formatMessage({ id: 'common.column.change-description' }),
            dataIndex: 'description',
            className: 'white-pre',
            width: '60%'
        }
    ]
    const getMaxVersion = () => {
        const sortArray = [...data]
        if (sortArray.length > 1) {
            sortArray.sort((a, b) => compareVersions(b?.version, a?.version) === -1 ? -1 : 1)
        }
        return sortArray[0].version
    }
    const isValidateVersion = (value) => {
        const format = value.split('.')
        if (value.match('^[0-9][0-9.]*$') && format.length === 3) {
            return false
        } else {
            return true
        }
    }


    const Content = () => {
        const [disable, setDisable] = useState(true)
        return <Row>
            {
                screenMode === SCREEN_MODE.EDIT ? <>
                    <Col span={24} >
                        <FormGroup inline labelSpan={3} controlSpan={3} label={intl.formatMessage({ id: 'common.column.version' })}>
                            <Form.Item name="version" rules={[{
                                validator: async (rule, value) => {
                                    if (value !== "" && value !== undefined) {
                                        if (isValidateVersion(value)) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_4' }))
                                        }
                                        if (data.length > 0 && (compareVersions(value.substring(value.length - 1) === "." ? `${value}0` : value, getMaxVersion()) === -1)) {
                                            throw new Error(intl.formatMessage({ id: 'IEM_5' }))
                                        }
                                    }
                                }
                            }]}>
                                <Input onChange={(e) => {
                                    if (e.target.value === "") {
                                        form.resetFields(['changeDescription'])
                                        setDisable(true)
                                    } else {
                                        setDisable(false)
                                    }
                                }} maxLength={25} />
                            </Form.Item>
                        </FormGroup>
                    </Col>
                    <Col span={24} >
                        <FormGroup inline labelSpan={3} controlSpan={21} label={intl.formatMessage({ id: 'common.label.change-description' })}>
                            <Form.Item name="changeDescription">
                                <Input.TextArea disabled={disable} maxLength={255} />
                            </Form.Item>
                        </FormGroup>
                    </Col>
                </> : <></>
            }
            <Col span={24}>
                <Table
                    locale={{ emptyText: intl.formatMessage({ id: 'common.table.no-data' }) }}
                    columns={columns}
                    dataSource={data ? data : []}
                    pagination={false}
                    bordered
                    style={{ marginBottom: '15px' }}
                />
            </Col>
        </Row>
    }

    return screenMode === SCREEN_MODE.EDIT ? <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'common.title.version_card' })}>
        <Content />
    </Card> : <Collapse bordered={true} className="rq-audit-trail">
        <Collapse.Panel
            className="description"
            header={<Typography.Title level={5}>{intl.formatMessage({ id: 'common.title.version_card' })}</Typography.Title>}
            key="1"
        >
            <Content />
        </Collapse.Panel>
    </Collapse>
}

export default LavVersion
