import { Card, Col, Form, InputNumber, Row } from "antd"
import intl from "../../../config/locale.config"
import { SCREEN_MODE } from "../../../constants"
import TriggerComment from "../../../modules/_shared/comment/trigger-comment"
import FormGroup from "../form-group"

interface LavEffortEstimationFormProps {
    screenMode?: SCREEN_MODE,
    hasDevelopment?: boolean,
    hasImplementation?: boolean
}
const LavEffortEstimationForm = ({ screenMode = SCREEN_MODE.CREATE, hasDevelopment, hasImplementation }: LavEffortEstimationFormProps) => {
    return <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'label-effort-estimation-hour' })}>
        <Row>
            <Col span={8} >
                <FormGroup inline labelSpan={9} controlSpan={10} label={
                    <TriggerComment screenMode={screenMode} field="req-elicitation">
                        {intl.formatMessage({ id: 'label-req-elicitation' })}
                    </TriggerComment>}>
                    <Form.Item name="req">
                        <InputNumber min={0} maxLength={2} max={99} />
                    </Form.Item>
                </FormGroup>
            </Col>
            <Col span={8} >
                <div style={{ paddingLeft: 10 }}>
                    <FormGroup inline labelSpan={10} controlSpan={10} label={
                        <TriggerComment screenMode={screenMode} field="documentation">
                            {intl.formatMessage({ id: 'label-documentation' })}
                        </TriggerComment>}>
                        <Form.Item name="documentation">
                            <InputNumber min={0} maxLength={2} max={99} />
                        </Form.Item>
                    </FormGroup>
                </div>
            </Col>
            {
                hasDevelopment && (
                    <Col span={8} >
                        <div style={{ paddingLeft: 10 }}>
                            <FormGroup className="rq-fg-comment" inline labelSpan={7} controlSpan={10} label={
                                <TriggerComment screenMode={screenMode} field="development">
                                    {intl.formatMessage({ id: 'label-development' })}
                                </TriggerComment>
                            }>
                                <Form.Item name="development">
                                    <InputNumber min={0} maxLength={2} max={99} />
                                </Form.Item>
                            </FormGroup>
                        </div>
                    </Col>
                )
            }


            {
                hasImplementation && (
                    <Col span={8}>
                        <div style={{ paddingLeft: 10 }}>
                            <FormGroup inline labelSpan={7} controlSpan={10} label={
                                <TriggerComment screenMode={screenMode} field="implementation">
                                    {intl.formatMessage({ id: 'label-implementation' })}
                                </TriggerComment>}>
                                <Form.Item name="implementation">
                                    <InputNumber min={0} maxLength={2} max={99} />
                                </Form.Item>
                            </FormGroup>
                        </div>
                    </Col>
                )
            }
        </Row>
    </Card>
}

export default LavEffortEstimationForm
