import { Card, <PERSON>, Row, Typography } from "antd"
import intl from "../../../config/locale.config"
import TriggerComment from "../../../modules/_shared/comment/trigger-comment"

interface EffortEstimation {
    reqElicitation?: number
    documentation?: number
    development?: number
    implementation?: number
}
interface LavEffortEstimationProps {
    data: EffortEstimation
}

const LavEffortEstimation = ({ data }: LavEffortEstimationProps) => {
    const { Title, Text } = Typography

    return <Card title={<Title level={5}>{intl.formatMessage({ id: 'label-effort-estimation-hour', })}</Title>} bordered={true}>
        <Row gutter={[16, 4]}>
            <Col span={11}>
                <TriggerComment field="req-elicitation">
                    <Text type="secondary">{intl.formatMessage({ id: 'label-req-elicitation' })}:</Text>
                </TriggerComment>
            </Col>
            <Col span={13}>{data?.reqElicitation}</Col>
        </Row>

        <Row gutter={[16, 4]}>
            <Col span={11}>
                <TriggerComment field="documentation">
                    <Text type="secondary">{intl.formatMessage({ id: 'label-documentation' })}:</Text>
                </TriggerComment>
            </Col>
            <Col span={13}>{data?.documentation}</Col>
        </Row>

        {data?.development || data?.development === null ? (
            <Row gutter={[16, 4]}>
                <Col span={11}>
                    <TriggerComment field="development">
                        <Text type="secondary">{intl.formatMessage({ id: 'label-development' })}:</Text>
                    </TriggerComment>
                </Col>
                <Col span={13}>{data?.development}</Col>
            </Row>
        ) : <></>}

        {data?.implementation || data?.implementation === null ? (
            <Row gutter={[16, 4]}>
                <Col span={11}>
                    <TriggerComment field="implementation">
                        <Text type="secondary">{intl.formatMessage({ id: 'label-implementation' })}:</Text>
                    </TriggerComment>
                </Col>
                <Col span={13}>{data?.implementation}</Col>
            </Row>
        ) : <></>}
    </Card>
}


export default LavEffortEstimation