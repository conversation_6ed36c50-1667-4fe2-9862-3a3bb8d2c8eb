/* eslint-disable eqeqeq */
/* eslint-disable no-loop-func */
import AppState from '@/store/types'
import { Col, Row, Space, Spin, Table } from 'antd'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import intl from '../../../config/locale.config'
import {
  BUTTON_TYPE, DEFAULT_PAGE_SIZE, MESSAGE_TYPES, SEARCH_TYPE
} from '../../../constants'
import { extractProjectCode, ShowAppMessage } from '../../share'
import TableService from '../../../services/lav-table-service'
import DeleteButton from '../commonButton/DeleteButton'
import LavPageHeader from '../lav-breadcumb'
import { setPagination, setUrl } from './action'
import { PaginationHistoryState } from './type'

const { Column } = Table

interface LavHistoryTableProps {
  title: string
  apiUrl?: string
  columns: any[]
  rowKey?: string
  showDelete?: boolean
  showUpdate?: boolean
  showRestore?: boolean
  updateComponent?: React.FC<any>
  restoreComponent?: React.FC<any>
  createComponent?: React.FC<any>
  deleteComponent?: React.FC<any>
  artefact_type: string
  showBreadcumb?: boolean
  artefactType?: number
  isCommon?: boolean
  [props: string]: any
  showHeader?: boolean
  isReload?: boolean
  isHasAction?: boolean
  reloadSuccess?: any
  onDataChange?: any
  getData?: any
  isAssignTask?: boolean
  committee?: boolean
  hasComment?: boolean
}

const LavHistoryTable = ({
  showBreadcumb = true,
  title,
  artefact_type,
  apiUrl,
  columns,
  rowKey = 'rowId',
  createComponent,
  showUpdate = true,
  updateComponent,
  showRestore = true,
  restoreComponent,
  showDelete = true,
  deleteComponent,
  showHeader = true,
  isReload = false,
  isHasAction = true,
  reloadSuccess,
  isCommon = false,
  onDataChange,
  artefactType,
  isAssignTask = false,
  committee = false,
  hasComment = false,
  ...rest
}: LavHistoryTableProps) => {
  const [isHistoryLoading, setHistoryIsLoading] = useState(false)
  const [lavHistoryColumns, setLavHistoryColumns] = useState<any[]>([])
  const [currentHistoryFilters, setCurrentHistoryFilters] = useState<any>({})
  const [currentHistorySorter, setCurrentHistorySorter] = useState<any>([])
  const [loadedHistorySession, setHistoryLoadedSession] = useState(false)
  const [currentHistoryPageIndex, setCurrentHistoryPageIndex] = useState(1)
  const currentUserHistoryProjects = localStorage.getItem('currentUserProjects') || ''
  const currentUserHistoryProjectsParse: any = JSON.parse(currentUserHistoryProjects)
  const currentProj = currentUserHistoryProjectsParse?.projects?.filter((e) => e.projectCode === extractProjectCode())
  let defaultPaging: any
  if (currentProj) {
    defaultPaging = currentProj[0]?.defaultPaging
  }
  const [currentHistoryPageSize, setCurrentHistoryPageSize] = useState(defaultPaging ? defaultPaging : DEFAULT_PAGE_SIZE)
  const [dataHistorySource, setHistoryDataSource] = useState<any>([])
  const [totalHistoryItems, setHistoryTotalItems] = useState(0)
  const paginationHistoryState = useSelector<AppState | null>((s) => s?.PaginationHistory) as PaginationHistoryState;

  const dispatch = useDispatch()
  useEffect(() => {
    if (isReload) {
      loadData(currentHistoryPageIndex, currentHistoryPageSize, currentHistoryFilters, currentHistorySorter, null);
    }
  }, [isReload])

  useEffect(() => {
    if (paginationHistoryState?.artefactType === -1) {
    } else {
      localStorage.setItem('paginationHistoryState', JSON.stringify(paginationHistoryState))
      if (loadedHistorySession) {
        loadData(
          paginationHistoryState.paginationProps?.pagination?.current,
          paginationHistoryState.paginationProps?.pagination?.pageSize,
          paginationHistoryState.paginationProps?.filters,
          [paginationHistoryState.paginationProps?.sorter],
          null
        )
        setHistoryLoadedSession(false)
      }
    }
  }, [paginationHistoryState, loadedHistorySession])

  useEffect(() => {
    if (columns) {
      configTable(true)
    }
  }, [columns])

  const configTable = (isLoadData) => {
    let tempColumns = columns || []
    let defaultSort: any = []
    let defaultFilters: any = null
    const state = localStorage.getItem('paginationHistoryState') || ''
    let pState: any
    if (state != '') {
      pState = JSON.parse(state)
    }
    if (((paginationHistoryState.paginationProps?.pagination !== '' || pState?.paginationProps?.pagination !== '') && artefactType === pState?.artefactType && isCommon === pState?.isCommon) && !pState) {
      setLavHistoryColumns(tempColumns)
    } else {
      if (pState?.paginationProps?.sorter && pState?.artefactType === artefactType) {
          defaultSort.push({ field: pState?.paginationProps?.sorter.field, order: pState?.paginationProps?.sorter.order })
        setCurrentHistorySorter(defaultSort)
        defaultFilters = pState?.paginationProps?.filters
        setCurrentHistorySorter(defaultFilters)
        tempColumns.forEach((element, index) => {
          if (element.dataIndex === 'version') {
            element.className = 'rq-code-column'
          }
          // Default sorter
          if (pState?.paginationProps?.sorter.field === element.dataIndex) {
            element.sortOrder = pState?.paginationProps?.sorter.order
          } else {
            delete element.sortOrder
          }
          // Default filter
          if (pState?.paginationProps?.filters) {
            if (pState?.paginationProps?.filters[index]) {
              element.defaultFilteredValue = pState?.paginationProps?.filters[index]
            }
            if (element.dataIndex === 'isCovered' && String(pState?.paginationProps?.filters[index]) === 'false') {
              element.defaultFilteredValue = false
            }
          }
        })

      } else {
        tempColumns.forEach((element, index) => {
          if (element.dataIndex === 'version') {
            element.className = 'rq-code-column'
          }
          // Default sorter
          if (element.sortOrder) {
            defaultSort.push({ field: element.dataIndex, order: element.sortOrder })
            setCurrentHistorySorter(defaultSort)
          }
          // Default filter
          if (element.defaultFilteredValue && element.defaultFilteredValue.length > 0) {
            let obj: any = {};
            obj[index] = element.defaultFilteredValue
            defaultFilters = obj;
            setCurrentHistoryFilters(obj);
          }
        })
      }
      let paging = {
        extra: '',
        filters: defaultFilters,
        pagination: {
          current: currentHistoryPageIndex,
          pageSize: currentHistoryPageSize,
        },
        sorter: defaultSort[0]
      }
      setHistoryLoadedSession(true)
      dispatch(setPagination({ artefactType, isCommon, paging }))
      setLavHistoryColumns(tempColumns)
    }
  }

  const loadData = (pageIndex, pageSize, filters, sorter, extra) => {
    let paramFilter = ''
    let paramSorter = ''
    if (sorter && sorter.length) {
      sorter.forEach(element => {
        if (element?.field) {
          let sortDir =
            element.order == 'ascend'
              ? 'asc'
              : element.order == 'descend'
                ? 'desc'
                : ''
          paramSorter += `&SortField=${element.field}&SortDir=${sortDir}`
        }
      });
    }
    var keys = filters ? Object.keys(filters) : null
    if (keys && keys.length > 0) {
      for (var i = 0; i < keys.length; i++) {
        const filterVal = filters[keys[i]]
        // Filter Status
        const columnKey = lavHistoryColumns[keys[i]]?.dataIndex || columns[keys[i]]?.dataIndex
        const columnFilterType = lavHistoryColumns[keys[i]]?.filterType || columns[keys[i]]?.filterType
        const columnFilterKey = lavHistoryColumns[keys[i]]?.filterKey || columns[keys[i]]?.filterKey
        if (columnFilterType === SEARCH_TYPE.MULTIPLE_CHOICES && filterVal) {
          filterVal.forEach((element) => {
            paramFilter += `&${columnFilterKey ? columnFilterKey : columnKey}=${element}`
          })
        } else {
          if (filterVal && filterVal[0] || i === 5 && filterVal && String(filterVal[0] === 'false')) {
            const currentFilterValue = filterVal[0]
            // Date
            if (Array.isArray(currentFilterValue)) {
              const fromDate = currentFilterValue[0] ? moment(currentFilterValue[0]).format("YYYY-MM-DD") : null
              const toDate = currentFilterValue[1] ? moment(currentFilterValue[1]).format("YYYY-MM-DD") : null

              if (fromDate) {
                paramFilter += `&FromDate=${fromDate}`
              }
              if (toDate) {
                paramFilter += `&ToDate=${toDate}`
              }
            } else {
              // String
              paramFilter += `&${columnKey}=${currentFilterValue}`
            }
          }
        }
      }
    }
    setHistoryIsLoading(true)
    dispatch(setUrl({ apiUrl, pageIndex, pageSize, paramFilter, paramSorter }))
    TableService
      .getData(apiUrl, pageIndex, pageSize, paramFilter, paramSorter)
      .then((res) => {        
        let response = res.data.data ? res.data.data : res.data
        setHistoryDataSource(response.map((e, index) => {
          return { ...e, rowId: index }
        }))
        setHistoryTotalItems(res.data.total)
        setCurrentHistoryPageIndex(pageIndex)
        setCurrentHistoryPageSize(pageSize)
        setCurrentHistoryFilters(filters)
        setCurrentHistorySorter(sorter)
        setHistoryIsLoading(false)
        if (onDataChange) {
          onDataChange(res.data)
        }
        /* if (isReload) {
          reloadSuccess()
        } */
      })
      .catch((e) => {
        setHistoryDataSource([])
        setHistoryTotalItems(0)
        setCurrentHistoryPageIndex(1)
        setCurrentHistoryPageSize(DEFAULT_PAGE_SIZE)
        setCurrentHistoryFilters(null)
        setCurrentHistorySorter(null)
        setHistoryIsLoading(false)
        if (onDataChange) {
          onDataChange(null)
        }
        /* if (isReload) {
          reloadSuccess()
        } */
      })
      
    localStorage.setItem('currentHistoryPageIndex', pageIndex)
  }
  const handleDelete = (id) => {
    TableService
      .deleteItem(apiUrl, id)
      .then((res) => {
        handleDataChange()
        ShowAppMessage(null, MESSAGE_TYPES.DELETE, artefact_type);
      })
      .catch((err) => {
        ShowAppMessage(err, null, artefact_type);
      })
  }

  const handleDataChange = () => {
    loadData(
      currentHistoryPageIndex,
      currentHistoryPageSize,
      currentHistoryFilters,
      currentHistorySorter,
      null
    )
  }

  const setEditting = () => {

  }

  const handleTableChange = (pagination, filters, sorter, extra) => {
    // Update Columns after sort
    let columnsUpdated = Object.assign(lavHistoryColumns)
    if (extra && extra.action == 'sort') {
      columnsUpdated.forEach((element, idx) => {
        if (element.dataIndex === sorter.field) {
          element.sortOrder = sorter.order
        } else {
          if (!element.sorter?.multiple) {
            element.sortOrder = null
          }
        }
      })
      setLavHistoryColumns(columnsUpdated)
    } else {
      if (extra && extra.action == 'filter') {
        columnsUpdated.forEach((element, idx) => {
          if (!filters || !filters[idx]) {
            element.defaultFilteredValue = null;
          }
          if (filters && filters[idx]) {
            element.defaultFilteredValue = filters[idx]
          }
          if (filters[idx] && Object.keys(filters[idx]).length > 0 && element.title == "Due Date") {
            element.defaultFilteredValue = [[filters[idx][0][0], filters[idx][0][1]]]
          }
        })
        setLavHistoryColumns(columnsUpdated)
      }
    }
    let defaultSort: any = []
    const paging = { pagination, filters, sorter, extra }
    dispatch(setPagination({ artefactType, paging }))       
    localStorage.setItem('currentHistoryPageIndex', pagination.current)
    loadData(pagination.current, pagination.pageSize, filters, defaultSort.length != 0 ? defaultSort : [sorter], extra)
  }

  return (
    <Spin spinning={isHistoryLoading}>
      {showHeader ? (
        <LavPageHeader
          showBreadcumb={showBreadcumb}
          title={intl.formatMessage({ id: title })}
        >
          <Space size="small">
            {createComponent ? (
              React.createElement(createComponent, {
                handleDataChange: handleDataChange,
              })
            ) : (
              <></>
            )}
          </Space>
        </LavPageHeader>
      ) : (
        <></>
      )}
      <Row gutter={[10, 10]} style={{marginLeft: 0, marginRight: 0}}>
        <Col span={24}>
          {
            !isHistoryLoading && lavHistoryColumns ? <Table
              locale={{
                emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                filterReset: 'Reset',
                filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                filterConfirm: 'Apply Filter',
                selectAll: 'All',
                selectionAll: 'Select All',
              }}
              className="lav-table"
              bordered
              dataSource={dataHistorySource}
              // columns={lavColumns}
              rowKey={rowKey}
              onChange={handleTableChange}
              pagination={{
                current: currentHistoryPageIndex,
                pageSize: currentHistoryPageSize,
                total: totalHistoryItems,
                size: 'small',
                showLessItems: true,
                showSizeChanger: true,
                position: ['topRight'],
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} ${intl.formatMessage({
                    id: 'common.table.pagination.of',
                  })} ${total} ${intl.formatMessage({
                    id: 'common.table.pagination.items',
                  })}`,
              }}
              loading={isHistoryLoading}
              scroll={{ x: 650 }}
              {...rest}
            >
              {lavHistoryColumns?.map((col, idx) => {
                return (
                  <Column
                    title={col.title}
                    width={col.width}
                    dataIndex={col.dataIndex}
                    key={idx}
                    sorter={col.sorter}
                    sortOrder={col.sortOrder ? col.sortOrder : null}
                    filtered={col.filtered}
                    filters={col.filters ? col.filters : undefined}
                    filterDropdown={col.filterDropdown ? col.filterDropdown : null}
                    filteredValue={col.filteredValue ? col.filteredValue : col.defaultFilteredValue}
                    defaultFilteredValue={
                      col.defaultFilteredValue ? col.defaultFilteredValue : null
                    }
                    onFilter={col.onFilter}
                    filterIcon={col.filterIcon ? col.filterIcon : null}
                    render={col.render ? col.render : (text) => text}
                  />
                )
              })}
              {(((showUpdate && updateComponent) || (showDelete && deleteComponent) || (showRestore && restoreComponent)) && isHasAction) ? (
                <Column
                  title={intl.formatMessage({ id: 'common.table.column.action' })}
                  dataIndex={rowKey}
                  className="rq-action"
                  width="80px"
                  render={(text, record: any, order) => (
                    <>
                      {showUpdate && updateComponent ? (
                        React.createElement(updateComponent, {
                          order: order,
                          record: record,
                          handleDataChange: handleDataChange,
                          setEditting: setEditting,
                        })
                      ) : (
                        <></>
                      )}
                      {showDelete && deleteComponent ? (
                        React.createElement(deleteComponent, {
                          record: record,
                          children: (
                            <DeleteButton
                              type={BUTTON_TYPE.ICON}
                              content={intl.formatMessage(
                                { id: 'CFD_7' },
                                { artefact_type: intl.formatMessage({ id: artefact_type }) }
                              )}
                              okCB={() => handleDelete(record?.id)}
                              confirmButton={intl.formatMessage({ id: 'common.action.ok' })}
                            />
                          ),
                        })
                      ) : (
                        <></>
                      )}
                      {showRestore && restoreComponent ? (
                        React.createElement(restoreComponent, {
                          order: order,
                          record: record,
                          handleDataChange: handleDataChange,
                        })
                      ) : (
                        <></>
                      )}
                    </>
                  )}
                />
              ) : (
                <></>
              )}
            </Table> : <></>
          }
        </Col>
      </Row>
    </Spin>
  )
}

export default LavHistoryTable
