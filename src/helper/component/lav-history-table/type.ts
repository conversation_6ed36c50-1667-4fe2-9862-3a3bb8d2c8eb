interface Sorter {
    column: any
    columnKey: string
    field: string
    order: string
}
interface PaginationHistory {
    pagination: any
    filters: any[]
    sorter: Sorter
    extra: any
}
export interface PaginationHistoryState {
    isCommon: boolean
    artefactType: number
    paginationProps?: PaginationHistory
    paginationUrl?: any
}
export const defaultState: PaginationHistoryState = {
    isCommon: false,
    artefactType: -1,
    paginationProps: {
        extra: '',
        filters: [],
        pagination: '',
        sorter: {
            column: '',
            columnKey: '',
            field: '',
            order: '',
        }
    },
    paginationUrl: {
        apiUrl: '',
        paramSorter: '',
        pageIndex: 1,
        pageSize: 20,
        paramFilter: ''
    }
}
export enum ActionEnum {
    RESET_STATE = '@@MODULES/LAV_HISTORY_TABLE/RESET_STATE',
    SET_PAGINATION = '@@MODULES/LAV_HISTORY_TABLE/SET_PAGINATION',
    SET_URL = '@@MODULES/LAV_HISTORY_TABLE/SET_URL',
    SET_COMMON = '@@MODULES/LAV_HISTORY_TABLE/SET_COMMON',
}