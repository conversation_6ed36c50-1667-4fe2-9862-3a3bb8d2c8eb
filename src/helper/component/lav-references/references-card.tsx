import { Card, Collapse, Row, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import intl from '../../../config/locale.config'
import { APP_ROUTES, COM_ARTEFACT_TYPE_ID, PROJECT_PREFIX, REQ_ARTEFACT_TYPE_ID } from '../../../constants'
import { arrayHaveData, extractProjectCode } from '../../share'
import LavReferenceItem from './reference-item'

const { Title } = Typography
const { Panel } = Collapse

interface LavReferencesCardProps {
    data: any
    isCommon?: boolean
    title: string
}
const LavReferencesCard = ({ data, isCommon = false, title }: LavReferencesCardProps) => {
    const [showCard, setShowCard] = useState(false);
    const [refData, setRefData] = useState<any>(null);
    const projectCode = extractProjectCode();
    const prefixUrl = `${PROJECT_PREFIX}${projectCode}`;


    useEffect(() => {
        setShowCard(false);
        setRefData(null);
        if (data) {
            let usecases = [];
            let mockupScreens = [];
            let businessRules = [];
            let emailTemplates = [];
            let nonFunctionalRequirements = [];
            let objects = [];
            let messages = [];
            let actors = [];
            let usecaseDiagrams = [];
            let meetingMinutes = [];
            let userRequirements = [];
            let stateTransitions = [];
            let objectRelationshipDiagrams = [];
            let otherRequirements = [];
            let dataMigrations = [];
            let workflows = [];
            let referenceDocuments = [];
            let permissionMatrices = [];
            let userStories = [];
            let epics = [];
            let sprints = [];


            if (isCommon) {
                usecases = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.USECASE) || [];
                mockupScreens = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.SCREEN) || [];
                businessRules = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE) || []
                emailTemplates = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE) || [];
                nonFunctionalRequirements = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT) || []
                objects = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.OBJECT) || []
                messages = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.MESSAGE) || []
                workflows = data.filter(e => e.artefactType === COM_ARTEFACT_TYPE_ID.WORKFLOW) || []
            } else {
                usecases = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.USECASE) || [];
                mockupScreens = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.SCREEN) || [];
                businessRules = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE) || []
                emailTemplates = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE) || [];
                nonFunctionalRequirements = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT) || []
                objects = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.OBJECT) || []
                messages = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.MESSAGE) || []
                actors = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.ACTOR) || []
                usecaseDiagrams = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM) || [];
                meetingMinutes = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE) || [];
                userRequirements = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT) || [];
                stateTransitions = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION) || [];
                objectRelationshipDiagrams = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM) || [];
                otherRequirements = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT) || [];
                dataMigrations = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION) || [];
                workflows = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.WORKFLOW) || [];
                referenceDocuments = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT) || [];
                permissionMatrices = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX) || [];
                userStories = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.USER_STORY) || [];
                epics = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.EPIC) || [];
                sprints = data.filter(e => e.artefactType === REQ_ARTEFACT_TYPE_ID.SPRINT) || [];
            }

            setRefData({
                actors,
                usecases,
                usecaseDiagrams,
                mockupScreens,
                businessRules,
                emailTemplates,
                nonFunctionalRequirements,
                meetingMinutes,
                userRequirements,
                stateTransitions,
                objects,
                objectRelationshipDiagrams,
                otherRequirements,
                dataMigrations,
                messages,
                workflows,
                referenceDocuments,
                permissionMatrices,
                userStories,
                epics,
                sprints
            })
            isCommon ? setShowCard(
                arrayHaveData(usecases) ||
                arrayHaveData(objects) ||
                arrayHaveData(mockupScreens) ||
                arrayHaveData(businessRules) ||
                arrayHaveData(messages) ||
                arrayHaveData(emailTemplates) ||
                arrayHaveData(nonFunctionalRequirements) ||
                arrayHaveData(workflows) 
            ) : setShowCard(
                arrayHaveData(actors) ||
                arrayHaveData(meetingMinutes) ||
                arrayHaveData(userRequirements) ||
                arrayHaveData(stateTransitions) ||
                arrayHaveData(usecases) ||
                arrayHaveData(usecaseDiagrams) ||
                arrayHaveData(objects) ||
                arrayHaveData(objectRelationshipDiagrams) ||
                arrayHaveData(businessRules) ||
                arrayHaveData(mockupScreens) ||
                arrayHaveData(nonFunctionalRequirements) ||
                arrayHaveData(otherRequirements) ||
                arrayHaveData(dataMigrations) ||
                arrayHaveData(messages) ||
                arrayHaveData(emailTemplates) ||
                arrayHaveData(workflows) ||
                arrayHaveData(referenceDocuments) ||
                arrayHaveData(permissionMatrices) ||
                arrayHaveData(userStories) ||
                arrayHaveData(epics) ||
                arrayHaveData(sprints)
            );
        }
    }, [data])

    return showCard ?
        <Collapse bordered={true} className="rq-audit-trail">
            <Panel header={<Title level={5}>{intl.formatMessage({ id: title })}</Title>} key="1">
                <Row>
                    {isCommon ? <>
                        <LavReferenceItem label='common.artefact.common-common-workflow' url={APP_ROUTES.COMMON_WORKFLOW_DETAIL} data={refData?.workflows} />
                        <LavReferenceItem label='common.artefact.common-common-usecase' url={APP_ROUTES.COMMON_USECASE_DETAIL} data={refData?.usecases} />
                        <LavReferenceItem label='common.artefact.common-common-object' url={APP_ROUTES.COMMON_OBJECT_DETAIL} data={refData?.objects} />
                        <LavReferenceItem label='common.artefact.common-common-screen' url={APP_ROUTES.COMMON_SCREEN_DETAIL} data={refData?.mockupScreens} />
                        <LavReferenceItem label='common.artefact.business-rule' url={APP_ROUTES.COMMON_CBR_DETAIL} data={refData?.businessRules} />
                        <LavReferenceItem label='common.artefact.message' url={APP_ROUTES.COMMON_MESSAGE_DETAIL} data={refData?.messages} displayKey='code' />
                        <LavReferenceItem label='common.artefact.common-email' url={APP_ROUTES.COMMON_EMAIL_DETAIL} data={refData?.emailTemplates} displayKey='code' />
                        <LavReferenceItem label='common.artefact.common-non-functional' url={APP_ROUTES.COMMON_NONFUNCTIONAL_DETAIL} data={refData?.nonFunctionalRequirements} displayKey='code' />
                    </> : <>
                        <LavReferenceItem label='common.artefact.actor-short' url={prefixUrl + APP_ROUTES.ACTOR_DETAIL} data={refData?.actors} />
                        <LavReferenceItem label='common.artefact.meeting-minustes' url={prefixUrl + APP_ROUTES.MEETING_DETAIL} data={refData?.meetingMinutes} displayKey='code' />
                        <LavReferenceItem label='common.artefact.user-requirements' url={prefixUrl + APP_ROUTES.USER_REQUIREMENT_DETAIL} data={refData?.userRequirements} />
                        <LavReferenceItem label='common.artefact.state-transition' url={prefixUrl + APP_ROUTES.STATE_TRANSITION_DETAIL} data={refData?.stateTransitions} />
                        {/* <LavReferenceItem label='common.artefact.permission' url={prefixUrl + APP_ROUTES.PERMISSION_MATRIX} data={refData?.permissionMatrix} />*/}
                        <LavReferenceItem label='common.artefact.use-case-specifications' url={prefixUrl + APP_ROUTES.USECASE_DETAIL} data={refData?.usecases} />
                        <LavReferenceItem label='common.artefact.usecase-diagram' url={prefixUrl + APP_ROUTES.USECASE_DIAGRAM_DETAIL} data={refData?.usecaseDiagrams} />
                        <LavReferenceItem label='common.artefact.objects' url={prefixUrl + APP_ROUTES.OBJECT_DETAIL} data={refData?.objects} />
                        <LavReferenceItem label='common.artefact.object-relationship' url={prefixUrl + APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL} data={refData?.objectRelationshipDiagrams} />
                        <LavReferenceItem label='common.artefact.business-rule' url={prefixUrl + APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL} data={refData?.businessRules} />
                        <LavReferenceItem label='common.artefact.screen' url={prefixUrl + APP_ROUTES.SCREEN_DETAIL} data={refData?.mockupScreens} />
                        <LavReferenceItem label='common.artefact.non-functional' url={prefixUrl + APP_ROUTES.NONFUNTIONAL_REQ_DETAIL} data={refData?.nonFunctionalRequirements} displayKey='code' />
                        <LavReferenceItem label='common.artefact.other-requirements' url={prefixUrl + APP_ROUTES.OTHER_REQUIREMENT_DETAIL} data={refData?.otherRequirements} />
                        <LavReferenceItem label='common.artefact.data-migration' url={prefixUrl + APP_ROUTES.DATA_MIGRATION_DETAIL} data={refData?.dataMigrations} />
                        <LavReferenceItem label='common.artefact.message' url={prefixUrl + APP_ROUTES.MESSAGE_DETAIL} data={refData?.messages} displayKey='code' />
                        <LavReferenceItem label='common.artefact.email' url={prefixUrl + APP_ROUTES.MAIL_DETAIL} data={refData?.emailTemplates} displayKey='code' />
                        <LavReferenceItem label='common.artefact.workflow' url={prefixUrl + APP_ROUTES.WORKFLOW_DETAIL} data={refData?.workflows} />
                        <LavReferenceItem label='common.artefact.reference-document' url={prefixUrl + APP_ROUTES.REFERENCE_DOCUMENT_DETAIL} data={refData?.referenceDocuments} />
                        <LavReferenceItem label='common.artefact.permission' url={prefixUrl + APP_ROUTES.PERMISSION_MATRIX} withoutId data={refData?.permissionMatrices} />
                        <LavReferenceItem label='common.artefact.user-story-requirements' url={prefixUrl + APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL} data={refData?.userStories} displayKey='code' />
                        <LavReferenceItem label='common.artefact.sprint' url={prefixUrl + APP_ROUTES.SPRINT_MANAGEMENT_DETAIL} data={refData?.sprints} />
                        <LavReferenceItem label='common.artefact.epic' url={prefixUrl + APP_ROUTES.EPIC_MANAGEMENT_DETAIL} data={refData?.epics} />
                    </>}
                </Row>
            </Panel>
        </Collapse> : <></>
}

export default LavReferencesCard
