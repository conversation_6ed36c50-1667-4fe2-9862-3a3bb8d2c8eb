import { Col, Tooltip, Typography } from 'antd'
import React from 'react'
import { Link } from 'react-router-dom'
import intl from '../../../config/locale.config'
import { arrayHaveData } from '../../../helper/share'

const { Text } = Typography

interface LavReferenceItemProps {
    label: string
    url: string
    data: any
    displayKey?: string
    colSpan?: number
    withoutId?: boolean
}
const LavReferenceItem = ({ colSpan = 8, label, url, data, displayKey = 'name', withoutId = false }: LavReferenceItemProps) => {
    return arrayHaveData(data) ? <Col span={colSpan}>
        <div><Text type="secondary">{intl.formatMessage({ id: label })}</Text></div>
        {data?.map((item: any, index) => (
            <Tooltip title={item["name"]}>
                {
                    displayKey == 'name' ?
                        <Link key={item.id} to={`${url}${withoutId ? '' : item.id}`}>
                            {index !== 0 ?
                                (item["code"] + item[displayKey]).length > 30 ? `, ${item["code"]}-${item[displayKey]}`.substring(0, 30) + '...' : `, ${item["code"]}-${item[displayKey]}` :
                                (item["code"] + item[displayKey]).length > 30 ? `${item["code"]}-${item[displayKey]}`.substring(0, 30) + '...' : `${item["code"]}-${(item[displayKey])}`

                            }
                        </Link> :

                        <Link key={item.id} to={`${url}${withoutId ? '' : item.id}`}>
                            {index !== 0 ?
                                (item[displayKey] + item["name"]).length > 30 ? `, ${item[displayKey]}-${item["name"]}`.substring(0, 30) + '...' : `, ${item[displayKey]}-${item["name"]}` :
                                (item[displayKey] + item["name"]).length > 30 ? `${item[displayKey]}-${item["name"]}`.substring(0, 30) + '...' : `${item[displayKey]}-${(item["name"])}`

                            }
                        </Link>
                }

            </Tooltip>
        ))}
    </Col> : <></>
}

export default LavReferenceItem
