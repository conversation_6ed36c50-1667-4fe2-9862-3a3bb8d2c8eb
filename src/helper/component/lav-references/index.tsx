import { Space } from 'antd'
import React from 'react'
import LavReferencesCard from './references-card'

interface LavReferencesProps {
    isCommon?: boolean
    data: any
}
const LavReferences = ({ data, isCommon = false }: LavReferencesProps) => {
    return (
        (data?.referenceFrom && (data?.referenceFrom).length > 0) || (data?.referenceTo && (data?.referenceTo).length > 0) ?
            <div className='rq-references'>
                <Space direction="vertical" size="small">
                    <LavReferencesCard data={data?.referenceFrom} isCommon={isCommon} title='common.reference-from' />
                    <LavReferencesCard data={data?.referenceTo} isCommon={isCommon} title='common.reference-to' />
                </Space>
            </div> : <></>
    )

}
export default LavReferences
