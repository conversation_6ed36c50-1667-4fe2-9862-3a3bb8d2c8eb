import { Card, Col, Row, Typography } from 'antd'
import moment from 'moment'
import intl from '../../../config/locale.config'
import { DATE_FORMAT } from '../../../constants'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'

interface AssigneeTaskDetailProps {
    data?: any,
}
const { Text, Title } = Typography

const AssignTaskDetail = ({ data }: AssigneeTaskDetailProps) => {
    return <Card title={<Title level={5}>{intl.formatMessage({ id: 'assigned_task.title.card' })}</Title>} bordered={true}>
        <Row>
            <Col span={3}>
                <TriggerComment field="assignee">
                    <Text type="secondary">{intl.formatMessage({ id: 'assigned_task.label.assignee' })}</Text>
                </TriggerComment>
            </Col>
            <Col className="description" span={5}>
                {data?.author || data?.assignee}
            </Col>
            <Col span={3}>
                <TriggerComment field="reviewer">
                    <Text type="secondary">{intl.formatMessage({ id: 'assigned_task.label.reviewer' })}</Text>
                </TriggerComment>
            </Col>
            <Col className="description" span={5}>
                {data?.reviewer}
            </Col>
            <Col span={3}>
                <TriggerComment field="customer">
                    <Text type="secondary">{intl.formatMessage({ id: 'assigned_task.label.customer' })}</Text>
                </TriggerComment>
            </Col>
            <Col className="description" span={5}>
                {data?.customer}
            </Col>
            <Col span={3}>
                <TriggerComment field="due-date">
                    <Text type="secondary">{intl.formatMessage({ id: 'assigned_task.label.due-date' })}</Text>
                </TriggerComment>
            </Col>

            <Col className="description" span={5}>
                {/* lavdate */}
                {data?.dueDate ? moment(data?.dueDate).format(DATE_FORMAT) : ''}
            </Col>
            <Col span={3}>
                <TriggerComment field="complete-date">
                    <Text type="secondary">{intl.formatMessage({ id: 'assigned_task.label.complete-date' })}</Text>
                </TriggerComment>
            </Col>
            <Col className="description" span={13}>
                {/* lavdate */}
                {data?.completeDate ? moment(data?.completeDate).format(DATE_FORMAT) : ''}
            </Col>
        </Row>
    </Card>
}

export default AssignTaskDetail


