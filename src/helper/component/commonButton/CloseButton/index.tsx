import { Button, Modal, Typography } from 'antd'
import intl from '../../../../config/locale.config'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import { ReplaceArtefactName } from '../../../../helper/share'

const CloseButton = ({
  content,
  okCB,
  
  title = '',
  confirmButton = '',
}) => {
  const modalConfirmConfig = useModalConfirmationConfig()
  const { Title, Text } = Typography

  const renderTitle = () =>
    title ? (
      <Title level={4}>
        <Text>{title}</Text>
      </Title>
    ) : (
      modalConfirmConfig.title
    )

  const showModal = () => {
    const modalContent =
      Modal.confirm({
        ...modalConfirmConfig,
        title: renderTitle(),
        content: ReplaceArtefactName(content),
        okText: confirmButton || modalConfirmConfig.okText,
        okButtonProps: { danger: true },
        onOk() {
          handleOk()
        },
      })
  }

  const handleOk = () => {
    okCB()
  }

  return (
    <>  
        <Button  onClick={handleOk} >{intl.formatMessage({ id: `common.action.close` })}</Button>
    </>
  )
}

export default CloseButton
