import { Modal, Button, Typography } from 'antd'
import intl from '../../../../config/locale.config'
import CustomSvgIcons from '../../custom-icons'
import useModalConfirmationConfig from '../../../hooks/useModalConfirmationConfig'
import { BUTTON_TYPE } from '../../../../constants'

const RemoveButton = ({
  content,
  okCB,
  type,
  title = '',
  confirmButton = '',
}) => {
  const modalConfirmConfig = useModalConfirmationConfig()
  const { Title, Text } = Typography

  const renderTitle = () =>
    title ? (
      <Title level={4}>
        <Text>{title}</Text>
      </Title>
    ) : (
      modalConfirmConfig.title
    )

  const showModal = () => {
    Modal.confirm({
      ...modalConfirmConfig,
      title: renderTitle(),
      content,
      okText: confirmButton || modalConfirmConfig.okText,
      okButtonProps: { danger: true },
      onOk() {
        handleOk()
      },
    })
  }

  const handleOk = () => {
    okCB()
  }

  return (
    <>
      {type === BUTTON_TYPE.TEXT && (
        <Button onClick={showModal} ghost className='btn-warning'>{intl.formatMessage({ id: `common.action.remove` })}</Button>
      )}

      {type === BUTTON_TYPE.ICON && (
        <Button
          type="text"
          icon={<CustomSvgIcons name="DeleteCustomIcon" />}
          onClick={showModal}
        />
      )}
    </>
  )
}

export default RemoveButton
