import { Modal, Button, Typography } from 'antd'
import intl from '../../../../config/locale.config'
import CustomSvgIcons from '../../custom-icons'
import useModalConfirmationConfig from '../../../../helper/hooks/useModalConfirmationConfig'
import { BUTTON_TYPE } from '../../../../constants'
import { ReplaceArtefactName } from '../../../../helper/share'

const DeleteButton = ({
  content,
  okCB,
  type,
  title = '',
  confirmButton = '',
}) => {
  const modalConfirmConfig = useModalConfirmationConfig()
  const { Title, Text } = Typography

  const renderTitle = () =>
    title ? (
      <Title level={4}>
        <Text>{title}</Text>
      </Title>
    ) : (
      modalConfirmConfig.title
    )

  const showModal = () => {
    const modalContent =
      Modal.confirm({
        ...modalConfirmConfig,
        title: renderTitle(),
        content: ReplaceArtefactName(content),
        okText: confirmButton || modalConfirmConfig.okText,
        okButtonProps: { danger: true },
        onOk() {
          handleOk()
        },
      })
  }

  const handleOk = () => {
    okCB()
  }

  return (
    <>
      {type === BUTTON_TYPE.TEXT && (
        <Button ghost onClick={showModal} danger>{intl.formatMessage({ id: `common.action.delete` })}</Button>
      )}

      {type === BUTTON_TYPE.ICON && (
        <Button
          type="text"
          icon={<CustomSvgIcons name="DeleteCustomIcon" />}
          onClick={showModal}
        />
      )}
    </>
  )
}

export default DeleteButton
