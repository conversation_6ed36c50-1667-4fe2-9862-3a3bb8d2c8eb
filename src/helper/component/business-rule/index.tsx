import { DeleteOutlined } from '@ant-design/icons'
import {
	AutoComplete,
	Card,
	Col,
	Form,
	InputNumber,
	Row
} from 'antd'
import React, { useEffect } from 'react'
import intl from '../../../config/locale.config'
import FormGroup from '../../../helper/component/form-group'
import CkeditorMention from '../ckeditor-mention'
import { BRRule } from './type'

const BusinessRuleComponent = (props: any) => {

	useEffect(() => {
		props.form?.setFieldsValue({
			[`step-${props.data?.keyId}`]: props.data?.step,
			[`br-rule-${props.data?.keyId}`]: props.data?.name,
			[`mentions-${props.data?.keyId}`]: props.data?.content
		})
	}, [props.data])

	const options: any = [
		{ type: BRRule.screenDisplayingRules, value: 'Screen Displaying Rules' },
		{ type: BRRule.confirmationRules, value: 'Confirmation Rules' },
		{ type: BRRule.validatingRules, value: 'Validating Rules' },
		{ type: BRRule.creatingRules, value: 'Creating Rules' },
		{ type: BRRule.deletingRules, value: 'Deleting Rules' },
		{ type: BRRule.updatingRules, value: 'Updating Rules' },
		{ type: BRRule.sendingEmailNotificationRules, value: 'Sending Email Notification Rules' },
		{ type: BRRule.sendingMessageNotificationRules, value: 'Sending Message Notification Rules' },
		{ type: BRRule.loggingInRules, value: 'Logging in Rules' },
		{ type: BRRule.loggingOutRules, value: 'Logging out Rules' }
	]

	return <Row>
		<Card key={props.index} style={{ width: '100%', marginBottom: 15 }}>
			<Row>
				<Col span={10}>
					<FormGroup required inline label={intl.formatMessage({ id: 'function.form.step' })} labelSpan={3} controlSpan={21}>
						<Form.Item name={`step-${props.data?.keyId}`} rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
							<InputNumber
								min={0}
								maxLength={9}
								onChange={(e) => {
									props.setAllItems(() => {
										const newData = {
											...props.data,
											step: e
										}
										const newAllItems = [
											...props.allItems
										]
										newAllItems[props.index] = newData
										return newAllItems
									})
								}}
							/>
						</Form.Item>
					</FormGroup>
				</Col>

				<Col span={11}>
					<FormGroup required inline label={intl.formatMessage({ id: 'function.form.br-rule' })} labelSpan={6} controlSpan={18}>
						<Form.Item name={`br-rule-${props.data?.keyId}`} rules={[
							{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) },
							{ validator: async (rule, value) => { if (value && value.trim().length === 0) { throw new Error(intl.formatMessage({ id: 'IEM_1' })) } } }
							]}>
							<AutoComplete
								maxLength={255}
								options={options}
								defaultValue={props.data?.name}
								onSelect={(value, option) => {
									props.setAllItems(() => {
										const newData = {
											...props.data,
											name: option.value,
											type: option.type
										}
										const newAllItems = [
											...props.allItems
										]
										newAllItems[props.index] = newData
										return newAllItems
									})
								}}
								onChange={(value) => {
									props.setAllItems(() => {
										const newData = {
											...props.data,
											name: value,
											type: value
										}
										const newAllItems = [
											...props.allItems
										]
										newAllItems[props.index] = newData
										return newAllItems
									})
								}}
							/>
						</Form.Item>
					</FormGroup>
				</Col>
				<Col span={2} push={2}>
					<DeleteOutlined
						onClick={() => {
							props.removeItem(props.index)
						}}
					/>
				</Col>
			</Row>

			<Row>
				<Col span={24}>
					<Form.Item name={`mentions-${props.data?.keyId}`} rules={[{
						validator: async (rule, value) => {
							if (!props.data?.content || props.businessRule?.content === "") {
								throw new Error(intl.formatMessage({ id: 'IEM_1' }))
							}
						}
					}]}>
						<CkeditorMention
							data={props.data.content || ''}
							saveDataPre={(dataCK) => {
								props.setAllItems(() => {
									const newData = {
										...props.data,
										content: dataCK,
									}
									const newAllItems = [
										...props.allItems
									]
									newAllItems[props.index] = newData
									return newAllItems
								})
							}}
							isCommon={props.isCommon}
						/>
					</Form.Item>
				</Col>
			</Row>
		</Card>
	</Row>
}

export default BusinessRuleComponent