import { FormInstance } from 'antd'
import intl from '../../../config/locale.config'

export interface Item {
  id: number
  name: string
  order: number
  object: string
  screen: string
  type: string
  status: number
  dateCreated: string
  dateUpdate: string
  description: string
  createdBy: string
  updateBy: string
}
export interface SourceFunction {
  id: number
  name: string
}
export enum PreConditionType {
  userPermission = 0,
  security = 1,
}
export enum BRRule {
  screenDisplayingRules = 0,
  confirmationRules = 1,
  validatingRules = 2,
  creatingRules = 3,
  deletingRules = 4,
  updatingRules = 5,
  sendingEmailNotificationRules = 6,
  sendingMessageNotificationRules = 7,
  loggingInRules = 8,
  loggingOutRules = 9,
}
export interface selectType {
  id: number
  name: string
}

export interface SourceFunctionData {
  id: number
  name: string
  sourceObjectProperties: SourceFunction[]
}
export interface Pagination {
  current: number
  pageSize: number
  total: number
}

export interface AddNewFunction {
  requestData: requestCreateFunction
  createAnother: boolean
}
export interface AddNewFunctionSuccess {
  mess: string
  createAnother: boolean
}

export interface requestCreateFunction {
  name: string
  listObject: []
  listActor: []
  listUserRequirement: []
  // listScreen:[],
  description: string
  status: number
  trigger: string
  postCondition: string
  createdBy: string
  dataCK: []
  dataCK2: []
  reqElicitation: number | null
  activeFlowPath: any
  documentation: number | null
  implementation: number | null
  storage: string
  jira: string
  confluence: string
}

export interface CreateFunctionState {
  isLoading: boolean
  listObject: []
  listActor: []
  listUserRequirement: []
  listOtherRequirement: []
  listCommonBusinessRule: []
  listMessages: [],
  listEmailTemplates: [],
  imageFile: any
  assign: any
  reloadData: boolean
  isBack: boolean
  message: string
}
export interface Error {
  functionName: string
  order: string
  funcPropertyError: propertyError
}

export interface propertyError {
  id: []
  errorMessage: string
  listFuncError: []
}
export enum Screen_Mode {
  VIEW = 1,
  EDIT = 2,
  CREATE = 3,
}

export enum Status {
  DRAFT = 0,
  SUBMITTED = 1,
  DELETE = 2,
  CANCELLED = 3,
}
export enum Status_Record {
  DRAF = 0,
  SUBMITTED = 1,
  CANCELLED = 2,
  CHECK_IN = 3,
  DELETE = 4,
}
export enum Type {
  CRUD = 0,
  Workflow = 1,
  Others = 2,
}
export enum ActionEnum {
  INIT_SCREEN = '@@MODULES/CREATEFUNCTION/INIT_SCREEN',
  INIT_SCREEN_FAILURE = '@@MODULES/CREATEFUNCTION/INIT_SCREEN_FAILURE',
  INIT_SCREEN_SUCCESS = '@@MODULES/CREATEFUNCTION/INIT_SCREEN_SUCCESS',
  ADD_NEW_FUNCTION = '@@MODULES/CREATEFUNCTION/ADD_NEW_FUNCTION',
  ADD_NEW_FUNCTION_SUCCESS = '@@MODULES/CREATEFUNCTION/ADD_NEW_FUNCTION_SUCCESS',
  ADD_NEW_FUNCTION_FAILURE = '@@MODULES/CREATEFUNCTION/ADD_NEW_FUNCTION_FAILURE',
  SAVE_IMG = '@@MODULES/CREATEFUNCTION/SAVE_IMG',
  SAVE_IMG_TO_STATE = '@@MODULES/CREATEFUNCTION/SAVE_IMG_TO_STATE',
  RESET_ERR_AND_MESS = '@@MODULES/CREATEFUNCTION/RESET_ERR_AND_MESS',
}
