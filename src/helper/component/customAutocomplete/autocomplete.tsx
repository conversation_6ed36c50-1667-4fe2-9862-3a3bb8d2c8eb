import { COMP_TYPE_LIST } from '../../../constants'
import { AutoComplete } from 'antd'
import React, { useEffect, useState } from 'react'

const CustomAutocomplete = (props) => {
    const [listSelect, setListSelect] = useState(COMP_TYPE_LIST)
    useEffect(() => {
        const list = COMP_TYPE_LIST.filter((e) => e?.value?.toLocaleLowerCase().includes(props.text.toLocaleLowerCase()))
        setListSelect(list)
    }, [])
    return (
        <AutoComplete
            maxLength={255}
            options={listSelect}
            style={{ width: '100%' }}
            value={props.text}
            onSelect={(value, option) => {
                props.handleChangeSCProperty(props.order, 'componentType', value)
            }}
            onSearch={(value) => {
                const list = COMP_TYPE_LIST.filter((e) => e?.value?.toLocaleLowerCase().includes(value.toLocaleLowerCase()))
                setListSelect(list)
            }}
            onChange={(value) => {
                props.handleChangeSCProperty(props.order, 'componentType', value)
            }}
        />
    )
}

export default CustomAutocomplete