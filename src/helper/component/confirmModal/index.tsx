import { Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import intl from '../../../config/locale.config'
import { normalModal } from './type'
const { confirm } = Modal

const showConfirm = ({ title, content, okCB, cancelCB }) => {
  confirm({
    title: title,
    icon: <ExclamationCircleOutlined />,
    content: content,
    okText: `${intl.formatMessage({ id: 'common.action.ok' })}`,
    okType: 'danger',
    cancelText: `${intl.formatMessage({ id: 'common.action.cancel' })}`,
    onOk() {
      okCB()
    },
    onCancel() {
      cancelCB()
    },
  })
}

const success = (data: normalModal) => {
  Modal.info({
    title: data.title,
    content: data.content,
    onOk() {
      if (data.okCB) {
        data.okCB()
      }
    },
  })
}

const info = (data: normalModal) => {
  Modal.info({
    title: data.title,
    content: data.content,
    onOk() {
      if (data.okCB) {
        data.okCB()
      }
    },
  })
}

const error = (data: normalModal) => {
  Modal.info({
    title: data.title,
    content: data.content,
    onOk() {
      if (data.okCB) {
        data.okCB()
      }
    },
  })
}

const warning = (data: normalModal) => {
  Modal.info({
    title: data.title,
    content: data.content,
    onOk() {
      if (data.okCB) {
        data.okCB()
      }
    },
  })
}

export default {
  showConfirm,
  info,
  success,
  error,
  warning,
}
