import { Card, Col, Form, Input, Row, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import intl from '../../../config/locale.config'
import { SCREEN_MODE } from '../../../constants'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'
import FormGroup from '../form-group'
import './styles.less'

interface LavRelatedLinksFormProps {
    screenMode?: SCREEN_MODE,
    form?: any
}
const LavRelatedLinksForm = ({ screenMode = SCREEN_MODE.CREATE, form }: LavRelatedLinksFormProps) => {
    const [storageWebLink, setStorageWebLink] = useState("");
    const [jiraWebLink, setJiraWebLink] = useState("");
    const [confluenceWebLink, setConfluenceWebLink] = useState("");
    useEffect(() => {
        if (screenMode === SCREEN_MODE.EDIT) {
            setStorageWebLink(form.getFieldValue('storageWebLink'));
            setJiraWebLink(form.getFieldValue('jiraWebLink'));
            setConfluenceWebLink(form.getFieldValue('confluenceWebLink'));
        }
    }, [screenMode])

    return <Card className='rq-form-block' type="inner" title={intl.formatMessage({ id: 'related-links.title' })}>
        <Row gutter={20}>
            <Col span={3}>
                <TriggerComment screenMode={screenMode} field="storage"><Typography.Text className='related-link-title'>{intl.formatMessage({ id: 'related-links.storage' })}</Typography.Text></TriggerComment>
            </Col>
            <Col span={8}>
                <Form.Item name="storageWebLink" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input 
                    maxLength={255} 
                    onChange={(e) => {
                        setStorageWebLink(e.target.value.trim())
                        if(!e.target.value.trim()) {
                            form.setFieldsValue({
                                storageLinkText: '',
                            })
                        }
                    } 
                } 
                    placeholder={intl.formatMessage({ id: 'common.place-holder.web-link' })} />
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item name="storageLinkText" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input maxLength={255} disabled={storageWebLink || form.getFieldValue('storageWebLink') ? false : true} placeholder={intl.formatMessage({ id: 'common.place-holder.link-text' })} />
                </Form.Item>
            </Col>
        </Row>

        <Row gutter={20}>
            <Col span={3}>
                <TriggerComment screenMode={screenMode} field="jira">
                    <Typography.Text className='related-link-title'>{intl.formatMessage({ id: 'related-links.jira' })}</Typography.Text>
                </TriggerComment>
            </Col>
            <Col span={8}>
                <Form.Item name="jiraWebLink" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input
                        maxLength={255}
                        onChange={(e) => {
                            setJiraWebLink(e.target.value.trim())
                            if(!e.target.value.trim()) {
                                form.setFieldsValue({
                                    jiraLinkText: '',
                                })
                            }
                        }}
                        placeholder={intl.formatMessage({ id: 'common.place-holder.web-link' })}
                    />
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item name="jiraLinkText" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input
                        maxLength={255}
                        disabled={jiraWebLink || form.getFieldValue('jiraWebLink') ? false : true}
                        placeholder={intl.formatMessage({ id: 'common.place-holder.link-text' })}
                    />
                </Form.Item>
            </Col>
        </Row>


        <Row gutter={20}>
            <Col span={3}>
                <TriggerComment screenMode={screenMode} field="confluence">
                    <Typography.Text className='related-link-title'>{intl.formatMessage({ id: 'related-links.confluence' })}</Typography.Text>
                </TriggerComment>
            </Col>
            <Col span={8}>
                <Form.Item name="confluenceWebLink" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input 
                    maxLength={255} 
                    onChange={(e) => {
                        setConfluenceWebLink(e.target.value.trim())
                        if(!e.target.value.trim()) {
                            form.setFieldsValue({
                                confluenceLinkText: '',
                            })
                        }
                    }
                } 
                    placeholder={intl.formatMessage({ id: 'common.place-holder.web-link' })} 
                    />
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item name="confluenceLinkText" rules={[{ max: 255, message: intl.formatMessage({ id: 'warning_max_length_255' }) }]}>
                    <Input maxLength={255} disabled={confluenceWebLink || form.getFieldValue('confluenceWebLink') ? false : true} placeholder={intl.formatMessage({ id: 'common.place-holder.link-text' })} />
                </Form.Item>
            </Col>
        </Row>
    </Card >
}

export default LavRelatedLinksForm
