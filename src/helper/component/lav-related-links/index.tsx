import { Card, Col, Row, Typography } from 'antd'
import React, { useEffect } from 'react'
import intl from '../../../config/locale.config'
import TriggerComment from '../../../modules/_shared/comment/trigger-comment'

const { Title, Text } = Typography

interface LavRelatedLinksProps {
    data?: any
}
const LavRelatedLinks = ({ data }: LavRelatedLinksProps) => {
    const isJsonString = (data) => {
        try {
            JSON.parse(data);
        } catch (e) {
            return '';
        }
        return JSON.parse(data);
    }
    const storage = isJsonString(data?.storage)
    const jira = isJsonString(data?.jira)
    const confluence = isJsonString(data?.confluence)

    

    const invalidLink = (url) => {
        if(url) {
            const validLinkLocal = url.match(/https?:\/\/(((www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-z]{2,63})|(localhost))\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/i);
            if (validLinkLocal) {
                return true
            } else {
                return false
            }
        }
        return false
    }


    return <Card className='rq-form-block' type="inner" title={<Title level={5}>{intl.formatMessage({ id: 'related-links.title' })}</Title>} bordered={true}>
        <Row gutter={[16, 4]}>
            <Col span={8}>
                <TriggerComment field="storage">
                    <Text type="secondary">{intl.formatMessage({ id: 'related-links.storage' })}:</Text>
                </TriggerComment>
            </Col>
            <Col span={16}>
                {(storage  && invalidLink(storage?.address)) ? <a target="_blank" href={storage?.address}>{storage?.textToDisplay || storage?.address}</a> : <></>}
            </Col>
            <Col span={8}>
                <TriggerComment field="jira">
                    <Text type="secondary">{intl.formatMessage({ id: 'related-links.jira' })}:</Text>
                </TriggerComment>
            </Col>
            <Col span={16}>
                {jira  && invalidLink(jira?.address) ? <a target="_blank" href={jira?.address}>{jira?.textToDisplay || jira?.address}</a> : <></>}
            </Col>
            <Col span={8}>
                <TriggerComment field="confluence">
                    <Text type="secondary">{intl.formatMessage({ id: 'related-links.confluence' })}:</Text>
                </TriggerComment>
            </Col>
            <Col span={16}>
                {confluence  && confluence?.address && invalidLink(confluence?.address) ? <a target="_blank" href={confluence?.address}>{confluence?.textToDisplay || confluence?.address}</a> : <></>}
            </Col>
        </Row>
    </Card>
}

export default LavRelatedLinks
