import React from 'react';
import { But<PERSON>, Space } from 'antd';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import './table-controls.css';

interface TableControlsProps {
  visible: boolean;
  position: {
    row: number;
    col: number;
    cellRect: DOMRect;
    tableElement: HTMLElement;
  } | null;
  onAddRow: (direction: 'above' | 'below') => void;
  onAddColumn: (direction: 'left' | 'right') => void;
  onDeleteRow: () => void;
  onDeleteColumn: () => void;
  onClose: () => void;
}

export const TableControls: React.FC<TableControlsProps> = ({
  visible,
  position,
  onAddRow,
  onAddColumn,
  onDeleteRow,
  onDeleteColumn,
  onClose
}) => {
  if (!visible || !position) {
    return null;
  }

  // Calculate position above the table
  const tableRect = position.tableElement.getBoundingClientRect();
  const popoverStyle: React.CSSProperties = {
    position: 'fixed',
    left: tableRect.left + (tableRect.width / 2) - 200, // Center above table
    top: tableRect.top - 50, // 50px above table
    zIndex: 1001,
    transform: 'translateX(-50%)',
  };

  return (
    <div className="table-controls-toolbar" style={popoverStyle}>
      <div className="table-controls-content">
        <Space size="small">
          <Button
            size="small"
            icon={<PlusOutlined />}
            onClick={() => onAddRow('above')}
            title="Add Row Above"
          >
            Row Above
          </Button>
          <Button
            size="small"
            icon={<PlusOutlined />}
            onClick={() => onAddRow('below')}
            title="Add Row Below"
          >
            Row Below
          </Button>
          <Button
            size="small"
            icon={<PlusOutlined />}
            onClick={() => onAddColumn('left')}
            title="Add Column Left"
          >
            Col Left
          </Button>
          <Button
            size="small"
            icon={<PlusOutlined />}
            onClick={() => onAddColumn('right')}
            title="Add Column Right"
          >
            Col Right
          </Button>
          <Button
            size="small"
            icon={<MinusOutlined />}
            onClick={onDeleteRow}
            title="Delete Row"
            danger
          >
            Delete Row
          </Button>
          <Button
            size="small"
            icon={<MinusOutlined />}
            onClick={onDeleteColumn}
            title="Delete Column"
            danger
          >
            Delete Col
          </Button>
        </Space>
      </div>
    </div>
  );
};
