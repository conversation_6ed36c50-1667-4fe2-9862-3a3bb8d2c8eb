/* Drag Handle Styles */
.drag-handle {
  position: absolute;
  display: none;
  align-items: center;
  justify-content: center;
  cursor: grab;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #6b7280;
  z-index: 10;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.drag-handle:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.drag-handle:active {
  cursor: grabbing;
  background: rgba(243, 244, 246, 1);
}

.drag-handle svg {
  pointer-events: none;
}

/* Drop target styles */
.drop-target {
  position: relative;
}

.drop-target::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  background: #3b82f6;
  border-radius: 2px;
  z-index: 5;
}

/* Editor container with drag handle space */
.canvas-editor-content {
  position: relative;
  padding-left: 0;
}

/* Block elements that should be draggable */
.canvas-editor-content p,
.canvas-editor-content h1,
.canvas-editor-content h2,
.canvas-editor-content h3,
.canvas-editor-content h4,
.canvas-editor-content h5,
.canvas-editor-content h6,
.canvas-editor-content blockquote,
.canvas-editor-content pre,
.canvas-editor-content ul,
.canvas-editor-content ol,
.canvas-editor-content table,
.canvas-editor-content hr {
  position: relative;
  transition: background-color 0.2s ease;
}

.canvas-editor-content p:hover,
.canvas-editor-content h1:hover,
.canvas-editor-content h2:hover,
.canvas-editor-content h3:hover,
.canvas-editor-content h4:hover,
.canvas-editor-content h5:hover,
.canvas-editor-content h6:hover,
.canvas-editor-content blockquote:hover,
.canvas-editor-content pre:hover,
.canvas-editor-content ul:hover,
.canvas-editor-content ol:hover,
.canvas-editor-content table:hover {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

/* Dragging state */
.canvas-editor-content.dragging {
  user-select: none;
}

.canvas-editor-content.dragging .drag-handle {
  cursor: grabbing;
}

/* Animation for smooth transitions */
@keyframes dragHandleShow {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.drag-handle.show {
  animation: dragHandleShow 0.2s ease-out;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .drag-handle {
    width: 20px;
    height: 20px;
    left: -28px;
  }
  
  .drag-handle svg {
    width: 8px;
    height: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .drag-handle {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: #9ca3af;
  }
  
  .drag-handle:hover {
    background: rgba(0, 0, 0, 0.9);
    color: #d1d5db;
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .canvas-editor-content p:hover,
  .canvas-editor-content h1:hover,
  .canvas-editor-content h2:hover,
  .canvas-editor-content h3:hover,
  .canvas-editor-content h4:hover,
  .canvas-editor-content h5:hover,
  .canvas-editor-content h6:hover,
  .canvas-editor-content blockquote:hover,
  .canvas-editor-content pre:hover,
  .canvas-editor-content ul:hover,
  .canvas-editor-content ol:hover,
  .canvas-editor-content table:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}
