import {
  FileTextOutlined,
  UnorderedListOutlined,
  OrderedListOutlined,
  CheckSquareOutlined,
  QuestionCircleOutlined,
  MinusOutlined,
  ExclamationCircleOutlined,
  CodeOutlined,
  TableOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  FileOutlined
} from '@ant-design/icons';

export interface SlashCommand {
  command: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  action: (editor: any) => void;
  category?: 'basic' | 'media' | 'database' | 'advanced';
}

export const createSlashCommands = (): SlashCommand[] => [
  // Basic Blocks
  { 
    command: '/text', 
    label: 'Text', 
    description: 'Plain text block',
    icon: <FileTextOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().insertContent('<p></p>').run();
    }
  },
  { 
    command: '/h1', 
    label: 'Heading 1', 
    description: 'Large heading',
    icon: <span style={{ fontWeight: 'bold' }}>H1</span>,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().toggleHeading({ level: 1 }).run();
    }
  },
  { 
    command: '/h2', 
    label: 'Heading 2', 
    description: 'Medium heading',
    icon: <span style={{ fontWeight: 'bold' }}>H2</span>,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().toggleHeading({ level: 2 }).run();
    }
  },
  { 
    command: '/h3', 
    label: 'Heading 3', 
    description: 'Small heading',
    icon: <span style={{ fontWeight: 'bold' }}>H3</span>,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().toggleHeading({ level: 3 }).run();
    }
  },
  { 
    command: '/bullet', 
    label: 'Bullet List', 
    description: 'Create a bullet list',
    icon: <UnorderedListOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().toggleBulletList().run();
    }
  },
  { 
    command: '/number', 
    label: 'Numbered List', 
    description: 'Create a numbered list',
    icon: <OrderedListOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().toggleOrderedList().run();
    }
  },
  { 
    command: '/todo', 
    label: 'To-do List', 
    description: 'Track tasks with checkboxes',
    icon: <CheckSquareOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().insertContent('<p>☐ </p>').run();
    }
  },
  { 
    command: '/quote', 
    label: 'Quote', 
    description: 'Create a blockquote',
    icon: <QuestionCircleOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().toggleBlockquote().run();
    }
  },
  { 
    command: '/divider', 
    label: 'Divider', 
    description: 'Add a horizontal line',
    icon: <MinusOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().setHorizontalRule().run();
    }
  },
  { 
    command: '/hr', 
    label: 'Horizontal Rule', 
    description: 'Add a horizontal line',
    icon: <MinusOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().setHorizontalRule().run();
    }
  },
  { 
    command: '/callout', 
    label: 'Callout', 
    description: 'Create a callout block',
    icon: <ExclamationCircleOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().insertContent('<blockquote>💡 <strong>Note:</strong> </blockquote>').run();
    }
  },
  { 
    command: '/code', 
    label: 'Code Block', 
    description: 'Insert a code block',
    icon: <CodeOutlined />,
    category: 'basic',
    action: (editor: any) => {
      editor.chain().focus().toggleCodeBlock().run();
    }
  },
  // Media & Embeds
  { 
    command: '/image', 
    label: 'Image', 
    description: 'Upload or embed an image',
    icon: <PictureOutlined />,
    category: 'media',
    action: (editor: any) => {
      editor.chain().focus().insertContent('<p>[Image placeholder - feature coming soon]</p>').run();
    }
  },
  { 
    command: '/video', 
    label: 'Video', 
    description: 'Embed a video',
    icon: <VideoCameraOutlined />,
    category: 'media',
    action: (editor: any) => {
      editor.chain().focus().insertContent('<p>[Video placeholder - feature coming soon]</p>').run();
    }
  },
  { 
    command: '/file', 
    label: 'File', 
    description: 'Upload or link to a file',
    icon: <FileOutlined />,
    category: 'media',
    action: (editor: any) => {
      editor.chain().focus().insertContent('<p>[File placeholder - feature coming soon]</p>').run();
    }
  },
  // Database Views
  { 
    command: '/table', 
    label: 'Table', 
    description: 'Create a table',
    icon: <TableOutlined />,
    category: 'database',
    action: (editor: any) => {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    }
  }
];

export const filterSlashCommands = (commands: SlashCommand[], searchQuery: string): SlashCommand[] => {
  if (!searchQuery.trim()) return commands;
  
  return commands.filter(cmd => 
    cmd.command.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cmd.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cmd.description.toLowerCase().includes(searchQuery.toLowerCase())
  );
};

export default createSlashCommands;
