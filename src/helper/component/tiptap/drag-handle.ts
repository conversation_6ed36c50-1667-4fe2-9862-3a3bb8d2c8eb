import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { DecorationSet } from '@tiptap/pm/view';

export interface DragHandleOptions {
  dragHandleWidth: number;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    dragHandle: {
      /**
       * Show drag handle
       */
      showDragHandle: () => ReturnType;
      /**
       * Hide drag handle
       */
      hideDragHandle: () => ReturnType;
    };
  }
}

export const DragHandle = Extension.create<DragHandleOptions>({
  name: 'dragHandle',

  addOptions() {
    return {
      dragHandleWidth: 24,
    };
  },

  addCommands() {
    return {
      showDragHandle:
        () =>
        ({ commands }) => {
          return commands.setMeta('showDragHandle', true);
        },
      hideDragHandle:
        () =>
        ({ commands }) => {
          return commands.setMeta('hideDragHandle', true);
        },
    };
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('dragHandle'),
        props: {
          decorations: (state) => {
            return DecorationSet.empty;
          },
        },
        view: (view) => {
          const dragHandle = document.createElement('div');
          dragHandle.className = 'drag-handle';
          dragHandle.innerHTML = `
            <svg width="10" height="16" viewBox="0 0 10 16" xmlns="http://www.w3.org/2000/svg">
              <g fill="currentColor" fill-rule="evenodd">
                <circle cx="2" cy="2" r="1"/>
                <circle cx="6" cy="2" r="1"/>
                <circle cx="2" cy="6" r="1"/>
                <circle cx="6" cy="6" r="1"/>
                <circle cx="2" cy="10" r="1"/>
                <circle cx="6" cy="10" r="1"/>
              </g>
            </svg>
          `;
          dragHandle.style.cssText = `
            position: absolute;
            left: -${this.options.dragHandleWidth + 4}px;
            top: 0;
            width: ${this.options.dragHandleWidth}px;
            height: 24px;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: grab;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #6b7280;
            z-index: 10;
            transition: all 0.2s ease;
          `;

          let dragHandleTimeout: NodeJS.Timeout;
          let isDragging = false;
          let dragStartPos = { x: 0, y: 0 };
          let draggedNode: any = null;
          let draggedPos: number = 0;

          const showDragHandle = (targetElement: HTMLElement) => {
            clearTimeout(dragHandleTimeout);
            const rect = targetElement.getBoundingClientRect();
            const editorRect = view.dom.getBoundingClientRect();
            
            dragHandle.style.display = 'flex';
            dragHandle.style.top = `${rect.top - editorRect.top}px`;
            dragHandle.style.opacity = '1';
          };

          const hideDragHandle = () => {
            if (!isDragging) {
              dragHandleTimeout = setTimeout(() => {
                dragHandle.style.display = 'none';
                dragHandle.style.opacity = '0';
              }, 100);
            }
          };

          const getDraggableNode = (element: HTMLElement): { node: any; pos: number } | null => {
            let current = element;
            while (current && current !== view.dom) {
              if (current.getAttribute('data-drag-handle')) {
                const pos = view.posAtDOM(current, 0);
                const node = view.state.doc.nodeAt(pos);
                return { node, pos };
              }
              
              // Check if this is a block-level element that should be draggable
              const tagName = current.tagName.toLowerCase();
              const draggableBlocks = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'pre', 'ul', 'ol', 'li', 'table', 'hr'];
              
              if (draggableBlocks.includes(tagName)) {
                const pos = view.posAtDOM(current, 0);
                const node = view.state.doc.nodeAt(pos);
                if (node) {
                  return { node, pos };
                }
              }
              
              current = current.parentElement!;
            }
            return null;
          };

          const handleMouseMove = (event: MouseEvent) => {
            if (isDragging) {
              event.preventDefault();
              
              const deltaY = event.clientY - dragStartPos.y;
              dragHandle.style.transform = `translateY(${deltaY}px)`;
              
              // Find drop target
              const elementBelow = document.elementFromPoint(event.clientX, event.clientY);
              if (elementBelow) {
                const dropTarget = getDraggableNode(elementBelow as HTMLElement);
                if (dropTarget && dropTarget.pos !== draggedPos) {
                  // Visual feedback for drop target
                  view.dom.querySelectorAll('.drop-target').forEach(el => {
                    el.classList.remove('drop-target');
                  });
                  
                  const dropElement = view.domAtPos(dropTarget.pos).node.parentElement;
                  if (dropElement) {
                    dropElement.classList.add('drop-target');
                  }
                }
              }
            } else {
              const target = event.target as HTMLElement;
              if (target && view.dom.contains(target)) {
                const draggableInfo = getDraggableNode(target);
                if (draggableInfo) {
                  const element = view.domAtPos(draggableInfo.pos).node.parentElement;
                  if (element) {
                    showDragHandle(element);
                  }
                } else {
                  hideDragHandle();
                }
              }
            }
          };

          const handleMouseDown = (event: MouseEvent) => {
            if (event.target === dragHandle || dragHandle.contains(event.target as Node)) {
              event.preventDefault();
              isDragging = true;
              dragStartPos = { x: event.clientX, y: event.clientY };
              
              dragHandle.style.cursor = 'grabbing';
              dragHandle.style.zIndex = '1000';
              
              // Find the node being dragged
              const handleRect = dragHandle.getBoundingClientRect();
              const targetElement = document.elementFromPoint(
                handleRect.right + 10,
                handleRect.top + handleRect.height / 2
              );
              
              if (targetElement) {
                const draggableInfo = getDraggableNode(targetElement as HTMLElement);
                if (draggableInfo) {
                  draggedNode = draggableInfo.node;
                  draggedPos = draggableInfo.pos;
                }
              }
            }
          };

          const handleMouseUp = (event: MouseEvent) => {
            if (isDragging) {
              isDragging = false;
              dragHandle.style.cursor = 'grab';
              dragHandle.style.transform = '';
              dragHandle.style.zIndex = '10';
              
              // Clean up drop targets
              view.dom.querySelectorAll('.drop-target').forEach(el => {
                el.classList.remove('drop-target');
              });
              
              // Find drop position
              const elementBelow = document.elementFromPoint(event.clientX, event.clientY);
              if (elementBelow && draggedNode && draggedPos !== undefined) {
                const dropTarget = getDraggableNode(elementBelow as HTMLElement);
                
                if (dropTarget && dropTarget.pos !== draggedPos) {
                  // Perform the move operation
                  const tr = view.state.tr;
                  const nodeSize = draggedNode.nodeSize;
                  
                  // Calculate new position
                  let newPos = dropTarget.pos;
                  if (newPos > draggedPos) {
                    newPos -= nodeSize;
                  }
                  
                  // Remove from old position and insert at new position
                  tr.delete(draggedPos, draggedPos + nodeSize);
                  tr.insert(newPos, draggedNode);
                  
                  view.dispatch(tr);
                }
              }
              
              draggedNode = null;
              draggedPos = 0;
              hideDragHandle();
            }
          };

          const handleMouseLeave = () => {
            if (!isDragging) {
              hideDragHandle();
            }
          };

          // Add event listeners
          view.dom.addEventListener('mousemove', handleMouseMove);
          view.dom.addEventListener('mousedown', handleMouseDown);
          view.dom.addEventListener('mouseup', handleMouseUp);
          view.dom.addEventListener('mouseleave', handleMouseLeave);
          
          // Append drag handle to editor
          view.dom.style.position = 'relative';
          view.dom.appendChild(dragHandle);

          return {
            destroy() {
              view.dom.removeEventListener('mousemove', handleMouseMove);
              view.dom.removeEventListener('mousedown', handleMouseDown);
              view.dom.removeEventListener('mouseup', handleMouseUp);
              view.dom.removeEventListener('mouseleave', handleMouseLeave);
              if (dragHandle.parentNode) {
                dragHandle.parentNode.removeChild(dragHandle);
              }
            },
          };
        },
      }),
    ];
  },
});

export default DragHandle;
