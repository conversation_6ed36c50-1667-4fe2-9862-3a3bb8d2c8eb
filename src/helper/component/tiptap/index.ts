// Import extensions for internal use
import BlockquoteExtension from './blockquote';
import BulletListExtension from './bullet-list';
import CodeBlockExtension from './code-block';
import CodeExtension from './code';
import DocumentExtension from './document';
import HardBreakExtension from './hard-break';
import HeadingExtension from './heading';
import HorizontalRuleExtension from './horizontal-rule';
import ListItemExtension from './list-item';
import OrderedListExtension from './ordered-list';
import ParagraphExtension from './paragraph';
import PlaceholderExtension from './placeholder';
import StrikeExtension from './strike';
import TableExtension from './table';
import TableCellExtension from './table-cell';
import TableHeaderExtension from './table-header';
import TableRowExtension from './table-row';
import TextExtension from './text';
import DragHandleExtension from './drag-handle';
import MarkdownTableParserExtension from './markdown-table-parser';

// Core Extensions
export { default as BlockquoteExtension } from './blockquote';
export { default as BulletListExtension } from './bullet-list';
export { default as CodeBlockExtension } from './code-block';
export { default as CodeExtension } from './code';
export { default as DocumentExtension } from './document';
export { default as HardBreakExtension } from './hard-break';
export { default as HeadingExtension } from './heading';
export { default as HorizontalRuleExtension } from './horizontal-rule';
export { default as ListItemExtension } from './list-item';
export { default as OrderedListExtension } from './ordered-list';
export { default as ParagraphExtension } from './paragraph';
export { default as PlaceholderExtension } from './placeholder';
export { default as StrikeExtension } from './strike';
export { default as TableExtension } from './table';
export { default as TableCellExtension } from './table-cell';
export { default as TableHeaderExtension } from './table-header';
export { default as TableRowExtension } from './table-row';
export { default as TextExtension } from './text';
export { default as DragHandleExtension } from './drag-handle';
export { default as MarkdownTableParserExtension } from './markdown-table-parser';
export { Markdown as MarkdownExtension } from 'tiptap-markdown';

// Slash Commands
export { createSlashCommands, filterSlashCommands } from './slash-commands';
export type { SlashCommand } from './slash-commands';

// Slash Menu Components
export { SlashMenu } from './slash-menu';
export { useSlashMenu } from './use-slash-menu';
export { CanvasEditor } from './canvas-editor';
export { FullScreenEditor } from './full-screen-editor';

// Table Controls
export { TableControls } from './table-controls';
export { useTableControls } from './use-table-controls';

// Text Converters
export { convertToHtml, convertToPlainText } from './text-converter';

// Extension configurations for common use cases
export const getCoreExtensions = () => [
  DocumentExtension,
  ParagraphExtension,
  TextExtension,
  HardBreakExtension,
];

export const getFormattingExtensions = () => [
  CodeExtension,
  StrikeExtension,
];

export const getBlockExtensions = () => [
  HeadingExtension,
  CodeBlockExtension,
  BlockquoteExtension,
  HorizontalRuleExtension,
];

export const getListExtensions = () => [
  BulletListExtension,
  OrderedListExtension,
  ListItemExtension,
];

export const getTableExtensions = () => [
  TableExtension,
  TableRowExtension,
  TableHeaderExtension,
  TableCellExtension,
];

export const getAllNotionExtensions = () => [
  ...getCoreExtensions(),
  ...getFormattingExtensions(),
  ...getBlockExtensions(),
  ...getListExtensions(),
  ...getTableExtensions(),
  PlaceholderExtension,
  DragHandleExtension,
];

// Get drag handle extension separately for optional inclusion
export const getDragHandleExtension = () => DragHandleExtension;

// For backward compatibility and ease of use
export const CanvasEditorExtensions = getAllNotionExtensions();
