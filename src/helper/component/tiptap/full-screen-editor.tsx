import React from 'react';
import { CanvasEditor } from './canvas-editor';

interface FullScreenEditorProps {
  content: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  editable?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const FullScreenEditor: React.FC<FullScreenEditorProps> = ({
  content,
  placeholder = "Type '/' for commands, or start typing...",
  onChange,
  onFocus,
  onBlur,
  editable = true,
  className,
  style,
  ...props
}) => {
  return (
    <div
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        ...style
      }}
      className={className}
    >
      <div
        style={{
          maxWidth: 800,
          margin: '0 auto',
          width: '100%',
          height: '100%',
          padding: '0 24px'
        }}
      >
        <CanvasEditor
          content={content}
          placeholder={placeholder}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          editable={editable}
          style={{
            height: '100%',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
            fontSize: 16,
            lineHeight: 1.6,
            color: '#333',
            minHeight: '60vh'
          }}
          {...props}
        />
      </div>

      {/* Global editor styles */}
      <style>
        {`
          .ProseMirror {
            outline: none !important;
            padding: 0 !important;
          }
          .ProseMirror p {
            margin: 0.3em 0;
            line-height: 1.5;
          }
          .ProseMirror p:first-child {
            margin-top: 0;
          }
          .ProseMirror p:last-child {
            margin-bottom: 0;
          }
          .ProseMirror h1 {
            font-size: 2em;
            font-weight: 600;
            margin: 1em 0 0.3em 0;
            line-height: 1.2;
          }
          .ProseMirror h1:first-child {
            margin-top: 0;
          }
          .ProseMirror h2 {
            font-size: 1.5em;
            font-weight: 600;
            margin: 0.8em 0 0.2em 0;
            line-height: 1.3;
          }
          .ProseMirror h2:first-child {
            margin-top: 0;
          }
          .ProseMirror h3 {
            font-size: 1.2em;
            font-weight: 600;
            margin: 0.6em 0 0.1em 0;
            line-height: 1.4;
          }
          .ProseMirror h3:first-child {
            margin-top: 0;
          }
          .ProseMirror ul, .ProseMirror ol {
            margin: 0.5em 0;
            padding-left: 1.5em;
          }
          .ProseMirror li {
            margin: 0.1em 0;
            line-height: 1.5;
          }
          .ProseMirror li p {
            margin: 0;
          }
          .ProseMirror pre {
            background: #f5f5f5;
            border-radius: 6px;
            padding: 1em;
            margin: 0.5em 0;
            overflow-x: auto;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            line-height: 1.4;
          }
          .ProseMirror code {
            background: #f5f5f5;
            border-radius: 3px;
            padding: 0.2em 0.4em;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9em;
          }
          .ProseMirror blockquote {
            border-left: 4px solid #ddd;
            margin: 0.5em 0;
            padding-left: 1em;
            color: #666;
            font-style: italic;
          }
          .ProseMirror blockquote p {
            margin: 0.2em 0;
          }
          .ProseMirror hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 1em 0;
          }
          .ProseMirror > *:first-child {
            margin-top: 0 !important;
          }
          .ProseMirror > *:last-child {
            margin-bottom: 0 !important;
          }
          
          /* Slash menu portal styles */
          [data-slash-menu] {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
          }
        `}
      </style>
    </div>
  );
};

export default FullScreenEditor;
