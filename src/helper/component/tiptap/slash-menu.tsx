import React from 'react';
import { Typography } from 'antd';
import type { SlashCommand } from './slash-commands';

interface SlashMenuProps {
  visible: boolean;
  position: { top: number; left: number };
  commands: SlashCommand[];
  onCommandSelect: (command: SlashCommand) => void;
  onClose: () => void;
}

export const SlashMenu: React.FC<SlashMenuProps> = ({
  visible,
  position,
  commands,
  onCommandSelect,
  onClose
}) => {
  if (!visible) return null;

  return (
    <div
      data-slash-menu
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        background: '#fff',
        border: '1px solid #e8e8e8',
        borderRadius: 8,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        zIndex: 1000,
        minWidth: 280,
        maxWidth: 320,
        maxHeight: 400,
        overflowY: 'auto'
      }}
    >
      <div style={{ padding: '8px 12px', borderBottom: '1px solid #f0f0f0' }}>
        <Typography.Text type="secondary" style={{ fontSize: 12 }}>
          Choose a block type
        </Typography.Text>
      </div>
      <div style={{ padding: '4px 0' }}>
        {commands.slice(0, 10).map((cmd, index) => (
          <div
            key={cmd.command}
            style={{
              padding: '8px 12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: 12,
              transition: 'background-color 0.2s',
              backgroundColor: index === 0 ? '#f5f5f5' : 'transparent' // Highlight first item
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f5f5f5';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = index === 0 ? '#f5f5f5' : 'transparent';
            }}
            onClick={() => onCommandSelect(cmd)}
          >
            <div style={{ 
              fontSize: 16,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: 20
            }}>
              {cmd.icon}
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: 500, fontSize: 14 }}>
                {cmd.label}
              </div>
              <div style={{ 
                fontSize: 12, 
                color: '#666',
                marginTop: 2
              }}>
                {cmd.description}
              </div>
            </div>
            <div style={{ 
              fontSize: 11, 
              color: '#999',
              fontFamily: 'monospace',
              backgroundColor: '#f8f8f8',
              padding: '2px 6px',
              borderRadius: 4
            }}>
              {cmd.command}
            </div>
          </div>
        ))}
        {commands.length === 0 && (
          <div style={{ 
            padding: '12px',
            textAlign: 'center',
            color: '#999',
            fontSize: 14
          }}>
            No matching commands
          </div>
        )}
      </div>
    </div>
  );
};

export default SlashMenu;
