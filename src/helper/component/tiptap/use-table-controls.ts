import { useState, useCallback, useRef } from 'react';
import { Editor } from '@tiptap/react';

interface TableControlsState {
  visible: boolean;
  position: {
    row: number;
    col: number;
    cellRect: DOMRect;
    tableElement: HTMLElement;
  } | null;
}

export const useTableControls = (editor: Editor | null) => {
  const [controlsState, setControlsState] = useState<TableControlsState>({
    visible: false,
    position: null,
  });
  
  const selectedCellRef = useRef<HTMLElement | null>(null);

  const showControls = useCallback((row: number, col: number, cellElement: HTMLElement) => {
    // Clear previous selection
    if (selectedCellRef.current) {
      selectedCellRef.current.classList.remove('selected-cell');
    }
    
    // Add selection class to current cell
    cellElement.classList.add('selected-cell');
    selectedCellRef.current = cellElement;
    
    // Get the table element
    const tableElement = cellElement.closest('table') as HTMLElement;
    if (!tableElement) return;
    
    const cellRect = cellElement.getBoundingClientRect();
    setControlsState({
      visible: true,
      position: {
        row,
        col,
        cellRect,
        tableElement,
      },
    });
  }, []);

  const hideControls = useCallback(() => {
    // Clear cell selection
    if (selectedCellRef.current) {
      selectedCellRef.current.classList.remove('selected-cell');
      selectedCellRef.current = null;
    }
    
    setControlsState({
      visible: false,
      position: null,
    });
  }, []);

  const addRow = useCallback((direction: 'above' | 'below') => {
    if (!editor || !controlsState.position) return;

    try {
      if (direction === 'above') {
        editor.chain().focus().addRowBefore().run();
      } else {
        editor.chain().focus().addRowAfter().run();
      }
      
      // Keep controls visible for the same cell after adding row
      // The cell position might change, so we need to re-calculate
      setTimeout(() => {
        if (selectedCellRef.current && controlsState.position) {
          const tableElement = selectedCellRef.current.closest('table') as HTMLElement;
          if (tableElement) {
            const newCellRect = selectedCellRef.current.getBoundingClientRect();
            const newRow = direction === 'above' ? controlsState.position.row + 1 : controlsState.position.row;
            
            setControlsState(prev => prev.position ? {
              ...prev,
              position: {
                ...prev.position,
                row: newRow,
                cellRect: newCellRect,
                tableElement: tableElement
              }
            } : prev);
          }
        }
      }, 100);
    } catch (error) {
      console.error('Error adding row:', error);
    }
  }, [editor, controlsState.position]);

  const addColumn = useCallback((direction: 'left' | 'right') => {
    if (!editor || !controlsState.position) return;

    try {
      if (direction === 'left') {
        editor.chain().focus().addColumnBefore().run();
      } else {
        editor.chain().focus().addColumnAfter().run();
      }
      
      // Keep controls visible for the same cell after adding column
      setTimeout(() => {
        if (selectedCellRef.current && controlsState.position) {
          const tableElement = selectedCellRef.current.closest('table') as HTMLElement;
          if (tableElement) {
            const newCellRect = selectedCellRef.current.getBoundingClientRect();
            const newCol = direction === 'left' ? controlsState.position.col + 1 : controlsState.position.col;
            
            setControlsState(prev => prev.position ? {
              ...prev,
              position: {
                ...prev.position,
                col: newCol,
                cellRect: newCellRect,
                tableElement: tableElement
              }
            } : prev);
          }
        }
      }, 100);
    } catch (error) {
      console.error('Error adding column:', error);
    }
  }, [editor, controlsState.position]);

  const deleteRow = useCallback(() => {
    if (!editor || !controlsState.position) return;

    try {
      editor.chain().focus().deleteRow().run();
      
      // Hide controls after deleting row
      hideControls();
    } catch (error) {
      console.error('Error deleting row:', error);
    }
  }, [editor, controlsState.position, hideControls]);

  const deleteColumn = useCallback(() => {
    if (!editor || !controlsState.position) return;

    try {
      editor.chain().focus().deleteColumn().run();
      
      // Hide controls after deleting column
      hideControls();
    } catch (error) {
      console.error('Error deleting column:', error);
    }
  }, [editor, controlsState.position, hideControls]);

  return {
    controlsState,
    showControls,
    hideControls,
    addRow,
    addColumn,
    deleteRow,
    deleteColumn,
  };
};
