import { useState, useEffect, useCallback, useMemo } from 'react';
import type { Editor } from '@tiptap/react';
import { createSlashCommands, filterSlashCommands } from './slash-commands';
import type { SlashCommand } from './slash-commands';

interface UseSlashMenuProps {
  editor: Editor | null;
}

interface SlashMenuState {
  visible: boolean;
  position: { top: number; left: number };
  searchQuery: string;
  filteredCommands: SlashCommand[];
}

export const useSlashMenu = ({ editor }: UseSlashMenuProps) => {
  const [menuState, setMenuState] = useState<SlashMenuState>({
    visible: false,
    position: { top: 0, left: 0 },
    searchQuery: '',
    filteredCommands: []
  });

  // Move slashCommands creation outside of render to prevent infinite updates
  const slashCommands = useMemo(() => createSlashCommands(), []);

  // Update filtered commands when search query changes
  useEffect(() => {
    const filtered = filterSlashCommands(slashCommands, menuState.searchQuery);
    setMenuState(prev => ({ ...prev, filteredCommands: filtered }));
  }, [menuState.searchQuery, slashCommands]);

  const showMenu = useCallback((position: { top: number; left: number }) => {
    setMenuState(prev => ({
      ...prev,
      visible: true,
      position,
      searchQuery: '',
      filteredCommands: slashCommands
    }));
  }, [slashCommands]);

  const hideMenu = useCallback(() => {
    setMenuState(prev => ({
      ...prev,
      visible: false,
      searchQuery: ''
    }));
  }, []);

  const updateSearchQuery = useCallback((query: string) => {
    setMenuState(prev => ({ ...prev, searchQuery: query }));
  }, []);

  const executeCommand = useCallback((command: SlashCommand) => {
    if (!editor) return;

    // Remove the slash and any search text
    const { state } = editor;
    const { selection } = state;
    const { from } = selection;
    
    // Find the start of the slash command
    const textBefore = state.doc.textBetween(Math.max(0, from - 20), from);
    const slashIndex = textBefore.lastIndexOf('/');
    const deleteFrom = from - (textBefore.length - slashIndex);
    
    editor.chain()
      .focus()
      .deleteRange({ from: deleteFrom, to: from })
      .run();
    
    // Execute the command
    command.action(editor);
    
    hideMenu();
  }, [editor, hideMenu]);

  // Handle keyboard events for slash menu
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!menuState.visible) return false;

    switch (event.key) {
      case 'Escape':
        hideMenu();
        return true;
      case 'ArrowDown':
      case 'ArrowUp':
        // TODO: Handle arrow key navigation
        return true;
      case 'Enter':
        // TODO: Execute selected command
        return true;
      default:
        return false;
    }
  }, [menuState.visible, hideMenu]);

  // Handle text input for search
  const handleTextInput = useCallback((text: string) => {
    if (menuState.visible && text !== '/') {
      setMenuState(prev => ({ ...prev, searchQuery: prev.searchQuery + text }));
      return true;
    }
    return false;
  }, [menuState.visible]);

  // Handle clicking outside menu
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (menuState.visible) {
      const target = event.target as HTMLElement;
      const slashMenuElement = document.querySelector('[data-slash-menu]');
      if (slashMenuElement && !slashMenuElement.contains(target)) {
        hideMenu();
      }
    }
  }, [menuState.visible, hideMenu]);

  return {
    menuState,
    showMenu,
    hideMenu,
    updateSearchQuery,
    executeCommand,
    handleKeyDown,
    handleTextInput,
    handleClickOutside
  };
};

export default useSlashMenu;
