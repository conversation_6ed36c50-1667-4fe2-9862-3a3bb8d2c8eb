/**
 * Utility functions to convert between different text formats for TipTap editor
 */
import TurndownService from 'turndown'
import { gfm } from 'turndown-plugin-gfm'
import { marked } from 'marked'

/**
 * Convert markdown-like text to HTML for the editor
 */
export const convertToHtml = (text: string): string => {
  return marked.parse(text) as string
}

// Initialize turndown service with custom configuration
const turndownService = new TurndownService({
  headingStyle: 'atx',
  hr: '---',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '*',
  strongDelimiter: '**',
  linkStyle: 'inlined',
  linkReferenceStyle: 'full',
})

turndownService.use(gfm)

// Add custom rules for better table handling
turndownService.addRule('tableCell', {
  filter: ['th', 'td'],
  replacement: function (content, node) {
    // Get clean text content without HTML tags
    const text = (node as HTMLElement).innerText || node.textContent || ''
    return text.trim().replace(/\s+/g, ' ')
  },
})

turndownService.addRule('tableRow', {
  filter: 'tr',
  replacement: function (content, node) {
    const cells = node.querySelectorAll('th, td')
    const cellContents = Array.from(cells).map((cell) => {
      // Get text content and clean it up, removing any HTML tags
      const text = (cell as HTMLElement).innerText || cell.textContent || ''
      return text.trim().replace(/\s+/g, ' ') // Normalize whitespace
    })
    return '| ' + cellContents.join(' | ') + ' |'
  },
})

turndownService.addRule('table', {
  filter: 'table',
  replacement: function (content, node) {
    const rows = node.querySelectorAll('tr')
    const result: string[] = []

    // Process header row
    const headerRow = rows[0]
    if (headerRow) {
      const headerCells = headerRow.querySelectorAll('th, td')
      const headers = Array.from(headerCells).map((cell) => {
        const text = (cell as HTMLElement).innerText || cell.textContent || ''
        return text.trim().replace(/\s+/g, ' ')
      })

      result.push('| ' + headers.join(' | ') + ' |')
      result.push('|' + headers.map(() => ' --- ').join('|') + '|')
    }

    // Process body rows
    for (let i = 1; i < rows.length; i++) {
      const row = rows[i]
      const cells = row.querySelectorAll('td, th')
      const cellContents = Array.from(cells).map((cell) => {
        const text = (cell as HTMLElement).innerText || cell.textContent || ''
        return text.trim().replace(/\s+/g, ' ')
      })
      result.push('| ' + cellContents.join(' | ') + ' |')
    }

    return '\n' + result.join('\n') + '\n\n'
  },
})

// Add rule to handle paragraphs properly
turndownService.addRule('paragraph', {
  filter: 'p',
  replacement: function (content) {
    return content.trim() + '\n\n'
  },
})

// Add rule to remove any remaining HTML tags
turndownService.addRule('stripHTML', {
  filter: function (node) {
    // Strip any HTML tags that might remain
    return (
      node.nodeType === 1 &&
      ![
        'a',
        'p',
        'br',
        'strong',
        'em',
        'code',
        'pre',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'ul',
        'ol',
        'li',
        'table',
        'thead',
        'tbody',
        'tr',
        'th',
        'td',
        'blockquote',
      ].includes(node.nodeName.toLowerCase())
    )
  },
  replacement: function (content) {
    return content
  },
})

/**
 * Convert HTML back to plain text/markdown using Turndown
 */
export const convertToPlainText = (html: string): string => {
  try {
    // Use turndown to convert HTML to markdown
    const markdown = turndownService.turndown(html)

    // Clean up any extra whitespace and normalize line breaks
    return markdown
      .replace(/\n{3,}/g, '\n\n') // Replace multiple newlines with double newlines
      .replace(/\|(\s*)\|/g, '| $1 |') // Ensure proper spacing in table cells
      .replace(/\|\s*\n\s*\|/g, '|\n|') // Fix table line breaks
      .trim()
  } catch (error) {
    console.error('Error converting HTML to markdown:', error)
    // Fallback to simple HTML tag removal if turndown fails
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\n{3,}/g, '\n\n')
      .trim()
  }
}
