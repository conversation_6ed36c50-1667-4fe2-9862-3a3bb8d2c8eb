# Canvas Editor Table Documentation

## Table Features

The Canvas Editor includes comprehensive table support with visible borders and interactive features.

### Table Styling

Tables in the Canvas Editor have:
- **Visible borders**: All table borders are clearly visible with a gray color scheme
- **Header styling**: Table headers have a distinct background color and bold text
- **Hover effects**: Cells highlight when hovered
- **Focus states**: Clear focus indicators for editing
- **Responsive design**: Tables adapt to different screen sizes
- **Dark mode support**: Automatic styling for dark themes

### Table Classes

- `.canvas-table`: Main table container
- `.canvas-table-header`: Table header cells
- `.canvas-table-cell`: Regular table cells
- `.canvas-table-row`: Table rows

### Creating Tables

Use the slash command `/table` to insert a new table, or use the table commands in the editor.

### Table Operations

The editor supports:
- Adding/removing rows and columns
- Resizing columns
- Merging cells (if supported by your TipTap configuration)
- Moving tables with drag-and-drop

### Styling Customization

To customize table appearance, modify the styles in `table-styles.css`:

```css
.canvas-editor-content .canvas-table {
  border: 2px solid #your-color;
  /* Add your custom styles */
}
```

### Border Visibility

All table borders are styled with:
- Main table border: 2px solid gray
- Cell borders: 1px solid gray
- No transparent or invisible borders
- Clear visual separation between cells

This ensures tables are always clearly visible and professional-looking in the editor.
