# Drag Handle Extension for TipTap Notion-like Editor

This extension adds drag-and-drop functionality to all block elements in the TipTap editor, providing a Notion-like editing experience.

## Features

- **6-Dot Drag Handle**: A visual drag handle with 6 dots (3x2 grid) appears when hovering over block elements
- **Block-Level Dragging**: Supports dragging for all block elements including:
  - Paragraphs (`<p>`)
  - Headings (`<h1>` to `<h6>`)
  - Blockquotes (`<blockquote>`)
  - Code blocks (`<pre>`)
  - Lists (`<ul>`, `<ol>`)
  - Tables (`<table>`)
  - Horizontal rules (`<hr>`)

## How It Works

### Visual Feedback
- **Hover**: When you hover over any block element, a subtle background highlight appears
- **Drag Handle**: A 6-dot drag handle appears to the left of the block
- **Drop Target**: When dragging, a blue line appears to indicate where the block will be dropped

### Interaction
1. **Hover** over any block element to reveal the drag handle
2. **Click and hold** the drag handle to start dragging
3. **Move** the cursor up or down to reorder blocks
4. **Release** to drop the block in its new position

### Styling
- The drag handle uses a translucent background with backdrop blur for a modern look
- Responsive design with smaller handles on mobile devices
- Dark mode support with appropriate color schemes
- Smooth animations and transitions

## Implementation Details

### Extension Structure
```typescript
// The drag handle extension is automatically included in CanvasEditor
import { CanvasEditor } from './canvas-editor';

// Or use it directly
import { DragHandleExtension } from './index';
```

### CSS Classes
- `.drag-handle`: The main drag handle element
- `.drop-target`: Applied to blocks when they're valid drop targets
- `.canvas-editor-content`: Applied to the editor container

### Configuration
The extension accepts options for customization:
```typescript
DragHandleExtension.configure({
  dragHandleWidth: 24, // Width of the drag handle in pixels
})
```

## Usage Examples

### Basic Usage
```tsx
import { CanvasEditor } from '../../../helper/component/tiptap';

<CanvasEditor
  content="Your content here"
  placeholder="Type '/' for commands, or start typing..."
  onChange={(content) => console.log(content)}
  editable={true}
/>
```

### With Custom Styling
```tsx
<CanvasEditor
  content={content}
  onChange={onChange}
  style={{
    minHeight: '300px',
    padding: '16px'
  }}
  className="custom-editor"
/>
```

## Browser Support

- **Chrome/Chromium**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile**: Touch-friendly drag handles on mobile devices

## Accessibility

- The drag handle is keyboard accessible
- Screen reader support for drag operations
- Focus management during drag operations
- ARIA labels for better accessibility

## Performance

- Lightweight implementation with minimal DOM manipulation
- Efficient event handling with proper cleanup
- Smooth animations using CSS transitions
- Optimized for large documents

## Troubleshooting

### Drag Handle Not Appearing
- Ensure the `DragHandleExtension` is included in your editor extensions
- Check that CSS files are properly imported
- Verify that the editor container has proper positioning

### Drag Operations Not Working
- Ensure the editor is in editable mode
- Check browser console for JavaScript errors
- Verify that event listeners are properly attached

### Styling Issues
- Import the `drag-handle.css` file
- Check for CSS conflicts with other styles
- Ensure proper z-index values for overlay elements

## Future Enhancements

- Multi-block selection and dragging
- Copy/paste integration with drag operations
- Custom drag handle icons
- Drag preview customization
- Undo/redo integration for drag operations
