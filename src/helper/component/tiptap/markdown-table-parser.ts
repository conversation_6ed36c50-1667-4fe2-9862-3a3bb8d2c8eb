/**
 * Markdown Table Parser Extension for TipTap
 * Automatically converts markdown table syntax to actual table nodes
 */

import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { TextSelection } from '@tiptap/pm/state';

// Plugin key for the markdown table parser
const markdownTableParserKey = new PluginKey('markdownTableParser');

// Function to detect if text contains a markdown table
const isMarkdownTable = (text: string): boolean => {
  const lines = text.split('\n');
  if (lines.length < 3) return false;
  
  // Check if we have at least a header row, separator row, and one data row
  const headerRow = lines[0];
  const separatorRow = lines[1];
  
  // Header row should have pipes
  if (!headerRow.includes('|')) return false;
  
  // Separator row should contain dashes and pipes
  if (!separatorRow.match(/^\|?[\s-:|]+\|?$/)) return false;
  
  return true;
};

// Function to parse markdown table and convert to table structure
const parseMarkdownTable = (text: string) => {
  const lines = text.split('\n').filter(line => line.trim());
  if (lines.length < 3) return null;
  
  const headerLine = lines[0];
  const dataLines = lines.slice(2);
  
  // Parse header
  const headers = headerLine.split('|')
    .map(cell => cell.trim())
    .filter(cell => cell.length > 0);
  
  // Parse data rows
  const rows = dataLines.map(line => 
    line.split('|')
      .map(cell => cell.trim())
      .filter(cell => cell.length > 0)
  ).filter(row => row.length > 0);
  
  return { headers, rows };
};

export const MarkdownTableParserExtension = Extension.create({
  name: 'markdownTableParser',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: markdownTableParserKey,
        props: {
          handlePaste: (view, event, slice) => {
            // Get pasted text
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;
            
            const text = clipboardData.getData('text/plain');
            if (!text || !isMarkdownTable(text)) return false;
            
            // Parse the markdown table
            const tableData = parseMarkdownTable(text);
            if (!tableData) return false;
            
            const { headers, rows } = tableData;
            const { state, dispatch } = view;
            const { tr } = state;
            
            // Create table node
            const tableNode = state.schema.nodes.table.create(null, [
              // Create header row
              state.schema.nodes.tableRow.create(null, 
                headers.map(headerText => 
                  state.schema.nodes.tableHeader.create(null, 
                    headerText ? state.schema.text(headerText) : null
                  )
                )
              ),
              // Create data rows
              ...rows.map(row => 
                state.schema.nodes.tableRow.create(null,
                  row.map(cellText => 
                    state.schema.nodes.tableCell.create(null,
                      cellText ? state.schema.text(cellText) : null
                    )
                  )
                )
              )
            ]);
            
            // Insert the table
            const { from } = state.selection;
            tr.replaceWith(from, from, tableNode);
            
            // Set selection after the table
            const resolvedPos = tr.doc.resolve(from + tableNode.nodeSize);
            tr.setSelection(TextSelection.near(resolvedPos));
            
            dispatch(tr);
            return true;
          },
          
          handleTextInput: (view, from, to, text) => {
            // Check if we're typing a table separator row (like |---|---|)
            if (!text.match(/[-|:\s]/)) return false;
            
            const { state } = view;
            const { doc } = state;
            
            // Get the current paragraph content including the new text
            const $from = doc.resolve(from);
            const paragraph = $from.parent;
            
            if (paragraph.type.name !== 'paragraph') return false;
            
            // Get the full text of the current paragraph
            const paragraphText = paragraph.textContent + text;
            
            // Look back a few paragraphs to see if we're building a table
            const parentPos = $from.before($from.depth);
            let textToCheck = '';
            
            // Collect text from current and previous paragraphs
            const parentNode = $from.node($from.depth - 1);
            for (let i = Math.max(0, $from.index() - 2); i <= $from.index(); i++) {
              if (i < parentNode.childCount) {
                const node = parentNode.child(i);
                if (node.type.name === 'paragraph') {
                  if (i === $from.index()) {
                    textToCheck += paragraphText + '\n';
                  } else {
                    textToCheck += node.textContent + '\n';
                  }
                }
              }
            }
            
            // Check if this looks like a markdown table
            if (isMarkdownTable(textToCheck.trim())) {
              const tableData = parseMarkdownTable(textToCheck.trim());
              if (!tableData) return false;
              
              const { headers, rows } = tableData;
              const { dispatch } = view;
              const tr = state.tr;
              
              // Create table node
              const tableNode = state.schema.nodes.table.create(null, [
                // Create header row
                state.schema.nodes.tableRow.create(null, 
                  headers.map(headerText => 
                    state.schema.nodes.tableHeader.create(null, 
                      headerText ? state.schema.text(headerText) : null
                    )
                  )
                ),
                // Create data rows
                ...rows.map(row => 
                  state.schema.nodes.tableRow.create(null,
                    row.map(cellText => 
                      state.schema.nodes.tableCell.create(null,
                        cellText ? state.schema.text(cellText) : null
                      )
                    )
                  )
                )
              ]);
              
              // Find the start position of the table text
              const startOfTable = Math.max(0, parentPos - textToCheck.length + paragraphText.length);
              
              // Replace the markdown table text with actual table
              tr.replaceWith(startOfTable, to + text.length, tableNode);
              
              // Set selection after the table
              const resolvedPos = tr.doc.resolve(startOfTable + tableNode.nodeSize);
              tr.setSelection(TextSelection.near(resolvedPos));
              
              dispatch(tr);
              return true;
            }
            
            return false;
          },
        },
      }),
    ];
  },
});

export default MarkdownTableParserExtension;
