/* Table Controls Toolbar Styling */
.table-controls-toolbar {
  position: fixed;
  z-index: 1001;
  pointer-events: auto;
}

.table-controls-content {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Button styling */
.table-controls-content .ant-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.table-controls-content .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-controls-content .ant-btn-dangerous {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.table-controls-content .ant-btn-dangerous:hover {
  background-color: #ff4d4f;
  color: white;
}

/* <PERSON>de controls when table is not focused */
.canvas-editor-content:not(.table-focused) .table-controls-toolbar {
  display: none;
}

/* Add visual feedback for selected table cell */
.canvas-editor-content table td.selected-cell,
.canvas-editor-content table th.selected-cell {
  background-color: rgba(24, 144, 255, 0.1);
  position: relative;
  box-shadow: inset 0 0 0 2px rgba(24, 144, 255, 0.3);
}
