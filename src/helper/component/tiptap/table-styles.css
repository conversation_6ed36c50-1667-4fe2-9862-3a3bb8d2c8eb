/* Canvas Editor Table Styles */

/* Table Controls Integration */
.canvas-editor-content.table-focused {
  position: relative;
}

/* Selected table cell styling */
.canvas-editor-content table td.selected-cell,
.canvas-editor-content table th.selected-cell {
  background-color: rgba(24, 144, 255, 0.08) !important;
  position: relative;
  box-shadow: inset 0 0 0 2px rgba(24, 144, 255, 0.3);
}

/* Hover effects for table cells when controls are available */
.canvas-editor-content.table-focused table td:hover,
.canvas-editor-content.table-focused table th:hover {
  background-color: rgba(24, 144, 255, 0.04) !important;
  cursor: pointer;
}

/* Ensure table controls don't interfere with table layout */
.canvas-editor-content table {
  position: relative;
}

/* Make table cell content more readable when selected */
.canvas-editor-content table td.selected-cell *,
.canvas-editor-content table th.selected-cell * {
  position: relative;
  z-index: 1;
}

/* Global text selection for canvas editor - ensure consistent selection styling */
.canvas-editor-content ::selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

.canvas-editor-content ::-moz-selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

.ProseMirror ::selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

.ProseMirror ::-moz-selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Canvas Editor Content Wrapper - Enable horizontal scrolling for large tables */
.canvas-editor-content {
  overflow-x: auto !important;
  overflow-y: visible !important;
  width: 100%;
  min-width: 100%;
}

/* ProseMirror editor content */
.ProseMirror {
  overflow-x: auto !important;
  overflow-y: visible !important;
  min-width: 100%;
  padding: 16px;
  outline: none;
}

/* Ensure tables can expand beyond container width */
.ProseMirror .tableWrapper {
  overflow-x: auto !important;
  width: 100%;
  margin: 1rem 0;
}

.ProseMirror .tableWrapper table {
  width: auto !important;
  min-width: 100% !important;
}

/* Main table container - make borders very visible and allow larger widths */
.canvas-editor-content .canvas-table,
.canvas-editor-content table.canvas-table,
.canvas-editor-content table,
.ProseMirror .canvas-table,
.ProseMirror table.canvas-table,
.ProseMirror table {
  border-collapse: collapse !important;
  border: 2px solid #374151 !important;
  width: auto !important;
  min-width: 100% !important;
  margin: 1rem 0 !important;
  font-size: 14px !important;
  background-color: #ffffff !important;
  border-radius: 6px !important;
  overflow: visible !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  table-layout: auto !important; /* Allow columns to expand based on content */
}

/* Table wrapper for horizontal scrolling */
.canvas-editor-content,
.ProseMirror {
  overflow-x: auto !important;
  overflow-y: visible !important;
}

/* Table header cells - strong borders and flexible width with text wrapping */
.canvas-editor-content .canvas-table-header,
.canvas-editor-content th.canvas-table-header,
.canvas-editor-content th,
.canvas-editor-content thead th,
.ProseMirror .canvas-table-header,
.ProseMirror th.canvas-table-header,
.ProseMirror th,
.ProseMirror thead th {
  border: 2px solid #374151 !important;
  padding: 12px 16px !important;
  background-color: #f9fafb !important;
  font-weight: 600 !important;
  color: #374151 !important;
  text-align: left !important;
  position: relative !important;
  min-width: 150px !important;
  max-width: 400px !important;
  width: auto !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  line-height: 1.4 !important;
  max-height: calc(1.4em * 5) !important; /* Max 5 lines */
  overflow: hidden !important;
}

/* Regular table cells - strong borders and flexible width with text wrapping */
.canvas-editor-content .canvas-table-cell,
.canvas-editor-content td.canvas-table-cell,
.canvas-editor-content td,
.canvas-editor-content tbody td,
.ProseMirror .canvas-table-cell,
.ProseMirror td.canvas-table-cell,
.ProseMirror td,
.ProseMirror tbody td {
  border: 2px solid #374151 !important;
  padding: 12px 16px !important;
  background-color: #ffffff !important;
  color: #374151 !important;
  vertical-align: top !important;
  position: relative !important;
  min-width: 150px !important;
  max-width: 400px !important;
  width: auto !important;
  min-height: 40px !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  line-height: 1.4 !important;
  max-height: calc(1.4em * 5) !important; /* Max 5 lines */
  overflow: hidden !important;
}

/* Table rows - strong borders */
.canvas-editor-content .canvas-table-row,
.canvas-editor-content tr.canvas-table-row,
.canvas-editor-content tr,
.ProseMirror .canvas-table-row,
.ProseMirror tr.canvas-table-row,
.ProseMirror tr {
  border-bottom: 2px solid #374151 !important;
}

.canvas-editor-content .canvas-table-row:last-child {
  border-bottom: none;
}

/* Hover effects */
.canvas-editor-content .canvas-table-cell:hover,
.canvas-editor-content .canvas-table-header:hover {
  background-color: #f3f4f6;
}

/* Focus styles for editing */
.canvas-editor-content .canvas-table-cell:focus,
.canvas-editor-content .canvas-table-header:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  background-color: #eff6ff;
  white-space: pre-wrap !important;
  overflow: visible !important;
  max-height: none !important; /* Remove height limit when editing */
  min-width: 200px !important;
}

/* Text selection styles for table cells */
.canvas-editor-content .canvas-table-cell::selection,
.canvas-editor-content .canvas-table-header::selection,
.canvas-editor-content .canvas-table-cell *::selection,
.canvas-editor-content .canvas-table-header *::selection,
.ProseMirror .canvas-table-cell::selection,
.ProseMirror .canvas-table-header::selection,
.ProseMirror .canvas-table-cell *::selection,
.ProseMirror .canvas-table-header *::selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Firefox text selection */
.canvas-editor-content .canvas-table-cell::-moz-selection,
.canvas-editor-content .canvas-table-header::-moz-selection,
.canvas-editor-content .canvas-table-cell *::-moz-selection,
.canvas-editor-content .canvas-table-header *::-moz-selection,
.ProseMirror .canvas-table-cell::-moz-selection,
.ProseMirror .canvas-table-header::-moz-selection,
.ProseMirror .canvas-table-cell *::-moz-selection,
.ProseMirror .canvas-table-header *::-moz-selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* TipTap specific selection styles for table content */
.ProseMirror-selectednode .canvas-table-cell,
.ProseMirror-selectednode .canvas-table-header {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

/* Multi-cell selection styles */
.canvas-editor-content .selectedCell,
.canvas-editor-content .canvas-table-cell.selectedCell,
.canvas-editor-content .canvas-table-header.selectedCell,
.ProseMirror .selectedCell,
.ProseMirror .canvas-table-cell.selectedCell,
.ProseMirror .canvas-table-header.selectedCell {
  background-color: #dbeafe !important;
  border-color: #3b82f6 !important;
  color: #1e40af !important;
}

/* Ensure text selection works properly across all table content */
.canvas-editor-content table,
.canvas-editor-content table *,
.ProseMirror table,
.ProseMirror table * {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* Override any TipTap specific selection styles that might cause issues */
.ProseMirror-selectednode,
.ProseMirror-selectednode * {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

/* Ensure text highlight works in all table contexts */
.canvas-editor-content .canvas-table .highlight,
.canvas-editor-content .canvas-table-cell .highlight,
.canvas-editor-content .canvas-table-header .highlight,
.ProseMirror .canvas-table .highlight,
.ProseMirror .canvas-table-cell .highlight,
.ProseMirror .canvas-table-header .highlight {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Cells with long content - expand width dynamically */
.canvas-editor-content .canvas-table-cell.has-long-content,
.canvas-editor-content .canvas-table-header.has-long-content,
.ProseMirror .canvas-table-cell.has-long-content,
.ProseMirror .canvas-table-header.has-long-content {
  max-width: 500px !important; /* Allow wider columns for long content */
  width: auto !important;
}

/* When content would overflow the 5-line limit, expand column width */
.canvas-editor-content .canvas-table-cell[data-content-length="long"],
.canvas-editor-content .canvas-table-header[data-content-length="long"],
.ProseMirror .canvas-table-cell[data-content-length="long"],
.ProseMirror .canvas-table-header[data-content-length="long"] {
  max-width: 600px !important;
  width: auto !important;
}

/* Selected cell styles */
.canvas-editor-content .canvas-table-cell.selectedCell,
.canvas-editor-content .canvas-table-header.selectedCell {
  background-color: #dbeafe;
  border-color: #3b82f6;
  white-space: pre-wrap !important;
  overflow: visible !important;
}

/* Allow content to expand when editing */
.canvas-editor-content .canvas-table-cell[contenteditable="true"],
.canvas-editor-content .canvas-table-header[contenteditable="true"] {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow: visible !important;
  max-width: none !important;
  width: auto !important;
}

/* Table resize handles */
.canvas-table .tableWrapper {
  position: relative;
  overflow-x: auto;
}

.canvas-table .resize-cursor {
  cursor: col-resize;
}

/* Column resize indicator */
.canvas-table .column-resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #3b82f6;
  z-index: 10;
  cursor: col-resize;
}

/* Empty cell placeholder */
.canvas-editor-content .canvas-table-cell:empty::before,
.canvas-editor-content .canvas-table-header:empty::before {
  content: '';
  display: inline-block;
  min-height: 1em;
}

/* Table controls (when table is selected) */
.canvas-table-controls {
  position: absolute;
  top: -30px;
  left: 0;
  display: flex;
  gap: 4px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 20;
}

.canvas-table-controls button {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.canvas-table-controls button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* Additional TipTap selection overrides for better cross-cell selection */
.ProseMirror .ProseMirror-textselection-mark {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

.ProseMirror .ProseMirror-gapcursor {
  background-color: #3b82f6 !important;
}

/* Force selection styling on table content */
.canvas-editor-content table td[data-selected],
.canvas-editor-content table th[data-selected],
.ProseMirror table td[data-selected],
.ProseMirror table th[data-selected] {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

/* Handle text selection inside selected table cells */
.canvas-editor-content table td[data-selected] ::selection,
.canvas-editor-content table th[data-selected] ::selection,
.ProseMirror table td[data-selected] ::selection,
.ProseMirror table th[data-selected] ::selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

.canvas-editor-content table td[data-selected] ::-moz-selection,
.canvas-editor-content table th[data-selected] ::-moz-selection,
.ProseMirror table td[data-selected] ::-moz-selection,
.ProseMirror table th[data-selected] ::-moz-selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .canvas-editor-content .canvas-table {
    border-color: #374151;
    background-color: #1f2937;
  }

  .canvas-editor-content .canvas-table-header {
    border-color: #374151;
    background-color: #374151;
    color: #f9fafb;
  }

  .canvas-editor-content .canvas-table-cell {
    border-color: #374151;
    background-color: #1f2937;
    color: #f9fafb;
  }

  .canvas-editor-content .canvas-table-row {
    border-color: #374151;
  }

  .canvas-editor-content .canvas-table-cell:hover,
  .canvas-editor-content .canvas-table-header:hover {
    background-color: #4b5563;
  }

  .canvas-editor-content .canvas-table-cell:focus,
  .canvas-editor-content .canvas-table-header:focus {
    background-color: #1e3a8a;
    outline-color: #60a5fa;
  }

  .canvas-editor-content .canvas-table-cell.selectedCell,
  .canvas-editor-content .canvas-table-header.selectedCell {
    background-color: #1e40af;
    border-color: #60a5fa;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .canvas-editor-content .canvas-table,
  .ProseMirror .canvas-table {
    font-size: 12px;
  }
  
  .canvas-editor-content .canvas-table-cell,
  .canvas-editor-content .canvas-table-header,
  .ProseMirror .canvas-table-cell,
  .ProseMirror .canvas-table-header {
    padding: 8px 12px;
    min-width: 100px;
    max-width: 200px;
    max-height: calc(1.3em * 4) !important; /* Reduce to 4 lines on mobile */
    line-height: 1.3 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
  }
  
  /* Ensure horizontal scrolling on mobile */
  .canvas-editor-content,
  .ProseMirror {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
}

/* Print styles */
@media print {
  .canvas-editor-content .canvas-table {
    border: 1px solid #000;
    page-break-inside: avoid;
  }

  .canvas-editor-content .canvas-table-cell,
  .canvas-editor-content .canvas-table-header {
    border: 1px solid #000;
  }
}
