import { CKEditor } from '@ckeditor/ckeditor5-react'
import { Editor } from 'ckeditor5-custom-build/build/ckeditor'
import React, { useEffect, useState } from 'react'
import { UploadAdapter } from './UploadAdapter'

const Ckeditor = React.forwardRef((props: any, ref: any) => {
  const [ckeditorData, setCkeditorData] = useState(props.data)
  const [showEditor, setShowEditor] = useState(false)
  useEffect(() => {
    setCkeditorData(props?.data)
    setTimeout(() => {
      setShowEditor(true)
    }, 0)
  }, [props?.data])

  const editorConfiguration = {
    toolbar: {
      viewportTopOffset: 80,
      items: props.comment ? [
        'blockQuote',
        'bold',
        'italic',
        'strikethrough',
        'underline',	
        'fontFamily',
        'alignment',
        'insertTable',
        'numberedList',
        'bulletedList',
      ] : [
        'blockQuote',
        'bold',
        'italic',
        'strikethrough',
        'underline',	
        'fontFamily',
        'alignment',
        'insertTable',
        'imageUpload',
        'numberedList',
        'bulletedList',
      ],
      shouldNotGroupWhenFull: true,
    },

    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableProperties',
        'tableCellProperties',
      ],
      defaultHeadings: { rows: 1 },
    },
    image: {
      styles: ['alignLeft', 'alignCenter', 'alignRight'],
      toolbar: [
        'imageStyle:alignLeft',
        'imageStyle:alignCenter',
        'imageStyle:alignRight',
        '|',
        '|',
        'imageTextAlternative',
      ],
    },
    placeholder: props.placeholder || ''
  }

  return (
    <>
      {showEditor ? <CKEditor
        ref={ref}
        editor={Editor}
        config={editorConfiguration}
        data={ckeditorData}
        onReady={(editor: any) => {
          if (editor) {
            editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
              return new UploadAdapter(loader);
            };
          }
        }}
        onChange={(event: any, editor: any) => {
          setCkeditorData(editor.getData())
          if (props.onChange) {
            props.onChange(editor.getData())
          }
        }}
        onBlur={(event: any, editor: any) => {
          if (props.validate) {
            props.validate(editor.getData())
          }
        }}
        onFocus={(event: any, editor: any) => { }}
      /> : <></>
      }
    </>
  )
})

export default Ckeditor
