export class UploadAdapter {
    private loader;
    constructor(loader: any) {
        this.loader = loader;
    }

    upload() {
        return this.loader.file
            .then(file => new Promise((resolve, reject) => {
                this._initListeners(resolve, reject, file);
            }));
    }

    // Initializes XMLHttpRequest listeners.
    _initListeners(resolve, reject, file) {
        resolve({
            default: this.loader.data
        });
    }
}