import { configureStore } from '@reduxjs/toolkit'
import createSagaMiddleware from 'redux-saga'
import { ApplicationState } from './types'
import rootReducer, { AppDataState } from './rootReducer'
import rootSaga from './sagas'
import moduleSaga from '../../store/saga'
import { createInjectorsEnhancer } from 'redux-injectors'

const configureAppStore = (initState: ApplicationState) => {
  const reduxSagaMonitorOptions = {}
  const sagaMiddleWare = createSagaMiddleware(reduxSagaMonitorOptions)
  const { run: runSaga } = sagaMiddleWare
  const middlewares = [sagaMiddleWare]
  const enhancers = [
    createInjectorsEnhancer({
      createReducer: rootReducer,
      runSaga,
    }),
  ]

  const store = configureStore({
    reducer: rootReducer(),
    preloadedState: initState,
    middleware: middlewares,
  })
  sagaMiddleWare.run(rootSaga)
  return store
}
export { configureAppStore }
export const store = configureAppStore(AppDataState)
// const sagaMiddleWare = createSagaMiddleware({});
// const configureAppStore = ()=>{
//   const store = createStore(rootReducer,applyMiddleware(sagaMiddleWare));
//   sagaMiddleWare.run(rootSaga);
//   return store;
// }
// export const store = configureAppStore();
