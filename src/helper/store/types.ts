import { Action as ReduxAction } from 'redux'
import AppState from '../../store/types'

export type ApplicationState = AppState
export type Action = ReduxAction
export interface TypeActon<T> extends Action {
  payload: T
}
// export interface ErrorAction extends Action {
//     payload: {error: Error}
// }
// export type ActionHandlers<T,P> = {
//     [key: string]: (draft: T, payload: P) => any
// }
