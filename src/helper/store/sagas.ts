import { all } from 'redux-saga/effects'
import MainModuleSaga from '../../store/saga'

export default function* rootSaga(): Generator<any, any, any> {
  let mainModuleSaga
  try {
    //const mainModuleSagaImport = require("@/store/saga");
    //mainModuleSaga = mainModuleSagaImport && mainModuleSagaImport.default;
    const mainModuleSagaImport = MainModuleSaga()
    mainModuleSaga = mainModuleSagaImport
  } catch (err) {}
  yield all([mainModuleSaga])
}
