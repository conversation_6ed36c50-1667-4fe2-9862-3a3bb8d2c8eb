import React, { useEffect } from 'react'
import Navigator from '../src/layout/router/navigator'
import { Provider } from 'react-redux'
import { store } from './helper/store/store'
import intl from './config/locale.config'
import { IntlProvider } from 'react-intl'
import { InteractionType, IPublicClientApplication } from '@azure/msal-browser'
import { MsalAuthenticationResult, MsalAuthenticationTemplate, MsalProvider } from '@azure/msal-react'
import { useHistory } from 'react-router-dom'
import { CustomNavigationClient } from './helper/NavigationClient'
import { loginRequest } from './authConfig'
import { Space, Spin, Typography } from 'antd'
import './App.less'
import { startTokenRefreshInterval } from './helper/api/aloApi'
import AuthNavigator from './layout/router/auth-navigator'

type AppProps = {
  pca: IPublicClientApplication
};

const App = ({ pca }: AppProps) => {
  const history = useHistory();
  const navigationClient = new CustomNavigationClient(history);
  pca.setNavigationClient(navigationClient);

  const authRequest = {
    ...loginRequest
  };

  const ErrorComponent: React.FC<MsalAuthenticationResult> = ({ error }) => {
    return <div className='app-loader'>
      <Space direction='vertical' align='center'>
        <Spin spinning></Spin>
        <Typography>An Error Occurred: {error ? error.errorCode : "unknown error"}</Typography>
      </Space>
    </div>;
  }

  const Loading = () => {
    return <div className='app-loader'>
      <Space direction='vertical' align='center'>
        <Spin spinning></Spin>
        <Typography>Authentication in progress...</Typography>
      </Space>
    </div>
  }

//   useEffect(() => {
//     const checkTokenExpiry = startTokenRefreshInterval()
//     return () => clearInterval(checkTokenExpiry);
// }, []);

  return (
    <MsalProvider instance={pca}>
      <Provider store={store}>
        <IntlProvider {...intl}>
          <MsalAuthenticationTemplate
            interactionType={InteractionType.Redirect}
            authenticationRequest={authRequest}
            errorComponent={ErrorComponent}
            loadingComponent={Loading}
          >
            <AuthNavigator></AuthNavigator>
          </MsalAuthenticationTemplate>

        </IntlProvider>
      </Provider>
    </MsalProvider>
  )
}

export default App
