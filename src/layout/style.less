@import '../commons.less';
@import '../modules/_shared/ai/components/styles.less';

.record-detail-right-control-container>.ant-space-item:last-child {
  height: 100%;
}

.record-detail-right-control-container {
  height: 100%;
}

.ant-spin-nested-loading {
  height: 100%;
}

.ant-spin-container {
  height: 100%;
}

//left menu
.antRowHeight {
  height: 100%;
}

.record-detail-left-control-container {
  height: 96%;
}

.record-detail-left-control-container>.ant-spin-nested-loading>.ant-spin-container>.ant-space-vertical {
  height: 100%;
}

.record-detail-left-control-container .ant-space-item:nth-child(2) {
  height: 100%;
}

.page-wrapper {
  padding-top: @navbar-height;
  padding-left: @sider-width ;
  transition: all ease-in-out .3s;
  overflow-x: hidden;

  &.fullfill {
    padding-left: 0 !important;
  }

  .ant-layout-sider {
    position: fixed;
    top: @navbar-height;
    left: 0;
    height: 100vh;
    transition: all ease-in-out .3s;
  }

  &.collapsed {
    padding-left: @sider-collapsed-width;

    .sider-menu-wrapper {
      .sidebar-content {
        overflow-y: auto;
      }
    }

    .siderbar-fixed-bottom {
      .version {
        display: none;
      }
    }
  }
}

.header-wrapper {
  position: fixed;
  z-index: 1;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ant-menu {
    min-width: @sider-width * 2;
    display: flex;
    align-items: center;
    justify-self: flex-start;

    &>button {
      margin-left: 20px;
    }
  }
}

.header-wrapper.ant-layout-header {
  padding: 0 !important;
  height: @navbar-height !important;
  line-height: @navbar-height !important;

  .ant-menu {
    line-height: @navbar-height !important;
  }
}

.sider-route-name {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
}

.ant-tooltip {
  .sider-route-name {
    color: #fff;
  }
}

.ant-menu-submenu-popup {
  .ant-menu-item {
    display: flex;
    align-items: center;
  }
}

.custom-sider-container {
  background-color: @sidebar-bg-color;
  width: @sider-width !important;
  max-width: @sider-width !important;
  min-width: @sider-width !important;

  &-collapsed {
    background-color: @sidebar-bg-color;
    width: @sider-collapsed-width !important;
    max-width: @sider-collapsed-width !important;
    min-width: @sider-collapsed-width !important;

    .project-name {
      padding-left: 10px !important;
      padding-right: 10px !important;

      .project-logo {
        width: 36px !important;
        min-width: 36px !important;
        height: 36px !important;
      }
    }
  }
}

.sider-menu-wrapper {
  overflow: hidden;
  height: 100%;

  .sidebar-content {
    height: calc(100% - 42px - @navbar-height);
    overflow-y: scroll;
    overflow-x: hidden;

    .sidebar-menu {
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  .project-and-collapse {
    padding: 0 1rem;
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-collapsed {
      margin-top: 1rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .project-text {
      opacity: .4;
      margin-bottom: 5px;
    }
  }

  .project-name {
    padding: 0 15px;
    display: flex;
    align-items: center;

    & span {
      font-size: 16px;
      font-weight: 700;
      color: @menu-item-text-color;
    }

    .project-logo {
      margin-right: 10px;
      height: 48px;
      width: 48px;
      min-width: 48px;
      border-radius: 3px;
      overflow: hidden;
      transition: all ease-in-out .3s;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .generate-srs-btn-container {
    padding: 10px;

    &-collapsed {
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
    }
  }

  .sider-submenu {
    ul {
      background-color: #eeeeee;
    }

    .ant-menu-submenu-title {
      padding-left: 16px !important;

      span {
        font-size: 14px;
      }
    }

    .ant-menu-item::after {
      border-right: none;
    }

    .sider-ndmenu {
      padding-left: 16px !important;
    }

    &-collapsed {
      .ant-menu-item-group-title {
        display: none;
      }
    }
  }

  .ant-menu {
    background-color: transparent !important;
    border: none;
    color: @menu-item-text-color;

    >.ant-menu-submenu {
      >.ant-menu-sub {
        .ant-menu-item {
          padding-left: 32px !important;
        }
      }
    }

    .ant-menu-submenu-title {
      margin: 0;
      color: @menu-heading-text-color !important;
    }

    .ant-menu-submenu-title .ant-menu-submenu-arrow,
    a {
      color: @menu-item-text-color !important;
    }

    .ant-menu-submenu-open {
      >.ant-menu-submenu-title {
        background-color: rgba(9, 30, 66, 0.08);
        font-weight: 500;
        border-radius: 3px;
      }
    }

    .ant-menu-item {
      width: 100%;
      height: 56px;
      margin: 0 !important;
      display: flex;
      justify-items: center;
      align-items: center;
      color: @text-color !important;
      border-radius: 3px;

      a {
        display: block;

        &::before {
          display: none;
        }
      }

      &:hover {
        background-color: rgba(9, 30, 66, 0.08);
      }

      &>.custom-svg-icon {
        min-width: 18px !important;
        min-height: 18px !important;
        height: 18px;
        width: 18px;
        display: flex;
        align-items: center;
        color: @text-color !important;

        &>svg {
          width: 100%;
          height: auto;
        }

        &>svg path {
          fill: @text-color;
        }
      }

      &-selected {
        background-color: rgba(9, 30, 66, 0.08) !important;

        .sider-route-name {
          font-weight: 600;
        }
      }
    }

    .ant-menu-title-content {
      margin-left: 10px;
    }
  }

  .siderbar-fixed-bottom {
    padding: 5px 10px;
    margin-top: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .version {
      font-size: 13px;
      opacity: 0.4;
    }
  }

  .ant-menu-sub.ant-menu-inline>.ant-menu-item,
  .ant-menu-sub.ant-menu-inline>.ant-menu-submenu>.ant-menu-submenu-title {
    height: 34px;
    line-height: 34px;
  }

  .ant-menu-vertical>.ant-menu-item,
  .ant-menu-vertical-left>.ant-menu-item,
  .ant-menu-vertical-right>.ant-menu-item,
  .ant-menu-inline>.ant-menu-item,
  .ant-menu-vertical>.ant-menu-submenu>.ant-menu-submenu-title,
  .ant-menu-vertical-left>.ant-menu-submenu>.ant-menu-submenu-title,
  .ant-menu-vertical-right>.ant-menu-submenu>.ant-menu-submenu-title,
  .ant-menu-inline>.ant-menu-submenu>.ant-menu-submenu-title {
    height: 38px;
    line-height: 38px;
  }
}

.ant-layout-content.main-content-view {
  background-color: #ffffff;
  max-height: calc(100vh - @navbar-height);
  overflow: auto;
  min-height: calc(100vh - @navbar-height);
}

.logo-and-header-menu {
  display: flex;

  .logo-container {
    width: @sider-width;
    padding-left: 1rem;

    img {
      height: 29px;
    }
  }
}

.app-loader {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, .6);
}

.rq-project-menu {
  height: 24px !important;
  line-height: 24px !important;

  a {
    display: flex;
    align-items: center;
  }
}

.tableDangerous figure.table {
  height: auto !important
}