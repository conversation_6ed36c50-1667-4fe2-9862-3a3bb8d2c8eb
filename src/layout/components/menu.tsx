import { Menu } from "antd"
import { useEffect, useState } from "react"
import { Link, useLocation } from "react-router-dom"
import intl from "../../config/locale.config"
import { APP_ROLES, APP_ROUTES, PROJECT_PREFIX } from "../../constants"
import { extractProjectCode, getProjectMethodology, hasCommonRole, hasRole } from "../../helper/share"

const { SubMenu } = Menu

interface AppMenuComponentProps {
    collapsed: boolean,
    menus: any[],
    isCommon?: boolean,
    onCustomMenuClick: any,
    isAdmin?: boolean
}
const AppMenuComponent = ({ collapsed, menus, isCommon = false, isAdmin = false, onCustomMenuClick }: AppMenuComponentProps) => {
    const [keyCurrent, setKeyCurrent] = useState<string>("")
    const [openKeys, setOpenKeys] = useState<any>([])
    const [storedKeyCurrent, setStoredKeyCurrent] = useState<any>([])
    const [storedOpenKeys, setStoredOpenKeys] = useState<any>([])
    const location = useLocation();
    const projectCode = extractProjectCode();
    const projectMethodology = getProjectMethodology(projectCode);

    useEffect(() => {
        if (storedKeyCurrent && storedKeyCurrent.length > 0) {
            setKeyCurrent(storedKeyCurrent);
        }
        if (storedOpenKeys && storedOpenKeys.length > 0) {
            setOpenKeys(storedOpenKeys);
        }
        setStoredKeyCurrent(keyCurrent);
        setStoredOpenKeys(openKeys);
    }, [collapsed])

    useEffect(() => {
        if (isCommon && !isAdmin) {
            const pathName = location.pathname;
            if (pathName.split('/')?.length > 3) {
                setKeyCurrent('/' + pathName.split('/')[1] + '/' + pathName.split('/')[2] + 's')
            } else {
                setKeyCurrent(pathName)
            }
        } else if (!isCommon && !isAdmin) {
            const pathName = location.pathname;
            const splitPathName = `/${pathName.split('/')[3]}`
            if (pathName.split('/')?.length > 4) {
                setKeyCurrent(splitPathName + 's')
            } else {
                setKeyCurrent(splitPathName)
            }
        } else if (isAdmin) {
            const pathName = location.pathname;
            setKeyCurrent(pathName)
        }
    }, [location])

    useEffect(() => {
        if (keyCurrent) {
            menus.forEach((m: any, index: number) => {
                const exists = m.chidlren ? m.chidlren.findIndex((e) => e.path === keyCurrent || e.path === keyCurrent + 's') : -1;
                if (exists != -1) {
                    const m_key_1 = `${m.label}_${index}`
                    const m_key_2 = `${keyCurrent}_${exists}`
                    setOpenKeys([...openKeys, m_key_1]);
                    setKeyCurrent(m_key_2)
                } else {
                    m.chidlren?.forEach((n: any, idx: number) => {
                        const existsIdx = n.chidlren ? n.chidlren.findIndex((y) => y.path === keyCurrent || y.path === keyCurrent + 's') : -1;
                        if (existsIdx != -1) {
                            const m_key_3 = `${m.label}_${index}`
                            const m_key_4 = `${n.label}_${idx}`
                            const m_key_5 = `${keyCurrent}_${existsIdx}`
                            setOpenKeys([...openKeys, m_key_3, m_key_4]);
                            setKeyCurrent(m_key_5)
                        }
                    })
                }
            })
            if (keyCurrent.indexOf(APP_ROUTES.VALIDATION_RESULT) != -1) {
                setOpenKeys([...openKeys, `${menus[menus.length - 1].label}_${menus.length - 1}`]);
                setKeyCurrent('#validate_srs_0')
            }
            if (collapsed) {
                setOpenKeys([]);
            }
        }
    }, [keyCurrent])

    useEffect(() => {
        if (!collapsed) {
            setOpenKeys(storedOpenKeys)
        }
    }, [collapsed])

    return <Menu mode="inline" style={{ backgroundColor: '#eeeeee', paddingTop: 20 }}
        selectedKeys={[keyCurrent]}
        openKeys={openKeys}
        onOpenChange={(keys) => setOpenKeys(keys)}
        className={`sidebar-menu ${keyCurrent} ${collapsed ? 'sider-submenu-collapsed' : 'sider-submenu'}`}>
        {
            menus?.map((menu, id1) => {
                if (menu?.projectMethodology && menu?.projectMethodology !== projectMethodology?.toLowerCase()) {
                    return <Menu.Item key={`${menu.path}_${id1}`} style={{ display: 'none' }}></Menu.Item>
                }
                if (menu.checkRole && menu.checkRole.length > 0) {
                    const checkRole = menu.checkRole.findIndex((e) => { return menu.isComon || menu.isAdmin ? hasCommonRole(e) : hasRole(e) })
                    if (checkRole == -1) return <Menu.Item key={`${menu.path}_${id1}`} style={{ display: 'none' }}></Menu.Item>;
                }
                
                if (menu.isGroup) {
                    return <SubMenu icon={menu.icon} className={collapsed ? 'sider-submenu-collapsed' : 'sider-submenu'} key={`${menu.label}_${id1}`} title={intl.formatMessage({ id: menu.label })}>
                        {
                            menu.chidlren?.map((ndMenu, id2) => {
                                if (ndMenu?.projectMethodology && ndMenu?.projectMethodology !== projectMethodology?.toLowerCase()) {
                                    return <Menu.Item key={`${ndMenu.path}_${id2}`} style={{ display: 'none' }}></Menu.Item>
                                }
                                if (ndMenu.checkRole && ndMenu.checkRole.length > 0) {
                                    const checkRole = ndMenu.checkRole.findIndex((e) => { return ndMenu.isComon ? hasCommonRole(e) : hasRole(e) })
                                    if (checkRole == -1) return <Menu.Item key={`${ndMenu.path}_${id2}`} style={{ display: 'none' }}></Menu.Item>;
                                }
                                if (ndMenu.isGroup) {
                                    if (ndMenu.label === "app.menu.utilities" && (hasRole(APP_ROLES.BA) || hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA_LEAD)) && ndMenu.isGroup) {
                                        return <div style={{ display: 'none' }}></div>
                                    } else {
                                        return <SubMenu icon={ndMenu.icon} className={collapsed ? 'sider-ndmenu-collapsed' : 'sider-ndmenu'} key={`${ndMenu.label}_${id2}`} title={intl.formatMessage({ id: ndMenu.label })}>
                                            {
                                                ndMenu.chidlren?.map((rdMenu, id3) => {
                                                    if (ndMenu?.projectMethodology && ndMenu?.projectMethodology !== projectMethodology?.toLowerCase()) {
                                                        return <Menu.Item key={`${rdMenu.path}_${id3}`} style={{ display: 'none' }}></Menu.Item>
                                                    }
                                                    if (rdMenu.checkRole && rdMenu.checkRole.length > 0) {
                                                        const checkRole = rdMenu.checkRole.findIndex((e) => { return rdMenu.isComon ? hasCommonRole(e) : hasRole(e) })
                                                        if (checkRole == -1) return <Menu.Item key={`${rdMenu.path}_${id3}`} style={{ display: 'none' }}></Menu.Item>;
                                                    }
                                                    return <Menu.Item key={`${rdMenu.path}_${id3}`} icon={rdMenu.icon} style={!collapsed ? { paddingLeft: '2rem' } : {}}>
                                                        <Link title={intl.formatMessage({ id: rdMenu.label })} to={(isCommon || isAdmin) ? rdMenu.path : `${PROJECT_PREFIX}${extractProjectCode()}${rdMenu.path}`}><span className="sider-route-name">{intl.formatMessage({ id: rdMenu.label })}</span></Link>

                                                    </Menu.Item>
                                                })
                                            }
                                        </SubMenu>
                                    }
                                } else {
                                    return <Menu.Item key={`${ndMenu.path}_${id2}`} icon={ndMenu.icon} style={!collapsed ? { paddingLeft: '1rem' } : {}}>
                                        <>
                                            {
                                                ndMenu.isCustom ?
                                                    <a style={{ cursor: 'pointer' }} onClick={(e) => onCustomMenuClick(e, ndMenu)}><span className="sider-route-name">{intl.formatMessage({ id: ndMenu.label })}</span></a> :
                                                    <Link title={intl.formatMessage({ id: ndMenu.label })} to={(isCommon || isAdmin) ? ndMenu.path : `${PROJECT_PREFIX}${extractProjectCode()}${ndMenu.path}`}><span className="sider-route-name">{intl.formatMessage({ id: ndMenu.label })}</span></Link>
                                            }
                                        </>
                                    </Menu.Item>
                                }
                            })
                        }
                    </SubMenu>
                } else {
                    return <Menu.Item key={menu.path} icon={menu.icon} style={!collapsed ? { paddingLeft: '1rem' } : {}} className={menu.className || ''}>
                        <Link title={intl.formatMessage({ id: menu.label })} to={isCommon || isAdmin ? menu.path : `${PROJECT_PREFIX}${extractProjectCode()}${menu.path}`}><span className="sider-route-name">{intl.formatMessage({ id: menu.label })}</span></Link>
                    </Menu.Item>
                }
            })
        }

    </Menu>
}
export default AppMenuComponent