import React from 'react'
import { Menu, Dropdown, But<PERSON> } from 'antd'
import { LogoutOutlined, UserOutlined, DownOutlined } from '@ant-design/icons'
import { PublicClientApplication } from '@azure/msal-browser'
import { msalConfig } from '../../authConfig'
import { useAccount, useMsal } from '@azure/msal-react'

export const SignOutButton = () => {
  const msalInstance = new PublicClientApplication(msalConfig);
  const { accounts } = useMsal();
    const account = useAccount(accounts[0] || {});

  const handleLogout = () => {
    msalInstance.logoutRedirect();
    // localStorage.clear();
  }

  const menu = (
    <Menu>
      <Menu.Item
        key="1"
        onClick={() => handleLogout()}
        icon={<LogoutOutlined />}
      >
        Sign Out
      </Menu.Item>
    </Menu>
  )

  return (
    <div className="user-profile">
      <UserOutlined />
      <Dropdown overlay={menu}>
        <Button type="link">
          {account?.name || `<EMAIL>`}
          <DownOutlined />
        </Button>
      </Dropdown>
    </div>
  )
}
