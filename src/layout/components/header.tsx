import { HomeOutlined, InboxOutlined } from '@ant-design/icons'
import { InteractionStatus } from '@azure/msal-browser'
import { useIsAuthenticated, useMsal } from '@azure/msal-react'
import { Button, Layout, Menu } from 'antd'
import axios from 'axios'
import { FC, useEffect, useState } from 'react'
import { NavLink, useHistory } from 'react-router-dom'
import FPT_logo from '../../assets/images/FPT_logo.png'
import logo from '../../assets/images/logo.png'
import { loginRequest } from '../../authConfig'
import intl from '../../config/locale.config'
import { API_URLS, APP_COMMON_ROLES, APP_ROUTES, PROJECT_PREFIX } from '../../constants'
import { currentUserRoles, extractProjectCode, hasCommonRole } from '../../helper/share'
import { SignOutButton } from './sign-out'

const { Header } = Layout
const { SubMenu } = Menu

type Props = {
  showDashboard?: boolean
  isCommon: boolean
  children?: JSX.Element | JSX.Element[]
  accessTokenReceived?: any
  isAdmin?: boolean
}

const AppHeader: FC<Props> = (props: Props) => {
  const UserRoles = currentUserRoles()
  const isAuthenticated = useIsAuthenticated()
  const { instance, accounts, inProgress } = useMsal()
  const history = useHistory()
  const [gotUserRole, setGotUserRole] = useState<boolean>(false)
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const getUserRole = async () => {
    const request = {
      ...loginRequest,
      account: accounts[0],
    }
    const response = await instance.acquireTokenSilent(request)
    const accessToken = response.accessToken
    localStorage.setItem('accessToken', accessToken)
    axios
      .get(API_URLS.AUTH, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'Access-Control-Allow-Origin': '*',
          Authentication: `Bearer ${accessToken}`,
          Authorization: `Bearer ${accessToken}`,
        },
      })
      .then((res: any) => {
        props.accessTokenReceived(accessToken)
        localStorage.setItem('currentUserProjects', JSON.stringify(res?.data))
        setGotUserRole(true)
      })
      .catch((error) => {
        history.push(APP_ROUTES.ACCESS_DENIED);
      })
  }

  useEffect(() => {
    if (
      isAuthenticated &&
      inProgress !== InteractionStatus.Startup &&
      inProgress !== InteractionStatus.HandleRedirect
    ) {
      getUserRole()
    }
  }, [isAuthenticated, inProgress])

  useEffect(() => {
    if (hasCommonRole(APP_COMMON_ROLES.SYSTEM_ADMIN)) {
      setIsAdmin(true)
    } else {
      setIsAdmin(false)
    }
  }, [gotUserRole])

  const showCommonMenu = () => {
    return hasCommonRole(APP_COMMON_ROLES.BA_MEMBER) || hasCommonRole(APP_COMMON_ROLES.REVIEWER) || hasCommonRole(APP_COMMON_ROLES.SYSTEM_ADMIN)
  }

  return (
    <Header className="header-wrapper">
      <div className="logo-and-header-menu">
        <div className="logo-container">
          <img src={FPT_logo} alt="FPT_logo" />
          <img src={logo} alt="logo" style={{ marginLeft: '0.75rem' }} />
        </div>
        {
          !props?.isAdmin ? <Menu disabledOverflow mode="horizontal">
            {!props.isCommon && props.showDashboard ? (
              <SubMenu key="dashboard_sub" icon={<HomeOutlined />} title="Dashboard">
                <Menu.Item key="project_statistic">
                  <NavLink to={`${PROJECT_PREFIX}${extractProjectCode()}/dashboard`}>
                    {intl.formatMessage({ id: 'app.menu.project_statistic' })}
                  </NavLink>
                </Menu.Item>
                <Menu.Item key="my_dashboard">
                  <NavLink to={`${PROJECT_PREFIX}${extractProjectCode()}/my-assigned-task`}>
                    {intl.formatMessage({ id: 'app.menu.my_dashboard' })}
                  </NavLink>
                </Menu.Item>
              </SubMenu>
            ) : (
              <></>
            )}

            <SubMenu key="projects" icon={<InboxOutlined />} title="Projects">
              <Menu.Item key="all-prj">
                <NavLink to={APP_ROUTES.PROJECTS}>
                  {intl.formatMessage({ id: 'app.menu.all_projects' })}
                </NavLink>
              </Menu.Item>
              {UserRoles?.map((prj, idx) => {
                return idx <= 4 && prj.lastAccess ?
                  <Menu.Item key={`prj-${idx}`} className="rq-project-menu">
                    <NavLink
                      to={`${APP_ROUTES.PROJECT_DETAIL}${prj.projectCode}`}
                    >
                      <img
                        height={18}
                        style={{ marginRight: 8 }}
                        src={`https://avatar.oxro.io/avatar.svg?name=${prj.projectName}`}
                        alt=""
                      />
                      {prj.projectName} ({prj.projectCode})
                    </NavLink>
                  </Menu.Item>
                  : <Menu.Item key={`prj-${idx}`} style={{ display: 'none' }}></Menu.Item>
              })}
            </SubMenu>
            {
              showCommonMenu() ? <Menu.Item className="no-after" key="li-common-requirement">
                <NavLink to={isAdmin ? APP_ROUTES.COMMON_COMMITTEE : APP_ROUTES.COMMON_COMPONENT}>
                  <Button type="primary" ghost size="middle">
                    {intl.formatMessage({ id: 'app.menu.common_requirement' })}
                  </Button>
                </NavLink>
              </Menu.Item> : <></>
            }
          </Menu> : <></>
        }
      </div>
      <SignOutButton />
    </Header>
  )
}
export default AppHeader
