import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Form, Input, Modal, notification, Row, Select, Space, Spin, Table, Typography } from "antd";
import debounce from 'lodash.debounce';
import { useEffect, useState } from "react";
import { Scrollbars } from 'react-custom-scrollbars';
import intl from "../../config/locale.config";
import { COM_ARTEFACT_TYPE_ID, DEFAULT_PAGE_SIZE, LIST_NFR_CATEGORY, MESSAGE_TYPE } from "../../constants";
import CustomModal from "../../helper/component/custom-modal";
import FormGroup from "../../helper/component/form-group";
import useModalConfirmationConfig from "../../helper/hooks/useModalConfirmationConfig";
import useWindowDimensions from "../../helper/hooks/useWindowDimensions";
import { extractProjectCode, ShowAppMessage } from "../../helper/share";
import CommonCBRDetailInfo from '../../modules/common/business-rule/detail/content-preview';
import CommonEmailDetailInfo from '../../modules/common/email-templates/detail/content-preview';
import CommonMessDetailInfo from '../../modules/common/messages/detail/content-preview';
import CommonNonFunctonalDetailInfo from '../../modules/common/non-functional-requirement/detail/content-preview';
import CommonObjectDetailInfo from "../../modules/common/object/detail/content-preview";
import CommonScreenDetailInfo from "../../modules/common/screen/detail/content-preview";
import CommonUseCaseDetailInfo from "../../modules/common/usecase/detail/content-preview";
import CommonWorkflowDetailInfo from "../../modules/common/workflow/detail/content-preview";
import { listCategoryDetail } from "../../modules/non-functional-requirement/type";
import AppCommonService from "../../services/app.service";

const reqNoti = notification
const { confirm } = Modal
const { Title } = Typography

interface SelectCommonComponentModalProps {
    onDismiss: () => void
}
const SelectCommonComponentModal = ({ onDismiss }: SelectCommonComponentModalProps) => {
    const { height: windowHeight } = useWindowDimensions();
    const modalConfirmConfig = useModalConfirmationConfig();
    const [form] = Form.useForm();
    const [components, setComponents] = useState([]);
    const [artefactDetails, setArtefactDetails] = useState<any>(null);
    const [componentDetails, setComponentDetails] = useState<any>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [componentSelected, setComponentSelected] = useState<any>(null);
    const [currentTitle, setCurrentTitle] = useState<any>(document.title);

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    placeholder={`Search ${dataIndex}`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => confirm()}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => confirm()}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : ''
    });

    const columns = [
        {
            title: intl.formatMessage({ id: 'select_common_component.column.code' }),
            dataIndex: 'code',
            ...getColumnSearchProps('code'),
            render: (text, item) => {
                return <a href="#" onClick={(e) => handleViewDetail(e, item)}>{text}</a>
            }
        },
        {
            title: intl.formatMessage({ id: 'select_common_component.column.name' }),
            dataIndex: 'name',
            ...getColumnSearchProps('name'),
            render: (text, item) => {
                const category = listCategoryDetail.find(
                    (category: any) => category.id === item?.category
                )
                return text ? text : item.objective ? item.objective : item.content ? item.content : category?.name ? category.name : ''
            }
        },
        {
            title: intl.formatMessage({ id: 'select_common_component.column.description' }),
            dataIndex: 'description',
            render: text => <div
                className="tableDangerous"
                dangerouslySetInnerHTML={{
                    __html: text
                }}
            ></div>
        },
    ];

    const categoryName = (catId) => {
        const cat = LIST_NFR_CATEGORY.find(
            (category: any) => category.id === catId
        )
        return cat?.name || ''
    }


    useEffect(() => {
        document.title = extractProjectCode() +"-"+ intl.formatMessage({ id: 'select_common_component.title' });
        resetForm();
        setIsLoading(true);
        AppCommonService.getCommonComponents().then(res => {
            setComponents(res?.data?.map(e => { return { value: e.id, label: e.name } }) || [])
        }).catch(err => {
            setComponents([]);
        }).finally(() => {
            setIsLoading(false);
        })
        return () => {
            resetForm();
        }
    }, [])

    const resetForm = () => {
        setIsLoading(false);
        form.resetFields(['component']);
        setComponentSelected(null)
        setArtefactDetails(null);
        setComponentDetails([]);
        setComponents([]);
    }

    const handleViewDetail = (e, item: any) => {
        e.preventDefault();
        let artPrefix = '';
        switch (item.artefactType) {
            case COM_ARTEFACT_TYPE_ID.WORKFLOW: {
                artPrefix = 'workflows';
                break;
            }
            case COM_ARTEFACT_TYPE_ID.OBJECT: {
                artPrefix = 'objects';
                break;
            }
            case COM_ARTEFACT_TYPE_ID.SCREEN: {
                artPrefix = 'screens';
                break;
            }
            case COM_ARTEFACT_TYPE_ID.USECASE: {
                artPrefix = 'usecases';
                break;
            }
            case COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE: {
                artPrefix = 'commonbusinessrules';
                break;
            }
            case COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT: {
                artPrefix = 'nonfunctionrequirements';
                break;
            }
            case COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE: {
                artPrefix = 'emailtemplates';
                break;
            }
            case COM_ARTEFACT_TYPE_ID.MESSAGE: {
                artPrefix = 'messages';
                break;
            }
        }
        setIsLoading(true);
        AppCommonService.viewDetailArtefact(artPrefix, componentSelected?.id, item.id).then(res => {
            setArtefactDetails({
                ...res.data,
                artefactType: item.artefactType
            });
        }).catch(err => {
            setArtefactDetails(null);
        }).finally(() => {
            setIsLoading(false);
        })

    }

    const confirmCancel = () => {
        confirm({
            ...modalConfirmConfig,
            content: intl.formatMessage({ id: 'CFD_3' }),
            onOk() {
                onDismiss()
                document.title = currentTitle;
            },
            onCancel() { },
        })
    }

    const handleComponentChange = (e) => {
        if (!e) return;
        setArtefactDetails(null);
        setComponentSelected(null)
        setComponentDetails([]);
        setIsLoading(true);
        AppCommonService.getComponentArtefacts(e).then(res => {
            if (res?.data) {
                setComponentSelected(res.data)
                let artefacts: any[] = [];
                artefacts = artefacts
                    .concat(res.data.workflows?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.WORKFLOW } }) || [])
                    .concat(res.data.objects?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.OBJECT } }) || [])
                    .concat(res.data.screens?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.SCREEN } }) || [])
                    .concat(res.data.useCases?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.USECASE } }) || [])
                    .concat(res.data.commonBusinessRules?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE } }) || [])
                    .concat(res.data.emailTemplates?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE } }) || [])
                    .concat(res.data.messages?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.MESSAGE } }) || [])
                    .concat(res.data.nonFunctionalRequirements?.map((e) => { return { ...e, artefactType: COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT } }) || [])
                artefacts.sort((a, b) => b.code.localeCompare(a.code));
                setComponentDetails(artefacts);
            }
        }).catch(err => {
            ShowAppMessage(MESSAGE_TYPE.ERROR);
        }).finally(() => {
            setIsLoading(false);
        })
    }


    const onSubmit = debounce((values: any, st?: string) => {
        if (!componentDetails || componentDetails.length <= 0 || !values.component) {
            reqNoti['warning']({
                message: 'Please select at least one artefact.',
                placement: 'bottomRight',
            })
            return
        }
        setIsLoading(true);
        //, { objectIds, screenIds, usecaseIds, commonBusinessRuleIds, nonFunctionalRequirementIds, emailTemplateIds, messageIds }
        AppCommonService.selectCommonComponent(values.component).then(() => {
            reqNoti['success']({
                description: intl.formatMessage({ id: 'SCD_9' }),
                message: intl.formatMessage({ id: 'common.dialog.success' }),
                placement: 'bottomRight',
            })
            onDismiss();
        }).catch(() => {
            ShowAppMessage(MESSAGE_TYPE.ERROR)
        }).finally(() => {
            setIsLoading(false);
        })
    }, 500)

    return <CustomModal
        isLoading={isLoading}
        closable={false}
        size="large"
        visible={true}
        footer={null}
    >
        <Form
            form={form}
            name=""
            labelCol={{ offset: 0, span: 2 }}
            onFinish={onSubmit}
            autoComplete="off"
            scrollToFirstError={{ block: 'center' }}
        >
            <div className='rq-modal-header' style={{ marginBottom: 16 }}>
                <Row>
                    <Col span={10}>
                        <Title level={4}>{intl.formatMessage({ id: 'select_common_component.title' })}</Title>
                    </Col>

                    <Col span={14}>
                        <Row justify="end">
                            <Space size="small">
                                <Button disabled={isLoading} onClick={debounce(confirmCancel, 500)}>
                                    {intl.formatMessage({ id: 'common.action.close' })}
                                </Button>
                                <Button disabled={isLoading} type="primary" className="success-btn" htmlType="submit">
                                    {intl.formatMessage({ id: 'common.action.select' })}
                                </Button>
                            </Space>
                        </Row>
                    </Col>
                </Row>
            </div>

            <Scrollbars autoHide autoHeight autoHeightMin={windowHeight - 220}>
                <FormGroup required inline label={intl.formatMessage({ id: 'select_common_component.component' })} labelSpan={3} controlSpan={6}>
                    <Form.Item name="component" rules={[{ required: true, message: intl.formatMessage({ id: 'IEM_1' }) }]}>
                        <Select
                            loading={isLoading}
                            showSearch
                            optionFilterProp="children"
                            onChange={handleComponentChange}
                            filterOption={(input, option: any) => {
                                return option?.label?.toLowerCase()?.includes(input?.toLowerCase())
                            }}
                            options={components} />
                    </Form.Item>
                </FormGroup>

                <Spin spinning={isLoading}>
                    <FormGroup inline label={intl.formatMessage({ id: 'select_common_component.description' })} labelSpan={3} controlSpan={21}>
                        {componentSelected ? <div style={{ background: '#f7f7f7', padding: 10, maxHeight: 100, overflow: 'auto', marginTop: 10 }} dangerouslySetInnerHTML={{ __html: componentSelected.description || '' }} /> : <></>}
                    </FormGroup>

                    <Space direction="vertical" size="large">
                        <Table
                            locale={{
                                emptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                                filterReset: 'Reset',
                                filterEmptyText: intl.formatMessage({ id: 'common.table.no-data' }),
                                filterConfirm: 'Apply Filter',
                                selectAll: 'All',
                                selectionAll: 'Select All',
                            }}
                            columns={columns}
                            dataSource={componentDetails}
                            rowKey='code'
                            className="lav-table"
                            bordered
                            pagination={{
                                defaultPageSize: DEFAULT_PAGE_SIZE,
                                size: 'small',
                                showLessItems: true,
                                showSizeChanger: true,
                                position: ['topRight'],
                                showTotal: (total, range) =>
                                    `${range[0]}-${range[1]} ${intl.formatMessage({
                                        id: 'common.table.pagination.of',
                                    })} ${total} ${intl.formatMessage({
                                        id: 'common.table.pagination.items',
                                    })}`,
                            }}
                            loading={isLoading}
                        />
                        {
                            artefactDetails ? <Card className='rq-form-block' title={
                                artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT ? artefactDetails.code + ' - ' + categoryName(artefactDetails.category) :
                                    artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE ? artefactDetails.code + ' - ' + (artefactDetails.subject ? artefactDetails.subject : intl.formatMessage({ id: 'common_component.card.email-template' })) :
                                        artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.MESSAGE ? artefactDetails.code + ' - ' + (artefactDetails.content ? artefactDetails.content : intl.formatMessage({ id: 'common_component.card.message' })) :
                                            artefactDetails.code + ' - ' + artefactDetails.name
                            }>
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.WORKFLOW ? <CommonWorkflowDetailInfo data={artefactDetails} /> : <></>}
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.OBJECT ? <CommonObjectDetailInfo data={artefactDetails} selectable={false} /> : <></>}
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.USECASE ? <CommonUseCaseDetailInfo data={artefactDetails} /> : <></>}
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.SCREEN ? <CommonScreenDetailInfo data={artefactDetails} selectable={false} /> : <></>}
                                {/* Email */}
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE ? <CommonEmailDetailInfo data={artefactDetails} /> : <></>}
                                {/* Message */}
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.MESSAGE ? <CommonMessDetailInfo data={artefactDetails} /> : <></>}
                                {/* Non functional Business */}
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT ? <CommonNonFunctonalDetailInfo data={artefactDetails} /> : <></>}
                                {/* Common Business Rule */}
                                {artefactDetails.artefactType === COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE ? <CommonCBRDetailInfo data={artefactDetails} /> : <></>}
                            </Card> : <></>
                        }

                    </Space>
                </Spin>
            </Scrollbars>
        </Form>
    </CustomModal>
}
export default SelectCommonComponentModal;