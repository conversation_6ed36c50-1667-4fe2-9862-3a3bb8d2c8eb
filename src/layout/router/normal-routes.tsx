import React, { Suspense } from "react";
import { Route, useHistory } from "react-router-dom";
import { APP_COMMON_ROLES, APP_ROUTES } from "../../constants";
import { hasCommonRole } from "../../helper/share";
import AppLoader from "../components/loader";
import NormalLayout from "../normal-layout";

export const NormalRoute = ({ isCommon = false, isAdmin = false, component: Component, ...rest }) => {
    const history = useHistory();
    if (isCommon && !hasCommonRole(APP_COMMON_ROLES.BA_MEMBER) && !hasCommonRole(APP_COMMON_ROLES.REVIEWER) && !hasCommonRole(APP_COMMON_ROLES.SYSTEM_ADMIN)) {
        history.push(APP_ROUTES.ACCESS_DENIED);
    }
    return (
        <Route {...rest} render={
            (props) => (
                <NormalLayout isCommon={isCommon} isAdmin={isAdmin}>
                    <Suspense fallback={<AppLoader />}>
                        <Component {...props} />
                    </Suspense>
                </NormalLayout>
            )
        } />
    );
};
