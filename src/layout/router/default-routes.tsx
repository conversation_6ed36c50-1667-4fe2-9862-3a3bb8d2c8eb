import React, { Suspense } from "react";
import { Route } from "react-router-dom";
import BlankLayout from "../blank-layout";
import AppLoader from "../components/loader";

export const DefaultRoute = ({ component: Component, ...rest }) => {
    return (
        <Route {...rest} render={
            (props) => (
                <BlankLayout>
                    <Suspense fallback={<AppLoader />}>
                        <Component {...props} />
                    </Suspense>
                </BlankLayout>
            )
        }/>
    );
};
