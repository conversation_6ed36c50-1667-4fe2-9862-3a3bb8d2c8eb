import React from 'react'
import { <PERSON>h<PERSON><PERSON><PERSON>, Redirect, Switch } from 'react-router-dom'
import { APP_ROUTES, PROJECT_PREFIX } from '../../constants'
import AccessDeniedPage from '../../modules/access-denied'
import NotFoundPage from '../../modules/page-not-found'
import ProjectsPage from '../../modules/project-management'
import ProjectDetails from '../../modules/project-management/detail'
import { DefaultRoute } from './default-routes'
import { NormalRoute } from './normal-routes'

// Main
const DashboardPage = React.lazy(() => import("../../modules/dashboard/statistic"));
const MyAssignedTask = React.lazy(() => import("../../modules/dashboard/my-dashboard/my-assigned-task"));
const ReviewTask = React.lazy(() => import("../../modules/dashboard/my-dashboard/my-pending-review-task"));
const QualityReport = React.lazy(() => import("../../modules/dashboard/my-dashboard/quality-report"));
const MeetingMinutesPage = React.lazy(() => import("../../modules/meeting-minutes"));
const UserRequirementPage = React.lazy(() => import("../../modules/user-requirement"));
const ReferenceDocumentPage = React.lazy(() => import("../../modules/reference-document"));
const ObjectRelationshipDiagramPage = React.lazy(() => import("../../modules/object-relationship-diagram"));
const ObjectPage = React.lazy(() => import("../../modules/objects"));
const ActorPage = React.lazy(() => import("../../modules/actor"));
const WorkflowPage = React.lazy(() => import("../../modules/workflow"));
const StateTransitionPage = React.lazy(() => import("../../modules/state-transition"));
const UsecaseDiagramPage = React.lazy(() => import("../../modules/usecase-diagram"));
const PermissionMatrixPage = React.lazy(() => import("../../modules/permission-matrix"));
const UsecasePage = React.lazy(() => import("../../modules/usecase"));

const UsecaseCreatePage = React.lazy(() => import("../../modules/usecase/form/form"));

const BusinessRulePage = React.lazy(() => import("../../modules/business-rule"));
const MockupScreenPage = React.lazy(() => import("../../modules/mockup-screen"));
const NonFunctionalRequirementPage = React.lazy(() => import("../../modules/non-functional-requirement"));
const OtherRequirementPage = React.lazy(() => import("../../modules/other-requirement"));
const ProjectWorkerAgentPage = React.lazy(() => import("../../modules/project-worker-agent"));
const DataMigrationPage = React.lazy(() => import("../../modules/data-migration"));
const MessagePage = React.lazy(() => import("../../modules/messages"));
const EmailTemplatePage = React.lazy(() => import("../../modules/email-templates"));
const EpicManagementPage = React.lazy(() => import("../../modules/epic-management"));
const ValidateSRS = React.lazy(() => import("../../modules/validate-srs"));
const GenerateSRS = React.lazy(() => import("../../modules/generate-srs"));
const GenerateWBS = React.lazy(() => import("../../modules/generate-wbs"));
const SprintManagement = React.lazy(() => import("../../modules/sprint-management"));
const UserStoryManagement = React.lazy(() => import("../../modules/user-story-management"));
const RecommendedCommonRequirement = React.lazy(() => import("../../modules/recommendedcommonrequirement"));

const GlossaryPage = React.lazy(() => import("../../modules/glossary"));

// Detail
const MeetingMinuteDetailPage = React.lazy(() => import("../../modules/meeting-minutes/detail"));
const UserRequirementDetailPage = React.lazy(() => import("../../modules/user-requirement/detail"));
const ReferenceDocumentDetailPage = React.lazy(() => import("../../modules/reference-document/detail"));
const ObjectRelationshipDiagramDetailPage = React.lazy(() => import("../../modules/object-relationship-diagram/detail"));
const ObjectDetailPage = React.lazy(() => import("../../modules/objects/detail"));
const ActorDetailPage = React.lazy(() => import("../../modules/actor/detail"));
const WorkFlowDetailPage = React.lazy(() => import("../../modules/workflow/detail"));
const StateTransitionDetailPage = React.lazy(() => import("../../modules/state-transition/detail"));
const UsecaseDiagramDetailPage = React.lazy(() => import("../../modules/usecase-diagram/detail"));
const UsecaseDetailPage = React.lazy(() => import("../../modules/usecase/detail"));
const BusinessRuleDetailPage = React.lazy(() => import("../../modules/business-rule/detail"));
const MockupScreenDetailPage = React.lazy(() => import("../../modules/mockup-screen/detail"));
const NonFunctionalRequirementDetailPage = React.lazy(() => import("../../modules/non-functional-requirement/detail"));
const OtherRequirementDetailPage = React.lazy(() => import("../../modules/other-requirement/detail"));

const DataMigrationDetailPage = React.lazy(() => import("../../modules/data-migration/detail"));
const MessageDetailPage = React.lazy(() => import("../../modules/messages/detail"));
const EmailTemplateDetailPage = React.lazy(() => import("../../modules/email-templates/detail"));
// const NonFunctionalRequirementPage = React.lazy(() => import("../../modules/non-functional-requirement"));
const SprintManagementDetailPage = React.lazy(() => import("../../modules/sprint-management/detail"));
const UserStoryManagementDetailPage = React.lazy(() => import("../../modules/user-story-management/detail"));
const EpicManagementDetailPage = React.lazy(() => import("../../modules/epic-management/detail"));
const RecommendDetailPage = React.lazy(() => import("../../modules/recommendedcommonrequirement/detail"));



/////////// COMMON
// Main
const CommonObjectPage = React.lazy(() => import("../../modules/common/object"));
const CommonUsecasePage = React.lazy(() => import("../../modules/common/usecase"));
const CommonScreenPage = React.lazy(() => import("../../modules/common/screen"));
const CommonComponentPage = React.lazy(() => import("../../modules/common/component"));
const CommonCommitteePage = React.lazy(() => import("../../modules/common/committee"));
const CommonCBR = React.lazy(() => import("../../modules/common/business-rule"))
const CommonNonfunctional = React.lazy(() => import("../../modules/common/non-functional-requirement"))
const CommonEmailTemplate = React.lazy(() => import("../../modules/common/email-templates"));
const CommonMessage = React.lazy(() => import("../../modules/common/messages"));
const CommonWorkflow = React.lazy(() => import("../../modules/common/workflow"));

// Detail
const CommonObjectDetailPage = React.lazy(() => import("../../modules/common/object/detail"));
const CommonUsecaseDetailPage = React.lazy(() => import("../../modules/common/usecase/detail"));
const CommonScreenDetailPage = React.lazy(() => import("../../modules/common/screen/detail"));
const CommonComponentDetailPage = React.lazy(() => import("../../modules/common/component/detail"));
const CommonBusinessRuleDetailPage = React.lazy(() => import("../../modules/common/business-rule/detail"))
const CommonEmailTemplateDetail = React.lazy(() => import("../../modules/common/email-templates/detail"))
const CommonNonFuntionalRequirementDetailPage = React.lazy(() => import("../../modules/common/non-functional-requirement/detail"))
const CommonMessageDetail = React.lazy(()=> import("../../modules/common/messages/detail"))
const CommonWorkflowDetail = React.lazy(() => import("../../modules/common/workflow/detail"));

// Admin
const AdminProject = React.lazy(() => import("../../modules/admin-project"));
const AdminSupervisorAgent = React.lazy(() => import("../../modules/admin-supervisor-agent"));
const AdminWorkerAgent = React.lazy(() => import("../../modules/admin-worker-agent"))

const Navigator = () => {
  const routerPrefix = `${PROJECT_PREFIX}:projectCode`;
  return (
    <HashRouter>
      <Switch>
        {/* ======================== FUNCTIONS ======================== */}
        {/* Main page */}
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.DASHBOARD}`} component={DashboardPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MEETING}`} component={MeetingMinutesPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USER_REQUIREMENT}`} component={UserRequirementPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.REFERENCE_DOCUMENT}`} component={ReferenceDocumentPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.OBJECT_RELATIONSHIP}`} component={ObjectRelationshipDiagramPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.OBJECT}`} component={ObjectPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.ACTOR}`} component={ActorPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.WORKFLOW}`} component={WorkflowPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.STATE_TRANSITION}`} component={StateTransitionPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USECASE_DIAGRAM}`} component={UsecaseDiagramPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.PERMISSION_MATRIX}`} component={PermissionMatrixPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USECASE}`} component={UsecasePage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.COMMON_BUSINESS_RULE}`} component={BusinessRulePage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.SCREEN}`} component={MockupScreenPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.NONFUNTIONAL_REQ}`} component={NonFunctionalRequirementPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.OTHER_REQUIREMENT}`} component={OtherRequirementPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.PROJECT_WORKER_AGENT}`} component={ProjectWorkerAgentPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.DATA_MIGRATION}`} component={DataMigrationPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MESSAGE}`} component={MessagePage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MAIL}`} component={EmailTemplatePage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MY_ASSIGNED_TASK}`} component={MyAssignedTask} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MY_ASSIGNED_TASK}/status/:status`} component={MyAssignedTask} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.REVIEW_TASK}`} component={ReviewTask} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.QUALITY_REPORT}`} component={QualityReport} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.EPIC_MANAGEMENT}`} component={EpicManagementPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.GENERATE_SRS}`} component={GenerateSRS} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.GENERATE_WBS}`} component={GenerateWBS} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.VALIDATION_RESULT}`} component={ValidateSRS} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.VALIDATION_RESULT}/:scope`} component={ValidateSRS} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.VALIDATION_RESULT}/:scope/:d`} component={ValidateSRS} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.SPRINT_MANAGEMENT}`} component={SprintManagement} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USER_STORY_MANAGEMENT}`} component={UserStoryManagement} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.EPIC_MANAGEMENT}`} component={EpicManagementDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.RECOMMENED}`} component={RecommendedCommonRequirement} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USECASE_CREATE}`} component={UsecaseCreatePage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.GLOSSARY}`} component={GlossaryPage} />

        {/* Detail page */}
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MEETING_DETAIL}:meetingID`} component={MeetingMinuteDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USER_REQUIREMENT_DETAIL}:userRequirementID`} component={UserRequirementDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}:referenceDocumentID`} component={ReferenceDocumentDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL}:objectRelationshipID`} component={ObjectRelationshipDiagramDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.OBJECT_DETAIL}:objectID`} component={ObjectDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.ACTOR_DETAIL}:actorID`} component={ActorDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.WORKFLOW_DETAIL}:workFlowID`} component={WorkFlowDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.STATE_TRANSITION_DETAIL}:stateTransitionID`} component={StateTransitionDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USECASE_DIAGRAM_DETAIL}:useCaseDiagramID`} component={UsecaseDiagramDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USECASE_DETAIL}:useCaseID`} component={UsecaseDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL}:businessRuleID`} component={BusinessRuleDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.SCREEN_DETAIL}:screenID`} component={MockupScreenDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.NONFUNTIONAL_REQ_DETAIL}:nonFunctionalID`} component={NonFunctionalRequirementDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.OTHER_REQUIREMENT_DETAIL}:otherRequirementID`} component={OtherRequirementDetailPage} />
        
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.DATA_MIGRATION_DETAIL}:dataMigrationID`} component={DataMigrationDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MESSAGE_DETAIL}:messID`} component={MessageDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MAIL_DETAIL}:emailID`} component={EmailTemplateDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.MAIL_DETAIL}:epicId`} component={EpicManagementPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.SPRINT_MANAGEMENT_DETAIL}:sprintId`} component={SprintManagementDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.EPIC_MANAGEMENT_DETAIL}:epicId`} component={EpicManagementDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.RECOMMENED_DETAIL}:recommendId`} component={RecommendDetailPage} />
        <NormalRoute exact path={`${routerPrefix}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}:userStoryId`} component={UserStoryManagementDetailPage} />



        {/* ======================== COMMON ======================== */}
        {/* Main page */}
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_OBJECT} component={CommonObjectPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_CBR} component={CommonCBR}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_NONFUNCTIONAL} component={CommonNonfunctional}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_EMAIL} component={CommonEmailTemplate}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_MESSAGE} component={CommonMessage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_WORKFLOW} component={CommonWorkflow}></NormalRoute>

        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_USECASE} component={CommonUsecasePage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_SCREEN} component={CommonScreenPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_COMPONENT} component={CommonComponentPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_COMMITTEE} component={CommonCommitteePage}></NormalRoute>
        {/* Detail page */}
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_OBJECT_DETAIL + ':id'} component={CommonObjectDetailPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_USECASE_DETAIL + ':usecaseID'} component={CommonUsecaseDetailPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_SCREEN_DETAIL + ':screenID'} component={CommonScreenDetailPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_COMPONENT_DETAIL + ':componentID'} component={CommonComponentDetailPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_CBR_DETAIL + ':businessRuleID'} component={CommonBusinessRuleDetailPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_EMAIL_DETAIL + ':emailID'} component={CommonEmailTemplateDetail}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_NONFUNCTIONAL_DETAIL + ':nonFunctionalID'} component={CommonNonFuntionalRequirementDetailPage}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_MESSAGE_DETAIL + ':messID'} component={CommonMessageDetail}></NormalRoute>
        <NormalRoute exact isCommon={true} path={APP_ROUTES.COMMON_WORKFLOW_DETAIL + ':workflowID'} component={CommonWorkflowDetail}></NormalRoute>

        <NormalRoute exact isCommon={false} isAdmin={true} path={APP_ROUTES.ADMIN_PROJECT} component={AdminProject}></NormalRoute>
        <NormalRoute exact isCommon={false} isAdmin={true} path={APP_ROUTES.ADMIN_SUPERVISOR_AGENT} component={AdminSupervisorAgent}></NormalRoute>
        <NormalRoute exact isCommon={false} isAdmin={true} path={APP_ROUTES.ADMIN_WORKER_AGENT} component={AdminWorkerAgent}></NormalRoute>
        
        {/* ======================== OTHER ======================== */}
        <DefaultRoute exact path={APP_ROUTES.PROJECTS} component={ProjectsPage}></DefaultRoute>
        <DefaultRoute exact path={`${APP_ROUTES.PROJECT_DETAIL}:projectCode`} component={ProjectDetails} ></DefaultRoute>
        <DefaultRoute exact path={APP_ROUTES.ACCESS_DENIED} component={AccessDeniedPage}></DefaultRoute>
        <DefaultRoute exact path={APP_ROUTES.PAGE_NOT_FOUND} component={NotFoundPage}></DefaultRoute>
        <DefaultRoute exact path="/" component={ProjectsPage} />
        <Redirect from='*' to={APP_ROUTES.PAGE_NOT_FOUND} />
      </Switch>
    </HashRouter>
  )
}
export default Navigator
