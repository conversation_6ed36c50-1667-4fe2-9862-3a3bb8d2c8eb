import { useEffect, useState } from 'react'
import { AccountInfo, InteractionRequiredAuthError, InteractionStatus } from '@azure/msal-browser'
import { useMsal } from '@azure/msal-react'
import { callMsGraph } from '../../graph'
import { loginRequest } from '../../authConfig'
import Navigator from './navigator'
import { Space, Spin, Typography } from 'antd'

const AuthNavigator = () => {
  const { instance, inProgress } = useMsal();
  const [graphData, setGraphData] = useState<any>(null);
  useEffect(() => {
    if (!graphData && inProgress === InteractionStatus.None) {
      callMsGraph().then(response => setGraphData(response)).catch((e) => {
        if (e instanceof InteractionRequiredAuthError) {
          instance.acquireTokenRedirect({
            ...loginRequest,
            account: instance.getActiveAccount() as AccountInfo
          });
        }
      });
    }
  }, [inProgress, graphData, instance]);

  const Loading = () => {
    return <div className='app-loader'>
      <Space direction='vertical' align='center'>
        <Spin spinning></Spin>
        <Typography>Authentication in progress...</Typography>
      </Space>
    </div>
  }

  return (
    graphData ? <Navigator></Navigator> : <Loading />
  )
}

export default AuthNavigator