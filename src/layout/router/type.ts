import React from 'react'

export interface MenuConfigType {
  name?: JSX.Element | string
  noBreadcrumb?: true
  path: string
  icon?: React.ReactNode | JSX.Element | string
  hidden?: boolean
  redirect?: string
  exact?: true
  component?: React.ComponentType
  isInputManagement?: boolean
  isHighLevel?: boolean
  isFunctionalReq?: boolean
  isAppendices?: boolean
  isSubMenu?: boolean
  isReport?: boolean
  isCommonArtefacts?: boolean
}
export interface RouteStore {
  path: string
  parent: string
  node: MenuConfigType
  child?: RouteStore[]
}
