import { Layout } from 'antd'
import React, { FC, useState } from 'react'
import AppHeader from '../components/header'
import '../style.less'

const { Content } = Layout

type Props = { children?: JSX.Element | JSX.Element[] }

const BlankLayout: FC<Props> = (props: Props) => {
    const [accessToken, setAccessToken] = useState(null);
    return (
        <Layout>
            <AppHeader isCommon={false} accessTokenReceived={setAccessToken} />

            <Layout className='page-wrapper expaned fullfill'>
                <Content className='main-content-view' style={{ position: 'relative' }}>
                    {accessToken ? props.children : <></>}
                </Content>
            </Layout>
        </Layout>
    )
}
export default BlankLayout
