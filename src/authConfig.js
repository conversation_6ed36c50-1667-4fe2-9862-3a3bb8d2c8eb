/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

import { LogLevel } from '@azure/msal-browser'

/**
 * Configuration object to be passed to MSAL instance on creation.
 * For a full list of MSAL.js configuration parameters, visit:
 * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-browser/docs/configuration.md
 */
export const msalConfig = {

  auth: {
    // clientId: '21f309b7-af03-4d54-b2dd-f41396179c63',
    clientId: process.env.REACT_APP_CLIENT_ID ?? '', // BA
    // clientId: '283f7b21-4484-467d-bf28-6cd23e27a648', //PROD
    authority: process.env.REACT_APP_AUTHORITY,
    redirectUri: process.env.REACT_APP_API_LOGIN + '/',
  },
  cache: {
    cacheLocation: 'localStorage', // This configures where your cache will be stored
    storeAuthStateInCookie: false, // Set this to "true" if you are having issues on IE11 or Edge
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) {
          return
        }
        switch (level) {
          case LogLevel.Error:
            console.error('###############')
            console.error(message)
            return
          case LogLevel.Info:
            console.info(message)
            return
          case LogLevel.Verbose:
            console.debug(message)
            return
          case LogLevel.Warning:
            console.warn(message)
            return
        }
      },
    },
  },
}

/**
 * Scopes you add here will be prompted for user consent during sign-in.
 * By default, MSAL.js will add OIDC scopes (openid, profile, email) to any login request.
 * For more information about OIDC scopes, visit:
 * https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-permissions-and-consent#openid-connect-scopes
 */
export const loginRequest = {
  // scopes: [
  //   //"api://39c40777-b211-46b0-b124-7a20ba46f7d5/access_as_user", 
  //   "api://ca13b70b-acd7-4f6b-b407-1f132a99c3be/access_as_user" //BA
  //   //"api://38758e2c-8d70-4591-9f61-c88a38d2b2ee/access_as_user" // PROD
  // ],
  scopes: [...(process.env.REACT_APP_SCOPES ? [process.env.REACT_APP_SCOPES] : [])]
}

/**
 * Add here the scopes to request when obtaining an access token for MS Graph API. For more information, see:
 * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-browser/docs/resources-and-scopes.md
 */
export const graphConfig = {
  graphMeEndpoint: 'https://graph.microsoft.com/v1.0/me',
}
