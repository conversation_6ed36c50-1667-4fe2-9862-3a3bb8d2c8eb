{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "devDependencies": {"@ckeditor/ckeditor5-adapter-ckfinder": "^31.1.0", "@ckeditor/ckeditor5-alignment": "^31.1.0", "@ckeditor/ckeditor5-autoformat": "^31.1.0", "@ckeditor/ckeditor5-basic-styles": "^31.1.0", "@ckeditor/ckeditor5-block-quote": "^31.1.0", "@ckeditor/ckeditor5-cloud-services": "^31.1.0", "@ckeditor/ckeditor5-dev-utils": "^25.4.5", "@ckeditor/ckeditor5-dev-webpack-plugin": "^25.4.5", "@ckeditor/ckeditor5-editor-classic": "^31.1.0", "@ckeditor/ckeditor5-essentials": "^31.1.0", "@ckeditor/ckeditor5-font": "^31.1.0", "@ckeditor/ckeditor5-heading": "^31.1.0", "@ckeditor/ckeditor5-image": "^31.1.0", "@ckeditor/ckeditor5-indent": "^31.1.0", "@ckeditor/ckeditor5-link": "^31.1.0", "@ckeditor/ckeditor5-list": "^31.1.0", "@ckeditor/ckeditor5-media-embed": "^31.1.0", "@ckeditor/ckeditor5-mention": "^31.1.0", "@ckeditor/ckeditor5-paragraph": "^31.1.0", "@ckeditor/ckeditor5-paste-from-office": "^31.1.0", "@ckeditor/ckeditor5-table": "^31.1.0", "@ckeditor/ckeditor5-theme-lark": "^31.1.0", "@ckeditor/ckeditor5-typing": "^31.1.0", "css-loader": "^5.2.7", "postcss": "^8.4.4", "postcss-loader": "^4.3.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "webpack": "^4.46.0", "webpack-cli": "^4.9.1"}, "scripts": {"build": "webpack --mode production"}}