user nginx;
worker_processes 1;
 
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;
 
events {
  worker_connections 1024;
}
 
http {
  include /etc/nginx/mime.types;
  default_type application/octet-stream;
  log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                  '$status $body_bytes_sent "$http_referer" '
                  '"$http_user_agent" "$http_x_forwarded_for"';
  
  gzip on;
  sendfile on;
  tcp_nopush on;
  keepalive_timeout 65;
  access_log /var/log/nginx/access.log main;
 
  server {
    listen 80;
 
    root /usr/share/nginx/html/;
    index index.html;
 
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
      expires 2d;
      add_header Cache-Control "public, no-transform";
    }
 
    location ~ ^/(static|assets|img)/ {
      root /usr/share/nginx/html/;
      try_files $uri $uri/ /index.html =404;
    }
 
    location ~ \.(js|ico|html|json)$ {
      root /usr/share/nginx/html/;
    }
 
    location ~ / {
      try_files /index.html = 404;
    }
  }
}