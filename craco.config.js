const CracoLessPlugin = require('craco-less')
const <PERSON>raco<PERSON><PERSON><PERSON> = require('craco-alias')

module.exports = {
  plugins: [
    {
      plugin: CracoLessPlugin,
      options: {
        lessLoaderOptions: {
          lessOptions: {
            modifyVars: {
              '@primary-color': '#2979FF',
              '@success-color': '#00C853',
              '@text-color': '#081833',
              '@disabled-color': '#515560',
              '@danger-color': '#FF3D00',
              '@font-size-base': '14px',
            },
            javascriptEnabled: true,
          },
        },
      },
    },
    {
      plugin: CracoAlias,
      options: {
        source: 'tsconfig',
        tsConfigPath: 'tsconfig.paths.json',
      },
    },
  ],
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      webpackConfig.module.rules.push({
        test: /\.mjs$/,
        include: /node_modules/,
        type: 'javascript/auto',
      })
      webpackConfig.resolve.extensions.unshift('.tsx', '.ts')

      return webpackConfig
    },
  },
}
